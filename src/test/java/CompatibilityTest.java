import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.BaseTest;
import cn.hydee.ydjia.merchantcustomer.controller.ActivityIntegralController;
import cn.hydee.ydjia.merchantcustomer.dto.ActivityIntegralDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActIntegralCommTypeReq;
import cn.hydee.ydjia.merchantcustomer.dto.resp.ActIntegralCommTypeResp;
import cn.hydee.ydjia.merchantcustomer.feign.domain.HomepageDimension;
import cn.hydee.ydjia.merchantcustomer.feign.domain.HomepageItem;
import cn.hydee.ydjia.merchantcustomer.feign.dto.HomeSetDTO;
import cn.hydee.ydjia.merchantcustomer.feign.service.MallRedisService;
import cn.hydee.ydjia.merchantcustomer.service.impl.ActivityIntegralCartServiceImpl;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: mengzilei
 * @Date: 2024-08-19  10:04
 *  测试首页装修 改版以后线上是否兼容
 */
public class CompatibilityTest extends BaseTest {

    @Autowired
    private MallRedisService mallRedisService;

    @Autowired
    private ActivityIntegralCartServiceImpl activityIntegralCartService;
    @Autowired
    private ActivityIntegralController activityIntegralController;

    @Test
    public void testQueryMain(){
        // 生产的value
        String homeSets = "[{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"groupType\":\"basics\",\"id\":\"dfe78c0cdfdd46cd8b034a74e43a1b3e\",\"isAutomaticCarousel\":0,\"sort\":0,\"subType\":\"second\",\"type\":\"navigation\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"f248769ce6ac488d957094e6d570a5ab\",\"isAutomaticCarousel\":0,\"sort\":1,\"subType\":\"five\",\"type\":\"advertisement\"},{\"chooseFlag\":0,\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"groupType\":\"basics\",\"id\":\"7e6e9003d05a49a0ae86129a00b45ca2\",\"isAutomaticCarousel\":0,\"sort\":2,\"subType\":\"first\",\"type\":\"hotZone\",\"value\":\"https://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/20231114/474e6a6bad724decbca66714299de089.png\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"extAttr\":\"[{\\\"cardColor\\\":\\\"red\\\",\\\"chooseFlag\\\":0,\\\"mainTitle\\\":\\\"热卖榜单\\\",\\\"sort\\\":0,\\\"subTitle\\\":\\\"甄选爆款\\\",\\\"url\\\":\\\"/activity/spike/list?merCode=500001&activityId=1806422502252037385\\\"},{\\\"cardColor\\\":\\\"yellow\\\",\\\"chooseFlag\\\":0,\\\"mainTitle\\\":\\\"限时抢购\\\",\\\"sort\\\":1,\\\"subTitle\\\":\\\"手慢无\\\",\\\"url\\\":\\\"/activity/spike/list?merCode=500001&activityId=1807178854712603139\\\"}]\",\"id\":\"2165e533c5694616ae36c5b698cbc7ed\",\"isAutomaticCarousel\":0,\"sort\":3,\"subType\":\"second\",\"title\":\"广告款-活动样式\",\"type\":\"adFrame\"},{\"chooseFlag\":1,\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"777e8e695d5d4e5ca517016740cf1a59\",\"isAutomaticCarousel\":1,\"sort\":4,\"subType\":\"first\",\"type\":\"activityAggregate\",\"value\":\"13\"},{\"chooseFlag\":0,\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"groupType\":\"basics\",\"id\":\"a0d5c1e55b96425a8a0c45d7c96b077e\",\"isAutomaticCarousel\":0,\"sort\":5,\"subType\":\"first\",\"type\":\"hotZone\",\"value\":\"https://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/20231114/1f144fe5e0524f0caa9b720979f5165e.png\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"47c6943485f74b5ca1255d405043e344\",\"isAutomaticCarousel\":0,\"sort\":6,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"3a58b757f1064680a120db513ba40514\",\"isAutomaticCarousel\":1,\"sort\":7,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"b4bdb27c7e914682abea7c734b19979c\",\"isAutomaticCarousel\":0,\"sort\":8,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"169f332bdaa148a185b5241fec0e0208\",\"isAutomaticCarousel\":0,\"sort\":9,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"c70f068432a64e24977b03e2f62fc426\",\"isAutomaticCarousel\":1,\"sort\":10,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"ce20de9655e74d31b7a332ff6cd0914b\",\"isAutomaticCarousel\":0,\"sort\":11,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"c126b2bbc05a4fc6ac0fbfe5260a52a1\",\"isAutomaticCarousel\":0,\"sort\":12,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"2b2b2aa8dc284c67903286701f5479d9\",\"isAutomaticCarousel\":0,\"sort\":13,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"f2193ef9f41347d99de7e8780c42bbef\",\"isAutomaticCarousel\":0,\"sort\":14,\"subType\":\"four\",\"type\":\"advertisement\"},{\"chooseFlag\":0,\"commodityOrigin\":1,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"6dabd0330eda49b1beca89e136f66ac5\",\"isAutomaticCarousel\":0,\"sort\":15,\"subType\":\"second\",\"type\":\"commodity\"},{\"chooseFlag\":0,\"commodityOrigin\":1,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"cab1ece2d60946a3a3bd3f204a7a1ea0\",\"isAutomaticCarousel\":0,\"sort\":16,\"subType\":\"second\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"7084956b8e4b46f6938f686f26e6e097\",\"id\":\"b27089ead5a04e64b15ef7df92a9ecc1\",\"isAutomaticCarousel\":0,\"sort\":17,\"subType\":\"four\",\"type\":\"advertisement\"}]";
       String homeSets2 = "[{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"groupType\":\"basics\",\"id\":\"9d84c6a1a67a4c0ea2aa044ff1c59b36\",\"isAutomaticCarousel\":0,\"sort\":0,\"subType\":\"second\",\"type\":\"navigation\"},{\"chooseFlag\":0,\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"1e2bea197bcb484085d4ec06b17d5e4c\",\"isAutomaticCarousel\":0,\"sort\":1,\"subType\":\"first\",\"type\":\"placeholder\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"groupType\":\"basics\",\"id\":\"7da91cbbdcfc4c4f8706e5a84724c43f\",\"isAutomaticCarousel\":0,\"sort\":2,\"subType\":\"five\",\"type\":\"advertisement\"},{\"chooseFlag\":0,\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"groupType\":\"basics\",\"id\":\"32457c93c50b40aa8bb656a20126833b\",\"isAutomaticCarousel\":0,\"sort\":3,\"subType\":\"first\",\"type\":\"hotZone\",\"value\":\"https://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/20231114/474e6a6bad724decbca66714299de089.png\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"extAttr\":\"[{\\\"cardColor\\\":\\\"red\\\",\\\"chooseFlag\\\":0,\\\"mainTitle\\\":\\\"热卖榜单\\\",\\\"sort\\\":0,\\\"subTitle\\\":\\\"甄选爆款\\\"},{\\\"cardColor\\\":\\\"yellow\\\",\\\"chooseFlag\\\":0,\\\"mainTitle\\\":\\\"限时抢购\\\",\\\"sort\\\":1,\\\"subTitle\\\":\\\"手慢无\\\",\\\"url\\\":\\\"/activity/spike/list?merCode=500001&activityId=1807178854712603139\\\"}]\",\"id\":\"a0f012fb0c714d9286fe0ece31838588\",\"isAutomaticCarousel\":0,\"sort\":4,\"subType\":\"second\",\"title\":\"广告款-活动样式\",\"type\":\"adFrame\"},{\"chooseFlag\":1,\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"a9c385a56aff4e1cb1b3ff424b8788ce\",\"isAutomaticCarousel\":0,\"sort\":5,\"subType\":\"first\",\"type\":\"activityAggregate\",\"value\":\"13\"},{\"chooseFlag\":0,\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"groupType\":\"basics\",\"id\":\"f50065c81fc343668ecca3221977fb05\",\"isAutomaticCarousel\":0,\"sort\":6,\"subType\":\"first\",\"type\":\"hotZone\",\"value\":\"https://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/ydjia-merchant-manager/500001/20231114/1f144fe5e0524f0caa9b720979f5165e.png\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"475f340d06c0447988cf270b6f5382f4\",\"isAutomaticCarousel\":0,\"sort\":7,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"18c7a08b1b9d4ea18a1cb707a880a95c\",\"isAutomaticCarousel\":1,\"sort\":8,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"9b9dd3d418fa4155b6f1ad5ee93d905c\",\"isAutomaticCarousel\":1,\"sort\":9,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"a3f0a3113a2849f0913e0e9951e4a12c\",\"isAutomaticCarousel\":1,\"sort\":10,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"70fc564a7e4a492a84454529952dc81d\",\"isAutomaticCarousel\":0,\"sort\":11,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"dc15832f777b4ddd9ae955071446b55e\",\"isAutomaticCarousel\":1,\"sort\":12,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"5887782a0e2646d29fcb82f28764491d\",\"isAutomaticCarousel\":0,\"sort\":13,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"719da3c4a3f740d1ab6a688c7de9ac3e\",\"isAutomaticCarousel\":0,\"sort\":14,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":1,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"d38152c8f927425dbf91ae7e100652cd\",\"isAutomaticCarousel\":0,\"sort\":15,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"8176bfca17ad4706ab8815a37a7efafe\",\"isAutomaticCarousel\":0,\"sort\":16,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"d32524d4c0f9432196830c6f97ceccff\",\"isAutomaticCarousel\":1,\"sort\":17,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"caecf6c9e3bc4f5c95b41985667c0359\",\"isAutomaticCarousel\":0,\"sort\":18,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"dc63dd7dc4f74c34a6f2c434e033a974\",\"isAutomaticCarousel\":0,\"sort\":19,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"40d6346c55e5447bb45768334e94e9be\",\"isAutomaticCarousel\":0,\"sort\":20,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"1ff53a4df6c84d06ab4bda29a327c35c\",\"isAutomaticCarousel\":0,\"sort\":21,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"97677457cfa24d9ca1951fe8b61bde30\",\"isAutomaticCarousel\":0,\"sort\":22,\"subType\":\"four\",\"type\":\"advertisement\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"68e6b52dfe8b4a788e26f0d303ab1e3d\",\"isAutomaticCarousel\":0,\"sort\":23,\"subType\":\"four\",\"type\":\"commodity\"},{\"commodityOrigin\":0,\"dimensionId\":\"abc07e4f17a844dfb953df1905e4ea8e\",\"id\":\"6b203d65693341159744fc1559c95d92\",\"isAutomaticCarousel\":0,\"sort\":24,\"subType\":\"four\",\"type\":\"advertisement\"}]";


        System.out.println(JSON.parseObject(homeSets2, new TypeReference<List<HomeSetDTO>>() {
        }));
    }


    @Test
    public void testQueryMain2(){
        // 测试环境的value
        String cacheKey = "mall:org_dimension:merCode:" + "500001" + ":" + "";
        Object object = mallRedisService.getObject("test_home");
        System.out.println(object);
        HomepageDimension dimension = (HomepageDimension) mallRedisService.getObject("mall:org_dimension:merCode:500001:");
        System.out.println(dimension);
//        System.out.println(JSON.parseObject(homeSets2, new TypeReference<List<HomeSetDTO>>() {
//        }));
    }



    @Test
    public void testQueryMainItem(){
        Object object = mallRedisService.getObject("test_item_mzl");
        String key = "test_item_mzl";
        List<HomepageItem> itemList = null;

        itemList = (List<HomepageItem>) object;
        System.out.println(itemList.size());
    }

    @Test
    public void testqueryActIntegralCommType(){
        ActIntegralCommTypeReq req = new ActIntegralCommTypeReq();
        req.setMerCode("500001");
        ActIntegralCommTypeResp actIntegralCommTypeResp = activityIntegralCartService.queryActIntegralCommType(req);

        System.out.println(actIntegralCommTypeResp);



    }

    @Test
    public void testqueryActivityIntegral(){
        ActivityIntegralDTO activityIntegralDTO = new ActivityIntegralDTO();
        activityIntegralDTO.setMerCode("500001");
        List<String> ft = new ArrayList<>();
        ft.add("7d6e99ec1e404c5ea2f4e32bb7918fea");

        activityIntegralDTO.setFtypeIds(ft);

        activityIntegralDTO.setSpecId(null);
        activityIntegralDTO.setTabType(LocalConst.STATUS_ZERO);
        activityIntegralDTO.setSpecStatus(LocalConst.STATUS_ONE);


        System.out.println(activityIntegralCartService.queryActivityIntegral(activityIntegralDTO,null,null));



    }

    @Test
    public void testqueryActivityIntegralCon(){
        ActivityIntegralDTO activityIntegralDTO = new ActivityIntegralDTO();
        activityIntegralDTO.setMerCode("500001");
        String userid = "";
//    activityIntegralController.getExchangeStorePage(activityIntegralDTO);


    }





}
