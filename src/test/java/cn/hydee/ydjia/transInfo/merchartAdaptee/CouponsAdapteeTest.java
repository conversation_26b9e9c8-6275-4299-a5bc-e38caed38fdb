package cn.hydee.ydjia.transInfo.merchartAdaptee;

import cn.hydee.ydjia.merchantcustomer.MerchantCustomerApplication;
import cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon.CouponIdprefix;
import cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon.MerCouponNew;
import cn.hydee.ydjia.merchantcustomer.transInfo.merchartAdaptee.CouponsAdapter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MerchantCustomerApplication.class)
public class CouponsAdapteeTest {

    @Resource(type = CouponsAdapter.class)
    private CouponsAdapter couponsAdapter;

    @Test
    public void getCouponsRuleInfo() {
        List<MerCouponNew> conponRules = couponsAdapter.getCouponsRuleInfo("999999", "2085654", null, null);
        for (MerCouponNew conponrule : conponRules) {
            System.out.println(conponrule);
        }
    }

    @Test
    public void couponsTake() {
        boolean res = couponsAdapter.couponsTake("999999", "2085654", "1021405", "领取中心领取优惠券");
        System.out.println(res);
    }

    @Test
    public void couponsTakeInfo() {
        List<String> storeCodes = new ArrayList<String>();
        //storeCodes.add("2002");

       // List<String> goodsCodes = new ArrayList<String>();
        // goodsCodes.add("207");
        Map<String,String> map=new HashMap<>();
        map.put("1008",10.02+"");
        map.put("1018",20.02+"");

        List<CouponIdprefix> CouponIdprefixs = couponsAdapter.couponsTakeInfo("999999", "2085654", storeCodes, map);
        for (CouponIdprefix couponIdprefix : CouponIdprefixs) {
            System.out.println(couponIdprefix.toString());
        }
    }

    @Test
    public void couponsRuleByIds() {
        ArrayList<String> list = new ArrayList<String>();
        list.add("A0251036855");
        List<CouponIdprefix> rules = couponsAdapter.couponsRuleByIds("666666", list);
        for (CouponIdprefix rule : rules) {
            System.out.println(rule);
        }
    }

    @Test
    public void couponsCheck() {
        ArrayList idprefixs = new ArrayList();
        idprefixs.add("A000853885");
        idprefixs.add("A000853878");
        Map<String, String> goodsInfos = new LinkedHashMap<>();
        goodsInfos.put("104", "7.00");
        goodsInfos.put("106", "2.00");
        System.out.println(couponsAdapter.couponsCheck("666666", idprefixs, goodsInfos));
    }
}