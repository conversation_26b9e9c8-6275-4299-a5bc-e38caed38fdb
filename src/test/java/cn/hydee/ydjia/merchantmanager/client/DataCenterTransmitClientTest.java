package cn.hydee.ydjia.merchantmanager.client;

import cn.hutool.core.lang.Assert;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.OverviewMemberPageReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.OverviewPageMemberDataReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberExactBaseLabelRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OverviewMemberCountRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.UserSaleAmountCountDTO;
import cn.hydee.ydjia.merchantmanager.feign.DataCenterTransmitClient;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

/**
 * @author: LongHua
 * @date: 2022/1/17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DataCenterTransmitClientTest {
    @Autowired
    private DataCenterTransmitClient dataCenterTransmitClient;

    @Test
    public void test() {
        overviewMemberCountTest();
        saleAmountProportionTest();
    }

    @Test
    public void overviewMemberCountTest() {
        String merCode = "510289";
        List<String> businessIds = Arrays.asList("222", "2332", "111");

        ResponseBase<OverviewMemberCountRespDTO> responseBase = dataCenterTransmitClient.countMemberByMerCode(null,null);
        Assert.notNull(responseBase);
        Assert.isTrue(responseBase.checkSuccess());
        log.info("responseBase:{}", JSON.toJSONString(responseBase));
    }

    @Test
    public void saleAmountProportionTest() {
        String merCode = "999999";
        List<String> businessIds = Arrays.asList("1001", "1009");
        String startTime = "2021-01-01";
        String endTime = "2022-01-01";

        ResponseBase<UserSaleAmountCountDTO> responseBase = dataCenterTransmitClient.getSaleAmountProportion(null);
        Assert.notNull(responseBase);
        Assert.isTrue(responseBase.checkSuccess());
        log.info("responseBase:{}", JSON.toJSONString(responseBase));
    }

    @Test
    public void getMemberPageTest() {

        OverviewMemberPageReqDTO reqDTO = new OverviewMemberPageReqDTO();
        reqDTO.setMerCode("510289");
        reqDTO.setOrganizationList(Arrays.asList("222", "2332"));
        ResponseBase<PageDTO<MemberExactBaseLabelRespDTO>> responseBase = dataCenterTransmitClient.getMemberPage(reqDTO);

        Assert.notNull(responseBase);
        Assert.isTrue(responseBase.checkSuccess());
        log.info("responseBase:{}", JSON.toJSONString(responseBase));
    }


    @Test
    public void getEffectMemberCountTest() {

        String merCode = "510289";
        List<String> organizationList = Arrays.asList("222", "2332");
        OverviewPageMemberDataReqDTO reqDTO = new OverviewPageMemberDataReqDTO();
        ResponseBase<Long> responseBase = dataCenterTransmitClient.getEffectMemberCount(reqDTO);

        Assert.notNull(responseBase);
        Assert.isTrue(responseBase.checkSuccess());
        log.info("responseBase:{}", JSON.toJSONString(responseBase.getData()));
    }
}
