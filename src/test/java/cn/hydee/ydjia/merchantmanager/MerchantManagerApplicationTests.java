package cn.hydee.ydjia.merchantmanager;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryOrderReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryStoreDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryStoreInAuthReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.WxAppIdDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OrgInAuthRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantmanager.feign.OrganizationClient;
import cn.hydee.ydjia.merchantmanager.feign.StoreClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.service.HttpService;
import cn.hydee.ydjia.merchantmanager.service.OrderInfoService;
import com.alibaba.fastjson.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MerchantManagerApplicationTests {

    public static final int pageNum = 1;
    public static final int pageSize = 5;

    @Value("${api.version}")
    protected String version;

    protected Gson gson;

    @Autowired
    private HttpService httpService;

    @Autowired
    private StoreClient storeClient;

    @Autowired
    private OrganizationClient organizationClient;

    @Autowired
    private EmpAuthHandler empAuthHandler;


    @Autowired
    private OrderInfoService orderInfoService;


    @Test
    public void queryOrder (){
        String json = "{\"currentPage\":1,\"orderSearchType\":1,\"pageSize\":20,\"empId\":\"\",\"endDate\":\"\",\"merCode\":\"500001\",\"orderStatus\":\"30\",\"prescriptionSheetMark\":\"\",\"payment\":\"\",\"proName\":\"\",\"receive\":\"\",\"searchValue\":\"2309300259148673\",\"startDate\":\"\",\"storeIds\":[],\"prescriptionStatus\":\"\",\"orderType\":\"\",\"sourceMedia\":\"\",\"storeSearchType\":1,\"couponConetnt\":\"\",\"couponType\":\"\",\"returnStatus\":2,\"isSuper\":1}";
        QueryOrderReqDTO queryOrderReqDTO = JSONObject.parseObject(json, QueryOrderReqDTO.class);
        orderInfoService.queryOrder(queryOrderReqDTO,"1695970231258714114");
    }


    @Test
    public void testAuth(){
        List<String> list = empAuthHandler.getMyStoreList(Arrays.asList("4c5e0c020a3b43859c3c03d703761c37",""), "999999_admin", "999999");
        System.out.println(list);
    }

    @Test
    public void testLoadAppId() {
        WxAppIdDTO appIdDTO = new WxAppIdDTO();
        appIdDTO.setMercode("666666");
        String appId = httpService.loadWxAppId(appIdDTO);
        System.out.println("appid  = " + appId);
    }

    @Before
    public void setUp() {
        gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").setPrettyPrinting().create();
    }

    @Test
    public void contextLoads() {
        System.out.println("version = " + version);
    }

    @Test
    public void testStore() {
        QueryStoreInAuthReqDTO queryStoreDTO = new QueryStoreInAuthReqDTO();
        queryStoreDTO.setUserId("49075f12386d434cace45cf5979141f8");
        queryStoreDTO.setMerCode("999999");
        ResponseBase<List<OrgInAuthRespDTO>> responseBase = organizationClient.queryUserOrganization(queryStoreDTO);
        List<OrgInAuthRespDTO> orgList = responseBase.getData().stream()
                .filter(org -> org.getOrType() == 2).collect(Collectors.toList());
        QueryStoreDTO queryStoreDTO2 = new QueryStoreDTO();
        queryStoreDTO2.setMerCode("999999");
        queryStoreDTO2.setExcelFlag(true);
        ResponseBase<PageDTO<StoreResDTO>> res = storeClient.queryStoreByCondition(queryStoreDTO2);
        List<StoreResDTO> storeList = res.getData().getData();
        System.out.println("orgListSize:" + orgList.size() + ",storeListSize:" + storeList.size());
        List<String> storeCodeList = storeList.stream().map(StoreResDTO::getStCode).collect(Collectors.toList());
        orgList.forEach(org -> {
            if (!storeCodeList.contains(org.getOrCode())) {
                System.out.println(org);
            }
        });

    }

}
