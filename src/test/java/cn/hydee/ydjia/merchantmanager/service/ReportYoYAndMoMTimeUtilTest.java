package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.resp.memberconsumptionreport.YoYAndMoM;
import cn.hydee.ydjia.merchantmanager.repository.YdjTaskRepo;
import cn.hydee.ydjia.merchantmanager.service.impl.ExportTaskFactory;
import cn.hydee.ydjia.merchantmanager.util.ReportYoYAndMoMTimeUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

/**
 * @author: <PERSON><PERSON>ua
 * @date: 2022/1/18
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ReportYoYAndMoMTimeUtilTest {

    @Autowired
    private ExportTaskFactory exportTaskFactory;

    @Autowired
    private YdjTaskRepo repo;

    @Test
    public void test(){
        YoYAndMoM<Object> objectYoYAndMoM = ReportYoYAndMoMTimeUtil.buildYoyAndMomObjWithAmount(null, null, new BigDecimal(140), null);
        log.info("json:{}", JSON.toJSONString(objectYoYAndMoM));
    }

    @Test
    public void testt(){
        YdjTask task = repo.selectByPrimaryKey(1724903767176528128L);
        exportTaskFactory.getTaskHandler("MEMBER_REPORT_CONSUMPTION_EXPORT").execute(task);
    }

    @Test
    public void testtt(){
        YdjTask ydjTask = repo.selectByPrimaryKey(1747103348841661697L);
        exportTaskFactory.getTaskHandler("ISP_EMP_REWARD_LIST").execute(ydjTask);
    }
}
