package cn.hydee.ydjia.merchantcustomer.service.v2;

import cn.hydee.ydjia.merchantcustomer.dto.CurrentStoreInfoDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.enums.PmtProductType;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 购物车处理服务V2测试类
 *
 * <AUTHOR>
 * @version 2.0
 */
@SpringBootTest
@ActiveProfiles("test")
class ShoppingCartHandleServiceV2Test {

    @Resource
    private ShoppingCartHandleServiceV2 shoppingCartHandleServiceV2;

    private ShoppingCartCommodityReqDTO testRequest;

    @BeforeEach
    void setUp() {
        testRequest = createTestRequest();
    }

    @Test
    @DisplayName("测试购物车V2处理 - 成功场景")
    void testHandleCartCommodityV2_Success() {
        // Given
        ShoppingCartCommodityReqDTO request = createValidRequest();

        // When
        CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);

        // Then
        assertNotNull(result, "返回结果不应为空");
        assertNotNull(result.getStoreCommodityList(), "门店商品列表不应为空");
        assertTrue(result.getTotalPrice().compareTo(BigDecimal.ZERO) >= 0, "总价应大于等于0");
        assertTrue(result.getBeforePrice().compareTo(BigDecimal.ZERO) >= 0, "优惠前价格应大于等于0");
        assertTrue(result.getReducePrice().compareTo(BigDecimal.ZERO) >= 0, "优惠金额应大于等于0");
    }

    @Test
    void testHandleCartCommodityV2_NullRequest() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.handleCartCommodityV2(null);
        });
    }

    @Test
    void testHandleCartCommodityV2_EmptyMerCode() {
        // Given
        testRequest.setMerCode("");

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.handleCartCommodityV2(testRequest);
        });
    }

    @Test
    void testHandleCartCommodityV2_EmptyUserId() {
        // Given
        testRequest.setUserId("");
        testRequest.setLoginUser(null);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.handleCartCommodityV2(testRequest);
        });
    }

    @Test
    void testValidateRequest_ValidRequest() {
        // Given
        ShoppingCartCommodityReqDTO request = createValidRequest();

        // When & Then
        assertDoesNotThrow(() -> {
            shoppingCartHandleServiceV2.validateRequest(request);
        });
    }

    @Test
    void testValidateRequest_InvalidRequest() {
        // Given
        ShoppingCartCommodityReqDTO request = new ShoppingCartCommodityReqDTO();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.validateRequest(request);
        });
    }

    /**
     * 创建测试请求对象
     */
    private ShoppingCartCommodityReqDTO createTestRequest() {
        ShoppingCartCommodityReqDTO request = new ShoppingCartCommodityReqDTO();
        request.setMerCode("TEST_MER_CODE");
        request.setUserId("123456");
        request.setIfCartOperate(true);
        request.setIfCartPageAdd(false);
        request.setReplaceCommodityNameFlag(true);
        request.setPrescriptionDrugComplianceVersion("2.0.0");
        request.setCollectivizationFlag(false);

        // 设置登录用户信息
        LoginUserDTO loginUser = LoginUserDTO.builder()
                .merCode("TEST_MER_CODE")
                .userId("123456")
                .build();
        request.setLoginUser(loginUser);

        // 设置购物车商品
        List<CartCommodityDTO> cartSpecs = createTestCartSpecs();
        request.setCartRedisSpecs(cartSpecs);

        // 设置门店列表
        List<StoreListResDTO> storeList = createTestStoreList();
        request.setStoreList(storeList);

        // 设置当前门店信息
        CurrentStoreInfoDTO currentStoreInfo = createCurrentStoreInfo();
        request.setCurrentStoreInfo(currentStoreInfo);

        return request;
    }

    /**
     * 创建有效的请求对象
     */
    private ShoppingCartCommodityReqDTO createValidRequest() {
        return createTestRequest();
    }

    /**
     * 创建测试购物车商品
     */
    private List<CartCommodityDTO> createTestCartSpecs() {
        List<CartCommodityDTO> cartSpecs = Lists.newArrayList();

        // O2O商品
        CartCommodityDTO spec1 = new CartCommodityDTO();
        spec1.setSpecId("100001");
        spec1.setStoreId("STORE_001");
        spec1.setCount(2);
        spec1.setPmtProductType(PmtProductType.NORMAL.getCode());
        spec1.setChoseFlag(1);
        cartSpecs.add(spec1);

        CartCommodityDTO spec2 = new CartCommodityDTO();
        spec2.setSpecId("100002");
        spec2.setStoreId("STORE_001");
        spec2.setCount(1);
        spec2.setPmtProductType(PmtProductType.NORMAL.getCode());
        spec2.setChoseFlag(1);
        cartSpecs.add(spec2);

        // B2C商品
        CartCommodityDTO b2cSpec = new CartCommodityDTO();
        b2cSpec.setSpecId("200001");
        b2cSpec.setStoreId(LocalConst.MERCHANT_B2C_FIXED_STORE);
        b2cSpec.setCount(1);
        b2cSpec.setPmtProductType(PmtProductType.NORMAL.getCode());
        b2cSpec.setIsB2c(1);
        b2cSpec.setChoseFlag(1);
        cartSpecs.add(b2cSpec);

        // 云仓商品
        CartCommodityDTO cloudSpec = new CartCommodityDTO();
        cloudSpec.setSpecId("300001");
        cloudSpec.setStoreId(LocalConst.CLOUD_FIXED_STORE + "WSC001");
        cloudSpec.setSpCode("WSC001");
        cloudSpec.setCount(3);
        cloudSpec.setPmtProductType(PmtProductType.NORMAL.getCode());
        cloudSpec.setChoseFlag(1);
        cartSpecs.add(cloudSpec);

        return cartSpecs;
    }

    /**
     * 创建测试门店列表
     */
    private List<StoreListResDTO> createTestStoreList() {
        List<StoreListResDTO> storeList = Lists.newArrayList();

        StoreListResDTO store1 = new StoreListResDTO();
        store1.setId("STORE_001");
        store1.setName("测试门店1");
        store1.setAddress("北京市朝阳区测试地址1号");
        store1.setPhone("010-12345678");
        storeList.add(store1);

        return storeList;
    }

    /**
     * 创建当前门店信息
     */
    private CurrentStoreInfoDTO createCurrentStoreInfo() {
        CurrentStoreInfoDTO currentStore = new CurrentStoreInfoDTO();
        currentStore.setStoreId("STORE_001");
        currentStore.setStoreName("测试门店1");
        currentStore.setStoreAddress("北京市朝阳区测试地址1号");
        return currentStore;
    }

    @Test
    @DisplayName("测试多种商品类型处理")
    void testHandleCartCommodityV2_MultipleProductTypes() {
        // Given
        ShoppingCartCommodityReqDTO request = createValidRequest();

        // When
        CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);

        // Then
        assertNotNull(result);
        if (!CollectionUtils.isEmpty(result.getStoreCommodityList())) {
            // 验证不同类型的门店都被正确处理
            boolean hasO2OStore = result.getStoreCommodityList().stream()
                    .anyMatch(store -> "STORE_001".equals(store.getStoreId()));
            boolean hasB2CStore = result.getStoreCommodityList().stream()
                    .anyMatch(store -> LocalConst.MERCHANT_B2C_FIXED_STORE.equals(store.getStoreId()));
            boolean hasCloudStore = result.getStoreCommodityList().stream()
                    .anyMatch(store -> store.getStoreId().startsWith(LocalConst.CLOUD_FIXED_STORE));

            // 至少应该有一种类型的门店
            assertTrue(hasO2OStore || hasB2CStore || hasCloudStore, "应该至少有一种类型的门店");
        }
    }

    @Test
    @DisplayName("测试空购物车处理")
    void testHandleCartCommodityV2_EmptyCart() {
        // Given
        ShoppingCartCommodityReqDTO request = createValidRequest();
        request.setCartRedisSpecs(Lists.newArrayList()); // 空购物车

        // When
        CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);

        // Then
        assertNotNull(result);
        // 空购物车应该返回默认结果
    }

    @Test
    @DisplayName("测试性能 - 批量处理")
    void testHandleCartCommodityV2_Performance() {
        // Given
        int testCount = 10;
        long totalTime = 0;

        // When
        for (int i = 0; i < testCount; i++) {
            ShoppingCartCommodityReqDTO request = createValidRequest();
            request.setUserId("test_user_" + i);

            long startTime = System.currentTimeMillis();
            CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
            long endTime = System.currentTimeMillis();

            totalTime += (endTime - startTime);
            assertNotNull(result);
        }

        // Then
        double avgTime = (double) totalTime / testCount;
        assertTrue(avgTime < 5000, "平均响应时间应小于5秒: " + avgTime + "ms");
        System.out.println("性能测试结果: 平均响应时间 = " + avgTime + "ms");
    }
}
