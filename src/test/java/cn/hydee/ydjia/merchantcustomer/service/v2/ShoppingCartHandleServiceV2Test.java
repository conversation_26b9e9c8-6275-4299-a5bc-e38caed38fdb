package cn.hydee.ydjia.merchantcustomer.service.v2;

import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.enums.PmtProductType;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 购物车处理服务V2测试类
 * 
 * <AUTHOR>
 * @version 2.0
 */
@SpringBootTest
@ActiveProfiles("test")
class ShoppingCartHandleServiceV2Test {
    
    @Resource
    private ShoppingCartHandleServiceV2 shoppingCartHandleServiceV2;
    
    private ShoppingCartCommodityReqDTO testRequest;
    
    @BeforeEach
    void setUp() {
        testRequest = createTestRequest();
    }
    
    @Test
    void testHandleCartCommodityV2_Success() {
        // Given
        ShoppingCartCommodityReqDTO request = createValidRequest();
        
        // When
        CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
        
        // Then
        assertNotNull(result);
        // 添加更多断言
    }
    
    @Test
    void testHandleCartCommodityV2_NullRequest() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.handleCartCommodityV2(null);
        });
    }
    
    @Test
    void testHandleCartCommodityV2_EmptyMerCode() {
        // Given
        testRequest.setMerCode("");
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.handleCartCommodityV2(testRequest);
        });
    }
    
    @Test
    void testHandleCartCommodityV2_EmptyUserId() {
        // Given
        testRequest.setUserId("");
        testRequest.setLoginUser(null);
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.handleCartCommodityV2(testRequest);
        });
    }
    
    @Test
    void testValidateRequest_ValidRequest() {
        // Given
        ShoppingCartCommodityReqDTO request = createValidRequest();
        
        // When & Then
        assertDoesNotThrow(() -> {
            shoppingCartHandleServiceV2.validateRequest(request);
        });
    }
    
    @Test
    void testValidateRequest_InvalidRequest() {
        // Given
        ShoppingCartCommodityReqDTO request = new ShoppingCartCommodityReqDTO();
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            shoppingCartHandleServiceV2.validateRequest(request);
        });
    }
    
    /**
     * 创建测试请求对象
     */
    private ShoppingCartCommodityReqDTO createTestRequest() {
        ShoppingCartCommodityReqDTO request = new ShoppingCartCommodityReqDTO();
        request.setMerCode("TEST_MER_CODE");
        request.setUserId("123456");
        request.setIfCartOperate(true);
        
        // 设置登录用户信息
        LoginUserDTO loginUser = LoginUserDTO.builder()
                .merCode("TEST_MER_CODE")
                .userId("123456")
                .build();
        request.setLoginUser(loginUser);
        
        // 设置购物车商品
        List<CartCommodityDTO> cartSpecs = createTestCartSpecs();
        request.setCartRedisSpecs(cartSpecs);
        
        return request;
    }
    
    /**
     * 创建有效的请求对象
     */
    private ShoppingCartCommodityReqDTO createValidRequest() {
        return createTestRequest();
    }
    
    /**
     * 创建测试购物车商品
     */
    private List<CartCommodityDTO> createTestCartSpecs() {
        List<CartCommodityDTO> cartSpecs = Lists.newArrayList();
        
        CartCommodityDTO spec1 = new CartCommodityDTO();
        spec1.setSpecId("SPEC_001");
        spec1.setStoreId("STORE_001");
        spec1.setCount(2);
        spec1.setPmtProductType(PmtProductType.NORMAL.getCode());
        cartSpecs.add(spec1);
        
        CartCommodityDTO spec2 = new CartCommodityDTO();
        spec2.setSpecId("SPEC_002");
        spec2.setStoreId("STORE_001");
        spec2.setCount(1);
        spec2.setPmtProductType(PmtProductType.NORMAL.getCode());
        cartSpecs.add(spec2);
        
        return cartSpecs;
    }
}
