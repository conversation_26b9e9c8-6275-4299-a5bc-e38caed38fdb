package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityGetDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SingleStoreCartCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.enums.PmtProductType;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 单门店购物车服务测试类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootTest
@ActiveProfiles("test")
class SingleStoreShoppingCartServiceTest {
    
    @Resource
    private SingleStoreShoppingCartService singleStoreShoppingCartService;
    
    private CartCommodityGetDTO testRequest;
    
    @BeforeEach
    void setUp() {
        testRequest = createTestRequest();
    }
    
    @Test
    @DisplayName("测试单门店购物车查询 - 成功场景")
    void testGetSingleStoreCommodity_Success() {\n        // Given\n        CartCommodityGetDTO request = createValidRequest();\n        \n        // When\n        SingleStoreCartCommodityRespDTO result = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n        \n        // Then\n        assertNotNull(result, \"返回结果不应为空\");\n        assertEquals(request.getCurrentStoreId(), result.getStoreId(), \"门店ID应该匹配\");\n        assertNotNull(result.getCommodities(), \"商品列表不应为空\");\n        assertTrue(result.getTotalPrice().compareTo(BigDecimal.ZERO) >= 0, \"总价应大于等于0\");\n        assertTrue(result.getBeforePrice().compareTo(BigDecimal.ZERO) >= 0, \"优惠前价格应大于等于0\");\n        assertTrue(result.getReducePrice().compareTo(BigDecimal.ZERO) >= 0, \"优惠金额应大于等于0\");\n    }\n    \n    @Test\n    @DisplayName(\"测试单门店购物车查询 - 空请求\")\n    void testGetSingleStoreCommodity_NullRequest() {\n        // When & Then\n        assertThrows(IllegalArgumentException.class, () -> {\n            singleStoreShoppingCartService.getSingleStoreCommodity(null);\n        });\n    }\n    \n    @Test\n    @DisplayName(\"测试单门店购物车查询 - 空门店ID\")\n    void testGetSingleStoreCommodity_EmptyStoreId() {\n        // Given\n        testRequest.setCurrentStoreId(\"\");\n        \n        // When & Then\n        assertThrows(IllegalArgumentException.class, () -> {\n            singleStoreShoppingCartService.getSingleStoreCommodity(testRequest);\n        });\n    }\n    \n    @Test\n    @DisplayName(\"测试单门店购物车查询 - 空商户编码\")\n    void testGetSingleStoreCommodity_EmptyMerCode() {\n        // Given\n        testRequest.setMerCode(\"\");\n        \n        // When & Then\n        assertThrows(IllegalArgumentException.class, () -> {\n            singleStoreShoppingCartService.getSingleStoreCommodity(testRequest);\n        });\n    }\n    \n    @Test\n    @DisplayName(\"测试单门店购物车查询 - 空用户ID\")\n    void testGetSingleStoreCommodity_EmptyUserId() {\n        // Given\n        testRequest.setUserId(\"\");\n        \n        // When & Then\n        assertThrows(IllegalArgumentException.class, () -> {\n            singleStoreShoppingCartService.getSingleStoreCommodity(testRequest);\n        });\n    }\n    \n    @Test\n    @DisplayName(\"测试参数验证 - 有效请求\")\n    void testValidateSingleStoreRequest_ValidRequest() {\n        // Given\n        CartCommodityGetDTO request = createValidRequest();\n        \n        // When & Then\n        assertDoesNotThrow(() -> {\n            singleStoreShoppingCartService.validateSingleStoreRequest(request);\n        });\n    }\n    \n    @Test\n    @DisplayName(\"测试参数验证 - 无效请求\")\n    void testValidateSingleStoreRequest_InvalidRequest() {\n        // Given\n        CartCommodityGetDTO request = new CartCommodityGetDTO();\n        \n        // When & Then\n        assertThrows(IllegalArgumentException.class, () -> {\n            singleStoreShoppingCartService.validateSingleStoreRequest(request);\n        });\n    }\n    \n    @Test\n    @DisplayName(\"测试O2O门店购物车\")\n    void testGetSingleStoreCommodity_O2OStore() {\n        // Given\n        CartCommodityGetDTO request = createValidRequest();\n        request.setCurrentStoreId(\"STORE_001\"); // O2O门店\n        \n        // When\n        SingleStoreCartCommodityRespDTO result = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n        \n        // Then\n        assertNotNull(result);\n        assertEquals(\"STORE_001\", result.getStoreId());\n    }\n    \n    @Test\n    @DisplayName(\"测试B2C门店购物车\")\n    void testGetSingleStoreCommodity_B2CStore() {\n        // Given\n        CartCommodityGetDTO request = createValidRequest();\n        request.setCurrentStoreId(LocalConst.MERCHANT_B2C_FIXED_STORE); // B2C门店\n        \n        // When\n        SingleStoreCartCommodityRespDTO result = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n        \n        // Then\n        assertNotNull(result);\n        assertEquals(LocalConst.MERCHANT_B2C_FIXED_STORE, result.getStoreId());\n    }\n    \n    @Test\n    @DisplayName(\"测试云仓门店购物车\")\n    void testGetSingleStoreCommodity_CloudStore() {\n        // Given\n        CartCommodityGetDTO request = createValidRequest();\n        request.setCurrentStoreId(LocalConst.CLOUD_FIXED_STORE + \"WSC001\"); // 云仓门店\n        \n        // When\n        SingleStoreCartCommodityRespDTO result = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n        \n        // Then\n        assertNotNull(result);\n        assertTrue(result.getStoreId().startsWith(LocalConst.CLOUD_FIXED_STORE));\n    }\n    \n    @Test\n    @DisplayName(\"测试价格计算\")\n    void testPriceCalculation() {\n        // Given\n        CartCommodityGetDTO request = createValidRequest();\n        \n        // When\n        SingleStoreCartCommodityRespDTO result = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n        \n        // Then\n        assertNotNull(result);\n        \n        // 验证价格计算逻辑\n        if (result.getCommodities() != null && !result.getCommodities().isEmpty()) {\n            // 手动计算预期价格\n            BigDecimal expectedTotal = result.getCommodities().stream()\n                    .filter(c -> c.getChoseFlag() != null && c.getChoseFlag() == 1)\n                    .map(c -> c.getPrice().multiply(new BigDecimal(c.getCount())))\n                    .reduce(BigDecimal.ZERO, BigDecimal::add);\n            \n            assertEquals(expectedTotal, result.getTotalPrice(), \"总价计算应该正确\");\n        }\n    }\n    \n    @Test\n    @DisplayName(\"测试性能 - 批量查询\")\n    void testPerformance_BatchQuery() {\n        // Given\n        int testCount = 5;\n        long totalTime = 0;\n        \n        // When\n        for (int i = 0; i < testCount; i++) {\n            CartCommodityGetDTO request = createValidRequest();\n            request.setUserId(\"test_user_\" + i);\n            \n            long startTime = System.currentTimeMillis();\n            SingleStoreCartCommodityRespDTO result = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n            long endTime = System.currentTimeMillis();\n            \n            totalTime += (endTime - startTime);\n            assertNotNull(result);\n        }\n        \n        // Then\n        double avgTime = (double) totalTime / testCount;\n        assertTrue(avgTime < 3000, \"平均响应时间应小于3秒: \" + avgTime + \"ms\");\n        System.out.println(\"单门店购物车性能测试结果: 平均响应时间 = \" + avgTime + \"ms\");\n    }\n    \n    /**\n     * 创建测试请求对象\n     */\n    private CartCommodityGetDTO createTestRequest() {\n        CartCommodityGetDTO request = new CartCommodityGetDTO();\n        request.setMerCode(\"TEST_MERCHANT_001\");\n        request.setUserId(\"123456789\");\n        request.setCurrentStoreId(\"STORE_001\");\n        request.setCurrentStoreName(\"测试门店1\");\n        request.setClientType(\"MINI_PROGRAM\");\n        \n        // 设置登录用户信息\n        LoginUserDTO loginUser = LoginUserDTO.builder()\n                .merCode(\"TEST_MERCHANT_001\")\n                .userId(\"123456789\")\n                .build();\n        request.setLoginUser(loginUser);\n        \n        return request;\n    }\n    \n    /**\n     * 创建有效的请求对象\n     */\n    private CartCommodityGetDTO createValidRequest() {\n        return createTestRequest();\n    }\n}"
