package cn.hydee.ydjia.merchantcustomer.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hydee.ydjia.BaseTest;
import cn.hydee.ydjia.merchantcustomer.domain.UserHistoryLogin;
import cn.hydee.ydjia.merchantcustomer.dto.UserLastLoginDTO;
import cn.hydee.ydjia.merchantcustomer.service.UserHistoryLoginService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class UserHistoryLoginServiceTest extends BaseTest {

    @Resource
    private UserHistoryLoginService userHistoryLoginService;

    @Test
    public void recordTest() {
//        Lists.newArrayList(1,2,3,4,5,6,7,8,9,10)
//                .parallelStream()
//                .forEach(o -> {
                    UserLastLoginDTO userLastLoginDTO = UserLastLoginDTO.builder()
                            .userId(1798632540774273286L)
                            .appid("wx1de43ed1a571ecd8")
                            .unionid(IdUtil.fastSimpleUUID())
                            .openid("o-Dwu5QcjVAMnRvePURZLr5QNE0o")
                            .merCode("500001")
                            .merType(2)
                            .build();
                    userHistoryLoginService.recordLastLoginInfo(userLastLoginDTO);

//                });
    }

    @Test
    public void lastTest() {
        UserHistoryLogin userHistoryLogin = userHistoryLoginService.lastLoginInfo("wxc112b1e9cca5bc59","oxyDX5bL3eC9b7zdmCyViHTE-oF8");
        log.info("成功：{}", JSON.toJSONString(userHistoryLogin));
    }
}
