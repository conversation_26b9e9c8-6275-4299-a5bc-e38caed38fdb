package cn.hydee.ydjia.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.BaseTest;
import cn.hydee.ydjia.merchantcustomer.domain.CommoditySpec;
import cn.hydee.ydjia.merchantcustomer.dto.EmpeRoleRspDTO;
import cn.hydee.ydjia.merchantcustomer.dto.EmployeeResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.EmpInfoReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivityEmployeeSpecReq;
import cn.hydee.ydjia.merchantcustomer.dto.resp.ActivityEmployeeSpecResp;
import cn.hydee.ydjia.merchantcustomer.feign.CommodityStoreSpecClient;
import cn.hydee.ydjia.merchantcustomer.feign.EmployeeClient;
import cn.hydee.ydjia.merchantcustomer.feign.mdmmiration.EmployeeClientAdapter;
import cn.hydee.ydjia.merchantcustomer.service.ShopEmmployService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

@Slf4j
public class EmployeeClientTest extends BaseTest {

    @Resource
    private EmployeeClientAdapter employeeClientAdapter;

    @Autowired
    private EmployeeClient employeeClient;


    @Resource
    private ShopEmmployService shopEmmployService;
    @Resource
    private CommodityStoreSpecClient commodityStoreSpecClient;

    @Test
    public void locateEmpByMobileTest() {
        ResponseBase<EmployeeResDTO> responseBase = employeeClientAdapter.locateEmpByMobile("500001", "13880772186");
        log.info("==>{}", JSON.toJSONString(responseBase.getData()));
    }

    @Test
    public void test_queryInfoByEmpCode() {
        EmpInfoReqDTO empInfoReqDTO = new EmpInfoReqDTO();
        empInfoReqDTO.setEmpCode("1153123");
        empInfoReqDTO.setMobile("18144255782");
        ResponseBase<EmpeRoleRspDTO> empeRoleRspDTOResponseBase = employeeClient.queryInfoByEmpCode(empInfoReqDTO);
        EmpeRoleRspDTO employeeResDTO = empeRoleRspDTOResponseBase.getData();
        Assert.assertNotNull(employeeResDTO.getEmpCode());
        Assert.assertNotNull(employeeResDTO.getEmpName());
        Assert.assertNotNull(employeeResDTO.getSubOrgCode());
        Assert.assertNotNull(employeeResDTO.getSubOrgName());
        Assert.assertNotNull(employeeResDTO.getAvatarPath());
        Assert.assertTrue(employeeResDTO.getRoleInfos() != null && !employeeResDTO.getRoleInfos().isEmpty());
        System.out.println(empeRoleRspDTOResponseBase);
    }

    @Test
    public void testTop() {
        ActivityEmployeeSpecReq req = new ActivityEmployeeSpecReq();

        // 10055038
        //10014026
        req.setStoreId("10014026");
        req.setMerCode("500001");
        List<ActivityEmployeeSpecResp> activityEmployeeSpecResps = shopEmmployService.topRecommend(req);
        System.out.println(activityEmployeeSpecResps);
    }

    @Test
    public void testListByErpCodes() {

        ResponseBase<List<CommoditySpec>> listResponseBase = commodityStoreSpecClient.listByErpCodes("500001", Collections.singletonList("100001"));
        System.out.println(listResponseBase.getData());
    }


}
