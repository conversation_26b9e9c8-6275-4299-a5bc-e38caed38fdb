<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.ydjia.merchantmanager.repository.YdjDeliverySetRangeRepo">
  <resultMap id="BaseResultMap" type="cn.hydee.ydjia.merchantmanager.domain.YdjDeliverySetRange">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mer_code" jdbcType="VARCHAR" property="merCode" />
    <result column="config_id" jdbcType="VARCHAR" property="configId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="range_id" jdbcType="VARCHAR" property="rangeId" />
    <result column="isvalid" jdbcType="INTEGER" property="isvalid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <resultMap id="ResResultMap" type="cn.hydee.ydjia.merchantmanager.dto.resp.DeliverySetRangeResDTO">
    <result column="config_id" jdbcType="VARCHAR" property="configId" />
    <result column="range_id" jdbcType="VARCHAR" property="rangeId" />

  </resultMap>
  <sql id="Base_Column_List">
      id,mer_code,config_id,`type`,range_id,isvalid,create_time,create_name,modify_name,modify_time
    </sql>
  <insert id="insertBatch" >
  INSERT INTO ydj_delivery_set_range (
    mer_code,config_id,`type`,range_id,create_name )
  values
  <foreach collection="list" item="item"  separator=",">
    (#{item.merCode},#{item.configId},#{item.type},#{item.rangeId},#{item.createName}  )
  </foreach>
  </insert>

  <select id="queryByMercode" resultMap="ResResultMap">
    SELECT  config_id, range_id from ydj_delivery_set_range
    where mer_code=#{merCode}
    and `type`=#{type}
    and isvalid= ${@<EMAIL>()};
  </select>

<update id="deleteBatch" >
  update ydj_delivery_set_range set isvalid=0,modify_name=#{userId}
  where mer_code=#{merCode}
  and isvalid=1
  and `type`=#{type}
  <if test="list != null and list.size() > 0">
    AND range_id in
    <foreach collection="list" item="rangeId" index="index" open="(" close=")" separator=",">
      #{rangeId,jdbcType=VARCHAR}
    </foreach>
  </if>
</update>

  <update id="deleteByDeliverySetId">
    update ydj_delivery_set_range
    set
      isvalid=0,
      modify_name=#{userId}
    where config_id in
      <foreach collection="deliverySetIdList" item="configId" index="index" open="(" close=")" separator=",">
        #{configId}
      </foreach>
  </update>
</mapper>