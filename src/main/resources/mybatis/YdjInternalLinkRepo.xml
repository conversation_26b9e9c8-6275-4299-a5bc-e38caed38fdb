<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.ydjia.merchantmanager.repository.YdjInternalLinkRepo">
    <resultMap id="BaseResultMap" type="cn.hydee.ydjia.merchantmanager.domain.YdjInternalLink">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="page_name" jdbcType="VARCHAR" property="pageName"/>
        <result column="page_type" jdbcType="INTEGER" property="pageType"/>
        <result column="link_address" jdbcType="VARCHAR" property="linkAddress"/>
        <result column="link_mini" jdbcType="VARCHAR" property="linkMini"/>
        <result column="link_area" jdbcType="VARCHAR" property="linkArea"/>
        <result column="sort_number" jdbcType="INTEGER" property="sortNumber"/>
        <result column="isvalid" jdbcType="INTEGER" property="isvalid"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="sort_number" jdbcType="INTEGER" property="sortNumber"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="business_type" jdbcType="INTEGER" property="businessType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,page_name,page_type,link_area,link_address,link_mini,`level`,isvalid,create_time,create_name,modify_name,modify_time,sort_number,business_type
    </sql>
    <select id="queryAll" resultType="cn.hydee.ydjia.merchantmanager.domain.YdjInternalLink">
        select
        <include refid="Base_Column_List"/>
        from ydj_internal_link
        where
        isvalid= ${@<EMAIL>()}
        order by sort_number asc;
    </select>
    <select id="searchInternalLink" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ydj_internal_link
        <where>
            <if test="pageType != null">
                page_type = #{pageType,jdbcType=INTEGER}
            </if>
            AND isvalid= ${@<EMAIL>()}
        </where>
        order by sort_number
    </select>
    <select id="searchByAddressAndBusinessType"
            resultType="cn.hydee.ydjia.merchantmanager.domain.YdjInternalLink">
        select
        <include refid="Base_Column_List"/>
        from ydj_internal_link
        WHERE  business_type = #{businessType} AND link_address LIKE concat('%',#{address},'%')
    </select>
</mapper>