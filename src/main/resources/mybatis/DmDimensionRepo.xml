<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.ydjia.merchantmanager.repository.DmDimensionRepo">
  <resultMap id="BaseResultMap" type="cn.hydee.ydjia.merchantmanager.domain.DmDimension">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="mer_code" jdbcType="VARCHAR" property="merCode" />
    <result column="search_hint" jdbcType="VARCHAR" property="searchHint" />
    <result column="is_use" jdbcType="TINYINT" property="isUse" />
    <result column="isvalid" jdbcType="BIT" property="isvalid" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
      <result column="poster_url" jdbcType="VARCHAR" property="posterUrl"/>
  </resultMap>

  <update id="softDelete">
        update dm_dimension
        set isvalid = ${@<EMAIL>()},
        modify_name = #{userName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateTitleAndName">
        update dm_dimension
        set
        `name` = #{name,jdbcType=VARCHAR},
        `title` = #{title,jdbcType=VARCHAR},
        search_hint = #{searchHint,jdbcType=VARCHAR},
        modify_name = #{userName,jdbcType=VARCHAR},
        `modify_time` = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateBaseInfo">
        UPDATE dm_dimension
        SET
            `name` = #{name,jdbcType=VARCHAR},
            `title` = #{title,jdbcType=VARCHAR},
            `background_color` = #{backgroundColor,jdbcType=VARCHAR},
            `border_flag` = #{borderFlag,jdbcType=TINYINT},
            `border_style` = #{borderStyle,jdbcType=TINYINT},
            `border_size` = #{borderSize,jdbcType=TINYINT},
            `border_color` = #{borderColor,jdbcType=VARCHAR},
            `share_desc` = #{shareDesc,jdbcType=VARCHAR},
            `share_img` = #{shareImg,jdbcType=VARCHAR},
            `modify_name` = #{userName,jdbcType=VARCHAR},
            `poster_url` = #{posterUrl,jdbcType=VARCHAR},
            `modify_time` = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE id = #{id}
    </update>

    <update id="updateDimensionAll">
        UPDATE dm_dimension
        SET
            `name` = #{name,jdbcType=VARCHAR},
            `title` = #{title,jdbcType=VARCHAR},
            `background_color` = #{backgroundColor,jdbcType=VARCHAR},
            `border_flag` = #{borderFlag,jdbcType=TINYINT},
            `border_style` = #{borderStyle,jdbcType=TINYINT},
            `border_size` = #{borderSize,jdbcType=TINYINT},
            `border_color` = #{borderColor,jdbcType=VARCHAR},
            `share_desc` = #{shareDesc,jdbcType=VARCHAR},
            `share_img` = #{shareImg,jdbcType=VARCHAR},
            `poster_url` = #{posterUrl,jdbcType=VARCHAR},
            `template_dimension_id` = #{templateDimensionId,jdbcType=BIGINT},
            `search_hint` = #{searchHint,jdbcType=VARCHAR},
            `modify_name` = #{userName,jdbcType=VARCHAR},
            `modify_time` = #{modifyTime,jdbcType=TIMESTAMP}
        WHERE id = #{id}
    </update>

</mapper>