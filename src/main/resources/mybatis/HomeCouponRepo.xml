<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.ydjia.merchantcustomer.repository.HomeCouponRepo">
    <resultMap id="BaseResultMap" type="cn.hydee.ydjia.merchantcustomer.feign.domain.DmCoupon">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="set_id" jdbcType="VARCHAR" property="setId"/>
        <result column="item_ids" jdbcType="VARCHAR" property="itemIds"/>
        <result column="special_id" jdbcType="VARCHAR" property="specialId"/>
    </resultMap>

    <sql id="BaseSqlColumn">
    id,set_id,item_ids,special_id
  </sql>

    <select id="selectLastOneSetId" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseSqlColumn"></include>
        FROM dm_coupon
        WHERE set_id = #{setId,jdbcType=VARCHAR}
        ORDER BY id DESC
        LIMIT 1
    </select>
</mapper>