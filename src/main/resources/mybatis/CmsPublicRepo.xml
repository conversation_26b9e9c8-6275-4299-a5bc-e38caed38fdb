<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.ydjia.merchantmanager.repository.CmsPublicRepo">
  <resultMap id="BaseResultMap" type="cn.hydee.ydjia.merchantmanager.domain.CmsPublic">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="mer_code" jdbcType="VARCHAR" property="merCode" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="sort" jdbcType="SMALLINT" property="sort" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="isvalid" jdbcType="BIT" property="isvalid" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="BaseColumn">
    id, mer_code, `type`, img, url, sort, `value`,remark,start_time,
    end_time,create_name,create_time,modify_name,modify_time
  </sql>

  <sql id="QuerySql">
    WHERE mer_code = #{merCode,jdbcType=VARCHAR}
    <if test="type != null">
      AND `type` = #{type,jdbcType=VARCHAR}
    </if>
    <if test="status != null">
      AND status = #{status,jdbcType=TINYINT}
    </if>
    <if test="remark != null and remark != ''">
      AND remark LIKE CONCAT('%',#{remark,jdbcType=VARCHAR},'%')
    </if>
  </sql>

  <select id="searchCmsPublic" resultType="cn.hydee.ydjia.merchantmanager.domain.CmsPublic">
    SELECT <include refid="BaseColumn"/>
     FROM cms_public
    <include refid="QuerySql"/>
    ORDER BY sort
    LIMIT #{offset},#{pageSize}
  </select>

  <select id="countCmsPublic" resultType="java.lang.Integer">
      SELECT COUNT(1) FROM cms_public
      <include refid="QuerySql"/>
  </select>

  <update id="softDelete">
        update cms_public
        set isvalid = ${@<EMAIL>()},
        modify_name = #{userName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>