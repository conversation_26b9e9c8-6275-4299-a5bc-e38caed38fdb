package cn.hydee.ydjia.merchantmanager.process;

import cn.hydee.batch.context.BatchDataContext;
import cn.hydee.batch.domain.BatchTask;
import cn.hydee.batch.processor.BatchDataImportProcessor;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.dto.ExcelCommentDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/7/6
 */
@Slf4j
@Component
public class BatchImportCommodityCommentProcessor implements BatchDataImportProcessor<ExcelCommentDTO, ExcelCommentDTO> {

    @Autowired
    private CommodityCommentService commodityCommentService;

    @Override
    public List<ExcelCommentDTO> process(BatchDataContext<ExcelCommentDTO> context) {
        String merCode = context.getExtra().get("merCode");
        String userName = context.getExtra().get("userName");
        List<ExcelCommentDTO> excelCommentDTOS = context.getData();
        if (CollectionUtils.isEmpty(excelCommentDTOS)) {
            return excelCommentDTOS;
        }

        this.commodityCommentService.handleImportComment(merCode, userName, excelCommentDTOS);

        return excelCommentDTOS;
    }

    @Override
    public void before(BatchTask batchTask) {

    }

    @Override
    public void after(BatchTask batchTask) {

    }

    @Override
    public void exceptionExe(BatchTask batchTask, Exception e) {
        log.warn("Failed to import commodity comment {}", batchTask, e);
    }
}
