package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.CmsPageItem;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmsPageItemRepo extends BaseMapper<CmsPageItem> {
    @DS(LocalConst.DB_MANAGER_MASTER)
    int insertBatch(@Param("list") List<CmsPageItem> list);
    @DS(LocalConst.DB_MANAGER_MASTER)
    int updateBatch(@Param("list") List<CmsPageItem> oldItem);
}