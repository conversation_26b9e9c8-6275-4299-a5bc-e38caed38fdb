package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.DecorationTemplateDimension;
import cn.hydee.ydjia.merchantmanager.dto.decorationTemplate.DecorationTemplateBatchDTO;
import cn.hydee.ydjia.merchantmanager.dto.decorationTemplate.DecorationTemplateDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DecorationTemplateDimensionRepo extends BaseMapper<DecorationTemplateDimension> {
	
	List<DecorationTemplateDTO> selectAllList(@Param("merCode") String merCode);

	@DS(LocalConst.DB_MANAGER_MASTER)
	int batchUpdateIsValid(@Param("list") List<Long> list,@Param("userName") String userName);

	@DS(LocalConst.DB_MANAGER_MASTER)
	int batchUpdateShareInfo(@Param("batchDTO") DecorationTemplateBatchDTO batchDTO);
}