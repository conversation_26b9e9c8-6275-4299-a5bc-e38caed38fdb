package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.WindowDiagramTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-05-11
 */
@Repository
public interface WindowDiagramTemplateRepo extends BaseMapper<WindowDiagramTemplate> {

    /**
     * 橱窗图模板列表
     *
     * @return
     */
    List<WindowDiagramTemplate> listWindowDiagramTemplate();
}
