package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.YdjMerChantSet;
import cn.hydee.ydjia.merchantmanager.dto.req.QuerySetReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjMerSetResDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/13 15:30
 */
@Repository
public interface YdjMerChantSetRepo {

    @DS(LocalConst.DB_MANAGER_MASTER)
    int insert(YdjMerChantSet ydjMerChantSet);

    @DS(LocalConst.DB_MANAGER_MASTER)
    int updateById(YdjMerChantSet ydjMerChantSet);

    @DS(LocalConst.DB_MANAGER_SLAVE)
    List<YdjMerSetResDTO> querySetListByCondition(QuerySetReqDTO querySetReqDTO);

    @DS(LocalConst.DB_MANAGER_SLAVE)
    YdjMerSetResDTO queryMessageByMerCode(@Param("sysKey") String sysKey, @Param("mercode")String mercode);

    @DS(LocalConst.DB_MANAGER_SLAVE)
    Integer queryIDMessageByMerCode(@Param("sysKey") String sysKey, @Param("mercode")String mercode);

    @DS(LocalConst.DB_MANAGER_SLAVE)
    int queryCount(QuerySetReqDTO querySetReqDTO);


}
