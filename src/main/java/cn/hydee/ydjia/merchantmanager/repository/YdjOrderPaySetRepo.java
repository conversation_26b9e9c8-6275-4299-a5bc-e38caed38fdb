package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.YdjOrderPaySet;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjOrderPaySetResDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface YdjOrderPaySetRepo extends BaseMapper<YdjOrderPaySet> {
    /**
     * 查询商家支付方式
     * @param merCode 商家编码
     * @return YdjOrderPaySetReqDTO 对象
     */
    @DS(LocalConst.DB_MANAGER_SLAVE)
    List<YdjOrderPaySetResDTO> queryOrderPaySet(@Param("merCode") String merCode);
}