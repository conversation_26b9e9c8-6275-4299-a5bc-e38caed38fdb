package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.YdjCloudDeliveryChargeType;
import cn.hydee.ydjia.merchantmanager.domain.YdjCloudDeliverySet;
import cn.hydee.ydjia.merchantmanager.dto.req.YdjCloudDeliverySetReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjDeliverySetResDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface YdjCloudDeliveryChargeTypeRepo extends BaseMapper<YdjCloudDeliveryChargeType> {
}