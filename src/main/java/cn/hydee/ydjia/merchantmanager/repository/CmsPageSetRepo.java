package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.CmsPageSet;
import cn.hydee.ydjia.merchantmanager.dto.SetSort;
import cn.hydee.ydjia.merchantmanager.dto.resp.DimenPageSetDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmsPageSetRepo extends BaseMapper<CmsPageSet> {
    @DS(LocalConst.DB_MANAGER_MASTER)
    int insertBatch(@Param("list") List<CmsPageSet> list);
    @DS(LocalConst.DB_MANAGER_MASTER)
    int updatePageSetSort(@Param("list") List<SetSort> sortList);
    @DS(LocalConst.DB_MANAGER_SLAVE)
    List<DimenPageSetDTO> selectListByDimenId(String id);
    @DS(LocalConst.DB_MANAGER_SLAVE)
    List<String> selectIdsByDimenIds(@Param("list") List<String> dimenIds);
}