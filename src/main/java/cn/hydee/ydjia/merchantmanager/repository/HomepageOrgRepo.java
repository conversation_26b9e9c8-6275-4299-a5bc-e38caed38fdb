package cn.hydee.ydjia.merchantmanager.repository;

import cn.hydee.ydjia.merchantmanager.domain.HomepageOrg;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <AUTHOR>
 * @create 2021/6/10
 * @desc
 */
public interface HomepageOrgRepo extends BaseMapper<HomepageOrg> {

    /**
     * 更新状态
     *
     * @param homepageOrg
     * @return
     */
    @DS(LocalConst.DB_MANAGER_MASTER)
    int updateStatus(HomepageOrg homepageOrg);
}
