package cn.hydee.ydjia.merchantmanager.tranceInfo.dto;

import cn.hydee.ydjia.merchantmanager.tranceInfo.merchartAdaptee.BaseMarketReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Timestamp;


/**
 * 用户地址表
 * <AUTHOR>
 * @tableName m_user_address
 */

/**
 * 用户地址表
 * <AUTHOR>
 * @tableName m_user_address
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserAddress extends BaseMarketReq {
	private Long id;
	private Long userId;
	private Long cityId;
	private String address;
	private String receivingPreson;
	private String receivingPresonTel;
	private Integer isdefault;
	private String longitude;
	private String latitude;
	private String houseNumber;
	private String cityName;
	private Timestamp modifiedon;
	private Integer areaId;
	private String areaName;
	private Integer addressFlag;
	private Integer delFlag;
	private Integer countryId;
	private Integer provinceId;
	private String countryName;
	private String provinceName;

	public UserAddress(String tokenKey) {
		super(tokenKey);
	}

	public UserAddress() {
	}
}

