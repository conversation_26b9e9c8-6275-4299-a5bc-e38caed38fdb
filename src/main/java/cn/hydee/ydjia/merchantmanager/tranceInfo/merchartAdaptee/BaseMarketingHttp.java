package cn.hydee.ydjia.merchantmanager.tranceInfo.merchartAdaptee;


import cn.hydee.ydjia.merchantmanager.config.InterfaceConfiguration;
import cn.hydee.ydjia.merchantmanager.dto.trans.Message;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 请求营销平台
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/16 14:42
 */
public abstract class BaseMarketingHttp extends BaseHttpRequest {

    @Autowired
    protected InterfaceConfiguration icConfig;

    protected String marketUrl(String path) {
        return icConfig.getMarketingBase() + path;
    }

    protected <V,T> T messagePostRevert(String url,V model,TypeReference<Message<T>>
            reference) {
        Message<T> result = postToReference(url, model, null,
                reference);
        if(result != null && result.isSuccess()) {
            return result.getData();
        }
        return null;

    }

    protected <V> boolean messagePostResult(String url,V model) {
        Message result = postToObject(url, model, null,Message.class);
        return result != null && result.isSuccess();
    }
}
