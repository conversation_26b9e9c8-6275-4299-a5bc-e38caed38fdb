package cn.hydee.ydjia.merchantmanager.dto.pmtcustomized;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 合作商定制促销活动配置信息 Response DTO
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@ApiModel(value = "合作商定制促销活动配置信息 Response DTO")
@Data
public class CustomizedEnumRespDTO implements Serializable {

    @ApiModelProperty(value = "枚举值")
    private String key;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "配置备注")
    private String remark;

}
