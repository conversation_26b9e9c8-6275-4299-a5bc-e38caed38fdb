package cn.hydee.ydjia.merchantmanager.dto.resp.marketreport;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MemberRepurchaseRespDto {

    @ApiModelProperty("机构编码")
    private String businessId;

    @ApiModelProperty("会员复购率")
    private String repurchaseRate;

    @ApiModelProperty("复购会员数")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer repurchaseMemberNum;

    @ApiModelProperty("复购订单销售额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal repurchaseMoney = BigDecimal.ZERO;

    @ApiModelProperty("复购订单毛利额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal repurchaseProfit = BigDecimal.ZERO;

    @ApiModelProperty("复购订单毛利率")
    private String repurchaseProfitRate;

    @ApiModelProperty("复购平均客单价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal repurchaseCusPrice;

    @ApiModelProperty("复购平均品单价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal repurchaseSkuPrice;

    @ApiModelProperty("复购平均客品数")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal repurchaseCusSkuNum;
}
