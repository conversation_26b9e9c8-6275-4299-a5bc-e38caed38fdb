package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 评价记录数据响应DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/25
 */
@Data
public class ActivityCommentRecordResDTO implements Serializable {

    private static final long serialVersionUID = -3114924860901667748L;

    @ApiModelProperty(value = "评价记录id")
    private Integer id;

    @ApiModelProperty(value = "评价商户编码")
    private String merCode;

    @ApiModelProperty(value = "评价用户id")
    private Long commentUserId;

    @ApiModelProperty(value = "评价会员卡号")
    private String commentUserCard;

    @ApiModelProperty(value = "评价会员名称")
    private String commentUserName;

    @ApiModelProperty(value = "评价订单类型， 1线上、2线下")
    private Integer commentType;

    @ApiModelProperty(value = "评价订单号")
    private String orderId;

    @ApiModelProperty(value = "评价门店编码")
    private String commentStoreCode;

    @ApiModelProperty(value = "评价门店名称")
    private String commentStoreName;

    @ApiModelProperty(value = "营业员信息，格式：员工编码(员工名称),员工编码(员工名称)...")
    private String empInfo;

    @ApiModelProperty(value = "评价内容")
    private String commentContent;

    @ApiModelProperty(value = "评价图片，用,分隔，存多张图片路径")
    private String commentPic;

    @ApiModelProperty(value = "评价时间")
    private Date commentTime;

    @ApiModelProperty(value = "评价详情集合")
    private List<ActivityCommentRecordDetailResDTO> recordDetailList;

}
