package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 人群统计数据响应对象
 *
 * <AUTHOR>
 * @date 2022/1/5
 */
@Data
public class CrowdExecuteStatisticsResDTO implements Serializable {

    private static final long serialVersionUID = 5950000748546628660L;

    @ApiModelProperty(value = "人群id")
    private Long crowdId;

    @ApiModelProperty(value = "短信通知人数")
    private Integer smsDeliveryNum = 0;

    @ApiModelProperty(value = "公众号通知人数")
    private Integer wxDeliveryNum = 0;

    @ApiModelProperty(value = "优惠券领取人数")
    private Integer couponReceivedNum = 0;

    @ApiModelProperty(value = "优惠券使用人数")
    private Integer couponUsedNum = 0;

}
