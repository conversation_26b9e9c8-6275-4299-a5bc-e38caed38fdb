package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.dto.resp.voice.TaskBarData;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2022/1/13 9:48
 */
@Data
public class AISpeechTaskDetail {
    private Integer id;
    private Integer memberNum;
    private String bizType;
    private Date timeStart;
    private Date timeEnd;
    private Integer taskNum;
    @ApiModelProperty("接通率")
    private String arrivedRate;
    @ApiModelProperty("实际拨打数")
    private Integer actualNum;
    @ApiModelProperty("接通数")
    private Integer arrivedNum;
    @ApiModelProperty("平均通话时长")
    private String avgCall;
    @ApiModelProperty("总通话时长")
    private Integer callTime;
    @ApiModelProperty(value = "总通话时长 时分秒格式")
    private String callTimeStr;
    @ApiModelProperty("短信发送成功人数")
    private Integer succeedNum;
    @ApiModelProperty("短信发送失败人数")
    private Integer failedNum;
    @ApiModelProperty("短信消耗数")
    private Integer feeNum;
    @ApiModelProperty("短信发送人数")
    private Integer mobileNum;
    @ApiModelProperty("短信发送人数")
    private String area;

    @ApiModelProperty(value = "饼图信息")
    private List<TaskBarData> barData;

    @ApiModelProperty(value = "头部详情")
    private HeaderData header;


    @Data
    public  static class HeaderData  {

        private String area;

        private Integer totalMembers;

        private Date activityBeginTime;

        private Date activityEndTime;

        private Integer sendSmsStatus;

        private String  name;

        private Integer voiceSuccSend;

        private String verbalScene;

        private Integer voiceFailSend;

        private Integer status;

        private String  timeRange;

        /**
         * 发送对象类型（1:会员导入，2：会员标签，3：人群，4：固定人群）
         */
        private Integer sendObjectType;

        /**
         * 发送对象
         */
        private String sendObject;

        /**
         * 发送内容
         */
        private String sendContent;

        @ApiModelProperty(value = "短信变量")
        private String messageVariable;

        @ApiModelProperty(value = "来源机构列表")
        private List<MemberSourceStoreRespDTO> sourceStores;

        @ApiModelProperty(value = "消费门店列表")
        private List<MemberSourceStoreRespDTO> consumeStores;

        @ApiModelProperty(value = "买过商品列表")
        private List<MemberCrowdConsumeCommRespDTO> consumeCommodities;

    }

}
