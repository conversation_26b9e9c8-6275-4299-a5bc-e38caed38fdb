package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/16
 */
@Data
@Accessors(chain = true)
public class ActivityIncomeDetailRespDTO {
    @ApiModelProperty("活动编码")
    private Long activityId;
    @ApiModelProperty("活动名称")
    private String activityName;
    @ApiModelProperty("活动装填")
    private String activityStatus;
    @ApiModelProperty("活动发起方")
    private String ispName;
    @ApiModelProperty("活动开始时间")
    private String activityStartTime;
    @ApiModelProperty("活动结束时间")
    private String activityEndTime;
    @ApiModelProperty("待结连锁动销激励")
    private Long chainRewardPendAmount = 0L;
    @ApiModelProperty("已结连锁动销激励")
    private Long chainRewardSettledAmount = 0L;

    @ApiModelProperty("动销激励待入账金额")
    private Long chainWaitSettleMoney = 0L;

    @ApiModelProperty("待结优惠券补贴")
    private Long couponRewardPendAmount = 0L;
    @ApiModelProperty("已结优惠券补贴")
    private Long couponRewardSettledAmount = 0L;

    @ApiModelProperty("优惠券补贴待入账金额")
    private Long couponWaitSettleAmount = 0L;

    @ApiModelProperty("当前活动预计总收入")
    private Long totalIncomeAmount = 0L;
    @ApiModelProperty(value = "员工激励已结算金额")
    private Long employSettledAmount = 0L;
    @ApiModelProperty(value = "员工激励待结算金额")
    private Long employPendAmount = 0L;

    @ApiModelProperty("员工奖励待入账金额")
    private Long employWaitSettleAmount = 0L;

    @ApiModelProperty("员工奖励已结算积分")
    private Long employIntegralSettler;

    @ApiModelProperty("员工奖励已结算积分")
    private Long employIntegralUnSettler;

    @ApiModelProperty("员工奖励待入账积分")
    private Long employIntegralWaitSettler = 0L;

    @ApiModelProperty("短信费用已结算积分")
    private Long smsSettler;

    @ApiModelProperty("短信费用已结算积分")
    private Long smsUnSettler;
}
