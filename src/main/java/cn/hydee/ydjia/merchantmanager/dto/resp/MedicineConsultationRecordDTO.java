package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 问诊记录表
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class MedicineConsultationRecordDTO {

    private Long id;

    private String merCode;

    private String memberId;

    @ApiModelProperty("平台来源：1-微问诊")
    private Integer platformType;

    private String storeId;

    private String storeName;

    @ApiModelProperty("问诊处方单状态：-1-已作废 1-待审方 2-审方通过 3-审方拒绝 6-初始化 7-待开方 10-开方拒绝")
    private Integer status;

    @ApiModelProperty("问诊人姓名")
    private String custName;

    @ApiModelProperty("问诊人年龄")
    private String custAge;

    @ApiModelProperty("问诊人姓名：1-男 2-女 3-未知")
    private Integer custGender;

    @ApiModelProperty("问诊人体重，单位：千克")
    private Double custWeight;

    @ApiModelProperty("问诊人联系电话")
    private String custPhone;

    @ApiModelProperty("问诊平台ID")
    private String presId;

    @ApiModelProperty("用户症状（本次处方针对病人的症状）")
    private String syptom;

    @ApiModelProperty("初步诊断（多个初步诊断之间使用|隔开）")
    private String syptmFlag;

    @ApiModelProperty("医生建议（针对本次咨询， 医生给的康复建议）")
    private String pharmAdvice;

    @ApiModelProperty("处方图片地址")
    private String picPath;

    @ApiModelProperty("开方时间")
    private Date prescribeTime;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("审核结果（若不通过则为审方拒绝原因）")
    private String auditResult;

    @ApiModelProperty("备注信息")
    private String remark;

    @ApiModelProperty("开方医生姓名")
    private String pharName;

    @ApiModelProperty("开方医生签名图片")
    private String docPicture;

    @ApiModelProperty("问诊音频存放路径")
    private String record;

    @ApiModelProperty("审方药剂师姓名")
    private String auditPharName;

    @ApiModelProperty("审方药剂师签名图片")
    private String pharmPicture;

    @ApiModelProperty("问诊药品列表")
    private List<MedicineConsultationDrugs> drugList;
}