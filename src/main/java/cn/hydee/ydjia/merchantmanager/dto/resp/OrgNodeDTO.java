package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 创建组织机构请求类
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/19 15:32
 */
@Data
public class OrgNodeDTO {
    @ApiModelProperty(value = "ID")
    private String id;
    @ApiModelProperty(value = "父节点ID")
    private String orParent;
    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "机构编码")
    private String orCode;
    @ApiModelProperty(value = "机构名称")
    private String orName;
    @ApiModelProperty(value = "机构分类")
    private Integer orType;
    @ApiModelProperty(value = "机构类型（1：公司，2：门店）")
    private Integer orClass;
    @ApiModelProperty(value = "状态（0：禁用，1：启用）")
    private Integer status;
    @ApiModelProperty(value = "是否有效1有效，0无效")
    private int  isvalid;
}
