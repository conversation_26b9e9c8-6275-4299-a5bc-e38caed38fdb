package cn.hydee.ydjia.merchantmanager.dto.resp.orderReport;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2022-03-07 9:42
 * @since 1.0
 */
@Data
public class OnlineOrderReportGroupDataRespDTO {

    @ApiModelProperty(value = "业务机构")
    private String businessId;

    @ApiModelProperty(value = "业务机构")
    private String businessName;

    @ApiModelProperty(value = "商品分类")
    private String commodityTypeName;

    @ApiModelProperty(value = "商品分组")
    private String commodityGroupName;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "上线门店数")
    private String storeNum;

    @ApiModelProperty(value = "规格")
    private String commoditySpec;

    @ApiModelProperty(value = "销售额")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal saleAmount;

    @ApiModelProperty("退款金额")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal refundAmount;

    @ApiModelProperty("销售订单数")
    @JsonSerialize(using=ToStringSerializer.class)
    private Integer saleOrders;

    @ApiModelProperty("退款订单数")
    @JsonSerialize(using=ToStringSerializer.class)
    private Integer refundOrders;

    @ApiModelProperty("毛利额")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profit;

    @ApiModelProperty("毛利率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitRate;

    @ApiModelProperty("综合毛利额")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal totalProfit;

    @ApiModelProperty("综合毛利率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal totalProfitRate;

}
