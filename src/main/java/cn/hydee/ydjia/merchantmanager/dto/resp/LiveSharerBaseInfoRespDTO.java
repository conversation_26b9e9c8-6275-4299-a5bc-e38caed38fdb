package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * @version 1.0
 * @Author: pengyayun
 * @Description:
 * @Date: 2020/7/13
 */
@Data
public class LiveSharerBaseInfoRespDTO {


    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 直播ID
     */
    private Long liveId;

    /**
     * 会员id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;




}
