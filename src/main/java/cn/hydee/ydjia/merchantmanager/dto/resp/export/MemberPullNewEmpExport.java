package cn.hydee.ydjia.merchantmanager.dto.resp.export;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
@Data
@Accessors(chain = true)
public class MemberPullNewEmpExport {
    @Excel(name = "员工编码", width = 20)
    private String empCode;

    @Excel(name = "员工姓名", width = 20)
    private String empName;

    @Excel(name = "所在门店", width = 20)
    private String storeName;

    @Excel(name = "完成量", width = 20)
    private Integer finishQuantity;

    @Excel(name = "奖励金额", width = 20)
    private BigDecimal rewardAmount = BigDecimal.ZERO;

}
