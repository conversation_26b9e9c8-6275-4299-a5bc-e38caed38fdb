package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * 商户已购买套餐列表查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/07/06 11:38
 */
@Builder
@Data
public class MerchantBuyServiceQueryReqDTO {

    @ApiModelProperty(value = "套餐ID")
    private Set<String> serIds;

    @ApiModelProperty(value = "商家编码")
    private String merCode;
}
