package cn.hydee.ydjia.merchantmanager.dto.resp.market.payGift;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付有礼活动列表响应DTO
 *
 * <AUTHOR>
 * @date 2023/6/15
 */
@Data
public class PayGiftListResDTO {

    @ApiModelProperty(value = "活动id")
    private Integer id;

    @ApiModelProperty(value = "商户号")
    private String merCode;

    @ApiModelProperty(value = "活动名称")
    private String activityDetailName;

    @ApiModelProperty(value = "活动起始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "状态（ 2 创建中 3 已创建 4 创建失败 5 待审核 6 审核拒绝 7 未开始 8 进行中 9 已中止 10 已结束 11 审核通过 12 已终止）")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "活动场景，（0 通用，1：线上，2：线下）")
    private Integer sceneRule;

    @ApiModelProperty(value = "门店规则，（1：全部，2：部分）")
    private Integer shopRule;

    @ApiModelProperty(value = "商品规则（1：全部，2：部分可用，3：部分不可用）")
    private Integer productRule;

    @ApiModelProperty(value = "修改时间（status=4时即失效时间）")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "活动规则类型（1：按金额阶梯赠送，2：按金额叠加赠送）")
    private Integer ruleType;

    @ApiModelProperty(value = "参与门店数")
    private Integer participatingStoresCount;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "统计数据对象")
    private PayGiftListStatisticsResDTO statistics;

}
