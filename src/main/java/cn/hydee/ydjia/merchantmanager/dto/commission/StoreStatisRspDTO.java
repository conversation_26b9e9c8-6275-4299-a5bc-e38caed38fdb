package cn.hydee.ydjia.merchantmanager.dto.commission;

import cn.hydee.starter.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/***
 * @ClassName: StoreStatisRspDTO
 * @Description: 门店业绩统计
 * @Author: koumingming
 * @Date: 2020-06-29 16:16
 * @version : V1.0
 */
@Data
public class StoreStatisRspDTO {

    @ApiModelProperty("销售总额")
    private BigDecimal saleTotalAmount = new BigDecimal(0);

    @ApiModelProperty("提成总额")
    private BigDecimal commissionTotalAmount = new BigDecimal(0);

    @ApiModelProperty("推荐店员数")
    private int recommendedSalerNum;

    @ApiModelProperty("门店业绩统计明细")
    private PageDTO<StoreStatisDetailRspDTO> storeStatisDetailRspDTOS;
}
