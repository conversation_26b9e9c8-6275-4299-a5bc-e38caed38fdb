package cn.hydee.ydjia.merchantmanager.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2022/5/22 19:22
 */
@Data
public class ActivityRecordRefundInfoResp {

    @ApiModelProperty(value = "奖励单id")
    private Long activityRecordId;
    @ApiModelProperty(value = "单号")
    private String orderNumber;
    @ApiModelProperty(value = "退款流水单号")
    private String bizId;
    @ApiModelProperty(value = "业务订单号")
    private String orderId;
    @ApiModelProperty(value = "业务退款单号")
    private String refundOrderId;
    @ApiModelProperty(value = "退款金额（退款后激励扣减金额）")
    private Long money;
    @ApiModelProperty(value = "退款后激励金额")
    private Long refundAfterMoney;
    @ApiModelProperty(value = "退款单创建时间")
    private Date createTime;

}
