package cn.hydee.ydjia.merchantmanager.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
@Data
public class LiveStatisticsOrderDetailRspDTO{
    /**
     * id
     */
    private Integer id;

    /**
     * 商户编号
     */
    private String merCode;

    /**
     * 直播活动ID
     */
    private Long liveId;

    /**
     * 订单号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderNo;

}