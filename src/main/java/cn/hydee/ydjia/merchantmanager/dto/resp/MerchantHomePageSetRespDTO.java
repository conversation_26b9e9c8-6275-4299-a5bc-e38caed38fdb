package cn.hydee.ydjia.merchantmanager.dto.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2020/12/24
 * @Param
 * @return
 */
@Data
public class MerchantHomePageSetRespDTO {

    @ApiModelProperty(value = "首页高频功能设置")
    private MerchantHomePageSetDTO merchantHomePageSet;

    @ApiModelProperty(value = "选中的功能")
    private List<MerchantMoreServiceRespDTO> functionBars;

    @ApiModelProperty(value = "更多功能")
    private List<MerchantMoreServiceRespDTO> moreServices;



}
