package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2020-06-19
 */
@Data
public class ActivityCouponDetailResDTO {

    @ApiModelProperty(value = "所需金额",required = true)
    private BigDecimal amount;
    @ApiModelProperty(value = "使用时间（1领取起n天有效2领取起n天后生效,生效后m天失效3开始时间-结束时间）",required = true)
    private Integer timeRule;
    @ApiModelProperty(value = "礼品名称",required = true)
    private String giftName;
    private String cname;
    @ApiModelProperty(value = "优惠券id",required = true)
    private Integer couponId;
    @ApiModelProperty(value = "券总量",required = true)
    private Integer totalCount;
    @ApiModelProperty(value = "配合type使用,面额(eg.满减用20表示,8折用8)",required = true)
    private BigDecimal denomination;
    @ApiModelProperty(value = "使用门槛0代表无门槛，数字代表订单满n元",required = true)
    private BigDecimal useRule;
    @ApiModelProperty(value = "活动id",required = true)
    private Integer activityId;
    @ApiModelProperty(value = "礼品ID",required = true)
    private String giftId;
    @ApiModelProperty(value = "优惠券类型:（0:全部，1：折扣，2：抵价，3：礼品券）")
    private Integer ctype;
    @ApiModelProperty(value = "所需积分",required = true)
    private BigDecimal integral;
    @ApiModelProperty(value = "每人限领张数",required = true)
    private Integer perCount;
    @ApiModelProperty(value = "门店规则（1：全部，2：部分）",required = true)
    private Integer shopRule;
    @ApiModelProperty(value = "使用场景（1：线上，2：线下）",required = true)
    private Integer sceneRule;
    @ApiModelProperty(value = "配合time_rule使用,值用逗号分隔",required = true)
    private String effectTime;
    @ApiModelProperty(value = "当前id",required = true)
    private Integer id;
    @ApiModelProperty(value = "最大优惠金额，设置0无上限",required = true)
    private BigDecimal maxPrice;
    @ApiModelProperty(value = "商品规则（1：全部，2：部分可用，3：部分不可用）",required = true)
    private Integer productRule;
    @ApiModelProperty(value = "礼品券礼品信息",required = true)
    private StoreSpec spec;
    @ApiModelProperty(value = "礼品券兑换商品数")
    private Integer giftLimitNum;
    @ApiModelProperty(value = "优惠券配送方式")
    private String deliveryType;
}
