package cn.hydee.ydjia.merchantmanager.dto.resp.chain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/16 17:13
 */
@Data
public class RewardRankGatherRespDTO {

    @ApiModelProperty("服务商(厂家)编码")
    private String ispName;

    @ApiModelProperty("已获得随心豆奖励")
    private BigDecimal rewardAmount = BigDecimal.ZERO;

    @ApiModelProperty("已获得积分奖励")
    private BigDecimal rewardBooking = BigDecimal.ZERO;

    @ApiModelProperty("激励发放时间")
    private String createTime;

}
