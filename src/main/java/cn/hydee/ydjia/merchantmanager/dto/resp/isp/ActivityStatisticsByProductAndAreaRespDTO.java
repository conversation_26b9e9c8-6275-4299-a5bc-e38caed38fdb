package cn.hydee.ydjia.merchantmanager.dto.resp.isp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工业活动商品销售统计响应对象-商品+连锁+门店分组
 * <AUTHOR>
 * @date 2022/9/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActivityStatisticsByProductAndAreaRespDTO extends ActivityStatisticsByProductRespDTO{

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty("区域汇总类型；0：省+城市，1：省，2：城市")
    private Integer type;

}
