package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/5/26   10:07
 * @since 1.0
 */
@Data
public class LivePayOrderIdReqDTO {

    @ApiModelProperty(value = "商户code", required = true)
    @NotBlank(message = "商户code不能为空")
    private String merCode;

    @ApiModelProperty(value = "直播id")
    private Long id;

    @ApiModelProperty(value = "直播类型；1：自主直播；2：订阅直播；3：厂家直播", required = true)
    @NotBlank(message = "直播类型不能为空")
    private String merType;

}
