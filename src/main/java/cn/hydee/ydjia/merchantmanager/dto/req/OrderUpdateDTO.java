package cn.hydee.ydjia.merchantmanager.dto.req;

import cn.hydee.ydjia.merchantmanager.domain.DomainBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/11/18 10:14
 */
@Data
public class OrderUpdateDTO extends DomainBase {

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "订单id不能为空")
    private Long id;
    @ApiModelProperty(value = "商家编码")
    @NotBlank(message = "商家编码不能为空")
    private String merCode;
    @ApiModelProperty(value = "门店id")
    private String storeId;
    @ApiModelProperty(value = "订单编号")
    private String serialNumber;
    @ApiModelProperty(value = "会员唯一标示")
    private Integer memberId;
    @ApiModelProperty(value = "订单类型 R处方药/N正常订单")
    private String orderType;
    @ApiModelProperty(value = "来源渠道对应码表的值")
    private String sourceChannel;
    @ApiModelProperty(value = "来源媒体来源媒体开放值")
    private String sourceMedia;
    @ApiModelProperty(value = "订单状态 2.待付款 4.待发货 6.待收货 8.待退货 10.待退款 12.已完成 20.已取消 30.退款完成 ")
    private Integer orderStatus;
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    @ApiModelProperty(value = "支付类型")
    private String payType;
    @ApiModelProperty(value = "支付状态")
    private Byte payStatus;
    @ApiModelProperty(value = "支付时间最后付款时间")
    private Date payTime;
    @ApiModelProperty(value = "商品总重")
    private Integer totalGoodsNumber;
    @ApiModelProperty(value = "订单商品总额")
    private BigDecimal totalOrderAmount;
    @ApiModelProperty(value = "积分抵扣")
    private BigDecimal integralDeduction;
    @ApiModelProperty(value = "优惠券抵扣")
    private BigDecimal couponDeduction;
    @ApiModelProperty(value = "活动优惠金额")
    private BigDecimal activityDiscountAmont;
    @ApiModelProperty(value = "其他优惠金额")
    private BigDecimal otherDiscountAmont;
    @ApiModelProperty(value = "原运费金额")
    private BigDecimal originalFreightAmount;
    @ApiModelProperty(value = "实际运费金额")
    private BigDecimal actualFreightAmount;
    @ApiModelProperty(value = "订单总金额商品总额-积分抵扣-优惠券抵扣-活动优惠-其它优惠+实际运费")
    private BigDecimal totalActualOrderAmount;
    @ApiModelProperty(value = "实际支付总金额")
    private BigDecimal actuallyPaid;
    @ApiModelProperty(value = "待支付金额")
    private BigDecimal amountTobepaid;
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "是否需要发票标识(1-需要 0-不需要)")
    private Integer isInvoice;
    @ApiModelProperty(value = "订单留言")
    private String orderMessage;
    @ApiModelProperty(value = "订单备注")
    private String orderRemark;
    @ApiModelProperty(value = "是否锁定")
    private Integer isLocked;
    @ApiModelProperty(value = "锁定人")
    private String lockedName;
    @ApiModelProperty(value = "锁定时间")
    private Date lockedTime;
    @ApiModelProperty(value = "取消人")
    private String cancelName;
    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;
    @ApiModelProperty(value = "取消原因描述")
    private String cancelReason;
    @ApiModelProperty(value = "处方单标识")
    private String prescriptionSheetMark;
    @ApiModelProperty(value = "是否新品")
    private Integer isNew;
    @ApiModelProperty(value = "是否新客户")
    private Integer isNewCustomer;
    @ApiModelProperty(value = "是否推送ERP")
    private Integer isPushErp;
    @ApiModelProperty(value = "推送ERP时间")
    private Date pushErpTime;
    @ApiModelProperty(value = "是否页面下单")
    private Integer isPageOrder;
    @ApiModelProperty(value = "")
    private Integer systemCheck;
    @ApiModelProperty(value = "是否跨境订单0否1是")
    private Integer isBorderOrder;
}
