package cn.hydee.ydjia.merchantmanager.dto.resp.industry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * 商品动销分析
 */

@Data
public class ActivityExcitationWareRespDTO {


    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "商品图片")
    private String mainPic;
    @ApiModelProperty(value = "商品规格")
    private String specSku;
    @ApiModelProperty(value = "条形码")
    private String barCode;
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

}
