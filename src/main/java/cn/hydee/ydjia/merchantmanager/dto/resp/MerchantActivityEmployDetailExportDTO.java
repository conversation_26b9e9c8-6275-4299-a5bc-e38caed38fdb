package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 
 * @date 2022/5/20 15:54
 */
@Data
public class MerchantActivityEmployDetailExportDTO {

    @ExcelProperty(value = "单号")
    @ColumnWidth(value = 20)
    private String orderNumber;

    @ExcelProperty(value = "员工编码")
    @ColumnWidth(value = 20)
    private String empCode;

    @ExcelProperty(value = "员工名称")
    @ColumnWidth(value = 20)
    private String empName;

    @ExcelProperty(value = "门店编码")
    @ColumnWidth(value = 20)
    private String storeCode;

    @ExcelProperty(value = "门店名称")
    @ColumnWidth(value = 20)
    private String storeName;

    @ExcelIgnore
    private Integer operationType;

    @ExcelProperty(value = "类型")
    @ColumnWidth(value = 20)
    private String operationTypeStr;

    @ExcelProperty(value = "业务订单号")
    @ColumnWidth(value = 20)
    private String orderId;

    @ExcelIgnore
    private BigDecimal moneyStr;

    @ExcelProperty(value = "激励收入")
    @ColumnWidth(value = 20)
    @ContentStyle(dataFormat = 2)
    private BigDecimal money;

    @ExcelIgnore
    private Integer orderState;

    @ExcelIgnore
    private Integer hasRefund;

    @ExcelProperty(value = "是否有退款")
    @ColumnWidth(value = 20)
    private String hasRefundStr;

    @ExcelIgnore
    private BigDecimal actualMoneyStr;

    @ExcelProperty(value = "实际激励金额")
    @ColumnWidth(value = 20)
    @ContentStyle(dataFormat = 2)
    private BigDecimal actualMoney;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelProperty(value = "结算时间")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date settlementTime;

    @ExcelProperty(value = "结算状态")
    @ColumnWidth(value = 20)
    private String orderStateStr;

}
