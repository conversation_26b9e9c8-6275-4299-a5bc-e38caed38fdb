package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/05/11 11:44
 */
@Data
public class MerchantDetailResDTO {
    @ApiModelProperty("统一接口平台管理后台")
    private String adminUrl;
    @ApiModelProperty("营销平台切换(type=1进入老微商城,type=2进入新微商城)")
    private Integer marketingPlatformType;
    @ApiModelProperty("统一接口平台接口")
    private String url;
    @ApiModelProperty("预警对接人")
    private String watcher;
    @ApiModelProperty("预警邮箱")
    private String watcherEmail;
    @ApiModelProperty("预警电话")
    private String watcherTel;
}
