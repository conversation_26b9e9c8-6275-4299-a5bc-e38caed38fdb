package cn.hydee.ydjia.merchantmanager.dto.resp.memberflow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @Name: RankingListQueryResp
 * @Description: 流量排行榜查询（按店员）返回类
 * @Author: Kaven
 * @Date: 2023/3/21 14:55
 */
@Data
public class RankingListQueryClerkResp {
    @ExcelIgnore
    @ApiModelProperty("排行")
    private Integer rank;

    @ExcelProperty("员工名称")
    @ApiModelProperty("员工名称")
    private String userName;

    @ExcelIgnore
    @ApiModelProperty("员工编码")
    private String empCode;

    @ExcelIgnore
    @ApiModelProperty("员工名称")
    private String empName;

    @ExcelProperty("所属门店")
    @ApiModelProperty("所属门店")
    private String beBusinessName;

    @ExcelProperty("分享次数")
    @ApiModelProperty("分享次数")
    private Long shareCount;

    @ExcelProperty("引导点击数")
    @ApiModelProperty("引导点击数")
    private Long guideClicks;

    @ExcelProperty("成交订单")
    @ApiModelProperty("成交订单")
    private Long orderCount;

    @ExcelProperty("销售额")
    @ApiModelProperty("销售额")
    private BigDecimal saleAmt;

    public String getUserName() {
        return StringUtils.isBlank(userName) ? empName : userName;
    }
}
