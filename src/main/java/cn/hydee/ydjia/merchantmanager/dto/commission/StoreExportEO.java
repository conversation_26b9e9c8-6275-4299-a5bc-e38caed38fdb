package cn.hydee.ydjia.merchantmanager.dto.commission;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/***
 * @ClassName: StoreExportEO
 * @Description: 门店业绩导出
 * @Author: koumingming
 * @Date: 2020-07-01 10:25
 * @version : V1.0
 */
@Data
public class StoreExportEO {

    @ExcelProperty("序号")
    private String serialNo;

    @ExcelIgnore
    private String storeId;
    @ExcelIgnore
    private String userId;

    @ExcelProperty("所在门店")
    private String storeName;

    @ExcelProperty("员工编码")
    private String empCode;

    @ExcelProperty("参与提成店员名称")
    private String empName;

    @ExcelProperty("时间区间")
    private String timeInterval;

    @ExcelProperty("销售金额（元）")
    private String saleAmount;

    @ExcelProperty("提成金额（元）")
    private String commissionAmount;
}
