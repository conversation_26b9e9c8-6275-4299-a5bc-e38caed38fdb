package cn.hydee.ydjia.merchantmanager.dto.resp.isp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
@Data
@Accessors(chain = true)
public class ActivityStatisticsByProductAndMerAndStoreExcelDTO {

//    @Excel(name = "连锁编码", orderNum = "5", width = 20)
//    private String merCode;
//
//    @Excel(name = "连锁名称", orderNum = "6", width = 20)
//    private String merName;

    @ExcelProperty("门店编码")
    @ColumnWidth(value = 20)
    private String storeCode;

    @ExcelProperty("门店名称")
    @ColumnWidth(value = 20)
    private String storeName;

    @ExcelProperty("商品名称")
    @ColumnWidth(value = 20)
    private String commodityName;

    @ExcelProperty("商品规格")
    @ColumnWidth(value = 20)
    private String specSku;

    @ExcelProperty("商品编码")
    @ColumnWidth(value = 20)
    private String erpCode;

    @ExcelProperty("销售数量")
    @ColumnWidth(value = 20)
    private Integer saleCommodityNum;

    @ExcelProperty("销售数量环比（%）")
    @ColumnWidth(value = 20)
    private BigDecimal saleCommodityLastRate;

    @ExcelProperty("销售金额")
    @ColumnWidth(value = 20)
    private BigDecimal saleCommodityAmount;

    @ExcelProperty("购买顾客数")
    @ColumnWidth(value = 20)
    private Integer customersNum = 0;

    @ExcelProperty("平均客品数")
    @ColumnWidth(value = 20)
    private BigDecimal averageCustomersNum;

//    @Excel(name = "可用库存数", orderNum = "95", width = 20)
//    private Integer stockNum;


    @ExcelProperty("退货率（%）")
    @ColumnWidth(value = 20)
    private BigDecimal returnRate;

}
