package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> Lee
 * @Create : 2022/1/10 14:27
 * @Description :
 */

@Data
public class ComboSubscribeReqDTO {

    @NotEmpty(message = "merCode不能为空")
    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @NotEmpty(message = "merAccount不能为空")
    @ApiModelProperty(value = "用户ID")
    private String merAccount;

    @NotEmpty(message = "comboId不能为空")
    @ApiModelProperty(value = "套餐产品ID")
    private String comboId;

    @NotEmpty(message = "comboName不能为空")
    @ApiModelProperty(value = "套餐名称")
    private String comboName;

    @ApiModelProperty(value = "购买条数")
    private Long buyNum;

    @ApiModelProperty(value = "赠送条数")
    private Integer giveNum;

    @NotNull(message = "totalFee不能为空")
    @ApiModelProperty(value = "实付金额（元）")
    private BigDecimal totalFee;

    @NotNull(message = "salePrice不能为空")
    @ApiModelProperty(value = "产品单价（元）")
    private BigDecimal salePrice;

    @Min(value = 1, message = "产品类型数值错误")
    @Max(value = 2, message = "产品类型数值错误")
    @ApiModelProperty(value = "产品类型 1=短信 2=语音")
    private Integer comboType;

}
