package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/16 18:27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntegralRankDateExportDTO {


    @ExcelProperty(value = "激励日期")
    @ColumnWidth(value = 20)
    private String createTime;

    @ExcelProperty(value = "店员名称")
    @ColumnWidth(value = 20)
    private String empName;

    @ExcelProperty(value = "店员编码")
    @ColumnWidth(value = 20)
    private String empCode;

    @ExcelProperty(value = "门店名称")
    @ColumnWidth(value = 20)
    private String empStoreName;

    @ExcelProperty(value = "门店编码")
    @ColumnWidth(value = 20)
    private String empStoreCode;

    @ExcelProperty(value = "获得积分")
    @ColumnWidth(value = 20)
    @ContentStyle(dataFormat = 2)
    private BigDecimal rewardBooking;
}
