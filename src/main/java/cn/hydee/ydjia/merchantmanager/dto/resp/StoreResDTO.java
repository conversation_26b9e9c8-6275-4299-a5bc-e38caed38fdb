package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.enums.OrgClassEnum;
import cn.hydee.ydjia.merchantmanager.excel.StoreIntegerConverter;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 员工查询返回DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/19 15:32
 */
@ContentStyle(dataFormat = 49)
@ExcelIgnoreUnannotated
@Data
public class StoreResDTO {

    @ColumnWidth(20)
    @ExcelProperty(value = "门店编码", index = 2)
    @ApiModelProperty(value = "门店编码")
    private String stCode;
    @ColumnWidth(20)
    @ExcelProperty(value = "门店名称", index = 1)
    @ApiModelProperty(value = "门店名称")
    private String stName;

    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区域")
    private String area;

    @ColumnWidth(20)
    @ExcelProperty(value = "门店地址", index = 3)
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ColumnWidth(20)
    @ExcelProperty(value = "电话", index = 5)
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ColumnWidth(20)
    @ExcelProperty(value = "门店图片", index = 0)
    @ApiModelProperty(value = "门店图片")
    private String stPath;
    @ColumnWidth(20)
    @ExcelProperty(value = "状态", index = 4, converter = StoreIntegerConverter.class)
    @ApiModelProperty(value = "门店启用状态")
    private Integer stStatus;

    @ApiModelProperty(value = "营业开始时间")
    private Integer openStartTime;
    @ApiModelProperty(value = "营业结束时间")
    private Integer openEndTime;
    @ApiModelProperty(value = "开始送药时间")
    private Integer deliveryStartTime;
    @ApiModelProperty(value = "结束送药时间")
    private Integer deliveryEndTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "门店自提", index = 6, converter = StoreIntegerConverter.class)
    @ApiModelProperty(value = "是否支持自提0不支持，1支持")
    private Integer isself;
    @ColumnWidth(20)
    @ExcelProperty(value = "送药上门", index = 7, converter = StoreIntegerConverter.class)
    @ApiModelProperty(value = "是否支持普通快递0不支持，1支持")
    private Integer isdelivery;
    @ColumnWidth(20)
    @ExcelProperty(value = "配送上门", index = 8, converter = StoreIntegerConverter.class)
    @ApiModelProperty(value = "是否支持送药上门0不支持，1支持")
    private Integer isdistribution;
    @ColumnWidth(20)
    @ExcelProperty(value = "状态", index = 9, converter = StoreIntegerConverter.class)
    @ApiModelProperty(value = "是否上线门店（0非上线门店，1上线门店）")
    private Integer onlineStatus;

    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "o2o服务范围(0,设置距离;1,自定义范围,2：全国范围)")
    private Integer o2oServiceScopeType;
    @ApiModelProperty(value = "服务范围")
    private BigDecimal serviceScope;
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "创建人")
    private String createName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改人")
    private String modifyName;
    @ApiModelProperty(value = "末次修改时间")
    private Date modifyTime;
    @ApiModelProperty(value = "是否中心店1中心店0非中心店")
    private Integer centerStore = 0;

    @ApiModelProperty(value = "门店支付或医保支付配置状态：0-未配置，1-已配置")
    private Integer payConfigStatus;
    @ApiModelProperty(value = "机构类型，3-仓库，其他为门店")
    private Integer stClass;
    private Integer areaId;

    /**
     * @return boolean
     * @description TODO 属于门店机构类型
     * <AUTHOR>
     * @date 2023/5/12
     */
    public boolean checkStoreType() {
        return OrgClassEnum.FRANCHISE_STORE.getCode().equals(stClass) || OrgClassEnum.DIRECT_STORE.getCode().equals(stClass);
    }

    public boolean checkKeyword(String keyword) {
        return (StringUtils.hasLength(stName) && stName.contains(keyword)) || (StringUtils.hasLength(stCode) && stCode.contains(keyword));
    }
}