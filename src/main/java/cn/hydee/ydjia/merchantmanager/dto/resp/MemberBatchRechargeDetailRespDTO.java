package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * @version 1.0
 * @Author: lisp
 * @Description: 会员充值
 * @Date: 2020/8/12
 */
@Data
public class MemberBatchRechargeDetailRespDTO {


    @ApiModelProperty(value = "会员姓名")
    private String memberName;

    @ApiModelProperty(value = "会员卡号")
    private String memberCard;

    @ApiModelProperty(value = "手机号码")
    private String memberPhone;;

    @ApiModelProperty(value = "充值本金")
    private BigDecimal capital;

    @ApiModelProperty(value = "充值状态，0：待充值，1：充值成功，2：充值失败")
    private Integer status;

    private Long userId;

}
