package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 直播活动详情
 *
 * <AUTHOR>
 * @date 2020/5/18   16:37
 * @since 1.0
 */
@Data
@EqualsAndHashCode
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveActivityDetailRspDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商户code")
    private String merCode;

    @ApiModelProperty("商户类型：1，商户；2，厂家")
    private String merType;

    @ApiModelProperty("商户logo地址")
    private String merLogoUrl;

    @ApiModelProperty("商户名称")
    private String merName;

    @ApiModelProperty("主题")
    private String name;

    @ApiModelProperty("直播封面")
    private String coverPicUrl;

    @ApiModelProperty("推流地址")
    private String pushStreamUrl;

    @ApiModelProperty("播放地址")
    private String playUrl;

    @ApiModelProperty("直播开始时间")
    private Date beginTime;

    @ApiModelProperty("实际开播时间")
    private Date realBeginTime;

    @ApiModelProperty(value = "直播列表卡片封面")
    private String listCardCover;

    @ApiModelProperty(value = "分享卡片封面")
    private String shareCardCover;

    @ApiModelProperty(value = "直播内容图文详情", required = true)
    private String graphicDetails;

    @ApiModelProperty("直播结束时间")
    private Date endTime;

    @ApiModelProperty("直播时长(秒)")
    private Integer duration;

    @ApiModelProperty("状态：0-未开播；1-开播中；2-直播完")
    private Integer status;

    @ApiModelProperty("审核状态：0-草稿；1-待审核；2-审核通过；3-审核不通过")
    private Byte auditStatus;

    @ApiModelProperty("备注（审核不通过原因）")
    private String remark;

    @ApiModelProperty("广告位图片地址")
    private String adPicUrl;

    @ApiModelProperty("广告位链接地址")
    private String adLinkUrl;

    @ApiModelProperty("背景色")
    private String bgColor;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty("修改人")
    private String updateName;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("直播流名称")
    private String streamName;

    @ApiModelProperty("直播的所有商品")
    private List<LiveCommodityRspDTO> commoditys;

    @ApiModelProperty("聊天群ID")
    private String groupId;

    @ApiModelProperty("优惠券活动ID")
    private Integer couponActivityId;

    @ApiModelProperty("抽奖活动ID")
    private Integer lotteryActivityId;
    /**
     * 在配置文件中配置
     */
    @ApiModelProperty("SDKAppID，直播中集成IM时使用")
    private Long sdkAppId;

    /**
     * 直播活动对应的优惠券信息
     */
    @ApiModelProperty("优惠券讯息")
    private List<ActivityCouponList> activityCouponList;

    @ApiModelProperty(value = "抽奖活动信息",required = true)
    private List<ActivityDetailRespDTO> activityDetailList;

    /**
     * 直播活动公告
     */
    @ApiModelProperty("直播公告")
    private String activityNotice;
    /**
     * 服务器url
     */
    @ApiModelProperty("服务器url")
    private String serviceUrl;
    /**
     * 直播活动公告
     */
    @ApiModelProperty("串流密钥")
    private String crossfireSecretKey;

    @ApiModelProperty(value = "B端活动公告")
    private String businessNotice;

    @ApiModelProperty(value = "直播介绍")
    private String liveIntroduce;

    /**
     * 订阅限制: 1-无对应商品，2-至少有一款对应商品，3-必须对应所有商品
     */
    @ApiModelProperty(value = "1-无对应商品，2-至少有一款对应商品，3-必须对应所有商品", required = true)
    private Byte subscribeLimitType;

    /**
     * 奖励规则
     */
    @ApiModelProperty(value = "奖励规则", required = true)
    private Integer prizeRule;

    /**
     * 奖励金额
     */
    @ApiModelProperty(value = "奖励金额", required = true)
    private BigDecimal prizeAmount;

    @ApiModelProperty(value = "rtmp播放地址")
    private String rtmpPlayUrl;

    @ApiModelProperty("粉丝团二维码图片url")
    private String fanBasePicUrl;

    @ApiModelProperty(value = "主播端小程序地址")
    private String anchorAppletsUrl;

    @ApiModelProperty(value = "C端小程序地址")
    private String clientAppletsUrl;

    @ApiModelProperty("订阅方")
    private String subscriber;

    @ApiModelProperty("订阅方logo地址")
    private String subscriberLogoUrl;

    /**
     * 海报位置:1-上方,2-左方,3-右方
     */
    private Integer posterSite;

    /**
     * 直播间海报
     */
    private String posterUrl;
    @ApiModelProperty(value = "正在直播活动观看人数（直播组件中）")
    private Long liveViewers;

    @ApiModelProperty(value = "虚拟人数")
    private Integer virtualVisits;

    @ApiModelProperty(value = "主播昵称")
    private String anchorNickName;

    @ApiModelProperty(value = "直播头像")
    private String livePortrait;

    @ApiModelProperty("直播播放二维码链接地址")
    private String qrCodeLinkUrl;
}
