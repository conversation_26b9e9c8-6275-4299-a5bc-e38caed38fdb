package cn.hydee.ydjia.merchantmanager.dto.resp.honey;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 过往病史表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Data
@Accessors(chain = true)
@TableName("t_medical_history")
@ApiModel(value="MedicalHistory对象", description="过往病史表")
public class MedicalHistoryEntity {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "过往病史(0=无；1=有)")
    private Integer pmyType;

    @ApiModelProperty(value = "过往病史描述")
    private String pmy;

    @ApiModelProperty(value = "过敏史(0=无；1=有)")
    private Integer amhType;

    @ApiModelProperty(value = "过敏史描述")
    private String amh;

    @ApiModelProperty(value = "家族病史(0=无；1=有)")
    private Integer fmhType;

    @ApiModelProperty(value = "家族病描述")
    private String fmh;

    @ApiModelProperty(value = "肝功能异常(0=无；1=有；)")
    private Integer liverType;

    @ApiModelProperty(value = "肝功能描述")
    private String liverDesc;

    @ApiModelProperty(value = "肾功能异常(0=无；1=有)")
    private Integer renalType;

    @ApiModelProperty(value = "肾功能描述")
    private String renalDesc;

    @ApiModelProperty(value = "是否妊娠哺乳(0=无；1=有)")
    private Integer nurseType;

    @ApiModelProperty(value = "妊娠说明")
    private String nurseDesc;

    @ApiModelProperty(value = "所属id(用药人id或者处方id)")
    private Long userApplyId;

    @ApiModelProperty(value = "类型（1：用药人 2：处方药）")
    private Integer type;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否有禁忌史(0=无；1=有)")
    private Integer tabooType;

    @ApiModelProperty(value = "禁忌史描述")
    private String tabooDesc;

    @ApiModelProperty(value = "是否有不良反应(0=无；1=有)")
    private Integer adrType;

    @ApiModelProperty(value = "不良反应描述")
    private String adrDesc;
}
