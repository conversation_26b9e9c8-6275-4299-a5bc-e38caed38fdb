package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 优惠券
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/06 16:19
 */
@Data
public class CouponResDTO {
    @ApiModelProperty(value = "优惠券ID")
    private Long id;
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "优惠券名称")
    private String cname;
    @ApiModelProperty(value = "优惠券logo")
    private String logo;
    @ApiModelProperty(value = "卡劵类型（1：折扣，2：抵价，3;礼品券）")
    private Integer ctype;
    @ApiModelProperty(value = "类型数据（面额，折扣）")
    private double denomination;
    @ApiModelProperty(value = "礼品id")
    private String proId;
    @ApiModelProperty(value = "最大优惠额度（0：无上限）")
    private double maxPrice;
    @ApiModelProperty(value = "优惠券状态 0失效，1生效")
    private Integer state;
    @ApiModelProperty(value = "使用时间规则（1：领取有效，2：有失效时间，3：具体时间）")
    private Integer timeRule;
    @ApiModelProperty(value = "使用时间规则值（用-分隔）")
    private String effectTime;
    @ApiModelProperty(value = "使用场景（1：线上，2：线下，3：线上线下通用）")
    private  int sceneRule;
    @ApiModelProperty(value = "退货规则（1：退货退回，2：退货失效）")
    private Integer returnRule;
    @ApiModelProperty(value = "客服电话")
    private String kefuPhone;
    @ApiModelProperty(value = "使用须知")
    private String note;
    @ApiModelProperty(value = "门店规则（1：全部，2：部分可用，3：部分不可用）")
    private Integer shopRule;
    @ApiModelProperty(value = "商品规则（1：全部，2：部分可用，3：部分不可用")
    private Integer productRule;
    @ApiModelProperty(value = "金额限制（0：无门槛）")
    private Double useRule;
    @ApiModelProperty(value = "到期提醒（0：不提醒）")
    private Double expireInfo;
    @ApiModelProperty(value = "总领取数")
    private Integer totalCount;
    @ApiModelProperty(value = "线上核销数")
    private Integer onlineCount;
    @ApiModelProperty(value = "线下核销数")
    private Integer offlineCount;
    @ApiModelProperty(value = "是否有效（0：否，1：是）")
    private Integer isValid;
    @ApiModelProperty(value = "创建人")
    private String createName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "修改人")
    private String updateName;
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    @ApiModelProperty(value = "优惠券限制商品信息")
    List<CouponProductDTO> listCouponProduct;
    @ApiModelProperty(value = "优惠券指定商品类型")
    List<CouponProductTypeDTO> listCouponProductType;
    @ApiModelProperty(value = "优惠券限制门店信息")
    List<CouponStoreDTO> listCouponStore;
    @ApiModelProperty(value = "根据礼品编码获取到的礼品信息")
    private GiftResDTO giftResDTO;
    @ApiModelProperty(value = "二维码链接")
    private String qrCodeUrl;
    @ApiModelProperty(value = "活动ＩＤ")
    private Integer activityId;
    @ApiModelProperty(value = "活动类型")
    private Integer activityType;
    /************用于B端展示,区分可用商品和可用分类***********/
    /**
     * 适用分类（1全部可用2指定可用3指定不可用）
     */
    @ApiModelProperty(value = "适用商品（1全部可用2指定可用3指定不可用）")
    private Integer typeRule;
    /**
     * 适用商品（1全部可用2指定可用3指定不可用）
     */
    @ApiModelProperty(value = "适用商品（1全部可用2指定可用3指定不可用）")
    private Integer proRule;

    /**
     * 种类是否可叠加(0:不可叠加,1:可叠加,2:不限制叠加)
     */
    @ApiModelProperty(value = "种类是否可叠加(0:不可叠加,1:可叠加,2:不限制叠加张数)")
    private Integer typeAddition;
    /**
     * 叠加张数
     */
    @ApiModelProperty(value = "叠加张数")
    private Integer typeAdditionNum;

    @ApiModelProperty(value = "员工使用规则 0:员工不可用 1:员工可用, 默认为1")
    private Integer empRule;
}
