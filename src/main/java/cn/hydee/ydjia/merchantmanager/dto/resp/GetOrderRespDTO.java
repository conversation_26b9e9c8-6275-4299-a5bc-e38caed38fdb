package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.domain.ExpressRecord;
import cn.hydee.ydjia.merchantmanager.domain.OrderCoupon;
import cn.hydee.ydjia.merchantmanager.domain.OrderDeliveryAddress;
import cn.hydee.ydjia.merchantmanager.domain.OrderDetail;
import cn.hydee.ydjia.merchantmanager.dto.resp.honey.MedicalPrescriptionApplyDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.honey.MedicalUserEntity;
import cn.hydee.ydjia.merchantmanager.dto.trans.MemberInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/26 11:20
 */
@Data
public class GetOrderRespDTO {
    @ApiModelProperty(value = "订单id")
    private String id;
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "门店id")
    private String storeId;
    @ApiModelProperty(value = "门店code")
    private String stCode;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "订单编号")
    private String serialNumber;
    @ApiModelProperty(value = "会员唯一标示")
    private String memberId;
    @ApiModelProperty(value = "订单类型 R处方药/N正常订单")
    private String orderType;
    @ApiModelProperty(value = "来源渠道对应码表的值")
    private String sourceChannel;
    @ApiModelProperty(value = "来源媒体来源媒体开放值")
    private String sourceMedia;
    @ApiModelProperty(value = "订单状态 2.待付款 4.待发货 6.待收货 8.待退货 10.待退款 12.已完成 20.已取消 30.退款完成 ")
    private Integer orderStatus;
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    @ApiModelProperty(value = "支付方式(0.在线支付 1.货到付款)")
    private Integer payMode;
    @ApiModelProperty(value = "支付类型")
    private Integer payType;
    @ApiModelProperty(value = "支付状态")
    private Integer payStatus;
    @ApiModelProperty(value = "支付时间最后付款时间")
    private Date payTime;
    @ApiModelProperty(value = "商品总重")
    private Integer totalGoodsNumber;
    @ApiModelProperty(value = "订单商品总额")
    private BigDecimal totalOrderAmount;
    @ApiModelProperty(value = "积分抵扣")
    private BigDecimal integralDeduction;
    @ApiModelProperty(value = "优惠券抵扣")
    private BigDecimal couponDeduction;
    @ApiModelProperty(value = "活动优惠金额")
    private BigDecimal activityDiscountAmont;
    @ApiModelProperty(value = "其他优惠金额")
    private BigDecimal otherDiscountAmont;
    @ApiModelProperty(value = "原运费金额")
    private BigDecimal originalFreightAmount;
    @ApiModelProperty(value = "实际运费金额")
    private BigDecimal actualFreightAmount;
    @ApiModelProperty(value = "订单总金额商品总额-积分抵扣-优惠券抵扣-活动优惠-其它优惠+实际运费")
    private BigDecimal totalActualOrderAmount;
    @ApiModelProperty(value = "订单支付心币数量")
    private Integer totalActualHb;
    @ApiModelProperty(value = "实际支付总金额")
    private BigDecimal actuallyPaid;
    @ApiModelProperty(value = "待支付金额")
    private BigDecimal amountTobepaid;
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "是否需要发票标识(1-需要 0-不需要)")
    private Integer isInvoice;
    @ApiModelProperty(value = "订单留言")
    private String orderMessage;
    @ApiModelProperty(value = "订单备注")
    private String orderRemark;
    @ApiModelProperty(value = "是否锁定")
    private Integer isLocked;
    @ApiModelProperty(value = "锁定人")
    private String lockedName;
    @ApiModelProperty(value = "锁定时间")
    private Date lockedTime;
    @ApiModelProperty(value = "取消人")
    private String cancelName;
    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;
    @ApiModelProperty(value = "取消原因描述")
    private String cancelReason;
    @ApiModelProperty(value = "处方单标识")
    private String prescriptionSheetMark;
    @ApiModelProperty(value = "是否新品")
    private Integer isNew;
    @ApiModelProperty(value = "是否新客户")
    private Integer isNewCustomer;
    @ApiModelProperty(value = "是否推送ERP")
    private Integer isPushErp;
    @ApiModelProperty(value = "推送ERP时间")
    private Date pushErpTime;
    @ApiModelProperty(value = "是否页面下单")
    private Integer isPageOrder;
    @ApiModelProperty(value = "是否已分摊 0 否 1 是")
    private Integer systemCheck;
    @ApiModelProperty(value = "是否跨境订单0否1是")
    private Integer isBorderOrder;
    @ApiModelProperty(value = "物流类型 0普通快递1配送上门2门店自提")
    private Integer deliveryType;
    @ApiModelProperty(value = "门店自提提货码")
    private Integer deliveryCode;

    @ApiModelProperty(value = "需求单审批状态0审批（未提交小蜜)1审批中2审批通过3审批拒绝")
    private  Integer prescriptionStatus;

    @ApiModelProperty(value = "订单明细列表")
    private List<OrderDetail> detailList;

    @ApiModelProperty(value = "发货门店信息")
    private StoreResDTO storeResDTO;

    @ApiModelProperty(value = "优惠券信息")
    private OrderCoupon orderCoupon;

    @ApiModelProperty(value = "优惠券集合")
    private List<OrderCoupon> orderCoupons;

    @ApiModelProperty(value = "发货物流信息列表")
    private List<ExpressRecord> recordList;
    @ApiModelProperty(value = "退货退款申请列表")
    private List<ReturnQuestRespDTO> returnList;
    @ApiModelProperty(value = "退货物流信息列表")
    private List<ExpressRecord> retRecordList;

    @ApiModelProperty(value = "收货人明细")
    private OrderDeliveryAddress orderDeliveryAddress;

    @ApiModelProperty(value = "处方申请单信息")
//    private PrescriptionApproval prescriptionApproval;
    private MedicalPrescriptionApplyDTO medicalPrescriptionApplyDTO;

    @ApiModelProperty(value = "会员信息")
    private MemberInfoVo memberInfoVo;

    @ApiModelProperty(value = "用药人id")
    private String medicalUserId;

    @ApiModelProperty(value = "余额")
    private BigDecimal payBalanceAmount;
    @ApiModelProperty(value = "抵扣邮费心币的数量")
    private Integer freightDeductionOfHb;

    @ApiModelProperty(value = "心币支付金额")
    private BigDecimal payOrderOfHb;

    @ApiModelProperty(value = "发货门店ID")
    private String sendStoreId;
    @ApiModelProperty(value = "发货门店名称")
    private String sendStoreName;
    @ApiModelProperty(value = "疫情管控订单，0：否，1：是")
    private Integer epidemicRegistration;
    @ApiModelProperty(value = "疫情管控信息")
    private EpidemicRegistration epidemicRegistrationDTO;
    @ApiModelProperty(value = "医保支付金额")
    private BigDecimal payMedicalAmount;
    @ApiModelProperty(value = "微信支付金额")
    private BigDecimal payWechatAmount;
    @ApiModelProperty(value = "特殊药品-用药人ID")
    private String specialMedicalUserId;
    @ApiModelProperty(value = "特殊药品-用药人信息")
    private MedicalUserEntity specialMedicalUserEntity;
    @ApiModelProperty(value = "供应商编码")
    private String spCode;
    @ApiModelProperty(value = "推广门店编码")
    private String spreadStoreCode;
    @ApiModelProperty(value = "推广门店名称")
    private String spreadStoreName;

    @ApiModelProperty(value = "分享人id")
    private String shareId;
    @ApiModelProperty(value = "分享人姓名")
    private String shareName;

    @ApiModelProperty(value = "订单应付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "已付金额")
    private BigDecimal paidFee;
    
    //支付明细
    List<OrderPaymentDetailDTO> payDetails;

    @ApiModelProperty(value = "用户绑定的店员编码")
    private String storeEmployeeCode;

    @ApiModelProperty(value = "流量来源入口（用于流量分析）")
    private String fromChannel;

    @ApiModelProperty(value = "订单转化页面")
    private String orderTransferPage;

    @ApiModelProperty(value = "是否为员工：1-是，0-否")
    private Integer isEmployee;
    @ApiModelProperty(value = "订单扩展信息：服务商收款参数等")
    private OrderInfoExtraDTO orderInfoExtraDTO;

    @ApiModelProperty(value = "订单商品门店价格")
    private List<OrderCommodityPriceDTO> orderCommodityPriceList;

    @ApiModelProperty(value = "支付券信息")
    private String paySaleInfo;

    @ApiModelProperty(value = "支付券金额")
    private BigDecimal paySaleSumAmount;

}
