package cn.hydee.ydjia.merchantmanager.dto.resp.memberconsumptionreport;

import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @author: LongHua
 * @date: 2022/1/7
 */
@Data
@NoArgsConstructor
public class ConsumptionAffectRespDTO {
    @ApiModelProperty(value = "订单数")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long totalOrders;

    @ApiModelProperty(value = "销售额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalSaleAmount;

    @ApiModelProperty(value = "毛利额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalProfit;

    @ApiModelProperty(value = "毛利率（会员毛利额/会员销售额）")
    private String totalProfitMargin;

    @ApiModelProperty(value = "平均客单价（会员销售额/会员订单数）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal avgCustomerPrice;

    @ApiModelProperty(value = "平均品单价（会员销售额/SKU数）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal avgSkuPrice;

    @ApiModelProperty(value = "平均客品数（SKU数/订单数）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal avgSkuNum;

    public ConsumptionAffectRespDTO(MemberConsumptionOrgRespDTO member) {
        if (member == null) {
            return;
        }
        // 订单数
        this.totalOrders = member.getTotalOrders();
        // 销售额
        this.totalSaleAmount = member.getTotalSaleAmount().setScale(2, RoundingMode.HALF_UP);
        // 毛利额
        this.totalProfit = member.getTotalProfit().setScale(2, RoundingMode.HALF_UP);
        // 毛利率
        this.totalProfitMargin = this.totalSaleAmount.compareTo(BigDecimal.ZERO) == 0 ?
                BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString() :
                NumberUtil.mul(NumberUtil.div(this.totalProfit, this.totalSaleAmount).setScale(4, RoundingMode.HALF_UP), NumberUtil.toBigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString();
        // 平均品单价
        this.avgSkuPrice = member.getTotalSku().equals(0L) ?
                BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP) :
                NumberUtil.div(this.totalSaleAmount, member.getTotalSku()).setScale(2, RoundingMode.HALF_UP);

        if (this.totalOrders.equals(0L)) {
            // 平均客单价
            this.avgCustomerPrice = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
            // 平均客品数
            this.avgSkuNum = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        } else {
            // 平均客单价
            this.avgCustomerPrice = NumberUtil.div(this.totalSaleAmount, this.totalOrders).setScale(2, RoundingMode.HALF_UP);
            // 平均客品数
            this.avgSkuNum = NumberUtil.div(member.getTotalSku(), this.totalOrders).setScale(2, RoundingMode.HALF_UP);
        }

    }

    public ConsumptionAffectRespDTO(ConsumptionAffectRespDTO memberAffect, ConsumptionAffectRespDTO nonMemberAffect, Long memberTotalSku, Long nonMemberTotalSku) {
        if (memberAffect == null) {
            return;
        }
        // 订单数
        this.totalOrders = memberAffect.getTotalOrders() + nonMemberAffect.getTotalOrders();
        // 销售额
        this.totalSaleAmount = NumberUtil.add(memberAffect.getTotalSaleAmount(), nonMemberAffect.getTotalSaleAmount()) ;
        // 毛利额
        this.totalProfit = NumberUtil.add(memberAffect.getTotalProfit(), nonMemberAffect.getTotalProfit()) ;
        // 毛利率
        this.totalProfitMargin =  this.totalSaleAmount.compareTo(BigDecimal.ZERO) == 0 ?
                BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString() :
                NumberUtil.mul(NumberUtil.div(this.totalProfit, this.totalSaleAmount).setScale(4, RoundingMode.HALF_UP), NumberUtil.toBigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString();

        Long totalSku = memberTotalSku + nonMemberTotalSku;

        // 平均品单价
        this.avgSkuPrice = totalSku.equals(0L) ?
                BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)
                : NumberUtil.div(this.totalSaleAmount, totalSku).setScale(2, RoundingMode.HALF_UP);

        if (this.totalOrders.equals(0L)) {
            // 平均客单价
            this.avgCustomerPrice = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
            // 平均客品数
            this.avgSkuNum = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        } else {
            // 平均客单价
            this.avgCustomerPrice = NumberUtil.div(this.totalSaleAmount, this.totalOrders).setScale(2, RoundingMode.HALF_UP);
            // 平均客品数
            this.avgSkuNum = NumberUtil.div(totalSku, this.totalOrders).setScale(2, RoundingMode.HALF_UP);
        }
    }
}
