package cn.hydee.ydjia.merchantmanager.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/17 11:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpecSearchDTO extends PageBase {
    @ApiModelProperty(value = "规格ID")
    private List<String> ids;
    @ApiModelProperty(value = "不包含的规格ID")
    private List<Long> notIds;
    @ApiModelProperty(value = "商家商品ID")
    private List<String> commodityIds;
    @NotBlank(message = "商户编码不可为空")
    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "商品ERP编码/商品名称")
    private String erpOrName;
    @ApiModelProperty(value = "商品对码状态")
    private Integer matchStatus;
    @ApiModelProperty(value = "商品ERP编码")
    private String erpCode;
    @ApiModelProperty(value = "商品条形码")
    private String barCode;
    @ApiModelProperty(value = "货主（0：自营，1：平安）")
    private Integer owner;
    @ApiModelProperty(value = "门店ID")
    private Set<String> storeIds;
    @ApiModelProperty("是否需要服务商信息，true-是")
    private Boolean needSrmInfo;
    @ApiModelProperty("商品类型， 1：普通商品，2：组合商品")
    private Integer commodityType;
    @ApiModelProperty(value = "商品类型集合（1：普通商品，2：组合商品，3：赠品，4：拆零商品）", hidden = true)
    private List<Integer> commodityTypeList;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("生产企业")
    private String manufacture;
    @ApiModelProperty("批准文号")
    private String approvalNumber;
    @ApiModelProperty("分组id")
    private String groupId;
    @ApiModelProperty("分组级别（1|2|3）")
    private Integer groupLevel;
    @ApiModelProperty("库存，售罄时传0")
    private Integer stock;
    @ApiModelProperty("状态，0-下架，1-上架")
    private Integer status;
    @ApiModelProperty("状态，0-未锁定，1-锁定价格，2-锁定库存，3-锁定价格和库存")
    private List<Integer> lockFlag;
    @ApiModelProperty("审核状态，0-审核不通过，1-审核通过，2-待审,3-未提交审核")
    private Integer auditStatus;
    @ApiModelProperty(value = "药品类型")
    private Integer drugType;
    private Boolean hasStoreSpec;
    private Boolean hasSpec;
    @ApiModelProperty(value = "品牌名")
    private String brandName;
    @ApiModelProperty(value = "是否包含图片")
    private Boolean hasPic;
    @ApiModelProperty(value = "是否包含橱窗图,true 包含 false 不包含")
    private Boolean hasMainPic;

    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    @ApiModelProperty(value = "最小库存")
    private Integer minStock;

    @ApiModelProperty(value = "最大库存")
    private Integer maxStock;

    @ApiModelProperty(value = "最小价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最大价格")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "商品ERP编码集合")
    private List<String> erpCodes;

    @ApiModelProperty(value = "是否疫情管控，空不进行现在，true-是疫情管控商品，false-非疫情管控商品")
    private Boolean hasOutBreak;

    private String userName;

    private String userId;

    private Boolean hasAllByCondition;

    @ApiModelProperty(value = "是否过滤服务商信息：true-是，false不过滤并返回服务商字段")
    private Boolean filterSrm;

    @ApiModelProperty(value = "是否查询云仓商品")
    private Boolean isSp;
    @ApiModelProperty(value = "服务商编码")
    private List<String> spCodes;
    @ApiModelProperty(value = "是否查询共享仓库存")
    private Boolean hasShare;
    @ApiModelProperty(value = "供应商名称/服务商名称")
    private String spName;
    @ApiModelProperty(value = "无标签条件，但需商品列表需展示标签属性时传true； 其他传false")
    private Boolean hasLabelSearch;
    @ApiModelProperty(value = "是否查询处方药（true-查所有类型，默认排除处方药）")
    private Boolean hasDrug;
}
