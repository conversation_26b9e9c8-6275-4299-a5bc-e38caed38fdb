package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.ydjia.merchantmanager.excel.Excel;
import cn.hydee.ydjia.merchantmanager.excel.ExcelKeyValue;
import cn.hydee.ydjia.merchantmanager.excel.KeyValue;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 营养保健导入模板对象
 * <AUTHOR>
 * @date 2019/10/10 16:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NutritionDTO extends ExcelObject {

    @Excel(name = "商品编码", orderNum = 0)
    private String erpCode;

    @Excel(name = "商品名称", orderNum = 1)
    private String name;

    @Excel(name = "品牌", orderNum = 2)
    private String brandName;

    @Excel(name = "批准文号", orderNum = 3)
    private String approvalNumber;

    @Excel(name = "生产企业", orderNum = 4)
    private String manufacture;

    @Excel(name = "参考价", orderNum = 5)
    private BigDecimal mPrice;

    @Excel(name = "单位", orderNum = 6)
    private String unit;

    @Excel(name = "重量（g）", orderNum = 7)
    private Integer weight;

    @Excel(name="货主（0自营 1平安 默认0）",orderNum = 8)
    private Integer owner;

    @Excel(name = "条形码", orderNum = 9)
    private String barCode;

    @Excel(name = "产地", orderNum = 10)
    private String produceOrigin;

    @Excel(name = "长（M）", orderNum = 11)
    private String length;

    @Excel(name = "宽（M）", orderNum = 12)
    private String width;

    @Excel(name = "高（M）", orderNum = 13)
    private String height;

    @Excel(name = "保健功能", orderNum = 14)
    private String keyFeature;

    @Excel(name = "有效期/保质期(天)", orderNum = 15)
    private Integer expireDays;

    @Excel(name = "运输方式： 常温(0)/冷藏(1)/冰冻(2)/阴凉(3)", orderNum = 16)
    private Integer freightType;

    @Excel(name = "是否易碎", orderNum = 17)
    private Integer isEasyBreak;

    @Excel(name = "是否液体", orderNum = 18)
    private Integer isLiquid;

    @Excel(name = "完整说明书", orderNum = 19)
    private String instructions;

    @Excel(name = "关键字(以、隔开)", orderNum = 20)
    private String keyWord;

    @ExcelKeyValue(start = 21)
    private List<KeyValue> keyValues;

    private String firstTypeId;

    private String firstTypeName;



    @Override
    public boolean isAllNull() {
        boolean valueNull = keyValues == null;
        if (!valueNull) {
            valueNull = true;
            for (KeyValue keyValue : keyValues) {
                valueNull = valueNull && StringUtils.isEmpty(keyValue.getSkuValue());
            }
        }
        return valueNull && StringUtils.isEmpty(erpCode) && StringUtils.isEmpty(name)
                && StringUtils.isEmpty(brandName) && StringUtils.isEmpty(manufacture)
                && StringUtils.isEmpty(approvalNumber) && StringUtils.isEmpty(barCode)
                && mPrice == null && weight == null && expireDays == null && freightType == null
                && isEasyBreak == null && isLiquid == null
                && StringUtils.isEmpty(unit) && StringUtils.isEmpty(produceOrigin)
                && StringUtils.isEmpty(length) && StringUtils.isEmpty(width)
                && StringUtils.isEmpty(height) && StringUtils.isEmpty(keyWord)
                && StringUtils.isEmpty(keyFeature) && StringUtils.isEmpty(instructions);
    }

    public void trim() {
        if (erpCode != null) {
            erpCode = erpCode.trim();
        }
        if (manufacture != null) {
            manufacture = manufacture.trim();
        }
        if (name != null) {
            name = name.trim();
        }
        if (brandName != null) {
            brandName = brandName.trim();
        }
        if (approvalNumber != null) {
            approvalNumber = approvalNumber.trim();
        }
        if (barCode != null) {
            barCode = barCode.trim();
        }
        if (unit != null) {
            unit = unit.trim();
        }
        if (produceOrigin != null) {
            produceOrigin = produceOrigin.trim();
        }
        if (length != null) {
            length = length.trim();
        }
        if (width != null) {
            width = width.trim();
        }
        if (height != null) {
            height = height.trim();
        }
        if (keyWord != null) {
            keyWord = keyWord.trim();
        }
        if (keyFeature != null) {
            keyFeature = keyFeature.trim();
        }
        for (KeyValue keyValue : keyValues) {
            keyValue.setSkuValue(keyValue.getSkuValue().trim());
        }
    }
}
