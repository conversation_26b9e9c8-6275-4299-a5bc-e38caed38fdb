package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: LongHua
 * @date: 2022/1/17
 */
@Data
@ApiModel("概率页会员统计DTO")
public class OverviewMemberCountRespDTO {
    @ApiModelProperty("生日会员：近7日生日会员数量")
    private Long birthdayCount;
    @ApiModelProperty("有效会员：近30天有消费的会员统计")
    private Long validConsumptionCount;
    @ApiModelProperty("沉睡会员：近三个月未消费&近一年内有消费的会员")
    private Long sleepingCount;
    @ApiModelProperty("流失会员：近六个月未消费&近一年内有消费的会员")
    private Long lostCount;
}
