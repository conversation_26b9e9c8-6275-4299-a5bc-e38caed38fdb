package cn.hydee.ydjia.merchantmanager.dto.resp;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资源查询统一返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/26 11:05
 */
@Data
public class ResourceDTO {
    private String id;
    private String reParent;
    private String reSystem;
    private String reEngSystem;
    private String appCode;
    private Integer isPay;
    private String reModule;
    private String reMenu;
    private String reButton;
    private String rePath;
    private String reMethod;
    private String reType;
    private String reUrl;
    private String reIcon;
    private Integer isvalid;
    private String createName;
    private Date createTime;
    private String modifyName;
    private Date modifyTime;
    private Integer reSort;
}
