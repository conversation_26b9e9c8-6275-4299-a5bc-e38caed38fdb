package cn.hydee.ydjia.merchantmanager.dto.resp.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version: V1.0
 * @author: qiming.li
 * @description: 消息统计
 * @data: 2023/5/23、11:45
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageCountRespDTO {
    @ApiModelProperty("待处理消息")
    private Integer waitCount;

    @ApiModelProperty("系统消息")
    private Integer systemCount;
}
