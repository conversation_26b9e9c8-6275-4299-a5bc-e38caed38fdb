package cn.hydee.ydjia.merchantmanager.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/19
 */
@Data
public class ILSBAAccountRespDTO {

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("动销激励收入待结算金额")
    private Long chainRewardPendAmount;

    @ApiModelProperty("动销激励收入已结算金额")
    private Long chainRewardSettledAmount;

    @ApiModelProperty("动销激励待入账金额")
    private Long chainWaitSettleMoney= 0L;

    @ApiModelProperty("优惠券补贴待结算金额")
    private Long couponRewardPendAmount;

    @ApiModelProperty("优惠券补贴已结算金额")
    private Long couponRewardSettledAmount;

    @ApiModelProperty("优惠券补贴待入账金额")
    private Long couponWaitSettleAmount = 0L;

    @ApiModelProperty("服务商编码")
    private String ispCode;

    @ApiModelProperty("服务商名称")
    private String ispName;

    @ApiModelProperty("总收入待结算金额")
    private Long totalIncomePendAmount;

    @ApiModelProperty("总收入已结算金额")
    private Long totalIncomeSettledAmount;

    @ApiModelProperty("总待入账金额")
    private Long totalWaitSettleMoney = 0L;

    @ApiModelProperty(value = "员工激励已结算金额")
    private Long employSettledAmount;

    @ApiModelProperty(value = "员工激励待结算金额")
    private Long employPendAmount;

    @ApiModelProperty("员工奖励待入账金额")
    private Long employWaitSettleAmount = 0L;

    @ApiModelProperty("员工奖励已结算积分")
    private Long employIntegralSettler;

    @ApiModelProperty("员工奖励已结算积分")
    private Long employIntegralUnSettler;

    @ApiModelProperty("员工奖励待入账积分")
    private Long employIntegralWaitSettler = 0L;

}
