/**
 * projectName: hydee-middle-market
 * fileName: ActivityDetailIspEntityDTO.java
 * packageName: cn.hydee.middle.market.dto.rsp.isp
 * date: 2022-05-18 9:44 下午
 * copyright(c) 2022 http://www.hydee.cn/ Inc. All rights reserved.
 */
package cn.hydee.ydjia.merchantmanager.dto.resp.industry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version: V1.0
 * @author: lyh
 * @className: DynamicSalesActivityDetailIspRespDTO
 * @packageName: cn.hydee.middle.market.dto.rsp.isp
 * @description: 商品动销趋势
 * @data: 2022-05-18 9:44 下午
 **/
@Data
public class DynamicSalesTrendWareRespDTO {

    @ApiModelProperty(value = "本品商品来源")
    private Integer wareSource;

    @ApiModelProperty(value = "本品标库商品编码")
    private String warePlatformCode;

    @ApiModelProperty(value = "本品服务商商品编码")
    private String wareIspCode;

    @ApiModelProperty(value = "本品商品名称")
    private String wareName;

    @ApiModelProperty(value = "动销数量")
    private Integer dynamicSalesQty;

    @ApiModelProperty(value = "日期:yyyy-mm-dd")
    private String dateByDay;

}