package cn.hydee.ydjia.merchantmanager.dto.marketblacklist;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品配置 Request DTO
 *
 * <AUTHOR>
 * @since 2024-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BlacklistCommodityReqDTO extends BlacklistBaseReqDTO {

    @ApiModelProperty("黑名单ID")
    private Integer blacklistId;

    @ApiModelProperty("商品编码（ERP 编码）")
    private String erpCode;

    @ApiModelProperty("商品名称")
    private String name;

}
