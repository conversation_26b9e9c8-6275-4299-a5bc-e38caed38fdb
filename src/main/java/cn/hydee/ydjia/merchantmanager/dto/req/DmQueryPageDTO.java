package cn.hydee.ydjia.merchantmanager.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * DM单维度发布状态设置
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/19 12:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DmQueryPageDTO extends PageBase {

    @NotBlank(message = "商家编码不可为空")
    @ApiModelProperty("商家编码")
    private String merCode;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发布状态：0-取消发布，1-发布")
    private Integer status;
}
