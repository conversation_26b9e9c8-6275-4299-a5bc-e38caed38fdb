package cn.hydee.ydjia.merchantmanager.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class ImActivityRewardApplyRecordDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("merCode")
    private String merCode;

    /**
     * 申请单号
     */
    @ApiModelProperty("申请单号")
    private String applyNo;

    /**
     * 申请原因
     */
    @ApiModelProperty("申请原因")
    private String applyReason;

    /**
     * 申请开始时间
     */
    @ApiModelProperty("申请开始时间")
    private String startDate;

    /**
     * 申请结束时间
     */
    @ApiModelProperty("申请结束时间")
    private String endDate;

    /**
     * 申请状态（1：审核中；2：审核通过；3：审核退回）
     */
    @ApiModelProperty("申请状态（1：审核中；2：审核通过；3：审核退回）")
    private Integer state;

    /**
     * 退回原因
     */
    @ApiModelProperty("退回原因")
    private String returnReason;

    /**
     * 激励状态状态（0：未补发；1：补发中；2：补发完成）
     */
    @ApiModelProperty("激励状态状态（0：未补发；1：补发中；2：补发完成）")
    private Integer rewardStatus;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createName;

    /**
     * 创建账号
     */
    @ApiModelProperty("创建账号")
    private String createAccount;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 末次更新时间
     */
    @ApiModelProperty("末次更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("订单数")
    private Integer orderCount;

    @ApiModelProperty("活动信息")
    private List<ImActivityRewardApplyRecordDetailDTO.ApplyActivity> activityList;

    @Data
    public static class ApplyActivity {
        @ApiModelProperty("服务商活动ID")
        private Long ispActivityId;

        @ApiModelProperty("商户工业营销活动ID")
        private Integer activityId;

        @ApiModelProperty("活动名称")
        private String activityName;

        @ApiModelProperty("状态")
        private Integer status;

        @ApiModelProperty(value = "活动起始时间")
        private Date beginTime;

        @ApiModelProperty(value = "活动结束时间")
        private Date endTime;

        @ApiModelProperty("服务商编码")
        private String ispCode;

        @ApiModelProperty("服务商名称")
        private String ispName;
    }
}
