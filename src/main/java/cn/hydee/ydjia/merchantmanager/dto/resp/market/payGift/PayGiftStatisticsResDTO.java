package cn.hydee.ydjia.merchantmanager.dto.resp.market.payGift;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付有礼活动统计数据响应DTO
 *
 * <AUTHOR>
 * @date 2023/6/15
 */
@Data
public class PayGiftStatisticsResDTO {

    @ApiModelProperty(value = "活动id")
    private Integer activityId;

    @ApiModelProperty(value = "参与人数")
    private Integer joinUserNum ;

    @ApiModelProperty(value = "参与订单数")
    private Integer joinOrderNum;

    @ApiModelProperty(value = "销售额")
    private BigDecimal saleAmount;

    @ApiModelProperty(value = "毛利额")
    private BigDecimal grossProfitAmount;

    /**
     * 毛利额/销售额*100%
     */
    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossProfitAmountRate;

    @ApiModelProperty(value = "日均客单价")
    private BigDecimal avgPrice;

    @ApiModelProperty(value = "环比日均客单价")
    private BigDecimal avgPriceLast;

    /**
     * (日均客单价-环比日均客单价）/环比日均客单价*100%
     */
    @ApiModelProperty(value = "客单价环比提升率")
    private BigDecimal avgPriceImproveRate;

}
