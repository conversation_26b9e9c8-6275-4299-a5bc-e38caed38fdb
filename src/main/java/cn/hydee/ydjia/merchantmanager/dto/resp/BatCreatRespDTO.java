package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/10/28 14:21
 */
@Data
public class BatCreatRespDTO {

    @ApiModelProperty(value = "品类 1-中西医药 ,2-营养保健 ,3-医疗器械 ,4-其它")
    private String model;
    @ApiModelProperty(value = "结果")
    private Boolean resut;
    @ApiModelProperty(value = "数量")
    private Integer number;
    @ApiModelProperty(value = "excel地址")
    private String excelPath;
}
