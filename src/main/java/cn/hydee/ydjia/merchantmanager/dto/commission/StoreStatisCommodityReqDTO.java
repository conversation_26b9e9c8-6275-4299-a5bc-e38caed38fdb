package cn.hydee.ydjia.merchantmanager.dto.commission;

import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Pattern;
import java.util.List;

/***
 * @ClassName: StoreStatisReqDTO
 * @Description: 门店业绩统计
 * @Author: lck
 * @Date: 2021-09-14 14:45
 * @version : V1.0
 */
@Data
@ToString(callSuper = true)
public class StoreStatisCommodityReqDTO {

    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty("门店集合")
    private List<String> storeIds;

    @ApiModelProperty("开始时间:yyyy-MM-dd")
    @Pattern(regexp = LocalConst.YYYY_MM_DD, message = "开始时间格式错误")
    private String startDate;

    @ApiModelProperty("结束时间:yyyy-MM-dd")
    @Pattern(regexp = LocalConst.YYYY_MM_DD, message = "结束时间格式错误")
    private String endDate;

}
