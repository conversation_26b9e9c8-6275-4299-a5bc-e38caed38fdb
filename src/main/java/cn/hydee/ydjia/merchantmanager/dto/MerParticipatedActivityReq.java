package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MerParticipatedActivityReq extends PageBase {

    @ApiModelProperty("商户号")
    @NotBlank(message = "商家编码不能为空")
    private String merCode;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "服务商名称")
    private String ispName;

    @ApiModelProperty(value = "地区ID集合")
    private List<Integer> areaIds;

    @ApiModelProperty(value = "服务商编码")
    private List<String> ispCodes;

    @ApiModelProperty("服务商活动编码")
    private List<Long> ispActivityIds;
}
