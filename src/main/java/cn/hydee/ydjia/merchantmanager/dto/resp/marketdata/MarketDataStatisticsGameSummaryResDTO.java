package cn.hydee.ydjia.merchantmanager.dto.resp.marketdata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/10/9 下午1:57
 */
@Data
public class MarketDataStatisticsGameSummaryResDTO implements Serializable {
    private static final long serialVersionUID = 5794793805098614474L;

    /**
     * 访问人数
     */
    @ApiModelProperty(value = "访问人数", notes = "")
    private Integer visitUserCount;
    /**
     * 参与次数
     */
    @ApiModelProperty(value = "参与次数", notes = "")
    private Integer joinCount;
    /**
     * 参与人数
     */
    @ApiModelProperty(value = "参与人数", notes = "")
    private Integer joinUserCount;
    /**
     * 中奖次数
     */
    @ApiModelProperty(value = "中奖次数", notes = "")
    private Integer winCount;
    /**
     * 中奖人数
     */
    @ApiModelProperty(value = "中奖人数", notes = "")
    private Integer winUserCount;

}
