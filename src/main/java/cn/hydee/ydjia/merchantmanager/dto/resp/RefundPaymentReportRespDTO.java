package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.batch.annotation.EnableAsyncBatchExportTask;
import cn.hydee.ydjia.merchantmanager.excel.LongConverter;
import cn.hydee.ydjia.merchantmanager.service.ExportTaskService;
import cn.hydee.ydjia.merchantmanager.service.impl.OrderPaymentExportProcessor;
import cn.hydee.ydjia.merchantmanager.service.impl.RefundPaymentExportProcessor;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2022-02-16 16:25
 * @since 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ExcelIgnoreUnannotated
@EnableAsyncBatchExportTask(businessType = ExportTaskService.REFUND_PAYMENT_EXPORT, taskName = "微商城退款订单报表",fileName = "微商城退款订单报表",
        sheetNames = {"微商城退款订单报表"}, batchSize = LocalConst.DEFAULT_BATCH_SIZE, processor = RefundPaymentExportProcessor.class)
public class RefundPaymentReportRespDTO {

    @ApiModelProperty(value = "退款单号")
    @ExcelProperty(value = "退款单号", converter = LongConverter.class)
    private Long refundId;

    @ApiModelProperty(value = "平台订单号")
    @ExcelProperty(value = "平台订单号", converter = LongConverter.class)
    private Long orderId;

    @ApiModelProperty(value = "支付单号")
    @ExcelProperty(value = "业务支付单号")
    private String paymentNo;

    @ApiModelProperty(value = "订单类型")
    @ExcelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "是否处方单")
    @ExcelProperty(value = "是否处方单")
    private String prescriptionSheetMark;

    @ApiModelProperty(value = "是否离店订单，0：否，1：是")
    @ExcelProperty(value = "是否离店订单")
    private String distanceOrder;

    @ApiModelProperty(value = "退款方式，1：全额退款，0：部分退款")
    @ExcelProperty(value = "退款类型")
    private String wholeReturn;

    @ApiModelProperty(value = "下单门店id")
    private String storeId;

    @ApiModelProperty(value = "下单门店code")
    @ExcelProperty(value = "下单门店ID")
    private String storeCode;

    @ApiModelProperty(value = "下单门店名称")
    @ExcelProperty(value = "下单门店名称")
    private String storeName;

    @ApiModelProperty(value = "发货门店ID")
    private String sendStoreId;

    @ApiModelProperty(value = "发货门店code")
    @ExcelProperty(value = "发货门店ID")
    private String sendStoreCode;

    @ApiModelProperty(value = "发货门店名称")
    @ExcelProperty(value = "发货门店名称")
    private String sendStoreName;

    @ApiModelProperty(value = "实收金额")
    @ExcelProperty(value = "订单实付金额")
    private BigDecimal actualOrderAmount;

    @ApiModelProperty(value = "退款金额")
    @ExcelProperty(value = "退款金额")
    private BigDecimal actualRefundAmount;

    @ApiModelProperty(value = "微信退款金额")
    @ExcelProperty(value = "微信退款金额")
    private BigDecimal wxPayRefundAmount;

    @ApiModelProperty(value = "余额退款金额")
    @ExcelProperty(value = "余额退款金额")
    private BigDecimal payBalanceRefundAmount;

    @ApiModelProperty(value = "医保退款金额")
    @ExcelProperty(value = "医保退款金额")
    private BigDecimal payMedicalRefundAmount;

    @ApiModelProperty(value = "退款完成时间")
    @ExcelProperty(value = "退款完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    @ApiModelProperty(value = "供应商编码")
    private String spCode;
}
