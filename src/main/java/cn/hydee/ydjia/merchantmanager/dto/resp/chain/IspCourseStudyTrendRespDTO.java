package cn.hydee.ydjia.merchantmanager.dto.resp.chain;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:01
 */
@Data
public class IspCourseStudyTrendRespDTO {

    @ApiModelProperty(value = "学习趋势数据日期集合（格式:yyyy-mm-dd）")
    private List<String> dateList;


    @ApiModelProperty("学习人数指标")
    private List<StudyStatistics> dataList;

    @Api(value = "学习人数指标")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public  static  class StudyStatistics {

        /**
         * @see
         */
        @ApiModelProperty(value = "学习趋势数据指标名")
        private String name;

        @ApiModelProperty(value = "学习趋势数据指标值集合，按dataList日期顺序返回")
        private List<Integer> data;
    }

}
