package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class MedicineHomeSettingRspDTO {

    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty("提醒方式列表；1：公众号；2：小程序消息；3：员工；4：短信；5站内信")
    private List<String> remindStyles;

    @ApiModelProperty("是否开通短信套餐；true：是，false：否")
    private Boolean openSmsCombo;

    @ApiModelProperty("0：关闭；1：开启")
    private Integer isValid;

    @ApiModelProperty("开启模板信息列表")
    private List<MedicineHomeSettingRespDTO.OpenTemplateInfo> templateInfos;

}