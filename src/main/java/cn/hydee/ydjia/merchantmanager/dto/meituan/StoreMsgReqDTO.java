package cn.hydee.ydjia.merchantmanager.dto.meituan;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**门店消息上报参数
 * <AUTHOR>
 * @date 2020-11-20 14:36
 * @since 1.0
 */
@Data
public class StoreMsgReqDTO{

    @NotNull(message = "门店授权ID不能为空")
    @ApiModelProperty(value = "门店授权ID")
    private Long id;

    @ApiModelProperty(value = "门店未读消息数")
    @NotNull(message = "门店未读消息数不能为空")
    private Integer unreadMsgCount;

    @NotNull(message = "用户消息不能为空")
    @ApiModelProperty(value = "用户消息")
    private List<UserMsgReqDTO> users;
}
