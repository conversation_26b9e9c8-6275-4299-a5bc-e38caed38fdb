package cn.hydee.ydjia.merchantmanager.dto.commission;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/17   17:00
 * @since 1.0
 */
@Data
@Builder
public class AddCommissionReqDTO {

    @ApiModelProperty(value = "方案名称", required = true)
    @NotNull(message = "方案名称不能为空")
    private String name;

    @ApiModelProperty(value = "提成商品")
    private List<CommissionCommodity> commodityList;

    @ApiModelProperty(value = "门店")
    private List<CommissionStore> storeList;

    @ApiModelProperty(value = "商户code")
    private String merCode;

    @ApiModelProperty(value = "用户名")
    private String userName;

}
