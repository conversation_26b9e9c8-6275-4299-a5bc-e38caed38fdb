package cn.hydee.ydjia.merchantmanager.dto.resp.third;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/27
 */
@Data
public class WxCodeResDTO {

    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty("APPID")
    private String appid;

    @ApiModelProperty("APP名称")
    private String appName;

    @ApiModelProperty("APP类型：1.小程序，2.H5")
    private Integer appType;

    @ApiModelProperty("机构编码集合")
    private List<String> orgList;

    @ApiModelProperty("二维码")
    private String code;

    @ApiModelProperty("地址")
    private String url;

}
