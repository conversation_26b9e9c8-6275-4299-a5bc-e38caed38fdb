package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.enums.sms.MemberLifeCircleTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: lisp
 * @time: 2021/01/04 10:47
 */

@Data
public class MemberCustomizeCrowdInfo extends MemberBaseCrowdInfo{


    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "人群类型")
    private Integer type;

    @ApiModelProperty(value = "人群状态")
    private Integer status;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date calculateTime;

    @ApiModelProperty(value = "人群计算状态")
    private Integer calculateStatus;

    @ApiModelProperty(value = "人群总数")
    private Integer totalCrowdNum = 0;

    @ApiModelProperty(value = "平均客单价")
    private BigDecimal averagePrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "带动销售金额")
    private BigDecimal driveSaleAmount = BigDecimal.ZERO;

    /**
     *  生命周期
     * @see  MemberLifeCircleTypeEnum
     */
    private Integer lifeCircleType;

}
