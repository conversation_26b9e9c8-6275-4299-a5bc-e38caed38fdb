package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/09
 */
@Data
public class CommoditySaleAmountRankQueryRespDTO {

    @ApiModelProperty("服务商编码")
    private String ispCode;

    @ApiModelProperty("服务商活动id")
    private Long id;

    @ApiModelProperty("排行激励类型(1.周销量排行激励，2.月销量排行激励)")
    private Integer type;

    @ApiModelProperty("激励配置ID")
    private Long excitationId;

    @ApiModelProperty(value = "保底金额")
    @Min(value = 1, message = "销售排行激励上榜保底金额不能低于1")
    private BigDecimal floorsMoney;

    @ApiModelProperty("销售额排名激励集合")
    private List<ExcitationWareInfo> excitationWareList;

    @ApiModelProperty(value = "销售额排名详细激励配置集合")
    private List<BaseRankDTO> rankList;

    @Data
    @ToString
    public static class ExcitationWareInfo{

        private Long id;

        @ApiModelProperty(value = "商品来源")
        private Integer wareSource;

        @ApiModelProperty(value = "标库商品编码")
        private String warePlatformCode;

        @ApiModelProperty(value = "服务商商品编码")
        private String wareIspCode;

        @ApiModelProperty(value = "服务商商品名称")
        private String name;

        @ApiModelProperty(value = "服务商商品规格")
        private String specSku;

        @ApiModelProperty(value = "服务商商品图片")
        private String mainPic;

        @ApiModelProperty(value = "条形码")
        private String barCode;
    }

    public boolean validate(){
        return Objects.nonNull(excitationId) && Objects.nonNull(floorsMoney) && !CollectionUtils.isEmpty(excitationWareList) && !CollectionUtils.isEmpty(rankList);
    }
}
