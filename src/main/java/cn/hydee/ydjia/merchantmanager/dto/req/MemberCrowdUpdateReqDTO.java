package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;


@Data
@ApiModel("更新人群状态接口参数类")
@ToString(callSuper = true)
public class MemberCrowdUpdateReqDTO extends MemberCrowdOperationReqDTO {

    @ApiModelProperty(value = "人群状态；1：启用中；2：已停用")
    @NotNull(message = "人群状态不能为空")
    private Integer status;

}
