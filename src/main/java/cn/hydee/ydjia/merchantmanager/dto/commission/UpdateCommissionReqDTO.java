package cn.hydee.ydjia.merchantmanager.dto.commission;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/17   17:00
 * @since 1.0
 */
@Data
public class UpdateCommissionReqDTO {

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "方案id不能为空")
    private Integer id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty(value = "状态：1-未发布；2-已发布；3-停用；4-作废", required = true)
    @NotNull(message = "方案状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "提成商品")
    private List<CommissionCommodity> commodityList;

    @ApiModelProperty(value = "门店")
    private List<CommissionStore> storeList;

    @ApiModelProperty(value = "商户code")
    private String merCode;

    @ApiModelProperty(value = "用户名")
    private String userName;
}
