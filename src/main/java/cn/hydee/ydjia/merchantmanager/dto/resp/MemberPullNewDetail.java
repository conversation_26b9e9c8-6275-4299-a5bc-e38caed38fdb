package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
public class MemberPullNewDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编码
     */
    private String merCode;

    private Integer taskId;

    /**
     * 储值卡ID
     */
    private Long userId;

    /**
     * 推荐方式；1：门店推荐，2：员工推荐
     */
    private Integer recommendType;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 员工编码
     */
    private String empCode;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 注册日期；yyyy-MM-dd
     */
    private Long regDate;

    /**
     * 创建时间
     */
    private Date createTime;


}
