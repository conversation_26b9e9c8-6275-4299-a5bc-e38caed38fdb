package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CrowdWareIspResDTO {
    @ApiModelProperty(value = "主建id")
    private Long id;
    @ApiModelProperty(value = "服务商编码")
    private String ispCode;
    @ApiModelProperty(value = "人群模板id")
    private Long crowdIspId;
    @ApiModelProperty(value = "商品来源")
    private Integer wareSource;
    @ApiModelProperty(value = "标库商品编码")
    private String warePlatformCode;
    @ApiModelProperty(value = "服务商商品编码")
    private String wareIspCode;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "商品图片")
    private String  mainPic;
    @ApiModelProperty(value = "商品规格")
    private String specSku;
    @ApiModelProperty(value = "条形码")
    private String barCode;
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;
}