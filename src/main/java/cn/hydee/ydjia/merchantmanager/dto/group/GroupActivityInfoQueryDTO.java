package cn.hydee.ydjia.merchantmanager.dto.group;

import cn.hydee.starter.dto.PageBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class GroupActivityInfoQueryDTO extends PageBase implements Serializable {


    private static final long serialVersionUID = 889908653143554091L;
    @ApiModelProperty(value = "活动ID")
    private Long id;

    @ApiModelProperty(value = "商家编码", required = true)
    private String merCode; // 商家编码

    @ApiModelProperty(value = "活动名称")
    private String name; // 活动名称

    @ApiModelProperty(value = "活动状态(0未开始，1进行中，2已结束)")
    private Integer schedule; // 活动状态

    @ApiModelProperty(value = "参与活动指定的门店ID")
    private String storeId; // 参与活动指定的门店ID

    @ApiModelProperty(value = "有效期-开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime; // 有效期-开始时间

    @ApiModelProperty(value = "有效期-结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;


}
