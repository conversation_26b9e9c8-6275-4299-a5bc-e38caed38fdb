package cn.hydee.ydjia.merchantmanager.dto.resp.marketreport;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: HuangYiBo
 * @time: 2021/11/24 09:04
 */

@Data
public class NumRespDTO {
    @ApiModelProperty(value = "总会员数")
    private Integer memberNumTotal = 0;
    @ApiModelProperty(value = "用券会员数")
    private Integer memberNumUse = 0;
    @ApiModelProperty(value = "未用券会员数")
    private Integer memberNumNotUse = 0;
}
