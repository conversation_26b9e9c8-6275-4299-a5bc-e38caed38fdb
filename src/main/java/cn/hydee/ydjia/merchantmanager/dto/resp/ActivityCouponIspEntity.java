package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ActivityCouponIspEntity {

    @ApiModelProperty(value = "主建ID")
    private Long id;

    @ApiModelProperty(value = "服务商编码")
    private String ispCode;

    @ApiModelProperty(value = "服务商活动id")
    private Long activityIspId;

    @ApiModelProperty(value = "服务商优惠券id")
    private Long couponIspId;

    @ApiModelProperty(value = "厂家承担金额（元）")
    private BigDecimal factoryAssume;

    @ApiModelProperty(value = "连锁承担金额（元）")
    private BigDecimal merchantAssume;

    @ApiModelProperty(value = "最多可核销张数")
    private Integer maxQty;

    @ApiModelProperty(value = "是否有效（0失效，1有效）")
    private Integer isValid;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateName;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

}