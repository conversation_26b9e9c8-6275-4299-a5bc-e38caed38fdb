package cn.hydee.ydjia.merchantmanager.dto.resp.market.payGift;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支付有礼抽奖统计数据响应DTO
 *
 * <AUTHOR>
 * @date 2023/6/15
 */
@Data
public class PayGiftDrawStatisticsResDTO {

    @ApiModelProperty(value = "抽奖活动发放次数")
    private Integer drawSendNum;

    @ApiModelProperty(value = "抽奖活动发放人数")
    private Integer drawSendUserNum;

    @ApiModelProperty(value = "抽奖活动参与次数")
    private Integer drawUsedNum;

    @ApiModelProperty(value = "抽奖活动参与人数")
    private Integer drawUsedUserNum;

}
