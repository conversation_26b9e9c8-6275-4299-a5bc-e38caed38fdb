package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.domain.DomainBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class ReturnQuestRespDTO extends DomainBase{
    @ApiModelProperty(value = "记录ID")
    private String id;
    @ApiModelProperty(value = "商家编码")
    @NotBlank
    private String merCode;
    @ApiModelProperty(value = "订单明细ID")
    private String orderDetailId;
    @ApiModelProperty(value = "物流记录id")
    private String recordId;
    @ApiModelProperty(value = "退货方式1.快递寄回 2.送回门店")
    private Integer refundType;
    @ApiModelProperty(value = "物流单号")
    private String courierNumber;
    @ApiModelProperty(value = "退款原因")
    private String refundReason;
    @ApiModelProperty(value = "退款退货说明")
    private String refundReturnDesc;
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "实际退款金额，由卖家录入")
    private BigDecimal actualRefundAmount;
    @ApiModelProperty(value = "余额退款金额")
    private BigDecimal refundBalanceAmount;
    @ApiModelProperty(value = "实际退还心币数量")
    private Integer actualRefundHb;
    @ApiModelProperty(value = "状态0待退货,1待退款，2退款完成 3驳回")
    private Integer status;
    @ApiModelProperty(value = "类型0退款1退货2退货退款3其他")
    private Integer type;
    @ApiModelProperty(value = "订单明细原状态")
    private Integer orderDetailStatus;
    @ApiModelProperty(value = "图片凭证")
    private String pictureVoucher;

    @ApiModelProperty(value = "是否退还运费 0.否 1.是")
    private Integer isReturnFreight;
    @ApiModelProperty(value = "应退邮费金额")
    private BigDecimal  freightAmount;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "是否在B端展示退款审核")
    private Integer presRefundNeedAgree;
}