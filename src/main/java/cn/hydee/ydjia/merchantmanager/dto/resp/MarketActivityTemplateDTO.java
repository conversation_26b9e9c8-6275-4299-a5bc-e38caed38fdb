package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * 专家模板实体DTO
 */
@Data
public class MarketActivityTemplateDTO {


    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("方案名")
    private String temName;

    @ApiModelProperty("节日名称")
    private String festivalName;

    @ApiModelProperty("方案类型（1：节日2：会员生命周期3：其他）")
    private Integer temType;

    @ApiModelProperty("小尺寸封面")
    private String cover;

    @ApiModelProperty("banner图")
    private String banner;

    @ApiModelProperty("活动开始时间")
    private Date beginTime;

    @ApiModelProperty("活动结束时间")
    private Date endTime;


    @ApiModelProperty("活动对象（1：全部会员2：人群）")
    private Integer activityObject;

    @ApiModelProperty("活动方式（1：自动发券）")
    private Integer activityWay;

    @ApiModelProperty("通知方式（1：短信），多个方式用#隔开")
    private Integer notificationWay;

    @ApiModelProperty("物料")
    private String materials;

    @ApiModelProperty("使用商户数")
    private Integer usedNum;

    @ApiModelProperty("置顶排序（1：不置顶 2：置顶）")
    private Integer sort;

    @ApiModelProperty("发送优惠券数量")
    private Integer couponNum;

    @ApiModelProperty(value = "方案描述")
    private String temDesc;
}
