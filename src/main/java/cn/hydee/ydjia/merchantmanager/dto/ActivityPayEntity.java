package cn.hydee.ydjia.merchantmanager.dto;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付渠道绑定优惠券&活动关系
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-22 14:19:30
 */
@Data
public class ActivityPayEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Integer id;
	/**
	 * 商户编码
	 */
	private String merCode;
	/**
	 * 活动ID
	 */
	private Integer activityId;
	/**
	 * 阶梯ID
	 */
	private Integer levelId;
	/**
     * 礼品类型（1：优惠券，2：活动，3海贝）
     */
    private Integer giftType;
    /**
     * 礼品ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer giftId;


    private Integer giftNum;
    /**
     * 是否有效（0：否，1：是）
     */
    private Integer isValid;
    /**
     * 末次修改时间
     */
    private Date updateTime;

	/**
	 * 礼包数量
	 */
	@TableField(exist = false)
	private Date total;

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
