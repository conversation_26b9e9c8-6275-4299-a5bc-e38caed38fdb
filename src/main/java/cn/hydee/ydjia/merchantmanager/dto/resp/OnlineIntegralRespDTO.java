package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/03/30 15:37
 */
@Data
public class OnlineIntegralRespDTO {
    @ApiModelProperty(value = "上一次健康豆数量")
    private Integer curTotal;
    @ApiModelProperty(value = "健康豆数量记录")
    private Integer currency;
    @ApiModelProperty(value = "会员ID")
    @NotBlank(message = "会员ID不能为空")
    private Long userId;
    @ApiModelProperty(value = "来源", hidden = true)
    private String sourceName;
    @ApiModelProperty(value = "创建时间", hidden = true)
    private String createTime;
    @ApiModelProperty(value = "备注")
    private String notes;
    @ApiModelProperty(value = "商户ID")
    private String merCode;
}
