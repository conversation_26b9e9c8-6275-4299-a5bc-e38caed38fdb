package cn.hydee.ydjia.merchantmanager.dto;

import cn.hutool.core.map.MapWrapper;
import lombok.Data;

import java.util.Date;

/**
 * @Name: BatchTaskReqDTO
 * @Description: 通用任务请求dto
 * @Author: Kaven
 * @Date: 2023/7/11 16:48
 */
@Data
public class BatchTaskReqDTO {
    /**
     * 任务ID
     */
    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 1-待处理 2-处理中 3-已完成 4-异常终止
     */
    private Integer status;

    /**
     * 任务类型：1-导入 2-导出 3-异步导出
     */
    private Integer taskType;

    /**
     * DTO类名称
     */
    private String dtoClassName;

    /**
     * 成功条数
     */
    private Integer successRecord;

    /**
     * 失败条数
     */
    private Integer failRecord;

    /**
     * 导入结果：1 成功（全部成功） 2 存在失败（部分失败） 3全部失败
     */
    private Integer result;

    /**
     * 原始文件
     */
    private String originalPath;

    /**
     * 结果文件
     */
    private String resultPath;

    /**
     * 提示信息
     */
    private String tips;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 额外信息
     */
    private MapWrapper<String, String> extra;

    /**
     * 业务Id
     */
    private String bizTypeId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 扩展查询字段1
     */
    private String extended1;
    /**
     * 扩展查询字段2
     */
    private String extended2;
    /**
     * 扩展查询字段3
     */
    private String extended3;
}
