package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Classname UserDownQrCodeReqDto
 * @Description 下载二维码参数
 * @Date 2020/9/7 14:17
 * @Created lizhaoyang-2695
 */
@Data
public class UserDownCodeReqDto {
    @ApiModelProperty(value = "用户编码")
    @NotBlank(message = "用户编码不可为空")
    private String merCode;
    @ApiModelProperty(value = "用户id")
    @NotEmpty(message = "用户编码id不可为空")
    private List<Integer> ids;

}