package cn.hydee.ydjia.merchantmanager.dto.resp.memberconsumptionreport;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: LongHua
 * @date: 2022/1/10
 */
@Data
public class MemberStoreInfoRespDTO {

    @ApiModelProperty("门店编码")
    private String businessId;
    @ApiModelProperty(value = "会员销售额")
    private BigDecimal memberSaleAmount;
    @ApiModelProperty(value = "总销售额")
    private BigDecimal totalSaleAmount;
    @ApiModelProperty(value = "毛利额")
    private BigDecimal totalProfit;
    @ApiModelProperty(value = "订单数")
    private Long totalOrders;
    @ApiModelProperty(value = "sku数量")
    private Long totalSku;


    public void assignInitialValueForNull(){
        if (memberSaleAmount == null) {
            memberSaleAmount = BigDecimal.ZERO;
        }
        if (totalSaleAmount == null) {
            totalSaleAmount = BigDecimal.ZERO;
        }
        if (totalProfit == null) {
            totalProfit = BigDecimal.ZERO;
        }
        if (totalOrders == null) {
            totalOrders = 0L;
        }
        if (totalSku == null) {
            totalSku = 0L;
        }
    }
}
