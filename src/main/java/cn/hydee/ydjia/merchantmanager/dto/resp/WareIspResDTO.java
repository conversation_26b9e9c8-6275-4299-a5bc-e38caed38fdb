package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
public class WareIspResDTO implements Serializable {

    private static final long serialVersionUID = -7177327017366738654L;

    @ApiModelProperty(value = "主建id")
    private Long id;
    @ApiModelProperty(value = "服务商编码")
    private String ispCode;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "服务商商品编码")
    private String wareIspCode;
    @ApiModelProperty(value = "标库商品编码")
    private String warePlatformCode;
    @ApiModelProperty(value = "商品来源")
    private Integer wareSource;
    @ApiModelProperty(value = "商品图片")
    private String  mainPic;
    @ApiModelProperty(value = "商品规格")
    private String specSku;
    @ApiModelProperty(value = "条形码")
    private String barCode;
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    @ApiModelProperty(value = "结算方式，1-固定成本")
    private Integer billType;
    @ApiModelProperty(value = "供应商销售价，商户进价（用于结算）")
    private String billValue;

    private String platformCode;

    private String erpCode;

    private Integer origin;

    @ApiModelProperty(value = "药品类型(0：甲类OTC，1:处方药，2：乙类OTC，3：非处方药)")
    private Integer drugType;
    private Long specId;
    @ApiModelProperty(value = "商品规格图片地址，保存相对地址，可切换域名使用")
    private String picUrl;

    @ApiModelProperty(value = "通用名")
    private String commonName;

    private String lastLevelId;

    private String lastLevelName;

    private List<String> erpCodes;

    private List<WareInfo> multiCommodityList;

    @Data
    @ToString
    public static class WareInfo extends CommoditySpec {
        @ApiModelProperty(value = "商品规格")
        private String specSku;
    }

}