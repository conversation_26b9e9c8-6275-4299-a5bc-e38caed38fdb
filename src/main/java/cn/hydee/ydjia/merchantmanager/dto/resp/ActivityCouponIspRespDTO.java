
package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class ActivityCouponIspRespDTO{

    @ApiModelProperty(value = "优惠券名称")
    private String cName;

    @ApiModelProperty(value = "券面金额")
    private BigDecimal cUseMoney;

    @ApiModelProperty(value = "服务商活动id")
    private Long activityIspId;

    @ApiModelProperty(value = "服务商优惠券id")
    private Long couponIspId;

    @ApiModelProperty(value = "厂家承担金额（元）")
    private BigDecimal factoryAssume;

    @ApiModelProperty(value = "连锁承担金额（元）")
    private BigDecimal merchantAssume;

    @ApiModelProperty(value = "最多可核销张数")
    private Integer maxQty;

}