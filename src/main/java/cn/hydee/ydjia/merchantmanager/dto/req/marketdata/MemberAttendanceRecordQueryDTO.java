package cn.hydee.ydjia.merchantmanager.dto.req.marketdata;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MemberAttendanceRecordQueryDTO extends PageBase {

    @ApiModelProperty(value = "结束时间", notes = "")
    private String beginDate;

    @ApiModelProperty(value = "开始时间", notes = "")
    private String endDate;

    @ApiModelProperty(value = "用户id", notes = "")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "商户编码", notes = "")
    @NotNull(message = "merCode不能为空")
    private String merCode;
}
