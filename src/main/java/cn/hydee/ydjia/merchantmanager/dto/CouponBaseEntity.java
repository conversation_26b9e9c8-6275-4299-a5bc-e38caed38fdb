package cn.hydee.ydjia.merchantmanager.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券规则基础表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-14 15:42:51
 */
@Data
public class CouponBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 券名称
     */
    private String cName;
    /**
     * 类型（1折扣2抵价3礼品）
     */
    private Integer cType;
    /**
     * logo
     */
    private String logo;
    /**
     * 配合type使用,面额(eg.满减用20表示,8折用8)
     */
    private BigDecimal denomination;
    /**
     * 面额小数位数0,1,2
     */
    private Integer denominationScale;
    /**
     * 面额小数取整方式:4:四舍五入1:向上取整2:向下取整
     */
    private Integer denominationRounding;
    /**
     * 礼品ID
     */
    private String giftId;
    private String giftName;
    /**
     * 最大优惠金额，设置0无上限
     */
    private BigDecimal maxPrice;
    /**
     * 券状态（0失效1生效）
     */
    private Integer state;
    /**
     * 使用时间（1领取起n天有效2领取起n天后生效,生效后m天失效3开始时间-结束时间）
     */
    private Integer timeRule;
    /**
     * 配合time_rule使用,值用逗号分隔
     */
    private String effectTime;
    /**
     * 使用场景（1线上线下2微商城3线下门店）
     */
    private Integer sceneRule;
    /**
     * 退货规则（1退货退回2退货失效）
     */
    private Integer returnRule;
    /**
     * 客服电话
     */
    private String kefuPhone;
    /**
     * 使用须知
     */
    private String note;
    /**
     * 适用门店（1全部门店2指定门店）
     */
    private Integer shopRule;
    /**
     * 适用商品（1全部可用2指定可用3指定不可用）
     */
    private Integer productRule;
    /**
     * 使用门槛0代表无门槛，数字代表订单满n元
     */
    private BigDecimal useRule;
    /**
     * 使用门槛类型,1:固定门槛,2:面值倍数
     */
    private Integer useRuleType;
    /**
     * 到期提醒默认（0代表不提醒,正整数代表n天微信提醒）
     */
    private Integer expireInfo;
    /**
     * 是否有效（0：否，1：是）
     */
    private Integer isValid;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    private String updateName;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 商户号
     */
    private String merCode;


    private Integer onlineCount;

    private Integer offlineCount;

    private Integer totalCount;

    @TableField(exist = false)
    private Integer payGiftNum;

    @TableField(exist = false)
    private Integer giftNum;

    @TableField(exist = false)
    private Integer proRule;

    @TableField(exist = false)
    private Integer typeRule;

    /**
     * 特价商品是否参与(0:不参与,1:参与)
     */
    private Integer specialsJoin;
    /**
     * 打折商品是否参与(0:不参与,1:参与)
     */
    private Integer salesJoin;
    /**
     * 赠品是否参与(0:不参与,1:参与)
     */
    private Integer giftJoin;
    /**
     * 会员价商品是否参与
     */
    private Integer memberPriceJoin;
    /**
     * 种类是否可叠加(0:不可叠加,1:可叠加)
     */
    private Integer typeAddition;
    /**
     * 叠加张数
     */
    private Integer typeAdditionNum;
    /**
     * 是否支持多次核销(0:不支持,1:支持)
     */
    private Integer multipleUsed;
    private Integer createChannel;
    /**
     * 组合商品规则 0:不参与 1: 全部参与 2:部分参与
     */
    private Integer combineProductRule;
    /**
     * 商户名称 C端优惠券显示
     */
    private String merchantName;
    /**
     * 发放类型： 1：只限领券的会员使用  2：任何人都可以使用
     */
    private Integer provideType;
    /**
     * 是否指定商品效期: 0-不指定 1:按失效日期指定 2-按失效天数指定,默认值为0
     */
    private Integer productExpireRule;
    /**
     * 结合productExpireRule使用, productExpireRule=1表示 失效日期不晚于当前值 productExpireRule=2表示失效天数小于等于当前值
     */
    private String productExpireValue;

    /**
     * 备注
     */
    private String remark;

    /**
     * 员工使用规则 0:员工不可用 1:员工可用, 默认为1
     */
    private Integer empRule;
    /**
     * 适用商品来源（1.连锁商品，2.云仓商品）
     */
    private Integer productFrom;
    /**
     * 优惠券来源（1商户自建，2.运营平台(专家方案) 3.服务商平台）
     */
    private Integer createFrom;
    /**
     * 关联ID
     */
    private String relationId;

    /**
     * 服务商编码
     */
    private String ispCode;
    /**
     * 服务商活动ID
     */
    private Long ispActivityId;
    /**
     * 礼品券限制兑换商品个数
     */
    private Integer giftLimitNum;

    public String[] splitEffectTime() {
        if (StringUtils.isNotBlank(effectTime)) {
            return effectTime.split(",");
        }
        return null;
    }
}
