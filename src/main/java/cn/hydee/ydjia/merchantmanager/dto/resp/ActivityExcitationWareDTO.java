package cn.hydee.ydjia.merchantmanager.dto.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ActivityExcitationWareDTO {


    @ApiModelProperty(value = "商品名称", required = true)
    private String name;

    @ApiModelProperty(value = "商品图片链接", required = true)
    private String mainPic;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "规格信息", required = true)
    private String specSku;

    @ApiModelProperty(value = "商品条形码", required = true)
    private String barCode;
}