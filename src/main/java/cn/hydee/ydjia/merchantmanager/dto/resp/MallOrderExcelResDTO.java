package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.batch.annotation.EnableAsyncBatchExportTask;
import cn.hydee.ydjia.merchantmanager.excel.BigDecimalConverter;
import cn.hydee.ydjia.merchantmanager.process.MallOrderExportProcessor;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 微商城订单导出实体
 *
 * @title: MallOrderExcelResDTO
 * @package cn.hydee.ydjia.merchantmanager.dto.resp
 * @author: canliang.nie
 * @date: 2022/11/25 11:04
 * @version: V1.0
 */
@Data
@EnableAsyncBatchExportTask(businessType = LocalConst.MALL_ORDER_EXPORT, taskName = "微商城订单数据导出",fileName = "微商城订单导出数据",
        sheetNames = {"微商城订单数据"}, batchSize = LocalConst.DEFAULT_BATCH_SIZE, processor = MallOrderExportProcessor.class)
@ColumnWidth(value = 25)
public class MallOrderExcelResDTO {

    @ExcelProperty(value = "订单号")
    private String orderId;

    @ExcelProperty(value = "订单状态")
    private String orderStatus;

    @ExcelProperty(value = "支付方式")
    private String payMode;

    @ExcelProperty(value = "支付状态")
    private String payStatus;

    @ExcelProperty(value = "实际运费金额", converter = BigDecimalConverter.class)
    private BigDecimal actualFreightAmount;

    @ExcelProperty(value = "订单商品总额", converter = BigDecimalConverter.class)
    private BigDecimal totalOrderAmount;

    @ExcelProperty(value = "实际支付总金额", converter = BigDecimalConverter.class)
    private BigDecimal actuallyPaid;

    @ExcelProperty(value = "医保支付", converter = BigDecimalConverter.class)
    private BigDecimal payMedicalAmount;

    @ExcelProperty(value = "优惠券ID")
    private String couponId;

    @ExcelProperty(value = "优惠券码")
    private String couponCode;

    @ExcelProperty(value = "优惠券优惠金额", converter = BigDecimalConverter.class)
    private BigDecimal couponDeduction;

    @ExcelProperty(value = "活动优惠金额", converter = BigDecimalConverter.class)
    private BigDecimal activityDiscountAmount;

    @ExcelProperty(value = "心币抵扣（单位：个）", converter = BigDecimalConverter.class)
    private BigDecimal totalActualHb;

    @ExcelProperty(value = "心币抵邮费（单位：个）", converter = BigDecimalConverter.class)
    private BigDecimal freightDeductionOfHb;

    @ExcelProperty(value = "心币支付金额", converter = BigDecimalConverter.class)
    private BigDecimal payOrderOfHb;

    @ExcelProperty(value = "是否需要发票标识")
    private String isInvoice;

    @ExcelProperty(value = "下单时间")
    private String orderTime;

    @ExcelProperty(value = "收货人")
    private String receiver;

    @ExcelProperty(value = "收货人手机")
    private String receiverMobile;

    @ExcelProperty(value = "完整详细地址")
    private String fullDetaiAddress;

    @ExcelProperty(value = "商品数量")
    private String totalGoodsNumber;

    @ExcelProperty(value = "开方单号")
    private String cfNo;

    @ExcelProperty(value = "开方时间")
    private String prescribeTime;

    @ExcelProperty(value = "开方来源")
    private String prescribeSource;

    @ExcelProperty(value = "开方状态")
    private String cfDesc;

    @ExcelProperty(value = "处方单号")
    private String presNo;

    @ExcelProperty(value = "审核状态")
    private String presDesc;

//    @ExcelProperty(value = "用药人姓名")
//    private String drugUserName;
//
//    @ExcelProperty(value = "用药人身份证号码")
//    private String cerNo;

    @ExcelProperty(value = "会员账号")
    private String memberPhone;

    @ExcelProperty(value = "会员卡号")
    private String memberCard;

    @ExcelProperty(value = "会员推荐编码")
    private String regMedium;

    @ExcelProperty(value = "会员来源")
    private String regSource;

    @ExcelProperty(value = "门店编码")
    private String stCode;

    @ExcelProperty(value = "门店名称")
    private String storeName;

    @ExcelProperty(value = "服务商编码")
    private String spCode;

    @ExcelProperty(value = "服务商名称")
    private String spName;

    @ExcelProperty(value = "推广门店")
    private String spreadStoreName;

    @ExcelProperty(value = "分享人id")
    private String sourceMedia;

    @ExcelProperty(value = "分享人名称")
    private String shareName;

    @ExcelProperty(value = "是否员工")
    private String isEmployee;

    @ExcelProperty(value = "姓名")
    private String userName;

    @ExcelProperty(value = "身份证")
    private String idCard;

    @ExcelProperty(value = "手机号")
    private String userPhone;

    @ExcelProperty(value = "体温")
    private String temperature;

    @ExcelProperty(value = "住址")
    private String address;

    @ExcelProperty(value = "近7天内是否驻留高、中风险地区所在市")
    private String highRiskArea;

    @ExcelProperty(value = "居民健康码")
    private String userHealthCodeImage;
    
    @ExcelProperty(value = "是否预售")
    private String presaleOrderType;

}
