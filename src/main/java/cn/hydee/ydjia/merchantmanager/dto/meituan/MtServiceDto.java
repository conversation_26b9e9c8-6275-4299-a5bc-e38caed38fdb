package cn.hydee.ydjia.merchantmanager.dto.meituan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Package : cn.hydee.ydjia.merchantmanager.dto.im
 * @Description :美团IM对象
 * @Create on : 2020/11/12 09:07
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class MtServiceDto extends BaseMtReq {
    @ApiModelProperty(value = "IM状态 0-关闭 1-开启")
    private Integer imStatus;

    @ApiModelProperty(value = "IM在线状态，0-在线或1-忙碌 ")
    private Integer onlineStatus;

    @ApiModelProperty(value = "消息id")
    private Long msgId;

    @ApiModelProperty(value = "用户id")
    private Long openUserId;

    @ApiModelProperty(value = "页码，需为大于等于1的整数，从1开始。")
    private Integer pageNum;

    @ApiModelProperty(value = "每页中的数据条数。字段范围：需为大于等于1的整数，最大值为200")
    private Integer pageSize;

    @ApiModelProperty(value = "网店名称")
    private String onlineClientName;

    @ApiModelProperty(value = "网店编码")
    private String onlineClientCode;

    @ApiModelProperty(value = "授权状态 0-未授权，1-已授权")
    private Integer authStatus;

    @ApiModelProperty(value = "门店code")
    private String appPoiCode;

}
