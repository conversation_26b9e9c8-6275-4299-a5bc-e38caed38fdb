package cn.hydee.ydjia.merchantmanager.dto.resp.isp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
@Data
@Accessors(chain = true)
public class EmpSaleRankRespDTO {
    @ApiModelProperty("店员名称")
    private String name;

    @ApiModelProperty("店员编码")
    private String empCode;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("门店编码")
    private String storeCode;

    @ApiModelProperty("连锁编码")
    private String merCode;

    @ApiModelProperty("连锁名称")
    private String merName;

    @ApiModelProperty("销售数量")
    private Integer saleNum;

    @ApiModelProperty("销售金额")
    private BigDecimal saleAmount = BigDecimal.ZERO;

    @ApiModelProperty("已获得奖励")
    private BigDecimal rewardAmount = BigDecimal.ZERO;

    @ApiModelProperty("已获得积分奖励")
    private BigDecimal rewardBooking = BigDecimal.ZERO;
}
