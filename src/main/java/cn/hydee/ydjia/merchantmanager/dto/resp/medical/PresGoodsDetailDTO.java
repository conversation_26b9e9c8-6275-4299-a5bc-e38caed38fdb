package cn.hydee.ydjia.merchantmanager.dto.resp.medical;

import lombok.Data;

/**
 * 处方商品。
 *
 * <AUTHOR>
 * @version V1.0 2022/06/21
 */
@Data
public class PresGoodsDetailDTO {

    /**
     * 商品编码。
     */
    private String wareCode;

    /**
     * 商品名称。
     */
    private String wareName;

    /**
     * 商品通用名。
     */
    private String genericName;

    /**
     * 生产厂家。
     */
    private String factory;

    /**
     * 批准文号。
     */
    private String approvalNo;

    /**
     * 药品规格。
     */
    private String specs;

    /**
     * 药品单位。
     */
    private String unit;

    /**
     * 药品数量。
     */
    private String quantity;

    /**
     * 药品剂型。
     */
    private String dosageForm;

    /**
     * 药品用法。
     */
    private String usageForm;

    /**
     * 药品用量。
     */
    private String dosage;

    /**
     * 用药频次。
     */
    private String frequency;

    /**
     * 使用天数。
     */
    private String days;

    /**
     * 有无过敏史：0、无|1、有。
     */
    private Integer isAllergy;

    /**
     * 是否服用过本药：0、否|1、是。
     */
    private Integer isUsed;

}
