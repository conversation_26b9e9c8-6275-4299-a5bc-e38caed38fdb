package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * @version 1.0
 * @Author: pengyayun
 * @Description: 检测结果
 * @Date: 2020/3/18
 */
@Data
public class MemberMedicalRecordResDTO {

    @ApiModelProperty(value = "检测员工编码")
    private String empCode;

    @ApiModelProperty(value = "检测员姓名")
    private String surveyorName;

    @ApiModelProperty(value = "检测机构编码")
    private String stCode;

    @ApiModelProperty(value = "检测机构名称")
    private String customerName;

    @ApiModelProperty(value = "检测时间")
    private String testTime;

    @ApiModelProperty(value = "样本编号")
    private String sampleNo;

    @ApiModelProperty(value = "病例号")
    private String medRecordNo;

    @ApiModelProperty(value = "体重")
    private String weight;

    @ApiModelProperty(value = "身高")
    private String height;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "检测结果")
    private List<MedicalResult> medicalResult;

    @Data
    public static class MedicalResult{

        @ApiModelProperty(value = "指标名称")
        private String name;

        @ApiModelProperty(value = "指标中文名称")
        private String targetName;

        @ApiModelProperty(value = "结果")
        private String result;

        @ApiModelProperty(value = "输入类型(0:未知 1:仪器录入 2:手动录入)")
        private Integer dataType;

        @ApiModelProperty(value = "设备sn号")
        private String deviceSN;

        @ApiModelProperty(value = "区间")
        private String medicalReference;

        @ApiModelProperty(value = "异常状态(0:未知，1:超低，2:偏低，3:正常，4:偏高，5:中高，6:重高，7:超高，10:严重偏低)")
        private Integer medicalStatus;

        @ApiModelProperty(value = "单位")
        private String medicalUnits;
    }
}
