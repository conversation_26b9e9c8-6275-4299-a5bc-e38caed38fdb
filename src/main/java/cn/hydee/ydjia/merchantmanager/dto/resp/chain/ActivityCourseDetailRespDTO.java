package cn.hydee.ydjia.merchantmanager.dto.resp.chain;


import cn.hydee.ydjia.merchantmanager.dto.resp.WareIspResDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/23 10:23
 */

@Data
public class ActivityCourseDetailRespDTO {


    @ApiModelProperty(value = "活动id", notes = "")
    private Integer activityId;

    @ApiModelProperty(value = "发布时间")
    private Date releaseTime;

    /**
     * 服务商
     */
    @ApiModelProperty(value = "服务商Logo")
    private String ispLogo;

    /**
     * 服务商名称
     */
    @ApiModelProperty(value = "服务商名称")
    private String ispName;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "是否参与 1.是，2.否")
    private Integer isEnable;


    @ApiModelProperty(value = "主体详情")
    private IspCourseDetailRespDTO ispCourseDetailRespDTO;


}
