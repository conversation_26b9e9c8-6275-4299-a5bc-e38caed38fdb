package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/1/4 14:55
 */
@Data
public class CountPreRespDTO {

    @ApiModelProperty(value = "待审批数量")
    private Integer checkNum;
    @ApiModelProperty(value = "拒绝数量")
    private Integer rejectNum;
    @ApiModelProperty(value = "待开方数量")
    private Integer waitPrescribeNum;
}
