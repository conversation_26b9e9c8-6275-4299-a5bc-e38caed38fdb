package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/12
 */
@Data
@Accessors(chain = true)
public class ExclusiveStoreAdviceRespDto {

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "门店编码")
    private String storeCode;

    @ApiModelProperty(value = "优秀门店")
    private Boolean excellentStore;

    @ApiModelProperty(value = "重点关注门店")
    private Boolean focusStore;

    @ApiModelProperty(value = "门店类型，优秀门店、重点关注门店")
    private String type;

    @ApiModelProperty(value = "巡店理由")
    private List<String> patrolStoreReasons;

    @ApiModelProperty(value = "动销店员率")
    private BigDecimal dynamicSalesAssistantQtyRate = BigDecimal.ZERO;

    @ApiModelProperty(value = "门店销量")
    private Integer storeSaleNum = 0;


}
