package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.starter.dto.PageBase;
import cn.hydee.ydjia.merchantmanager.dto.payment.PayCommonConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-03-26
 */
@Data
public class ThirdPayConfigDTO extends PageBase implements Serializable {

    private static final long serialVersionUID = -1726433786366546687L;

    @ApiModelProperty(value = "商家编码")
    @NotBlank(message = "商家编码不能为空")
    private String merCode;
    @ApiModelProperty(value = "支付类型：0-微信，1-支付宝")
    private Integer thirdPayType;
    @ApiModelProperty(value = "门店支付设置")
    private List<PayCommonConfigDTO> list;
    @ApiModelProperty(hidden = true)
    private String userName;
    @ApiModelProperty(value = "门店ID")
    private String storeId;
    @ApiModelProperty(value = "0-关闭，1-开通")
    private Integer status;
    @ApiModelProperty(hidden = true)
    private Long total;
    @ApiModelProperty(hidden = true)
    private Long pages;
    @ApiModelProperty(value = "门店ID集合", hidden = true)
    private List<String> storeIds;
    @ApiModelProperty(hidden = true)
    private Boolean appIdFlag;
    @ApiModelProperty(hidden = true)
    private String appId;
    @ApiModelProperty(value = "主键ID集合")
    private List<String> ids;
    @ApiModelProperty(value = "批量更新支付设置时的支付设置参数：{outMerCode、payKey、merchantCertificate、payAutoConfig、status}")
    private PayCommonConfigDTO payCommonConfigDTO;

    @ApiModelProperty(value = "公司id，组织id")
    private String orgId;
    @ApiModelProperty(value = "关键字:门店名称/门店编码")
    private String storeProperty;
    @ApiModelProperty(hidden = true)
    private Boolean pageFlag;

    @ApiModelProperty(value = "页面来源：0-门店独立收款，1-线上医保支付门店列表", hidden = true)
    private Integer origin;
}
