package cn.hydee.ydjia.merchantmanager.dto.resp.industry.export;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/16 22:35
 */
@Data
public class ImRewardDetailExportDTO {

    @ApiModelProperty(name = "")
    @Excel(name = "奖励生成时间", orderNum = "0", width = 20)
    private String rewardCreateTime;
    @Excel(name = "ERP订单号", orderNum = "10", width = 20)
    private String orderId;
    @Excel(name = "订单时间", orderNum = "20", width = 20)
    private String orderTime;
    @Excel(name = "商品编码", orderNum = "30", width = 20)
    private String commodityCode;
    @Excel(name = "商品规格值", orderNum = "31", width = 20)
    private String commoditySpec;
    @Excel(name = "商品名称", orderNum = "40", width = 20)
    private String commodityName;
    @Excel(name = "商品单价", orderNum = "50", width = 20)
    private BigDecimal commodityPrice;
    @Excel(name = "商品数量", orderNum = "60", width = 20, type = 10)
    private Integer commodityNum;
    @Excel(name = "奖励金额", orderNum = "70", width = 20, type = 10)
    private BigDecimal rewardAmount;
    @Excel(name = "奖励员工编码", orderNum = "80", width = 20)
    private String rewardEmpCode;
    @Excel(name = "奖励员工名称", orderNum = "90", width = 20)
    private String rewardEmpName;
    /**
     * 奖励类型名(10:单品动销连锁奖励11:优惠券连锁奖励12:目标达成连锁奖励20:单品动销员工奖励21:员工销售排名奖励)
     */
    @Excel(name = "奖励类型", orderNum = "100", width = 20)
    private String rewardTypeName;
    @Excel(name = "奖励状态", orderNum = "110", width = 20)
    private String rewardState;
    @Excel(name = "奖励发放备注", orderNum = "120", width = 20)
    private String remark;
    @Excel(name = "活动名称", orderNum = "130", width = 20)
    private String activityName;
    @Excel(name = "活动厂家", orderNum = "140", width = 20)
    private String ispName;
    /**
     * (1:erp订单 2:微商城连锁订单 3:云仓订单)
     */
    @Excel(name = "订单类型", orderNum = "150", width = 20)
    private String orderType;
    @Excel(name = "门店编码", orderNum = "160", width = 20)
    private String storeCode;
    @Excel(name = "门店名称", orderNum = "170", width = 20)
    private String storeName;
    @Excel(name = "营业员编码", orderNum = "180", width = 20)
    private String saleEmpCode;
    @Excel(name = "营业员名称", orderNum = "190", width = 20)
    private String saleEmpName;
    @Excel(name = "分享人编码", orderNum = "200", width = 20)
    private String sharerCode;
    @Excel(name = "分享人名称", orderNum = "210", width = 20)
    private String sharerName;
    @Excel(name = "微商城单号", orderNum = "220", width = 20)
    private String onlineOrderId;
    @Excel(name = "关联原单(仅退货)", orderNum = "230", width = 20)
    private String originalOrderId;
}
