package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/18
 */
@Data
public class PublicAccountInfoRespDTO {
    @ApiModelProperty("账户名称")
    private String accountName;
    @ApiModelProperty("开户银行")
    private String bankName;
    @ApiModelProperty("卡号")
    private String bankNumber;
    @ApiModelProperty("开户支行")
    private String depositBankName;
}
