package cn.hydee.ydjia.merchantmanager.dto;


import cn.hydee.ydjia.merchantmanager.excel.Excel;
import cn.hydee.ydjia.merchantmanager.excel.ExcelKeyValue;
import cn.hydee.ydjia.merchantmanager.excel.KeyValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 中西医药导入模板对象
 * <AUTHOR>
 * @date 2019/10/10 16:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MedicineDTO extends ExcelObject{

    @Excel(name="商品编码",orderNum = 0)
    private String erpCode;

    @Excel(name="商品名称",orderNum = 1)
    private String name;

    @Excel(name="通用名",orderNum = 2)
    private String commonName;

    @Excel(name="品牌",orderNum = 3)
    private String brandName;

    @Excel(name="批准文号",orderNum = 4)
    private String approvalNumber;

    @Excel(name="生产企业",orderNum = 5)
    private String manufacture;

    @Excel(name="剂型",orderNum = 6)
    private String dosageForm;

    @Excel(name="药品类型：OTC甲类(0)/处方(1)/OTC乙类(2)/OTC(4)",orderNum = 7)
    private String drugType;

    @Excel(name="参考价",orderNum = 8)
    private String mPrice;

    @Excel(name="单位",orderNum = 9)
    private String unit;

    @Excel(name="重量（g）",orderNum = 10)
    private String weight;

    @Excel(name="货主（0自营 1平安 默认0）",orderNum = 11)
    private String owner;

    @Excel(name="条形码",orderNum = 12)
    private String barCode;

    @Excel(name="是否含麻黄碱（0否 1是）",orderNum = 13)
    private String hasEphedrine;

    @Excel(name="产地",orderNum = 14)
    private String produceOrigin;

    @Excel(name = "长（M）", orderNum = 15)
    private String length;

    @Excel(name = "宽（M）", orderNum = 16)
    private String width;

    @Excel(name = "高（M）", orderNum = 17)
    private String height;

    @Excel(name = "用法用量", orderNum = 18)
    private String usageAndDosage;

    @Excel(name = "主要成分", orderNum = 19)
    private String mainComponents;

    @Excel(name = "功能主治/功能", orderNum = 20)
    private String keyFeature;

    @Excel(name = "有效期/保质期(天)", orderNum = 21)
    private String expireDays;

    @Excel(name = "运输方式： 常温(0)/冷藏(1)/冰冻(2)/阴凉(3)", orderNum = 22)
    private String freightType;

    @Excel(name = "是否易碎（0否 1是）", orderNum = 23)
    private String isEasyBreak;

    @Excel(name = "是否液体（0否 1是）", orderNum = 24)
    private String isLiquid;

    @Excel(name = "完整说明书", orderNum = 25)
    private String instructions;

    @Excel(name = "是否需身份证（0否 1是）", orderNum = 26)
    private String needId;

//    @Excel(name="关键字(以、隔开)",orderNum = 25)
//    private String keyWord;

    @ExcelKeyValue(start = 27)
    private List<KeyValue> keyValues;

    private String firstTypeId;
    private String firstTypeName;

    @Override
    public boolean isAllNull() {
        boolean valueNull = keyValues == null;
        if (!valueNull) {
            valueNull = true;
            for (KeyValue keyValue : keyValues) {
                valueNull = valueNull && StringUtils.isEmpty(keyValue.getSkuValue());
            }
        }
        return valueNull && StringUtils.isEmpty(erpCode) && StringUtils.isEmpty(name)
                && StringUtils.isEmpty(brandName) && StringUtils.isEmpty(manufacture)
                && StringUtils.isEmpty(approvalNumber) && StringUtils.isEmpty(barCode)
                && StringUtils.isEmpty(commonName) && StringUtils.isEmpty(dosageForm)
                && mPrice == null && weight == null && expireDays == null && freightType == null
                && isEasyBreak == null && isLiquid == null
                && hasEphedrine == null && needId == null
                && drugType == null
                && StringUtils.isEmpty(unit) && StringUtils.isEmpty(produceOrigin)
                && StringUtils.isEmpty(length) && StringUtils.isEmpty(width)
                && StringUtils.isEmpty(height) && StringUtils.isEmpty(keyFeature)
                && StringUtils.isEmpty(instructions);
    }

    public void trim() {
        if (erpCode != null) {
            erpCode = erpCode.trim().replace(" ","");
        }
        if (manufacture != null) {
            manufacture = manufacture.trim().replace(" ","");
        }
        if (name != null) {
            name = name.trim();
        }
        if (brandName != null) {
            brandName = brandName.trim().replace(" ","");
        }
        if (approvalNumber != null) {
            approvalNumber = approvalNumber.trim().replace(" ","");
        }
        if (barCode != null) {
            barCode = barCode.trim().replace(" ","");
        }
        if (unit != null) {
            unit = unit.trim().replace(" ","");
        }
        if (produceOrigin != null) {
            produceOrigin = produceOrigin.trim().replace(" ","");
        }
        if (length != null) {
            length = length.trim().replace(" ","");
        }
        if (width != null) {
            width = width.trim().replace(" ","");
        }
        if (height != null) {
            height = height.trim().replace(" ","");
        }
        if (keyFeature != null) {
            keyFeature = keyFeature.trim().replace(" ","");
        }
        if (commonName != null) {
            commonName = commonName.trim();
        }
        if (dosageForm != null) {
            dosageForm = dosageForm.trim().replace(" ","");
        }
        if (!CollectionUtils.isEmpty(keyValues)) {
            for (KeyValue keyValue : keyValues) {
                if (!StringUtils.isEmpty(keyValue.getSkuValue())) {
                    keyValue.setSkuValue(keyValue.getSkuValue().trim().replace(" ",""));
                }
            }
        }
    }
}
