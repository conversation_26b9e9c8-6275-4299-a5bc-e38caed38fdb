package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/9 16:20
 */
@Data
public class CommodityImgDTO {

    @NotNull(message = "商户ID不可为空")
    @ApiModelProperty(value = "商品ID")
    private String commodityId;
    @NotNull(message = "商户编码不可为空")
    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "图片信息")
    @NotEmpty(message = "商品图片不可为空")
    private List<Data> imgs;

    @lombok.Data
    public static class Data {
        private String picUrl;
        private Integer sort;
        private Integer type;
    }
}
