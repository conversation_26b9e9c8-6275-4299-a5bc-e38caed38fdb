package cn.hydee.ydjia.merchantmanager.dto.sms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/1/14 10:40
 */
@Data
public class QueryExpectSmsRequest {
    @ApiModelProperty(name = "发送短信会员数")
    @NotNull(message = "发送短信人数必须大于零")
    private Integer sendMemberNum;
    @ApiModelProperty(name = "短信内容")
    @NotBlank(message = "短信内容不能为空")
    private String smsContent;
}
