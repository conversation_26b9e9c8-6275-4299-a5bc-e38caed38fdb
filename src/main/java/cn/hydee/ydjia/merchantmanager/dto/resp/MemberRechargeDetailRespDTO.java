package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Author: lisp
 * @Description: 会员充值
 * @Date: 2020/8/12
 */
@Data
public class MemberRechargeDetailRespDTO {


    @ApiModelProperty(value = "充值id")
    private Long id;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "商户编码")
    private Long userId;

    @ApiModelProperty(value = "会员卡号")
    private String memberCard;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "商户编码")
    private String phone;

    /**
     * 储值卡id
     */
    @ApiModelProperty(value = "储值卡id")
    private Long cardId;

    /**
     * 储值卡类型
     */
    @ApiModelProperty(value = "储值卡类型")
    private String cardType;

    @ApiModelProperty(value = "储值卡名称")
    private String cardName;

    /**
     * 充值本金
     */
    @ApiModelProperty(value = "充值本金")
    private BigDecimal capital;

    /**
     * 赠送金额
     */
    @ApiModelProperty(value = "赠送金额")
    private BigDecimal handselAmount;

    /**
     * 赠送心币
     */
    @ApiModelProperty(value = "赠送心币")
    private Integer handselIntegral;

    /**
     * 充值时间
     */
    @ApiModelProperty(value = "充值时间")
    private Date createTime;

    /**
     * 储值卡CODE
     */
    @ApiModelProperty(value = "储值卡CODE")
    private Integer cardCode;

    @ApiModelProperty(value = "支付流水号")
    private String paymentNo;


    @ApiModelProperty(value = "赠送优惠券张数")
    private Integer handselCoupon;

    @ApiModelProperty(value = "领取的优惠券列表")
    private List<Integer> couponIds;

    @ApiModelProperty(value = "充值类型；1：自动充值；2：批量充值")
    private Integer rechargeType;

}
