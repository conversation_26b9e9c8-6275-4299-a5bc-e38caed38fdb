package cn.hydee.ydjia.merchantmanager.dto.req;

import cn.hydee.ydjia.merchantmanager.domain.CommoditySpecSku;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Name: PmtRulePresaleDTO
 * @Description: 预售规则请求DTO
 * @Author: Kaven
 * @Date: 2022/12/30 09:22
 */
@Data
public class PmtRulePresaleDTO  {

    private static final long serialVersionUID = 1L;

    /**
     * 规则id
     */
    @ApiModelProperty(value = "规则id")
    private Long id;

    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码")
    private String merCode;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 商品规格ID
     */
    @NotBlank(message = "商品规格id不能为空")
    @ApiModelProperty(value = "商品规格ID")
    private String specId;

    /**
     * 是否限制库存(1-是 0-否)
     */
    @NotNull(message = "是否限制库存不能为空")
    @ApiModelProperty(value = "是否限制库存(1-是 0-否)")
    private Integer limitStockFlag;

    /**
     * 活动限制库存数
     */
    @ApiModelProperty(value = "活动限制库存数")
    private Integer limitStockQty;

    /**
     * 活动价格
     */
    @NotNull(message = "活动价格不能为空")
    @ApiModelProperty(value = "活动价格")
    private BigDecimal actPrice;

    /**
     * 优惠模式(1-固定比例,2-固定价格)
     */
    @ApiModelProperty(value = "优惠模式(1-固定比例,2-固定价格)")
    private Integer pmtMode;

    /**
     * 定金固定比例/固定价格
     */
    @NotNull(message = "定金固定比例/固定价格不能为空")
    @ApiModelProperty(value = "定金固定比例/固定价格")
    private BigDecimal discount;

    /**
     * 剩余库存
     */
    @ApiModelProperty(value = "剩余库存")
    private Integer leftStock;

    /**
     * 限购模式(0-不限购 1-每人限购 2-每单限购)
     */
    @NotNull(message = "限购模式不能为空")
    @ApiModelProperty(value = "限购模式:0-不限购，1-每人限购，2-每单限购")
    private Integer limitMode;

    /**
     * 限购商品数
     */
    @ApiModelProperty(value = "限购商品数")
    private Integer confineNum;

    /**
     * 活动排序
     */
    @ApiModelProperty(value = "活动排序")
    private Integer sort;

    @ApiModelProperty(value = "ERP编码")
    private String erpCode;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品厂家")
    private String manufacture;

    @ApiModelProperty(value = "商品图片链接")
    private String picUrl;

    @ApiModelProperty(value = "参考价格")
    private BigDecimal mPrice;

    @ApiModelProperty(value = "规格对应SKU")
    private List<CommoditySpecSku> specSkus;

    @ApiModelProperty(value = "商品库存")
    private Integer stock;

    /**
     * 是否有效 0-无效 1-有效
     */
    @ApiModelProperty(value = "是否有效 0-无效 1-有效")
    private Integer isValid;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifyName;

    /**
     * 末次修改时间
     */
    @ApiModelProperty(value = "末次修改时间")
    private LocalDateTime modifyTime;


}
