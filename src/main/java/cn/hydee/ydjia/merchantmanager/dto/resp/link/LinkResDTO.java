package cn.hydee.ydjia.merchantmanager.dto.resp.link;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 内部链接数据返回对象
 *
 * <AUTHOR>
 * @create 2020-12-22
 */
@Data
public class LinkResDTO implements Serializable {

    private static final long serialVersionUID = 4265996734139387047L;

    @ApiModelProperty("商家编码")
    private String merCode;

    @ApiModelProperty("H5二维码")
    private String h5CodeImg;
    @ApiModelProperty("H5链接")
    private String h5Url;
    @ApiModelProperty("小程序二维码")
    private String appletCodeImg;
    @ApiModelProperty("小程序appId")
    private String appletAppId;
    @ApiModelProperty("小程序链接")
    private String appletUrl;

}
