package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/5/18   16:37
 * @since 1.0
 */
@Data
@EqualsAndHashCode
@ToString
@Builder
public class LiveSubscrPageRspDTO {

    @ApiModelProperty("商户名称")
    private String merName;

    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty("观看人数")
    private String viewNum;

    @ApiModelProperty("购买人数")
    private Long totalBuyNum;

    @ApiModelProperty("支付量")
    private Long totalDealNum;

    @ApiModelProperty("下单量")
    private Long totalOrderNum;

    @ApiModelProperty("下单金额")
    private String totalOrderAmount;

    @ApiModelProperty("支付金额")
    private String totalPayOrderAmount;


}
