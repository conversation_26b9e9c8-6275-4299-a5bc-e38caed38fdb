package cn.hydee.ydjia.merchantmanager.dto.resp.market.payGift;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 支付有礼活动列表统计数据响应DTO
 *
 * <AUTHOR>
 * @date 2023/6/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PayGiftListStatisticsResDTO extends PayGiftStatisticsResDTO {

    @ApiModelProperty(value = "复购销售额(即优惠券带动销售额)")
    private BigDecimal repurchaseAmount;

}
