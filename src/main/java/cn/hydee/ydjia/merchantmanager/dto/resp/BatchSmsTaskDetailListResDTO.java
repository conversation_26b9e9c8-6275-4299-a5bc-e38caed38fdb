package cn.hydee.ydjia.merchantmanager.dto.resp;

import lombok.Data;

/**
 * @version 1.0
 * @Author: pengyayun
 * @Description:
 * @Date: 2022/1/12
 */
@Data
public class BatchSmsTaskDetailListResDTO {

    /**
     * 短信主题 如果业务方需要区分主题时传
     */
    private String msgTheme;
    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 失败原因
     */
    private String reportDesc;

    private Integer feeLength;

    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 业务方批次号
     */
    private String bizBatchNo;
    /**
     * 业务场景
     */
    private String bizType;

    /**
     * 短信类型
     */
    private Integer msgType;

    /**
     * 接收手机号
     */
    private String mobile;

    /**
     * 1-待发送，2-发送成功，3-发送失败
     */
    private Integer sendStatus;

    private String memberCard;

    /**
     * 会员姓名
     */
    private String memberName;

    /**
     * 小程序打开次数
     */
    private Integer openMiniAppletCount;
}
