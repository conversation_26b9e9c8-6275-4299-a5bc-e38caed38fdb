package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR> Lee
 * @Create : 2021/8/17 17:15
 * @Description :
 */

@Data
public class ListEmpReqDTO {

    @ApiModelProperty(value = "商户编号")
    private String merCode;

    @ApiModelProperty(value = "员工编码列表")
    private Set<String> empCodes;

    @ApiModelProperty(value = "员工编码列表")
    private Set<String> userIds;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "页数")
    private Integer pageSize;

}
