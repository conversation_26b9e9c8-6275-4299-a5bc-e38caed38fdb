package cn.hydee.ydjia.merchantmanager.dto.resp.memberflow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @Name: RankingListQueryResp
 * @Description: 流量排行榜查询（按门店）返回类
 * @Author: Kaven
 * @Date: 2023/3/21 14:55
 */
@Data
public class RankingListQueryStoreResp {
    @ExcelIgnore
    @ApiModelProperty("排行")
    private Integer rank;

    @ExcelProperty("门店编码")
    @ApiModelProperty("门店编码")
    private String storeCode;

    @ExcelProperty("门店名称")
    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("门店编码")
    @ExcelIgnore
    private String businessId;

    @ApiModelProperty("门店名称")
    @ExcelIgnore
    private String businessName;

    @ExcelProperty("UV")
    @ApiModelProperty("uv")
    private Long uv;

    @ExcelProperty("PV")
    @ApiModelProperty("pv")
    private Long pv;

    @ExcelProperty("销售额")
    @ApiModelProperty("销售额")
    private BigDecimal saleAmt;

    @ExcelProperty("UV价值")
    @ApiModelProperty("UV价值")
    private BigDecimal uvValue;

    public String getStoreCode() {
        return StringUtils.isBlank(storeCode) ? businessId : storeCode;
    }

    public String getStoreName() {
        return StringUtils.isBlank(storeName) ? businessName : storeName;
    }
}
