package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2022/2/24 15:48
 */
@Data
@Api("区域信息")
public class AreaRespDTO {
    @ApiModelProperty(value = "区域id")
    private Integer id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "区域父id")
    private Integer parentId;
    @ApiModelProperty(value = "区域类型")
    private Integer areaType;
    @ApiModelProperty(value = "子树")
    private List<AreaRespDTO> children;
}
