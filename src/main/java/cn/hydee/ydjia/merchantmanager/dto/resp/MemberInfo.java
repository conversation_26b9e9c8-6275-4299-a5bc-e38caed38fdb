package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员信息
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/12 11:45
 */
@Data
public class MemberInfo {

    @ApiModelProperty(value = "会员卡id")
    private Long vipCardId;
    @ApiModelProperty(value = "用户id")
    private Long userId;
    @ApiModelProperty(value = "会员名称")
    private  String memberName;
    @ApiModelProperty(value = "会员昵称")
    private  String nickName;
    @ApiModelProperty(value = "身份证号")
    private String idCard;
    @ApiModelProperty(value = "手机")
    private String memberPhone;
    @ApiModelProperty(value = "会员卡号")
    private String cardNumber;
}
