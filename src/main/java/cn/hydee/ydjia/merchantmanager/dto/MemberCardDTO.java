package cn.hydee.ydjia.merchantmanager.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/03/31 10:25
 */
@Data
public class MemberCardDTO {
    @ApiModelProperty(value = "会员卡背景,图片地址或者颜色型号")
    private String cardBgContent;
    @NotNull(message = "会员卡背景类型不能为空")
    @ApiModelProperty(value = "会员卡背景类型,1:颜色,2:图片")
    private Integer cardBgType;
    @ApiModelProperty(value = "颜色")
    private String color;
    @ApiModelProperty(value = "审核状态")
    private Integer cardStatus;
    @ApiModelProperty(value = "会员卡标题")
    @NotBlank(message = "会员卡标题不能为空")
    private String cardTitle;
    @ApiModelProperty(value = "会员卡类型")
    private Integer cardType;
    @ApiModelProperty(value = "创建人", hidden = true)
    private Integer createId;
    @ApiModelProperty(value = "数据主键")
    private Integer id;
    @ApiModelProperty(value = "开卡送积分")
    private Integer integral;
    @ApiModelProperty(value = "微信开卡设置", hidden = true)
    private Integer isCard;
    @ApiModelProperty(value = "支付注册设置")
    private Integer isPay;
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;
    @ApiModelProperty(value = "商户ID")
    private String merCode;
    @ApiModelProperty(value = "默认发卡机构")
    private String organization;
    @ApiModelProperty(value = "特权说明")
    private String prerogative;
    @ApiModelProperty(value = "联系电话")
    private String serviceTel;
    @ApiModelProperty(value = "修改人ID", hidden = true)
    private Integer updateId;
    @ApiModelProperty(value = "使用须知")
    private String useNotice;
    @ApiModelProperty(value = "微信公众号引导菜单设置")
    private List<CustomCell> customCells;
    @ApiModelProperty(value = "商户名")
    private String merBrandName;
    @ApiModelProperty(value = "商户logo")
    private String merLogoUrl;
    @ApiModelProperty(value = "会员卡注册回调地址", hidden = true)
    private String wxActivateAfterSubmitUrl;
    @ApiModelProperty(hidden = true)
    private String wxBgContent;
    /**
     * 0：未授权
     * 1：未创建
     * 2：未审核
     * 3：审核成功
     * 4：审核失败
     */
    @ApiModelProperty(value = "会员卡状态")
    private Integer statusFlag;
    @ApiModelProperty(value = "支付商户号",hidden = true)
    private String payMerCode;
    @ApiModelProperty(value = "支付消息点击请求地址",hidden = true)
    private String memberCardPayCallBack;


    @Data
    static class CustomCell {
        @ApiModelProperty(value = "微信公众号引导菜单")
        private String name;
        @ApiModelProperty(value = "微信公众号引导语")
        private String tips;
        @ApiModelProperty(value = "微信公众号菜单跳转地址")
        private String url;
    }

    public boolean openPay() {
        return isPay != null && isPay == 1;
    }

    public boolean withPicture() {
        return cardBgType == 2;
    }

    @ApiModelProperty(value = "微信公众号字段")
    private RegOptions regOptions;

    @Data
    public static class RegOptions {

        private Boolean memberName;

        private Boolean memberNameRequired;

        private Boolean memberSex;

        private Boolean memberSexRequired;

        private Boolean memberBirthday;

        private Boolean memberBirthdayRequired;

        private Boolean memberIdcard;

        private Boolean memberIdcardRequired;

        private Boolean memberAddress;

        private Boolean memberAddressRequired;

        private Boolean recommend;

        private Boolean recommendRequired;

        private Boolean recommendStore;

        private Boolean recommendStoreRequired;
    }
}
