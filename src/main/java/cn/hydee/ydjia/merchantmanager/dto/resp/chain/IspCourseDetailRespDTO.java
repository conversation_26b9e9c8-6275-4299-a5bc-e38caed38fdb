package cn.hydee.ydjia.merchantmanager.dto.resp.chain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 课程详情响应对象
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
@Data
public class IspCourseDetailRespDTO {

    @ApiModelProperty(value = "课程id")
    private Long id;

    @ApiModelProperty("服务商编码")
    private String ispCode;

    @ApiModelProperty(value = "课程名称")
    private String activityDetailName;

    @ApiModelProperty(value = "课程介绍")
    private String activityNote;

    @ApiModelProperty(value = "课程封面")
    private String cover;

    @ApiModelProperty(value = "上架状态（1未上架、3已上架")
    private Integer status;

    @ApiModelProperty(value = "首次上架时间")
    private Date firstReleaseTime;

    @ApiModelProperty(value = "修改时间。未下架状态时，表示下架时间")
    private Date updateTime;

    @ApiModelProperty(value = "课时数")
    private Integer courseCount;

    @ApiModelProperty(value = "课时激励配置")
    private IspCourseExcitationRespDTO courseExcitation;

    @ApiModelProperty(value = "关联商品")
    private List<IspWareRelationRespDTO> relationWareList;

    @ApiModelProperty(value = "课程目录")
    private List<IspCourseListRespDTO> courseList;

}
