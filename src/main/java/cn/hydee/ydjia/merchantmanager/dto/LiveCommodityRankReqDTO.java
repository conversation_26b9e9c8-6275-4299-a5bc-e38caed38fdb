package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 直播间商品排序保存
 */
@Data
public class LiveCommodityRankReqDTO {

    @ApiModelProperty(value = "商户code", required = true)
    @NotNull(message = "商户code不能为空")
    private String merCode;

    @ApiModelProperty(value = "直播id", required = true)
    @NotNull(message = "直播id不能为空")
    private Long liveId;

    @ApiModelProperty(value = "1:自主直播 2:厂家直播")
    @NotNull(message = "直播类型不能为空")
    private Integer merType;

    @ApiModelProperty("直播商品id-排序集合")
    @NotEmpty(message = "集合不能为空")
    private List<CommoditySort> commoditySortList;

    @Data
    public static class CommoditySort{
        @ApiModelProperty("直播商品ID")
        private Long id;

        @ApiModelProperty("商品序号，数值越小优先级越高")
        private Long serialNumber;
    }
}
