package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 优惠券核销统计
 * <AUTHOR>
 * @date 2022-01-06 18:44
 * @since 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CouponSaleCountRespDTO {

    @ApiModelProperty(value = "领取量")
    private Integer couponCount = 0;

    @ApiModelProperty(value = "核销量")
    private Integer usedNum = 0;

}
