package cn.hydee.ydjia.merchantmanager.dto.wx;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2021/1/25
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxMpMessageNotifyRequestDTO {
    private String requestBody;
    private String signature;
    private String timestamp;
    private String nonce;
    private String openid;
    private String encType;
    private String msgSignature;
    private String appId;
}
