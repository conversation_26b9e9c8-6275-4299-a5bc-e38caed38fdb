package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: ChengZW
 * @date: 2021/4/13
 **/
@Data
public class MaterialArticleDTO {
    private Integer id;
    @ApiModelProperty(hidden = true)
    private String merCode;
    /**
     * 标题
     */
    @NotBlank(message = "文章标题不能为空")
    private String title;
    /**
     * 摘要
     */
    @NotBlank(message = "文章摘要不能为空")
    private String digest;
    /**
     * 文章内容
     */
    @NotBlank(message = "文章内容不能为空")
    private String content;
    /**
     * 原文链接
     */
    private String contentSourceUrl;
    /**
     * 封面图id
     */
    @NotNull(message = "封面图不能为空")
    private Integer thumbId;
    /**
     * 封面图url
     */
    private String thumbUrl;
    /**
     * 封面图url 长条型，比例2.35:1
     */
    private String longLocalThumbUrl;
    /**
     * 封面图url 正方形 比例：1：1
     */
    private String squareLocalThumbUrl;
    /**
     * 作者
     */
    private String author;
    /**
     * 分组id
     */
    private Integer groupId;
    /**
     * 状态：1正常，0已删除
     */
    private Integer isValid;
    /**
     * 素材标签集合,逗号隔开
     */
    private List<MaterialKeywordDTO> labelList;
    private LocalDateTime createTime;
    private String materialType;
    @ApiModelProperty(value = "文章内插入的视频素材")
    private List<MaterialBaseDTO> videoList;
    @ApiModelProperty(value = "文章内插入的音频素材")
    private List<MaterialBaseDTO> voiceList;
}
