package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/18   16:37
 * @since 1.0
 */
@Data
@EqualsAndHashCode
@ToString
@Builder
public class LiveTotalDataRspDTO {

    @ApiModelProperty("总直播场数")
    private Long totalCount;

    @ApiModelProperty("总直播时长")
    private Long totalDuration;

    @ApiModelProperty("总累计观看人数")
    private Long totalWatchNum;

    @ApiModelProperty("总用户支付订单数")
    private Long totalOrderNum;

    @ApiModelProperty("总用户支付金额")
    private String totalPayAmount;

    @ApiModelProperty("总观看用户支付率")
    private String totalVisitOrderRate;

    @ApiModelProperty("总商品销量")
    private String totalCommodityNum;

    @ApiModelProperty("正在直播")
    private List<LiveSimpleRspDTO> liveUnderwayList;

}
