package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class MedicineSendStatisticDTO {

    @ApiModelProperty("用药指导")
    private Long medicineGuide = 0L;

    @ApiModelProperty("用药提醒")
    private Long medicineRemind = 0L;

    @ApiModelProperty("用药依从")
    private Long medicineComply = 0L;

    @ApiModelProperty("复购提醒")
    private Long medicineRepurchase = 0L;

    @ApiModelProperty("渠道消息详情")
    private List<MedicineChannelDetail> channelDetails;

}