package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 活动方案详情
 */

@Data
public class ActivityDetailInfo{

    @ApiModelProperty("活动id")
    private String activityId;

    @ApiModelProperty("人群id")
    private Long crowdId;

    @ApiModelProperty("活动模板编码")
    private String activityTemplateCode;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动开始时间")
    private String beginTime;

    @ApiModelProperty("活动结束时间")
    private String endTime;

}
