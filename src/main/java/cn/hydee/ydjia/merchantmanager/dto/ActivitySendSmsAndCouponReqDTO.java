package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/5/16
 */
@Data
public class ActivitySendSmsAndCouponReqDTO{

    @ApiModelProperty("商户编码")
    @NotBlank
    private String merCode;

    @ApiModelProperty("服务商活动id")
    @NotNull
    private Long activityIspId;

    @ApiModelProperty("服务商编码")
    @NotBlank
    private String ispCode;

    @ApiModelProperty(value = "服务商短息模板id（群发短信必传）")
    private Long messageIspId;

    @ApiModelProperty(value = "服务商优惠券id（群发优惠券必传）")
    private Long couponIspId;

}
