package cn.hydee.ydjia.merchantmanager.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2020-04-13 14:16
 */
@Data
public class ActivityCommoditySpecExtDTO extends ActivitySpecExtDTO implements Serializable {

    private static final long serialVersionUID = 1666252596106767013L;

    @ApiModelProperty(value = "活动ID")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long activityId;

    @ApiModelProperty(value = "商品规格ID")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long specID;

    @ApiModelProperty("规格名称")
    private String commoditySpecName;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("生产厂家")
    private String prodManufacture;

    /*@ApiModelProperty(value = "活动前价格")
    private BigDecimal beforePrice;

    @ApiModelProperty(value = "商品库存")
    private Integer stock;

    @ApiModelProperty(value = "商品数量")
    private Integer count;

    @ApiModelProperty(value = "活动价（不包含优惠分摊金额）")
    private BigDecimal discount;

    @ApiModelProperty(value = "限购数量")
    private Integer limitAmount;

    @ApiModelProperty(value = "库存数量")
    private Integer stockAmount;

    @ApiModelProperty(value = "已售数量")
    private Integer saleAmount;

    @ApiModelProperty(value = "剩余库存")
    private Integer leftAmount;*/

    @ApiModelProperty(value = "商品规格关联的门店ID，逗号隔开")
    private String storeIds;

}
