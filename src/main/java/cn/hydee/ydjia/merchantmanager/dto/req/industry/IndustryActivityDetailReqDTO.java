package cn.hydee.ydjia.merchantmanager.dto.req.industry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *  活动详情
 * <AUTHOR>
 * @date 2022/5/25 18:46
 */
@Data
public class IndustryActivityDetailReqDTO {

    @ApiModelProperty("商户号")
    @NotBlank(message = "商家编码不能为空")
    private String merCode;

    @ApiModelProperty("活动Id")
    @NotNull(message = "活动Id不能为空")
    private Integer activityId;

    @ApiModelProperty("工业活动Id")
    @NotNull(message = "工业活动Id不能为空")
    private Long activityIspId;

    @ApiModelProperty(value = "服务商编码")
    @NotBlank(message = "服务商编码不能为空")
    private String ispCode;
}
