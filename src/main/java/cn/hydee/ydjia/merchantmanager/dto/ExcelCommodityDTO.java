package cn.hydee.ydjia.merchantmanager.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/10/10 13:44
 */
@Data
@AllArgsConstructor
public class ExcelCommodityDTO {
    @Excel(name="条形码")
    private String barCode;
    @Excel(name="商品编码",orderNum = "1")
    private String erpCode;
    @Excel(name="参考价(元)",orderNum = "2")
    private BigDecimal price;
    @Excel(name="商品名称",orderNum = "3")
    private String name;
    @Excel(name="货主（0自营，1平安。默认0）",orderNum = "4")
    private String owner;
    @Excel(name="需要身份证（0否，1是。默认0）",orderNum = "5")
    private String needId;

    /**
     * 商家编码
     * */
    private String merCode;

    public ExcelCommodityDTO(){

    }

    public void trim() {
        try {
            if (!StringUtils.isEmpty(erpCode)){
                erpCode = erpCode.trim();
            }
            if (!StringUtils.isEmpty(name)){
                name = name.trim();
            }
            if (!StringUtils.isEmpty(barCode)){
                barCode = barCode.trim();
            }
            if (!StringUtils.isEmpty(owner)){
                owner = owner.trim();
            }
            if (!StringUtils.isEmpty(needId)){
                needId = needId.trim();
            }
        } catch (Exception e) {
            throw WarnException.builder().code(LocalError.EXCEL_FIELD_ERROR.getCode()).
                    tipMessage(LocalError.EXCEL_FIELD_ERROR.getMsg()).build();
        }
    }
}
