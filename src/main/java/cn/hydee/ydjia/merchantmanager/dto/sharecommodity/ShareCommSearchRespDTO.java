package cn.hydee.ydjia.merchantmanager.dto.sharecommodity;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * @Name: ShareCommSearchReqDTO
 * @Description: 共享仓商品库存查询返回类
 * @Author: Kaven
 * @Date: 2023/3/7 09:27
 */
@Data
@Deprecated
public class ShareCommSearchRespDTO extends PageBase {

    @ApiModelProperty(value = "商家编码")
    private String merCode;

    @ApiModelProperty(value = "商品分类")
    private String typeId;

    @ApiModelProperty(value = "商品编码")
    private String code;

    @ApiModelProperty(value = "最小库存")
    private Integer minStock;

    @ApiModelProperty(value = "最大库存")
    private Integer maxStock;

    @ApiModelProperty(value = "锁定状态")
    private Integer status;
}
