package cn.hydee.ydjia.merchantmanager.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 创建活动错误对象
 */
@Data
public class PmtErrorRecordDTO {
//    创建失败原因 失败详情  门店编码  门店名称  商品编码、商品名称、商品规格、活动开始时间、活动结束时间
//
//如：
//活动冲突  与满减活动【满100减20】活动冲突 麓谷店  0001  维生素 30g/瓶 2020.01.01 12:00:00     2020.02.01 12:00:00
//商品状态不符   商品已下架    麓谷店  0001  维生素 30g/瓶 - -

    @Excel(name = "创建失败原因",width = 16)
    private String error;
    @Excel(name = "失败详情", orderNum = "1",width = 30)
    private String reason;
    @Excel(name = "门店编码", orderNum = "2",width = 10)
    private String stCode;
    @Excel(name = "门店名称", orderNum = "3",width = 40)
    private String storeName;
    @Excel(name = "商品编码", orderNum = "4",width = 12)
    private String erpCode;
    @Excel(name = "商品名称", orderNum = "5",width = 40)
    private String commName;
    @Excel(name = "商品规格", orderNum = "6",width = 25)
    private String specName;
    @Excel(name = "冲突活动开始时间", orderNum = "7",width = 20)
    private String startTime = "-";
    @Excel(name = "冲突活动结束时间", orderNum = "8",width = 20)
    private String endTime = "-";
    @Excel(name = "操作人", orderNum = "9",width = 20)
    private String createName;

    @ApiModelProperty("1-商品状态不符，2-活动冲突")
    private String reasonFlag = "1";

}
