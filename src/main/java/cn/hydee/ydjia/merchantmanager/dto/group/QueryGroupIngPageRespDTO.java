package cn.hydee.ydjia.merchantmanager.dto.group;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * **/
@Data
public class QueryGroupIngPageRespDTO {

    @ApiModelProperty(value = "团编码")
    private String groupCode;

    @ApiModelProperty(value = "会员卡号")
    private String userId;

    @ApiModelProperty(value = "会员姓名")
    private String userName;

    @ApiModelProperty(value = "会员昵称")
    private String userNickname;

    @ApiModelProperty(value = "会员联系方式")
    private String userTel;

    @ApiModelProperty(value = "开团开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "此团结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "门店ID")
    private String storeId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "团满人数")
    private Integer fullNum;

    @ApiModelProperty(value = "参团人数")
    private Integer addNum;

    @ApiModelProperty(value = "拼团状态(0待付款，1.待成团，2已成团，3拼团失败,4.手动成团,5拼团失败后已回收库存，6已发货)")
    private Integer groupStatus;

    @ApiModelProperty(value = "购买份数(在待付款情况下是没有份数的)")
    private Integer quantity;

    @ApiModelProperty(value = "拼团活动价格")
    private BigDecimal activityPrice;

    @ApiModelProperty(value = "产品显示图片")
    private String imgUrl;

    @ApiModelProperty(value = "产品SKUID")
    private String specId;

    @ApiModelProperty(value = "产品名称")
    private String productName;
    
    @ApiModelProperty(value = "实付运费总金额")
    private BigDecimal sumFreightAmount;
    
    @ApiModelProperty(value = "实付总金额")
    private BigDecimal sumActualOrderAmount;
    
    @ApiModelProperty(value = "商品编码")
    private String commodityId;
    
    @ApiModelProperty(value = "商品编码")
    private String erpCode;
    
    @ApiModelProperty(value = "成团时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;
}
