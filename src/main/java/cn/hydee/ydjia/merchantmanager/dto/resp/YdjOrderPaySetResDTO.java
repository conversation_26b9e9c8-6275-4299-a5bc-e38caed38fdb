package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <Description>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/28 11:55
 */
@Data
public class YdjOrderPaySetResDTO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "商家编码")
    @NotEmpty(message = "商家编码不能为空")
    private String merCode;
    @ApiModelProperty(value = "快递订单是否支持货到付款0否1是")
    private Integer deliveryOrder;
    @ApiModelProperty(value = "配送订单是否支持货到付款0否1是")
    private Integer distributionOrder;
    @ApiModelProperty(value = "门店自提订单是否支持货到付款0否1是")
    private Integer selfOrder;
    @ApiModelProperty(value = "0停用，1启用")
    private Integer status;

}
