package cn.hydee.ydjia.merchantmanager.dto.req.marketreport;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: HuangYiBo
 * @time: 2021/12/1 17:06
 */

@Data
public class MarketReportExportReqDTO {
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "商户编码")
    private Integer merCode;
    @ApiModelProperty(value = "门店编码")
    private List<String> businessIdList;
    @ApiModelProperty(value = "1:周 2:月 3:季度 4:年 5:自定义")
    private String timeType;
    @ApiModelProperty(value = "下单场景（1：线上，2：线下）")
    private Integer sceneRuleFlag;
    @ApiModelProperty("领卡渠道")
    private Integer fromFlag;

    @ApiModelProperty(value = "消费渠道 0:不限 1:线上 2:线下 默认0")
    private Integer consumeType = 0;

    private String type;

    private String userName;
}
