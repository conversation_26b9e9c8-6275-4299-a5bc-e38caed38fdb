package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 大屏banner配置响应对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/29
 */
@Data
public class ScreenBannerConfigResDTO implements Serializable {

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "大屏banner是否展示开关：0不展示、1展示")
    private Integer status;

    @ApiModelProperty(value = "大屏banner图集合")
    private List<ScreenBannerResDTO> bannerList;

    /**
     * 大屏banner
     */
    @Data
    public static class ScreenBannerResDTO implements Serializable {
        @ApiModelProperty(value = "商户编号")
        private String mer_code;

        @ApiModelProperty(value = "banner图地址")
        private String imgUrl;

        @ApiModelProperty(value = "banner图排序号")
        private Integer sort;
    }

}
