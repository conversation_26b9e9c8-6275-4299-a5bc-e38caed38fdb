package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: lisp
 * @time: 2022/1/05 09:44
 */

@Data
public class MemberUsableAmountListReqDTO {

    @ApiModelProperty(value = "商户编码")
    @NotNull(message = "商户编码不能为空")
    private String merCode;


    @ApiModelProperty(value = "会员卡号")
    @NotEmpty(message = "会员卡号不能为空")
    private List<String> memberCards;



}
