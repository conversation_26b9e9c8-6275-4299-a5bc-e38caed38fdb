package cn.hydee.ydjia.merchantmanager.dto.excel;

import cn.hydee.ydjia.merchantmanager.dto.commission.OssFileRspDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/7/6
 */
@Data
public class CommodityCommentImportRespDTO {

    @ApiModelProperty(value = "导入成功数量")
    private Integer successCount;
    @ApiModelProperty(value = "导入失败数量")
    private Integer failCount;
    @ApiModelProperty(value = "下载数据path")
    private String resultPath;
}
