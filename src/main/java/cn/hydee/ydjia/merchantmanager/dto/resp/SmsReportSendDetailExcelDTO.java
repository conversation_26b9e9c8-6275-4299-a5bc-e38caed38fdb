package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * 触达分析——短信发送明细导出实体
 *
 * @title: SmsReportSendDetailExcelDTO
 * @package cn.hydee.ydjia.merchantmanager.dto.resp
 * @author: canliang.nie
 * @date: 2023/3/10 11:01
 * @version: V1.0
 */
@Data
public class SmsReportSendDetailExcelDTO {

    @ExcelProperty("任务编号")
    @ColumnWidth(value = 20)
    private String bizBatchNo;

    @ExcelProperty("场景类型")
    @ColumnWidth(value = 20)
    private String bizName;

    @ExcelProperty("短信类型")
    @ColumnWidth(value = 20)
    private Integer msgType;

    @ExcelProperty("短信主题")
    @ColumnWidth(value = 20)
    private String msgTheme;

    @ExcelProperty("短信内容")
    @ColumnWidth(value = 20)
    private String content;

    @ExcelProperty("发送时间")
    @ColumnWidth(value = 20)
    private Date createTime;

    @ExcelProperty("触达人数")
    @ColumnWidth(value = 20)
    private Integer succeedNum;

    @ExcelProperty("计费条数")
    @ColumnWidth(value = 20)
    private Integer succeedFeeLen;

}
