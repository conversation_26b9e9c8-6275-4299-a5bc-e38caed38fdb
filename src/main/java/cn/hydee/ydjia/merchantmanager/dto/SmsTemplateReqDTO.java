package cn.hydee.ydjia.merchantmanager.dto;


import cn.hydee.starter.dto.PageBase;
import cn.hydee.ydjia.merchantmanager.dto.resp.MessageVariableDTO;
import cn.hydee.ydjia.merchantmanager.enums.MsgType;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 短信模板请求DTO
 * @date 2022/1/9 15:52
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Api("短信模板请求DTO")
@AllArgsConstructor
@NoArgsConstructor
public class SmsTemplateReqDTO extends PageBase {

    @NotBlank(message = "商户编码不能为空")
    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "创建人/修改人")
    private String createName;

    @ApiModelProperty(value = "短信模板ID")
    private Long id;

    @ApiModelProperty(value = "短信类型 1:通知短信 2:营销短信")
    private Integer sceneType;

    @ApiModelProperty(value = "短信主题")
    private String modelTitle;

    @ApiModelProperty(value = "短信模板内容")
    private String modelContent;

    @ApiModelProperty(value = "启用状态")
    private Integer status;

    @ApiModelProperty(value = "搜索关键词")
    private String keyWord;

    @ApiModelProperty(value = "测试发送手机号")
    private String testMobile;

    @ApiModelProperty("短信变量")
    private List<MessageVariableDTO> messageVariables;

    @ApiModelProperty(value = "短信变量json字符串", hidden = true)
    private String messageVariable;

    @ApiModelProperty(value = "消耗短信条数")
    private Integer feeSmsNum;

    @ApiModelProperty(value = "带变量的手机号")
    private String varMobiles;

    @ApiModelProperty("组织机构")
    private String organization;


    public void validData() {
        if (this.getSceneType() == null
        || !this.getSceneType().equals(MsgType.NOTIFY.getCode())
        || !this.getSceneType().equals(MsgType.MARKETING.getCode())) {
            throw new IllegalArgumentException("短信类型错误");
        }
        if (StringUtils.isEmpty(this.getModelTitle())) {
            throw new IllegalArgumentException("短信主题不能为空");
        }
        if (StringUtils.isEmpty(this.getModelContent())) {
            throw new IllegalArgumentException("短信模板内容不能为空");
        }
    }

    public String getMessageVariable(){
        if(!CollectionUtils.isEmpty(this.messageVariables)){
            return JSON.toJSONString(this.messageVariables);
        }
        return this.messageVariable;
    }
}
