package cn.hydee.ydjia.merchantmanager.dto.resp.memberflow;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Name: TradedGoodsExportResp
 * @Description: 流量排行榜成交商品导出类
 * @Author: Kaven
 * @Date: 2023/3/31 17:12
 */
@Data
public class TradedGoodsExportResp {
    @ExcelProperty("商品编码")
    @ApiModelProperty("商品编码")
    private String wareCode;

    @ExcelProperty("商品名称")
    @ApiModelProperty("商品名称")
    private String wareName;

    @ExcelProperty("规格")
    @ApiModelProperty("规格")
    private String spec;

    @ExcelProperty("订单数")
    @ApiModelProperty("订单数")
    private Long orderCount;
}
