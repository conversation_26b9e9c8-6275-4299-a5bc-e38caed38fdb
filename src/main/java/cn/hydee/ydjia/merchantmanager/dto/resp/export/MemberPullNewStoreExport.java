package cn.hydee.ydjia.merchantmanager.dto.resp.export;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hydee.ydjia.merchantmanager.util.BigDecimalUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 会员拉新规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberPullNewStoreExport implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "门店编码", width = 20)
    private String storeCode;

    @Excel(name = "门店名称", width = 20)
    private String storeName;

    @Excel(name = "拉新任务量", width = 20)
    private Integer taskQuantity;

    @Excel(name = "完成量", width = 20)
    private Integer finishQuantity;

    @Excel(name = "完成率", width = 20)
    private BigDecimal finishPercentage = BigDecimal.ZERO;

    @Excel(name = "奖励金额", width = 20)
    private BigDecimal rewardAmount = BigDecimal.ZERO;
}
