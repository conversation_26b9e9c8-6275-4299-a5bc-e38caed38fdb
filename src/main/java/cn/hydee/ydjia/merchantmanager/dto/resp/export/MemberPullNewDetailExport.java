package cn.hydee.ydjia.merchantmanager.dto.resp.export;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/6
 */
@Data
@Accessors(chain = true)
public class MemberPullNewDetailExport {
    @Excel(name = "会员姓名", width = 20)
    private String memberName;

    @Excel(name = "会员编码", width = 20)
    private String memberCard;

    @ApiModelProperty("推荐渠道；1：门店推荐，2：员工推荐")
    private Integer recommendType;

    @Excel(name = "推荐来源", width = 20)
    private String recommendTypeName;

    @ApiModelProperty("注册时间")
    @Excel(name = "注册时间", width = 20)
    private String registerTime;

    @ApiModelProperty("奖励金额")
    @Excel(name = "奖励金额（元）", width = 20)
    private BigDecimal rewardAmount = BigDecimal.ZERO;

    public String getRecommendTypeName() {
        if (recommendType != null && recommendType == 1) {
            return "门店推荐";
        }
        if (recommendType != null && recommendType == 2) {
            return "员工推荐";
        }
        return recommendTypeName;
    }
}
