package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.batch.annotation.EnableAsyncBatchExportTask;
import cn.hydee.ydjia.merchantmanager.excel.BigDecimalConverter;
import cn.hydee.ydjia.merchantmanager.process.MallOrderDetailExportProcessor;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商城订单明细导出实体
 *
 * @title: MallOrderDetailExcelResDTO
 * @package cn.hydee.ydjia.merchantmanager.dto.resp
 * @author: canliang.nie
 * @date: 2022/11/25 16:41
 * @version: V1.0
 */
@Data
@EnableAsyncBatchExportTask(businessType = LocalConst.MALL_ORDER_DETAIL_EXPORT, taskName = "微商城订单明细数据导出",fileName = "微商城订单明细导出数据",
        sheetNames = {"微商城订单明细数据"}, batchSize = LocalConst.DEFAULT_BATCH_SIZE, processor = MallOrderDetailExportProcessor.class)
@ColumnWidth(value = 25)
public class MallOrderDetailExcelResDTO {
    @ExcelProperty(value = "订单号")
    private String orderId;

    @ExcelProperty(value = "订单状态")
    private String orderStatus;

    @ExcelProperty(value = "支付方式")
    private String payMode;

    @ExcelProperty(value = "支付状态")
    private String payStatus;

    @ExcelProperty(value = "商品名称")
    private String commodityName;

    @ExcelProperty(value = "商品编码")
    private String commodityCode;

    @ExcelProperty(value = "商品数量")
    private String commodityNumber;

    @ExcelProperty(value = "商品实付总金额", converter = BigDecimalConverter.class)
    private BigDecimal totalActualAmount;

    @ExcelProperty(value = "优惠券ID")
    private String couponId;

    @ExcelProperty(value = "优惠券码")
    private String couponCode;

    @ExcelProperty(value = "优惠券优惠金额", converter = BigDecimalConverter.class)
    private BigDecimal couponAmount;

    @ExcelProperty(value = "活动优惠金额", converter = BigDecimalConverter.class)
    private BigDecimal activityDiscountAmount;

    @ExcelProperty(value = "心币抵扣（单位：个）", converter = BigDecimalConverter.class)
    private BigDecimal totalHb;

    @ExcelProperty(value = "心币支付金额", converter = BigDecimalConverter.class)
    private BigDecimal payOrderOfHb;

    @ExcelProperty(value = "下单时间")
    private String orderTime;

    @ExcelProperty(value = "收货人")
    private String receiver;

    @ExcelProperty(value = "收货人手机")
    private String receiverMobile;

    @ExcelProperty(value = "完整详细地址")
    private String fullDetaiAddress;

    @ExcelProperty(value = "开方单号")
    private String cfNo;

    @ExcelProperty(value = "开方时间")
    private String prescribeTime;

    @ExcelProperty(value = "开方来源")
    private String prescribeSource;

    @ExcelProperty(value = "开方状态")
    private String cfDesc;

    @ExcelProperty(value = "处方单号")
    private String presNo;

    @ExcelProperty(value = "审核状态")
    private String presDesc;

//    @ExcelProperty(value = "用药人姓名")
//    private String drugUserName;
//
//    @ExcelProperty(value = "用药人身份证号码")
//    private String cerNo;

    @ExcelProperty(value = "会员账号")
    private String memberPhone;

    @ExcelProperty(value = "会员卡号")
    private String memberCard;

    @ExcelProperty(value = "会员推荐编码")
    private String regMedium;

    @ExcelProperty(value = "会员来源")
    private String regSource;

    @ExcelProperty(value = "门店编码")
    private String stCode;

    @ExcelProperty(value = "门店名称")
    private String storeName;

    @ExcelProperty(value = "服务商编码")
    private String spCode;

    @ExcelProperty(value = "服务商名称")
    private String spName;

    @ExcelProperty(value = "投放渠道")
    private String deliveryChannelName;

    @ExcelProperty(value = "推广门店")
    private String spreadStoreName;

    @ExcelProperty(value = "分享人id")
    private String sourceMedia;

    @ExcelProperty(value = "分享人名称")
    private String shareName;

    @ExcelProperty(value = "是否员工")
    private String isEmployee;

    @ExcelProperty(value = "姓名")
    private String userName;

    @ExcelProperty(value = "身份证")
    private String idCard;

    @ExcelProperty(value = "手机号")
    private String userPhone;

    @ExcelProperty(value = "体温")
    private String temperature;

    @ExcelProperty(value = "住址")
    private String address;

    @ExcelProperty(value = "近7天内是否驻留高、中风险地区所在市")
    private String highRiskArea;

    @ExcelProperty(value = "居民健康码")
    private String userHealthCodeImage;

    @ExcelProperty(value = "是否预售")
    private String presaleOrderType;

}
