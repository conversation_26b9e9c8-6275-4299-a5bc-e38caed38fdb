package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/8
 */
@Data
@ContentRowHeight(value = 20)
@HeadRowHeight(value = 25)
@Accessors(chain = true)
public class MemberBatchRechargeImportExcelRespDTO {

    @ExcelProperty("会员卡号")
    @ColumnWidth(value = 20)
    private String memberCard;


    @ExcelProperty("充值金额")
    @ColumnWidth(value = 20)
    private String amount;

    @ExcelProperty("失败原因")
    @ColumnWidth(value = 80)
    private String result;

}
