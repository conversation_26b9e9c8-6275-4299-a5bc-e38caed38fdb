package cn.hydee.ydjia.merchantmanager.dto.payment;

import cn.hydee.ydjia.merchantmanager.dto.resp.OrderPaymentDetailDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderPaymentLogDTO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date delTime;
    private Long id;

    private String merCode;

    private String businessType;

    private String businessId;

    private Integer paymentTypeId;

    private String paymentNo;

    private String origPaymentNo;

    private BigDecimal paymentFee;

    private BigDecimal paidFee;

    private String paymentTime;

    private Long memberId;

    private String memberNo;

    private String backNo;

    private String backTime;

    private String backStatus;

    private String backNotes;

    private String returnUrl;

    private String reqTime;

    private String openId;

    private Integer queryStatus;

    private Integer paidStatus;

    private Integer refundStatus;

    private String orderBody;

    private String ip;

    private String sftInstOrderNo;

    @ApiModelProperty(value = "收款账户类型：1-商户级，2-门店级")
    private Integer collectionAccountType;

    private Integer isvalid;

    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    private String modifyName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;
    
    //支付明细
    private List<OrderPaymentDetailDTO> payDetails;
}
