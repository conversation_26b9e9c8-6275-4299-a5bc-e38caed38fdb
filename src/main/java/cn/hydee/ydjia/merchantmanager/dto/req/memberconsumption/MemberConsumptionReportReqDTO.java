package cn.hydee.ydjia.merchantmanager.dto.req.memberconsumption;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: LongHua
 * @date: 2022/1/7
 */
@Data
public class MemberConsumptionReportReqDTO {
    @ApiModelProperty(value = "1:周 2:月 3:季度 4:年 5:自定义")
    private String timeType;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "商户编码，不填表示全部")
    private String merCode;
    @ApiModelProperty(value = "门店编码集合，不填表示全部")
    private List<String> businessIdList;
    @ApiModelProperty(value = "用户名", hidden = true)
    private String userName;
    @ApiModelProperty(value = "消费渠道 0:不限 1:线上 2:线下 默认0")
    private Integer consumeType = 0;
}
