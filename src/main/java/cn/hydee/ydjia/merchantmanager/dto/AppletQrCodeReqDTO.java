package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.web.bind.annotation.PathVariable;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/5/18   16:37
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppletQrCodeReqDTO {

    @ApiModelProperty(value = "小程序地址", required = true)
    @NotNull(message = "小程序url不能为空")
    private String appletUrl;

    @ApiModelProperty(value = "商户code", required = true)
    private String merCode;

    @ApiModelProperty(value = "直播id", required = true)
    @NotNull(message = "直播id不能为空")
    private Long liveId;

    @ApiModelProperty(value = "商户类型，1：商家；2：厂家")
    private String merType;

    @ApiModelProperty(value = "app类型，1：C端；2：B端", required = true)
    @NotNull(message = "app类型不能为空")
    private String appType;

    @ApiModelProperty(value = "操作人，仅appType=1时有意义", hidden = true)
    private String operator;

    @ApiModelProperty(value = "待生成二维码的小程序appid，仅appType=1时有意义")
    private String appid;

}
