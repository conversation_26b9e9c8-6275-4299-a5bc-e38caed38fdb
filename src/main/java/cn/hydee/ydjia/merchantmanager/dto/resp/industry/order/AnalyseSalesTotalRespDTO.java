package cn.hydee.ydjia.merchantmanager.dto.resp.industry.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @ClassName DynamicSalesTotalRespDTO
 * <AUTHOR>
 * @Date 2022/12/6 11:35
 * @Version 1.0
 **/
@Data
@ToString
public class AnalyseSalesTotalRespDTO {

    @ApiModelProperty(value = "累计销售数量")
    private BigDecimal totalSaleNum = BigDecimal.ZERO;
    @ApiModelProperty(value = "累计销售金额")
    private BigDecimal totalSaleAmount = BigDecimal.ZERO;
}
