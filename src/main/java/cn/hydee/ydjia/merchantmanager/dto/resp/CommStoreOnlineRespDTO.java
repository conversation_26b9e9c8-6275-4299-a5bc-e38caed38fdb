package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/22
 */
@Data
public class CommStoreOnlineRespDTO {

    @ApiModelProperty(value = "门店ID")
    private String id;

    @ApiModelProperty(value = "商家编码")
    private String merCode;

    @ApiModelProperty(value = "门店编码")
    private String stCode;

    @ApiModelProperty(value = "门店名称")
    private String stName;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区")
    private String area;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "维度")
    private String latitude;

    @ApiModelProperty(value = "门店图片地址")
    private String stPath;

    @ApiModelProperty(value = "状态（0：停用，1：启用）")
    private String stStatus;

    @ApiModelProperty(value = "营业开始时间")
    private Long openStartTime;

    @ApiModelProperty(value = "营业结束时间")
    private Long openEndTime;

    @ApiModelProperty(value = "开始送药时间")
    private Long deliveryStartTime;

    @ApiModelProperty(value = "结束送药时间")
    private Long deliveryEndTime;

    @ApiModelProperty(value = "是否支持自提")
    private Integer isself;

    @ApiModelProperty(value = "是否支持送药上门0：不支持，1支持")
    private Integer isdelivery;

    @ApiModelProperty(value = "配送方式送药上门，0不支持，1支持")
    private Integer isdistribution;

    @ApiModelProperty(value = "门店上线状态")
    private Integer onlineStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "o2o服务范围(0,设置距离;1,自定义范围,2：全国范围)")
    private Integer o2oServiceScopeType;

    @ApiModelProperty(value = "服务范围")
    private Integer serviceScope;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String modifyName;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "商品上架状态，0：停用，1：启用")
    private Integer commOnlineStatus;

    @ApiModelProperty(value = "SKU ID")
    private String specId;
}
