package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON>iB<PERSON>
 * @time: 2020/9/8 09:46
 */

@Data
public class MemberLabelSelectRespDTO {


    @ApiModelProperty(value = "自定义标签列表")
    private List<MemberLabelDetailRespDTO> customLabels;

    @ApiModelProperty(value = "平台标签列表")
    private List<MemberLabelDetailRespDTO> platformLabels;
}
