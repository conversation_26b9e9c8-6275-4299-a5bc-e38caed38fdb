package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/12 15:08
 */
@Data
public class YdjMerMessageResDTO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "投诉内容")
    private String complaintContext;
    @ApiModelProperty(value = "投诉时间")
    private Date complaintTime;
    @ApiModelProperty(value = "回复内容")
    private String messageContext;
    @ApiModelProperty(value = "回复时间")
    private Date messageTime;
    @ApiModelProperty(value = "消息状态（0：未回复，1：已回复）")
    private Integer messageStatus;
    @ApiModelProperty(value = "是否匿名（0：否，1：是）")
    private Integer isVisit;
    @ApiModelProperty(value = "会员id")
    private Long userId;
    @ApiModelProperty(value = "会员卡号")
    private String memberCard;
    @ApiModelProperty(value = "会员昵称")
    private String nickName;
    @ApiModelProperty(value = "会员姓名")
    private String memberName;
    @ApiModelProperty(value = "会员手机号")
    private String memberPhone;
    @ApiModelProperty(value = "是否有效")
    private Integer isValid;
    @ApiModelProperty(value = "会员头像路径")
    private String memberHead;

    @ApiModelProperty(value = "回复人")
    private String revertName;
    @ApiModelProperty(value = "回复信息")
    private String revertContext;
    @ApiModelProperty(value = "回复时间")
    private Date revertTime;

}
