package cn.hydee.ydjia.merchantmanager.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/08/19 16:40
 */
@Data
public class CouponStatisticsSourceDTO {
    private String merCode;
    @ApiModelProperty(value = "券类型(1:折扣,2:满减,3:礼品)")
    private Integer cType;
    private String cName;
    @ApiModelProperty(value = "领取量")
    private Integer receivedNum;
    @ApiModelProperty(value = "线上使用人数")
    private Integer userNum;
    @ApiModelProperty("线下使用人数")
    private Integer offlineUserNum;
    @ApiModelProperty(value = "线上支付金额")
    private BigDecimal paymentAmount;
    @ApiModelProperty(value = "线下支付金额")
    private BigDecimal offlinePaymentAmount;
    @ApiModelProperty(value = "发送渠道")
    private String sourceChannel;
    private Integer couponId;

    public CouponStatisticsSourceDTO() {
        this.paymentAmount = BigDecimal.ZERO;
    }

}

