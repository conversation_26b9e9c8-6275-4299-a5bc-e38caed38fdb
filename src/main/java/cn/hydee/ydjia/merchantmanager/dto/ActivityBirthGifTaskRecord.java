package cn.hydee.ydjia.merchantmanager.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 生日礼包语音短信任务记录表
 * <AUTHOR>
 * @since 2022-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityBirthGifTaskRecord implements Serializable {

    private static final long serialVersionUID=1L;

    private Long id;

    private String merCode;

    /**
     * 是否发送短信通知，1-发送；2-不发送
     */
    private Integer sendSmsStatus;

    /**
     * 是否发送语音通知，1-发送；2-不发送
     */
    private Integer sendVoiceStatus;

    /**
     * 短信内容
     */
    private String smsContent;

    /**
     * 话术id
     */
    private String verbalId;

    /**
     * 话术名称
     */
    private String verbalName;

    /**
     * 操作人
     */
    private String userName;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
