package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.starter.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/09
 */
@Data
public class CommoditySimpleRankQueryRespDTO {

    @ApiModelProperty("服务商编码")
    private String ispCode;

    @ApiModelProperty("服务商活动id")
    private Long id;

    @ApiModelProperty("激励基数(1.按销售数量计算，2.按销售额计算)")
    private Integer excitationType;

    @ApiModelProperty("单品销售排名激励集合")
    private PageDTO<ExcitationWareInfo> pageData;

    @Data
    @ToString
    public static class ExcitationWareInfo{

        private Long id;

        @ApiModelProperty(value = "商品来源")
        private Integer wareSource;

        @ApiModelProperty(value = "标库商品编码")
        private String warePlatformCode;

        @ApiModelProperty(value = "服务商商品编码")
        private String wareIspCode;

        @ApiModelProperty(value = "服务商商品名称")
        private String name;

        @ApiModelProperty(value = "服务商商品规格")
        private String specSku;

        @ApiModelProperty(value = "服务商商品图片")
        private String mainPic;

        @ApiModelProperty(value = "商品排序优先级,数字越小优先级越高,用于随心看")
        private Integer sort;

        @ApiModelProperty(value = "保底金额")
        private BigDecimal floorsMoney;

        @ApiModelProperty(value = "单品销售排名详细激励配置集合")
        private List<BaseRankDTO> rankList;

        @ApiModelProperty(value = "条形码")
        private String barCode;

        public String specialKey(String spCode,Long activityId){
            String formatStr = "%s_%s_%s";
            return String.format(formatStr,spCode,activityId,id);
        }
    }
}
