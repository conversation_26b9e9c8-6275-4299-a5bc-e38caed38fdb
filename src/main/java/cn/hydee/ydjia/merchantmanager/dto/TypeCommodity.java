package cn.hydee.ydjia.merchantmanager.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hydee.starter.dto.PageBase;
import cn.hydee.ydjia.merchantmanager.domain.Commodity;
import cn.hydee.ydjia.merchantmanager.domain.CommoditySpecSku;
import cn.hydee.ydjia.merchantmanager.dto.resp.MultilevelTypeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品规格信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/17 17:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TypeCommodity extends CommodityAndSpec {
    private boolean currentType;
}
