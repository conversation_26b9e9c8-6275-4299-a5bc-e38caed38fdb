package cn.hydee.ydjia.merchantmanager.dto.nr.index;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class IndexOrderYesterdayData {

    /**
     * 支付订单数
     */
    @ApiModelProperty(value = "支付订单数")
    private BigDecimal orderNumber = BigDecimal.ZERO;



    /**
     * 订单销售额 单位：元
     */
    @ApiModelProperty(value = "订单销售额 单位：元")
    private BigDecimal orderAmount = BigDecimal.ZERO;

    /**
     * 销售数量
     */
    @ApiModelProperty(value = "销售数量")
    private BigDecimal saleQty = BigDecimal.ZERO;

    /**
     * 客单价 = 当天的销售额/订单数
     */
    @ApiModelProperty(value = "客单价")
    private BigDecimal passengerPrice = BigDecimal.ZERO;

    /**
     * 客品数 = 当天的销售商品数量/订单数
     */
    @ApiModelProperty(value = "客品数")
    private BigDecimal passengerNumber = BigDecimal.ZERO;

}
