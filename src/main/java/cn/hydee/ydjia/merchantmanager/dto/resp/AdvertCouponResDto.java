package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.yxt.coupon.api.dto.CouponDiscountLadderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @version: V1.0
 * @author: JackLi
 * @className: AdvertCouponResDto
 * @packageName: cn.hydee.middle.market.dto.rsp
 * @description: 描述
 * @data: 2020/9/4 10:59
 **/
@Data
public class AdvertCouponResDto {
    /**
     * 优惠券张数
     */
    @ApiModelProperty(value = "优惠券张数", notes = "")
    private Integer giftNum;

    /**
     *优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private Integer id;


    private String merCode;
    /**
     * 券名称
     */
    @ApiModelProperty(value = "券名称")
    @Deprecated
    private String cName;

    @ApiModelProperty(value = "券名称")
    private String name;
    /**
     * 类型（1折扣2抵价3礼品）
     */
    @ApiModelProperty(value = "类型（1折扣2抵价3礼品）")
    @Deprecated
    private Integer cType;

    @ApiModelProperty(value = "类型（1折扣2抵价3礼品）")
    private Integer type;
    /**
     * logo
     */
    private String logo;
    /**
     * 配合type使用,面额(eg.满减用20表示,8折用8)
     */
    @ApiModelProperty(value = "配合type使用,面额(eg.满减用20表示,8折用8)")
    @Deprecated
    private BigDecimal denomination;
    /**
     * 礼品ID
     */
    @ApiModelProperty(value = "礼品ID")
    private String giftId;

    @ApiModelProperty(value = "礼品名称")
    private String giftName;
    /**
     * 最大优惠金额，设置0无上限
     */
    @ApiModelProperty(value = "最大优惠金额，设置0无上限")
    @Deprecated
    private BigDecimal maxPrice;
    /**
     * 券状态（0失效1生效）
     */
    @ApiModelProperty(value = "券状态（0失效1生效")
    private Integer state;
    /**
     * 使用时间（1领取起n天有效2领取起n天后生效,生效后m天失效3开始时间-结束时间）
     */
    @ApiModelProperty(value = "使用时间（1领取起n天有效2领取起n天后生效,生效后m天失效3开始时间-结束时间）")
    private Integer timeRule;
    /**
     * 配合time_rule使用,值用逗号分隔
     */
    @ApiModelProperty(value = "配合time_rule使用,值用逗号分隔")
    private String effectTime;
    /**
     * 使用场景（1线上线下2微商城3线下门店）
     */
    @ApiModelProperty(value = "使用场景（1线上线下2微商城3线下门店）")
    @Deprecated
    private Integer sceneRule;
    /**
     * 退货规则（1退货退回2退货失效）
     */
    @ApiModelProperty(value = "退货规则（1退货退回2退货失效）")
    private Integer returnRule;
    /**
     * 客服电话
     */
    @ApiModelProperty(value = "客服电话")
    private String kefuPhone;
    /**
     * 使用须知
     */
    @ApiModelProperty(value = "使用须知")
    private String note;
    /**
     * 适用门店（1全部门店2指定门店）
     */
    @ApiModelProperty(value = "适用门店（1全部门店2指定门店）")
    private Integer shopRule;
    /**
     * 适用商品（1全部可用2指定可用3指定不可用）
     */
    @ApiModelProperty(value = "适用商品（1全部可用2指定可用3指定不可用）")
    private Integer productRule;
    /**
     * 使用门槛0代表无门槛，数字代表订单满n元
     */
    @ApiModelProperty(value = "使用门槛0代表无门槛，数字代表订单满n元")
    @Deprecated
    private BigDecimal useRule;
    /**
     * 到期提醒默认（0代表不提醒,正整数代表n天微信提醒）
     */
    @ApiModelProperty(value = "到期提醒默认（0代表不提醒,正整数代表n天微信提醒）")
    private Integer expireInfo;

    @ApiModelProperty(value = "线上核销数量(所有活动)")
    private Integer onlineCount;

    @ApiModelProperty(value = "线下核销数量(所有活动)")
    private Integer offlineCount;

    @ApiModelProperty(value = "总领取量(所有活动)")
    private Integer totalCount;

    @ApiModelProperty(value = "发放张数")
    private Integer totalSend;
    /**
     * productRule表示商品/商品分类的规则
     * proRule=1 & typeRule=1 => productRule=1
     * proRule=2 || typeRule=2 => productRule=2
     * proRule=3 || typeRule=3 => productRule=3
     */
    @ApiModelProperty(value = " 商品规则（1：全部，2：部分可用，3：部分不可用）")
    private Integer proRule;
    @ApiModelProperty(value = " 商品分类规则（1：全部，2：部分可用，3：部分不可用）")
    private Integer typeRule;


    @ApiModelProperty("优惠券兑换商品数")
    private Integer giftLimitNum;

    @ApiModelProperty(value = "使用场景-新（1-线下门店 2-自营商品 4-三方商品")
    private List<Integer> sceneRuleList;

    @ApiModelProperty(value = "礼品券可选商品最大兑换数量")
    private Integer optionalGiftLimitNum;

    @ApiModelProperty(value = "优惠阶梯")
    private List<CouponDiscountLadderDTO> couponDiscountLadderList;
}
