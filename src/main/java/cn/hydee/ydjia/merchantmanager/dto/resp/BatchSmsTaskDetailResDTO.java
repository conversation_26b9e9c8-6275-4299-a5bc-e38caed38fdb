package cn.hydee.ydjia.merchantmanager.dto.resp;

import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: pengyayun
 * @Description:
 * @Date: 2022/1/12
 */
@Data
public class BatchSmsTaskDetailResDTO {

//    /**
//     * 任务名称
//     */
//    private String taskName;
//
//    /**
//     * 短信内容
//     */
//    private String content;
//    /**
//     * 短信类型： 1-通知类，2-营销类
//     */
//    private Integer smsType;
//    /**
//     * 业务场景（由业务方自定义）如果业务方需要区分发送短信业务场景时传
//     */
//    private String bizType;
//    /**
//     * 短信类型： 1-通知类，2-营销类
//     */
//    private String msgType;
    /**
     * 短信主题 如果业务方需要区分主题时传
     */
    private String msgTheme;

    /**
     * 提交发送手机号数量
     */
    private Integer mobileNum;
    /**
     * 提交发送手机号数量
     */
    private Integer succeedNum;
    /**
     * 失败发送数量
     */
    private Integer failedNum;
    /**
     * 未知发送数量
     */
    private Integer unknowNum;
    /**
     * 计费条数
     */
    private Integer feeNum;
    /**
     * 任务开始时间
     */
    private Integer beginTime;
    /**
     * 任务结束时间
     */
    private Integer endTime;
    /**
     * 任务创建时间
     */
    private Integer createTime;

}
