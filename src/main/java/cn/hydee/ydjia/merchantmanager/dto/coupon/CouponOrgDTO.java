package cn.hydee.ydjia.merchantmanager.dto.coupon;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CouponOrgDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织机构编码")
    @ExcelProperty(value = "组织机构编码")
    private String orgCode;

    @ApiModelProperty(value = "组织机构名称")
    @ExcelProperty(value = "组织机构名称")
    private String orgName;

}
