package cn.hydee.ydjia.merchantmanager.dto.marketblacklist;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店配置 Request DTO
 *
 * <AUTHOR>
 * @since 2024-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BlacklistStoreReqDTO extends BlacklistBaseReqDTO {

    @ApiModelProperty("黑名单ID")
    private Integer blacklistId;

    @ApiModelProperty("门店名称")
    private String stName;

    @ApiModelProperty("门店编码")
    private String stCode;

}
