package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class MedicineRepurchaseStatisticDTO {


    @ApiModelProperty("复购药品数")
    private Integer drug;

    @ApiModelProperty("复购人数")
    private Integer number;

    @ApiModelProperty("复购订单")
    private Integer order;

    @ApiModelProperty("复购金额")
    private BigDecimal amount;

    @ApiModelProperty("日期")
    private String date;



}