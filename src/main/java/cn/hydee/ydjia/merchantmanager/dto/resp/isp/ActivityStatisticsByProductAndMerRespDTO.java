package cn.hydee.ydjia.merchantmanager.dto.resp.isp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工业活动商品销售统计响应对象-商品+连锁分组
 *
 * <AUTHOR>
 * @date 2022/9/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActivityStatisticsByProductAndMerRespDTO extends ActivityStatisticsByProductRespDTO {

    @ApiModelProperty(value = "连锁编码")
    private String merCode;

    @ApiModelProperty(value = "连锁名称")
    private String merName;

    @ApiModelProperty(value = "铺货门店数（有库存门店数）")
    private Integer stockStoreNum = 0;

    @ApiModelProperty("参与门店数")
    private Integer joinStoreNum = 0;

}
