package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
@Data
@Accessors(chain = true)
public class IspIncomeDetailReqDTO {
    @NotBlank
    @ApiModelProperty("商户编码")
    private String merCode;

    @NotBlank
    @ApiModelProperty("服务商编码")
    private String ispCode;
}
