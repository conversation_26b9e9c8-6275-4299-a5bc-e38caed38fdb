package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/14 14:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpeechTaskPageExportDTO {

    @Excel(name = "活动名称", orderNum = "0", width = 20)
    private String missionName;
    @Excel(name = "拨打话术", orderNum = "0", width = 20)
    private String bizType;
    @Excel(name = "发送状态", orderNum = "0", width = 20)
    private String taskStatusStr;

    @Excel(name = "拨打周期开始", orderNum = "0", width = 20)
    private String timeStart;
    @Excel(name = "拨打周期结束", orderNum = "0", width = 20)
    private String timeEnd;
    
    @Excel(name = "创建人", orderNum = "0", width = 20)
    private String createName;
    @Excel(name = "创建人编码",orderNum = "0", width = 20)
    private String createCode;
    
    @ApiModelProperty(value = "任务状态 1-未执行；2-执行中；3-已完成；4-已取消")
    private Integer taskStatus;
    
    @Excel(name = "拨打人数", orderNum = "0", width = 20)
    private Integer actualNum;
    @Excel(name = "接通人数", orderNum = "0", width = 20)
    private Integer arrivedNum;
    @Excel(name = "未接通人数", orderNum = "0", width = 20)
    private Integer failedArrivedNum;
    @Excel(name = "接通率", orderNum = "0", width = 20)
    private String arrivedAvg;
    @Excel(name = "平均通话时长", orderNum = "0", width = 20)
    private String avgCall;
    @Excel(name = "总通话时长", orderNum = "0", width = 20)
    private String totalCallStr;
    @Excel(name = "ROI（销售）", orderNum = "0", width = 20)
    private BigDecimal ROISaleAmount;
    @ApiModelProperty(value = "")
    @Excel(name = "ROI（毛利额）", orderNum = "0", width = 20)
    private BigDecimal ROIProfit;

    public Integer getFailedArrivedNum(){
        if(actualNum != null && arrivedNum != null){
            return actualNum - arrivedNum;
        }
        return 0;
    }

    public String getTaskStatusStr(){
        if(this.taskStatus == null){
            return "发送失败";
        }
        switch (this.taskStatus){
            case 1:
                return "未开始";
            case 2:
                return "进行中";
            case 3:
                return "已完成";
            case 4:
                return "已取消";
        }
        return "";
    }
}
