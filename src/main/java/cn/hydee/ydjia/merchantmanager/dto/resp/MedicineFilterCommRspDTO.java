package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/9   15:12
 * @since 1.0
 */

@Data
public class MedicineFilterCommRspDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "类型；1：用药指导；2：用药提醒；3：用药依从；4：复购提醒", required = true)
    private Integer type;

    @ApiModelProperty(value = "规格id")
    private Long specId;

    @ApiModelProperty(value = "erpCode")
    private String erpCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "通用名")
    private String commonName;

    @ApiModelProperty(value = "厂家")
    private String manufacture;

    @ApiModelProperty(value = "条形码")
    private String barCode;

}
