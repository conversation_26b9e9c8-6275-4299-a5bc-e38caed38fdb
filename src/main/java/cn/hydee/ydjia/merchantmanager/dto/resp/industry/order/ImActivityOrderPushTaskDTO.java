package cn.hydee.ydjia.merchantmanager.dto.resp.industry.order;

import cn.hydee.ydjia.merchantmanager.util.LocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/12/21 16:41
 */
@Data
public class ImActivityOrderPushTaskDTO {

    private Long id;
    private String merCode;
    private Integer successNum;
    private Integer failedNum;
    /**
     * 任务状态（1:待处理 2:处理中 3:已完成 ）
     */
    private Integer taskStatus;
    private String queryJson;
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
