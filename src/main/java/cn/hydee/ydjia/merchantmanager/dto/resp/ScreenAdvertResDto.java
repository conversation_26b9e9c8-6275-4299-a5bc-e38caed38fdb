package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @version: V1.0
 * @author: JackLi
 * @className: ScreenAdvertResDto
 * @packageName: cn.hydee.middle.market.dto.rsp
 * @description: 描述
 * @data: 2020/9/4 10:03
 **/
@Data
public class ScreenAdvertResDto {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码")
    private String merCode;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Integer activityId;

    /**
     * 开关设置（0：关；1：开）
     */
    @ApiModelProperty(value = "开关设置（0：关；1：开）")
    private Integer isOpen;

    /**
     * 推广内容（1：图片广告；2：优惠券广告）
     */
    @ApiModelProperty(value = "推广内容（1：图片广告；2：优惠券广告）")
    private Integer type;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    /**
     * 推广人群（1：所有用户；2：所有登录会员；3：非登录会员；4：会员卡种）
     */
    @ApiModelProperty(value = "推广人群（1：所有用户；2：所有登录会员；3：非登录会员；4：会员卡种）")
    private Integer targetUser;

    /**
     * 首页;“1”代表选中；“0”非选中；
     */
    @ApiModelProperty(value = "首页;“1”代表选中；“0”非选中；")
    private Integer homePage;

    /**
     * 商品详情页;“1”代表选中；“0”非选中；
     */
    @ApiModelProperty(value = "商品详情页;“1”代表选中；“0”非选中；")
    private Integer proInfoPage;

    /**
     * 商品分类页;“1”代表选中；“0”非选中；
     */
    @ApiModelProperty(value = "商品分类页;“1”代表选中；“0”非选中；")
    private Integer proTypePage;

    /**
     * 搜索页;“1”代表选中；“0”非选中；
     */
    @ApiModelProperty(value = "搜索页;“1”代表选中；“0”非选中；")
    private Integer searchPage;

    @ApiModelProperty(value = "页面集合: 1：首页；2：商品详情页；3：商品分类页；4：搜索页；5：商城首页")
    private ScreenAdvertPageDTO pageDTO;

    /**
     * 开屏广告所支持的页面集合的字符串，逗号","隔开:（1：首页；2：商品详情页；3：商品分类页；4：搜索页；5：商城首页）
     */
    private String pageStr;

    /**
     * 推送频率；1：永久一次；2：每天一次；3：每次进入商城
     */
    @ApiModelProperty(value = "推送频率；1：永久一次；2：每天一次；3：每次进入商城")
    private Integer frequency;

    /**
     * 广告限制：是否领取奖励后不再弹广告。0否，1是
     */
    @ApiModelProperty(value = "广告限制：是否领取奖励后不再弹广告。0否，1是")
    private Integer adLimit;

    /**
     * 样式背景（1：系统样式）
     */
    @ApiModelProperty(value = "样式背景（1：系统样式）")
    private Integer style;

    @ApiModelProperty(value = "卡种编码", notes = "卡种编码")
    private String cardCode;

    @ApiModelProperty(value = "卡种等级", notes = "卡种等级")
    private String cardGrade;

    /**
     * 是否有效（0：否；1：是）
     */
    @ApiModelProperty(value = "是否有效（0：否；1：是）")
    private Integer isValid;

    /**
     * 绑定的优惠券
     **/
    @ApiModelProperty(value = "绑定的优惠券")
    List<AdvertCouponResDto> couponBases;

    /**
     * 绑定的链接信息
     **/
    @ApiModelProperty(value = "绑定的链接信息")
    List<BindLinkInfoResDto> linkInfos;
}
