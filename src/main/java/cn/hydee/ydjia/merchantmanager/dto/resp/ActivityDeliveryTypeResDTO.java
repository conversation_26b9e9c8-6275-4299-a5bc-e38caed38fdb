package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/11/4 下午1:46
 */
@Data
public class ActivityDeliveryTypeResDTO {

    @ApiModelProperty(value = "配送方式")
    private String name;

    @ApiModelProperty(value = "活动id")
    private Integer activityId;

    @ApiModelProperty(value = "活动类型（14配送有礼普通快递,15配送有礼配送上门,16配送有礼门店自提）")
    private Integer activityType;

    @ApiModelProperty(value = "失效状态 0:失效 1:有效")
    private Integer status;
}
