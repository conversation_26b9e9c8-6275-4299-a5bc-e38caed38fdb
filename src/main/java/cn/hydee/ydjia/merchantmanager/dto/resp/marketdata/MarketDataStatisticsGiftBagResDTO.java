package cn.hydee.ydjia.merchantmanager.dto.resp.marketdata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2020/10/9 下午1:57
 */
@Data
public class MarketDataStatisticsGiftBagResDTO implements Serializable {

    private static final long serialVersionUID = 6200447236618428772L;

    /**
     * 类型 1：新人礼包 2：生日礼包
     */
    @ApiModelProperty(value = "类型 1：新人礼包 2：生日礼包", notes = "")
    private Integer type;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", notes = "")
    private Long userId;
    /**
     * 会员卡号
     */
    @ApiModelProperty(value = "会员卡号", notes = "")
    private String memberCard;
    /**
     * 会员姓名
     */
    @ApiModelProperty(value = "会员姓名", notes = "")
    private String memberName;
    /**
     * 会员手机号
     */
    @ApiModelProperty(value = "会员手机号", notes = "")
    private String memberPhone;
    /**
     * 送券量
     */
    @ApiModelProperty(value = "送券量", notes = "")
    private Integer couponCount;
    /**
     * 送抽奖次数
     */
    @ApiModelProperty(value = "送抽奖次数", notes = "")
    private Integer lotteryCount;
    /**
     * 送心币量
     */
    @ApiModelProperty(value = "送心币量", notes = "")
    private Integer seaShellCount;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createTime;

}
