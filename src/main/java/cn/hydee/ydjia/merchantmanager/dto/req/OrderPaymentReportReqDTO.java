package cn.hydee.ydjia.merchantmanager.dto.req;

import cn.hydee.starter.dto.PageBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单支付报表查询入参
 * <AUTHOR>
 * @date 2022-02-16 15:07
 * @since 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderPaymentReportReqDTO extends QueryOrderBaseReqDTO {

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "支付类型，0.在线支付 1.线下支付")
    private String payMode;

    @ApiModelProperty(value = "开始日期")
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date  endDate;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "下单门店")
    private String sendStoreId;

    @ApiModelProperty(value = "授权门店ID")
    private List<String> authorizeStoreList;

    private Boolean searchCount = true;
}
