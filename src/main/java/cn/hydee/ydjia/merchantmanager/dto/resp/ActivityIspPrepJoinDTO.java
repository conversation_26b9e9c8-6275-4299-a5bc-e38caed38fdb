
package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class ActivityIspPrepJoinDTO extends ActivityIspJoinRespDTO{

    @ApiModelProperty(value = "活动商品匹配")
    private Boolean matchCommodity;

    @ApiModelProperty(value = "对码商品列表")
    private List<CommoditySpec> commoditySpecs;

//    @ApiModelProperty(value = "工业优惠券id列表")
//    private List<Long> couponIspIds;

    @ApiModelProperty(value = "工业活动商品信息")
    private Map<String, WareIspResDTO> wareInfoMap;

//    @ApiModelProperty(value = "工业活动优惠券商品信息")
//    private List<CouponWareDetailIspEntity> couponWareDetailIspList;
//
//    @ApiModelProperty(value = "工业活动优惠券关联信息")
//    private List<ActivityCouponIspEntity> activityCouponIspEntities;
//
//    @ApiModelProperty(value = "工业活动激励商品信息")
//    private List<ActivityExcitationWareIspEntity> activityExcitationWareIspEntities;
}