package cn.hydee.ydjia.merchantmanager.dto.resp.isp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/14 14:12
 */
@Data
@ExcelIgnoreUnannotated
public class ExclusiveStoreAdviceExportDTO {

    @ExcelProperty("门店类型")
    @ColumnWidth(value = 20)
    private String type;

    @ExcelProperty("门店名称")
    @ColumnWidth(value = 20)
    private String storeName;

    @ExcelProperty("门店编码")
    @ColumnWidth(value = 20)
    private String storeCode;


    @ExcelProperty("巡店理由")
    @ColumnWidth(value = 20)
    private String patrolStoreReason;

    private List<String> patrolStoreReasons;

    public String getPatrolStoreReason(){
        if(!CollectionUtils.isEmpty(patrolStoreReasons)){
            return String.join("，", patrolStoreReasons);
        }
        return "";
    }

}
