package cn.hydee.ydjia.merchantmanager.dto.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Data
public class PrescriptionApprovalDTO {
    @ApiModelProperty(value = "需求单申请cfNo")
    private String id;
    @ApiModelProperty(value = "开方来源：1：莲藕 2：用户自传 3：微问诊")
    private Integer prescriptionType;
//    @ApiModelProperty(value = "处方单图片",required = true)
//    @NotEmpty(message = "处方端图片不能为空")
//    private String image;
//    @ApiModelProperty(value = "用药人姓名")
//    private String userName;
//    @ApiModelProperty(value = "用药人身份证号码")
//    private String cerNo;
//    @ApiModelProperty(value = "有无过敏史0无1有",required = true)
//    private Integer allergyHistory;
//    @ApiModelProperty(value = "门店编码",required = true)
//    private String stCode;
    @ApiModelProperty(value = "商家编码",required = true)
    @NotEmpty(message = "商家编码不能为空")
    private String merCode;
    @ApiModelProperty(value = "订单ID",required = true)
    @NotEmpty(message = "订单号不能为空")
    private String orderId;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "会员ID")
    private String memberId;
//    @ApiModelProperty(value = "门店ID")
//    private String storeId;
    private String createName;
    private Date createTime;
    private String modifyName;
    private Date modifyTime;
    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;
    @ApiModelProperty(value = "用药人")
    private String approvalPharmacist;
    @ApiModelProperty(value = "审批时间")
    private Date auditTime;
//    @ApiModelProperty(value = "用药人ID",required = true)
//    private String  drugUserId;

    @ApiModelProperty(value = "处方单号，审方平台生成")
    private String presNo;

    @ApiModelProperty(value = "问诊记录ID")
    private Long consultationRecordId;

    @ApiModelProperty(value = "对接平台：0-小蜜 1-药事云")
    private Integer platformType;
}