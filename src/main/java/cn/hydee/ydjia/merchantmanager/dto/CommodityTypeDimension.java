package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.ydjia.merchantmanager.domain.CommodityType;
import cn.hydee.ydjia.merchantmanager.dto.commission.DomainBase;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("commodity_type_dimension")
public class CommodityTypeDimension extends DomainBase {
    private String name;
    private String merCode;
    /** 关联平台编码 */
    private String refPlatformCode;
    private Integer useStatus;

    @TableField(exist = false)
    private Integer childNum;

    @TableField(exist = false)
    private Integer commodityNum;

    @TableField(exist = false)
    private List<CommodityType> children;
}