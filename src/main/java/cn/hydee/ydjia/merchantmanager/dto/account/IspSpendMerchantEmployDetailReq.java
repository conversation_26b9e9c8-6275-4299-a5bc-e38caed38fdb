package cn.hydee.ydjia.merchantmanager.dto.account;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动角度 - 员工激励明细列表
 * @date 2022/5/19 17:11
 */
@Data
public class IspSpendMerchantEmployDetailReq extends PageBase {
    @NotNull(message = "服务商编码不可为空")
    @ApiModelProperty(value = "服务商编码(必传)")
    private String ispCode;
    @NotNull(message = "商户编码不可为空")
    @ApiModelProperty(value = "商户编码(必传)")
    private String merCode;
    @ApiModelProperty(value = "业务订单号")
    private String orderId;
    @ApiModelProperty(value = "平台（账户中心）订单号")
    private String orderNumber;
    @ApiModelProperty(value = "订单状态 (1、待结算  2、已结算 5、待入账）")
    private Integer orderState;
    @ApiModelProperty(value = "业务类型（3、订单奖励  4、排名）")
    private Integer operationType;
    @ApiModelProperty(value = "活动id")
    private Long activityId;
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = " 激励模式 1即时激励，2激励记账")
    private Integer modeType;

}
