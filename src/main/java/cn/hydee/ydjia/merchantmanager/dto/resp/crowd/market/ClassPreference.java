package cn.hydee.ydjia.merchantmanager.dto.resp.crowd.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 品类偏好DTO
 * <AUTHOR>
 */

@Data
public class ClassPreference {

    @ApiModelProperty(value = "品类分布名称")
    private String classPreferenceName;

    @ApiModelProperty(value = "品类分布数值")
    private Integer classPreferenceNum = 0;

    @ApiModelProperty(value = "品类分布总值")
    private Integer classPreferenceCount = 0;

}
