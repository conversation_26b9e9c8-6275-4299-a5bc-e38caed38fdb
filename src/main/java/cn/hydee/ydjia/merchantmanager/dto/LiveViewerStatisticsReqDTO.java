package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/5/19   16:20
 * @since 1.0
 */
@Data
public class LiveViewerStatisticsReqDTO {

    @ApiModelProperty(value = "商户code", required = true)
    @NotNull(message = "商户code不能为空")
    private String merCode;

    @ApiModelProperty(value = "直播id", required = true)
    @NotNull(message = "直播id不能为空")
    private Long liveId;
}
