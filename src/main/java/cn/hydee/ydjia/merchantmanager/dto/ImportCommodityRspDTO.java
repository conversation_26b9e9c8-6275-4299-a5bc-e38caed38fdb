package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.ydjia.merchantmanager.dto.commission.CommissionCommodity;
import cn.hydee.ydjia.merchantmanager.dto.commission.OssFileRspDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/2   14:48
 * @since 1.0
 */
@Data
@Builder
public class ImportCommodityRspDTO {

    @ApiModelProperty(value = "成功商品信息",notes = "成功商品信息")
    private List<CommissionCommodity> successCommodityList;

    @ApiModelProperty(value = "失败商品文件信息",notes = "失败商品文件信息")
    private OssFileRspDto ossFileRsp;

    @ApiModelProperty(value = "导入成功商品数量",notes = "导入成功商品数量")
    private Integer successCount;

    @ApiModelProperty(value = "导入失败商品数量",notes = "导入失败商品数量")
    private Integer failCount;

}
