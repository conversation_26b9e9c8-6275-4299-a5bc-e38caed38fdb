package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.config.excel.CustomDateConverter;
import cn.hydee.ydjia.merchantmanager.dto.SdpBizlogInfoDTO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Classname UserSearchRespDTO
 * @Description 用户列表查询DTO
 * @Date 2020/9/7 14:17
 * @Created lizhaoyang-2695
 */
@Data
@ExcelIgnoreUnannotated
public class UserSearchRespDto {
    @ApiModelProperty(value = "主键id")
    private Integer id;
    @ExcelProperty(value = "用户姓名",index = 0)
    @ApiModelProperty(value = "用户名称")
    private String userName;
    @ExcelProperty(value = "用户电话",index = 1)
    @ApiModelProperty(value = "用户电话")
    private String userMobile;
    @ApiModelProperty(value = "门店id")
    private String storeId;
    @ExcelProperty(value = "所属门店",index = 2)
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "分销人编码")
    private String shareCode;
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "用户类型 1-员工 2-分销员")
    private Integer userType;
    @ExcelProperty(value = "邀请人",index = 3)
    @ApiModelProperty(value = "邀请人姓名")
    private String leaderUserName;
    @ExcelProperty(value = "邀请人电话",index = 4)
    @ApiModelProperty(value = "邀请人电话")
    private String leaderMobile;
    @ExcelProperty(value = "累计分销总额",index = 5)
    @ApiModelProperty(value = "分销金额")
    private BigDecimal distributionAmount = new BigDecimal(0);
    @ExcelProperty(value = "累计佣金",index = 6)
    @ApiModelProperty(value = "分佣金额")
    private BigDecimal commissionAmount = new BigDecimal(0);
    @ExcelProperty(value = "直接邀请数",index = 7)
    @ApiModelProperty(value = "直接邀请数")
    private Integer directInvitCount = 0;
    @ExcelProperty(value = "间接邀请数",index = 8)
    @ApiModelProperty(value = "间接邀请数")
    private Integer indirectInvitCount = 0;
    @ApiModelProperty(value = "累计订单数")
    private Integer orderCount = 0;
    @ApiModelProperty(value = "商品佣金")
    private BigDecimal commodityCommissionAmount = new BigDecimal(0);
    @ApiModelProperty(value = "邀请返佣金")
    private BigDecimal invitComissionAmount = new BigDecimal(0);
    @ExcelProperty(value = "加入时间",index = 9, converter = CustomDateConverter.class)
    @ApiModelProperty(value = "加入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "变更日志")
    private List<SdpBizlogInfoDTO> logList;
    @ApiModelProperty(value = "员工在职状态 1-在职 0-离职")
    private Integer empStatus;
}