package cn.hydee.ydjia.merchantmanager.dto.meituan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**消息上报实体
 * <AUTHOR>
 * @date 2020-11-20 14:28
 * @since 1.0
 */
@Data
public class ReportMsgReqDTO {

    @NotNull(message = "商户编码不能为空")
    @ApiModelProperty(value = "商户编码")
    String merCode;

    @NotNull(message = "店铺消息不能为空")
    @ApiModelProperty(value = "店铺消息")
    List<StoreMsgReqDTO> stores;
}
