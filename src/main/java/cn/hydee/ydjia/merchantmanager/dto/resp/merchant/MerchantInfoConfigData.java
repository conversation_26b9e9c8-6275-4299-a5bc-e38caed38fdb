package cn.hydee.ydjia.merchantmanager.dto.resp.merchant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname MerchantInfoConfigData
 * @Description商户基本信息配置数据对象
 * @Date 2022/3/3 10:40
 * @Created by Jack
 */
@Data
public class MerchantInfoConfigData {
    @ApiModelProperty("商户对外信息维护结果")
    Boolean merChantInfoResult;
    @ApiModelProperty("商户资质信息维护结果")
    Boolean merchantCertificateResult;
    @ApiModelProperty("商户资质信息维护项总数")
    String  merchantCertificateNumber;
    @ApiModelProperty("商户资质信息维护数据")
    String  merchantCertificateData;

}
