package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2019/10/22 14:41
 */
@Data
public class ProductMatchReqDTO {


    @ApiModelProperty(value = "商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String name;

    @ApiModelProperty(value = "生产厂家")
    @NotBlank(message = "生产厂家不能为空")
    private String manufacture;

    @ApiModelProperty(value = "批准文号")
    @NotBlank(message = "批准文号不能为空")
    private String approvalNumber;

    @ApiModelProperty(value = "产品条形码")
    @NotBlank(message = "产品条形码不能为空")
    private String barCode;

    private String merCode;
}
