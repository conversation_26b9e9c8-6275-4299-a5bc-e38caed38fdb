package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: Huang<PERSON>iBo
 * @time: 2020/11/2 15:44
 */

@Data
public class MemberValueCardInfoRespDTO {

    @ApiModelProperty(value = "卡片名称")
    private String cardName;

    @ApiModelProperty(value = "卡片ID")
    private Integer cardCode;

    @ApiModelProperty(value = "卡片背景 1样式 2图片")
    private Integer cardBackground;

    @ApiModelProperty(value = "卡片内容")
    private String cardContent;

    @ApiModelProperty(value = "充值金额")
    private BigDecimal rechargeAmount;

    @ApiModelProperty(value = "赠送金额")
    private BigDecimal donationAmount;

    @ApiModelProperty(value = "赠送心币")
    private Integer donationIntegral;

    @ApiModelProperty(value = "优惠券活动ID")
    private Integer activityId;

    @ApiModelProperty(value = "优惠券集合")
    private List<CouponBaseEntity> couponList;
}
