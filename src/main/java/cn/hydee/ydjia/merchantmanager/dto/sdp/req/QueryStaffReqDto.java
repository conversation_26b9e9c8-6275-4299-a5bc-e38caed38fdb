package cn.hydee.ydjia.merchantmanager.dto.sdp.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/9/7 11:08
 */
@Data
public class QueryStaffReqDto extends PageBase {
    @NotBlank(message = "商家编码不能为空")
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "门店id")
    private String storeId;
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    @ApiModelProperty(value = "订单id")
    private String orderId;
    @ApiModelProperty(value = "员工手机号")
    private String userMobile;
    @ApiModelProperty(value = "是否需要分页(1.分页 0.不分页 默认分页)")
    private Integer isPage = 1;

}
