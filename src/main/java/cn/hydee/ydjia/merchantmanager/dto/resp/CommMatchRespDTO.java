package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品对码失败导出对象模型
 *
 * <AUTHOR>
 * @date 2020/3/19
 */
@Data
public class CommMatchRespDTO {
    @Excel(name = "商品编码", orderNum = "0", width = 20)
    private String erpCode;
    @Excel(name = "商品名称", orderNum = "1", width = 20)
    private String name;
    @Excel(name = "生产企业", orderNum = "2", width = 20)
    private String manufacture;
    @Excel(name = "批准文号", orderNum = "3", width = 20)
    private String approvalNumber;
    @Excel(name = "条形码", orderNum = "4", width = 20)
    private String barCode;
    @Excel(name = "参考价", orderNum = "5", width = 20)
    private BigDecimal price;
    @Excel(name = "失败原因", orderNum = "6", width = 20)
    private String reason;
}
