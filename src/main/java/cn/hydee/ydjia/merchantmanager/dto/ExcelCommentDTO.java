package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.batch.annotation.EnableBatchImportTask;
import cn.hydee.batch.dto.BatchImportBaseDTO;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.process.BatchImportCommodityCommentProcessor;
import cn.hydee.ydjia.merchantmanager.util.ExcelConstants;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2020/06/15 13:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
@EnableBatchImportTask(businessType = ExcelConstants.MALL_COMMODITY_COMMENT_IMPORT, taskName = "批量导入商品评论",
        processor = BatchImportCommodityCommentProcessor.class, batchSize = ExcelConstants.IMPORT_DEFAULT_BATCH_SIZE)
public class ExcelCommentDTO extends BatchImportBaseDTO {

    @NotBlank(message = "商品编码不能为空")
    @Pattern(regexp = "[a-zA-Z0-9]{1,16}", message = "商品编码书写错误或不规范")
    @ExcelProperty(value = "商品编码")
    private String erpCode;
    @NotBlank(message = "评价内容不能为空")
    @ExcelProperty(value = "评价内容")
    private String comment;


    public void trim() {
        try {
            erpCode = erpCode.trim();
            comment = comment.trim();
        } catch (Exception e) {
            throw WarnException.builder().code(LocalError.EXCEL_FIELD_ERROR.getCode()).
                    tipMessage(LocalError.EXCEL_FIELD_ERROR.getMsg()).build();
        }
    }
}
