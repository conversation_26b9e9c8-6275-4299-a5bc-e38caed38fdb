package cn.hydee.ydjia.merchantmanager.dto.resp.merchant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname MallConfigData
 * @Description OMS配置数据对象
 * @Date 2022/3/3 16:48
 */
@Data
public class OmsConfigV2Data {

    @ApiModelProperty("o2o店铺配置结果")
    Boolean o2oStoreConfigResult;

    @ApiModelProperty("o2o店铺配置数据")
    String  o2oStoreConfigData;

    @ApiModelProperty("订单配送结果")
    Boolean orderDeliveryResult;

}
