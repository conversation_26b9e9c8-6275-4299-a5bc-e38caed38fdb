package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/03/30 16:05
 */
@Data
public class MemberArchivalDTO {
    @ApiModelProperty(value = "用药禁忌")
    private String avoidMedication;
    @ApiModelProperty(value = "过敏史")
    private List<String> allergyHistory;
    @ApiModelProperty(value = "易感病症")
    private List<String> diseaseName;
}
