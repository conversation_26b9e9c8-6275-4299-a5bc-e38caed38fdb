package cn.hydee.ydjia.merchantmanager.dto.resp.market.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CouponProductInfoDTO {
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String erpCode;
    /**
     * 商品橱窗图片url
     */
    @ApiModelProperty(value = "商品图片地址")
    private String mainPic;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "商品参考价")
    private BigDecimal mPrice;


}
