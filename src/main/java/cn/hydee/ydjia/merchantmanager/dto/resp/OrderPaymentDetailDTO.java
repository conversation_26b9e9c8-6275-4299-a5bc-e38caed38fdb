package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @version: V1.0
 * @author: duans
 * @className: OrderPaymentDetailDTO
 * @packageName: cn.hydee.ydjia.merchantmanager.dto.resp
 * @description:
 * @data: 2022-09-19 14:25
 **/

@Data
public class OrderPaymentDetailDTO {

	@ApiModelProperty(value = "支付方式编码")
	private String paymentTypeId;

	@ApiModelProperty(value = "支付方式名称")
	private String payInnerName;

	@ApiModelProperty(value = "支付金额")
	private BigDecimal paidFee;

	@ApiModelProperty(value = "支付时间")
	private Date paymentTime;
}