package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/5/18   16:37
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveCommodityRspDTO {

    private Long id;

    @ApiModelProperty("商户code")
    private String merCode;

    @ApiModelProperty("直播ID")
    private Long liveId;

    @ApiModelProperty("商品规格ID")
    private Long specId;

    @ApiModelProperty("商品规格名称")
    private String specName;

    @ApiModelProperty("商品图片")
    private String commodityPic;

    @ApiModelProperty("商品ID")
    private String commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("参考价")
    private BigDecimal mPrice;

    @ApiModelProperty("商品编码")
    private String erpCode;

    @ApiModelProperty("品牌名")
    private String brandName;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品条形码")
    private String barCode;

    @ApiModelProperty("来源，1：商家，2：厂家")
    private Integer source;

    @ApiModelProperty("商品类型: 1-普通商品，2-组合商品，3-云仓商品")
    private Integer commodityType;

    @ApiModelProperty("上架状态 false:下架 true:上架")
    private Boolean shelfStatus;

    @ApiModelProperty("成交订单数")
    private Integer orderCount;

    @ApiModelProperty(value = "销量")
    private Integer totalQuantity;

    @ApiModelProperty(value = "销售额")
    private BigDecimal totalAmount;

    @ApiModelProperty("商品序号，数值越小优先级越高")
    private Long serialNumber;

    @ApiModelProperty("浏览人数")
    private Integer viewers;

    @ApiModelProperty("浏览次数")
    private Integer viewerNum;

    @ApiModelProperty("讲解状态 false:未讲解 true:讲解中")
    private Boolean explainStatus;

    @ApiModelProperty(value = "服务商编码")
    private String spCode;

}
