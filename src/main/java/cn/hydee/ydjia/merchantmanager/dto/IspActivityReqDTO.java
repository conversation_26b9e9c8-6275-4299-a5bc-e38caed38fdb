package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
@Data
@Accessors(chain = true)
public class IspActivityReqDTO {
    @ApiModelProperty("活动id")
    private String activityId;

    @ApiModelProperty("服务商活动id")
    private String ispActivityId;

    @ApiModelProperty("商户编码")
    private String merCode;
}
