package cn.hydee.ydjia.merchantmanager.dto.resp.marketreport;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: HuangYiBo
 * @time: 2021/11/23 11:40
 */

@Data
public class MarketReportBasicDataRespDTO {
    @ApiModelProperty(value = "本期用券带动销售额")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal paymentExtUse;
    @ApiModelProperty(value = "本期总销售额占比")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal paymentExtPer;
    @ApiModelProperty(value = "环比用券带动销售额增长率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal paymentExtMoMUseRate;
    @ApiModelProperty(value = "同比用券带动销售额增长率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal paymentExtYoYUseRate;
    @ApiModelProperty(value = "本期用券毛利额")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitUse;
    @ApiModelProperty(value = "本期总毛利额占比")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitUsePer;
    @ApiModelProperty(value = "环比用券毛利额增长率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitMoMUseRate;
    @ApiModelProperty(value = "同比用券毛利额增长率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitYoYUseRate;
    @ApiModelProperty(value = "本期用券毛利率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitUseRate;
    @ApiModelProperty(value = "本期未用券毛利率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitNotUseRate;
    @ApiModelProperty(value = "环比用券毛利率增长率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitUseMoMRateRate;
    @ApiModelProperty(value = "同比用券毛利率增长率")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal profitUseYoYRateRate;
    @ApiModelProperty(value = "本期用券客单价")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusTraUse;
    @ApiModelProperty(value = "本期未用券客单价")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusTraNotUse;
    @ApiModelProperty(value = "环比用券客单价增长")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusTraMoMUseRate;
    @ApiModelProperty(value = "同比用券客单价增长")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusTraYoYUseRate;
    @ApiModelProperty(value = "本期用券品单价")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal proUnitPriceUse;
    @ApiModelProperty(value = "本期未用券品单价")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal proUnitPriceNotUse;
    @ApiModelProperty(value = "环比用券品单价增长")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal proUnitPriceMoMUseRate;
    @ApiModelProperty(value = "同比用券品单价增长")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal proUnitPriceYoYUseRate;
    @ApiModelProperty(value = "本期用券客品数")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusNumUse;
    @ApiModelProperty(value = "本期未用券客品数")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusNumNotUse;
    @ApiModelProperty(value = "环比用券客品数增长")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusNumMoMUseRate;
    @ApiModelProperty(value = "同比用券客品数增长")
    @JsonSerialize(using=ToStringSerializer.class)
    private BigDecimal perCusNumYoYUseRate;
}
