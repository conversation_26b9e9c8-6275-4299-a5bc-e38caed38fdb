package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/1 9:23
 */
@Data
public class CommMatchQueryDTO {
    @NotBlank(message = "商家编码不可为空")
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "产品条形码")
    private String barCode;
    @NotBlank(message = "商品名称不可为空")
    @ApiModelProperty(value = "商品名称")
    private String name;
    @NotBlank(message = "生产厂家不可为空")
    @ApiModelProperty(value = "生产厂家")
    private String manufacture;
    @NotBlank(message = "批准文号不可为空")
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "是否查询规格键值")
    private Boolean hasSpec;

}
