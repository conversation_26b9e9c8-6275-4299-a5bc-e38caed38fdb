package cn.hydee.ydjia.merchantmanager.dto.commission;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/***
 * @ClassName: StoreExportEO
 * @Description: 门店业绩导出
 * @Author: lck
 * @Date: 2021-09-14 10:25
 * @version : V1.0
 */
@Data
public class StoreCommodityExportEO {

    @ExcelProperty("序号")
    private String serialNo;
    @ExcelProperty("商品名称")
    private String commodityName;
    @ExcelProperty("商品编码")
    private String commodityCode;
    @ExcelProperty("规格")
    private String specName;
    @ExcelProperty("销售数量")
    private String commodityNumber;
    @ExcelProperty("销售金额（元）")
    private String saleAmount;

    @ExcelProperty("提成金额（元）")
    private String commissionAmount;
    @ExcelProperty("下单时间")
    private String orderTime;
    @ExcelProperty("结算时间")
    private String commissionTime;
    @ExcelProperty("门店编码")
    private String storeCode;
    @ExcelProperty("门店名称")
    private String storeName;
    @ExcelProperty("员工工号")
    private String empCode;
    @ExcelProperty("员工姓名")
    private String empName;
    @ExcelProperty("平台订单号")
    private String orderId;
    @ExcelProperty("系统订单号")
    private String thirdOrderNo;
    @ExcelProperty("状态")
    private String commissionStatus;
    @ExcelIgnore
    private String storeId;
    @ExcelIgnore
    private String userId;

}
