package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/15   11:29
 * @since 1.0
 */
@Data
public class MedicineCommodityDelReqDTO {

    @ApiModelProperty(value = "商户编码", required = true)
    @NotNull(message = "商户编码不能为空")
    private  String merCode;

    @ApiModelProperty(value = "类型；1：用药指导；2：用药提醒；3：用药依从；4：复购提醒", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "erpCodes")
    @NotNull
    private List<String> erpCodes;
}
