package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/8
 */
@Data
public class SpecStatisticsQueryDTO {

    @ApiModelProperty(value = "商品编码", required = true)
    @NotBlank(message = "商品编码不能为空")
    @Pattern(regexp= LocalConst.MER_CODE_PATTERN,message="商户编码错误")
    private String merCode;

    @ApiModelProperty(value = "分组ID(末级)")
    private String groupId;

    @ApiModelProperty("分组级别（1|2|3）")
    private Integer groupLevel;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品编码")
    private String erpCode;

    @ApiModelProperty(value = "商品编码集合")
    private List<String> erpCodes;

    @ApiModelProperty(value = "商品名称/编码")
    private String erpOrName;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "药品类型(0：甲类OTC，1:处方药，2：乙类OTC，3：非处方药，4：OTC)")
    private Integer drugType;

    @ApiModelProperty("商品类型,1:普通商品， 2：组合商品")
    private Integer commodityType;

    @ApiModelProperty(value = "排序类型(1:上架门店数排序，2：下架门店数排序，3:商品总库存排序，4：商品售罄门店数排序， 5：商品锁定门店数排序)")
    private Integer sortType;

    @ApiModelProperty(value = "排序方式（1：升序，2：降序）")
    private Integer sortMethod;

    @ApiModelProperty(value = "货主 0：自营，1：平安，默认为0")
    private Integer owner;

    @ApiModelProperty(value = "是否有橱窗图，true :有，false：无")
    private Boolean hasMainPic;

    @ApiModelProperty(value = "品牌ID")
    private String brandId;

    /**
     * 门店ID集合
     */
    private List<String> storeIds;

    @ApiModelProperty(value = "SKU ID集合")
    private List<String> specIds;

    @ApiModelProperty("当前页，从第1页开始，不传默认为1")
    private Integer currentPage;

    @ApiModelProperty("每页显示条数，不传默认20")
    private Integer pageSize;

    /**
     *  门店商品表名
     */
    private String onlineStoreTableName;

    private String userName;
}
