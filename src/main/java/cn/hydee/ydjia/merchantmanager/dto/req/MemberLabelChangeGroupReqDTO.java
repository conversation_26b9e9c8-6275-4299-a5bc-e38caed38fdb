package cn.hydee.ydjia.merchantmanager.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("会员标签换组接口参数类")
@ToString(callSuper = true)
public class MemberLabelChangeGroupReqDTO {


    @ApiModelProperty(value = "标签组id", required = true)
    private Long groupId;

    @ApiModelProperty(value = "标签id集合")
    @NotNull(message = "标签id不能为空")
    private List<Long> labelIdList;

}
