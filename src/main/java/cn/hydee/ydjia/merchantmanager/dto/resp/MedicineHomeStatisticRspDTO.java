package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MedicineHomeStatisticRspDTO {

    @ApiModelProperty("发送统计")
    private MedicineSendStatisticDTO sendStatisticDTO;

    @ApiModelProperty("复购统计")
    private List<MedicineRepurchaseStatisticDTO> repurchaseStatistics;

    @ApiModelProperty("渠道消息统计")
    private MedicineChannelMsgStatisticDTO channelMsgStatisticDTO;


}