package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ActivityBirthGiftResDTO {
    /**
     * ID
     */
    @ApiModelProperty(value = " id")
    private Integer id;
    /**
     * 活动模板编码
     */
    @ApiModelProperty(value = " 活动模板编码")
    private String activityTemplateCode;

    @ApiModelProperty(value = " 失效状态 0 失效  1有效  默认0")
    private Integer status;

    /**
     * 商户编号
     */
    private String merCode;

    @ApiModelProperty(value = "是否有效（0：否，1：是）")
    private Integer isValid;//是否有效（0：否，1：是）

    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    private String updateName;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "新人礼包阶梯权益")
    private List<ActivityBirthGiftLevelDTO> birthGiftLevelList;

    @ApiModelProperty("生日礼包任务记录")
    private ActivityBirthGifTaskRecord taskRecord;
}
