package cn.hydee.ydjia.merchantmanager.dto.resp.marketdata;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 优惠券售卖信息数据统计
 *
 * <AUTHOR>
 */
@Data
public class MarketDataStatisticsCouponSaleDTO {

    @ApiModelProperty(value = "优惠券售卖张数")
    private Integer couponSaleSum;

    @ApiModelProperty(value = "优惠券购买人数")
    private Integer couponUser;

    @ApiModelProperty(value = "优惠券售卖总金额")
    private BigDecimal couponSaleAmount;
}
