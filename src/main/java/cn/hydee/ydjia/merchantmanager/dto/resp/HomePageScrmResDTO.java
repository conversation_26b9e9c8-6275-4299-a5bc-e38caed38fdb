package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.dto.resp.memberconsumptionreport.StoreSaleRankingPageRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2022/1/15
 * @Param
 * @return
 */
@Data
public class HomePageScrmResDTO {
    @ApiModelProperty(value = "累计会员总数")
    private Long memberTotalNum;
    @ApiModelProperty(value = "会员动销数")
    private Long memberMovingSaleNum;
    @ApiModelProperty(value = "会员销售占比")
    private BigDecimal memberSaleRate;
    @ApiModelProperty(value = "新增会员人数")
    private Long memberAddNum;
    @ApiModelProperty(value = "优惠券核销量（张）")
    private Long couponUseNum;

    @ApiModelProperty(value = "生日会员数")
    private Long birthdayMemberNum = 0L;


    @ApiModelProperty(value = "有效会员数")
    private Long validMemberNum = 0L;


    @ApiModelProperty(value = "沉睡会员数")
    private Long sleepMemberTotalNum = 0L;

    @ApiModelProperty(value = "流失会员数")
    private Long attritionMemberTotalNum = 0L;

    @ApiModelProperty(value = "门店销售排行")
    private List<StoreSaleRankingPageRespDTO> saleRankingPageRespDTOList;

    @ApiModelProperty(value = "套餐版本 1-基础版 2-专业版")
    private Integer packageVersion = 0;

}
