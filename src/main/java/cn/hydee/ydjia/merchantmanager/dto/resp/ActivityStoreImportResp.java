package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.dto.commission.OssFileRspDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2022年06月17日
 */
@Data
public class ActivityStoreImportResp {

    @ApiModelProperty(value = "导入成功数量")
    private Integer successCount;
    @ApiModelProperty(value = "导入失败数量")
    private Integer failCount;
    @ApiModelProperty(value = "导入成功门店集合")
    private List<ActivityStoreImportResp.ActivityStore> activityStores;
    @ApiModelProperty(value = "重复门店集合")
    private List<ActivityStoreImportResp.ActivityStore> redundantActivityStores;
    @ApiModelProperty(value = "下线门店集合")
    private List<ActivityStoreImportResp.ActivityStore> offlineActivityStores;
    @ApiModelProperty(value = "无效门店集合")
    private List<ActivityStoreImportResp.ActivityStore> invalidActivityStores;
    @ApiModelProperty(value = "失败门店文件信息")
    private OssFileRspDto ossFileRspDto;

    @Data
    public static class ActivityStore {

        @ApiModelProperty(value = "门店ID")
        private String storeId;
        @ApiModelProperty(value = "门店编码")
        private String storeCode;
        @ApiModelProperty(value = "门店名称")
        private String storeName;
        @ApiModelProperty(value = "门店电话")
        private String mobile;
        @ApiModelProperty(value = "门店地址")
        private String address;
    }
}
