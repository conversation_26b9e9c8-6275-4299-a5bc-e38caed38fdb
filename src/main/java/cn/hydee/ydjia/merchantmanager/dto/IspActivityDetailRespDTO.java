package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
@Data
public class IspActivityDetailRespDTO {
    /**
     * ID
     */
    @ApiModelProperty(value = "id")
    private Integer id;

    /**
     * 活动模板编码
     */
    @ApiModelProperty(value = "活动模板编码")
    private String activityTemplateCode;

    /**
     * 活动类型（0：其他类型，1：免费，2：积分，3：现金）
     */
    @ApiModelProperty(value = "活动类型（0：其他类型，1：免费，2：积分，3：现金）")
    private Integer activityType;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityDetailName;

    /**
     * 活动说明
     */
    @ApiModelProperty(value = "活动说明")
    private String activityNote;

    /**
     * 活动起始时间
     */
    @ApiModelProperty(value = "活动起始时间")
    private LocalDateTime beginTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime endTime;

    /**
     * 商户编号
     */
    private String merCode;

    /**
     * 时间状态，对应入参state
     */
    @ApiModelProperty(value = "时间状态，对应入参state")
    private Integer state;

    /**
     * 每个人限领礼包数量
     */
    @ApiModelProperty(value = "优惠券礼包每个人限领数量")
    private Integer everyoneLimitSum;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "每人次数限制（0：不限制）")
    private Integer countRule;

    /**
     * 消耗积分
     */
    @ApiModelProperty(value = "消耗积分")
    private Integer integralRule;

    /**
     * 底部文案
     */
    @ApiModelProperty(value = "底部文案")
    private String bottomNote;

    @ApiModelProperty(value = "封面")
    private String cover;

    @ApiModelProperty(value = "参与模式（0：渠道，1：免费，2：积分，3：活动")
    private Integer joinRule;

    @ApiModelProperty(value = "次数限制类型（1：每人，2：每天）")
    private Integer countType;

    @ApiModelProperty(value = "金额限制（0：无门槛）")
    private BigDecimal useRule;

    @ApiModelProperty(value = "使用类型（1：商品参与，2：订单参与）")
    private Integer useType;

    @ApiModelProperty(value = "是否分享 0否 1是")
    private Integer isShare;

    @ApiModelProperty(value = "是否在领券中心展示0否1是")
    private Integer isShow;

    @ApiModelProperty(value = "当前活动全路径")
    private String activityUrl;

    @ApiModelProperty(value = "当前活动小程序路径")
    private String activityMiniUrl;

    @ApiModelProperty(value = "活动绑定的优惠券数量")
    private Integer bindCouponNum;

    @ApiModelProperty(value = "参加活动的总量")
    private Integer joinNum;

    @ApiModelProperty(value = "售卖礼包数量")
    private Integer couponGiftSum;

    @ApiModelProperty(value = "售卖礼包总金额")
    private BigDecimal couponGiftAmount;

    @ApiModelProperty(value = "购买礼包人数")
    private Integer couponGiftUser;


    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否周期性活动：0否，1是(TA003,TA004专用)")
    private Integer isCycle;

    @ApiModelProperty(value = "周期性活动则返回文本（每月、每周xx号或每天几点到几点）(TA003,TA004专用)")
    private String cycleText;

    @ApiModelProperty(value = "是否可抽奖：0否，1是(TA003,TA004专用)")
    private Integer isLotteryDraw;

    /**
     * 服务商活动id
     */
    private Integer ispActivityId;

    /**
     * 厂商活动发布状态（1.草稿，2.未发布，3.已发布，4.已暂停，5.已终止，6.已结束）
     */
    private Integer ispActivityStatus;

    private String ispCode;
}
