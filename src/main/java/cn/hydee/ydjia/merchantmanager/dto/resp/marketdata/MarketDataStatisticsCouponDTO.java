package cn.hydee.ydjia.merchantmanager.dto.resp.marketdata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 优惠券主页面数据统计
 *
 * <AUTHOR>
 */

@Data
public class MarketDataStatisticsCouponDTO {

    @ApiModelProperty(value = "优惠券礼包数据")
    private MarketDataStatisticsCouponGiftDTO marketDataStatisticsCouponGiftDTO;

    @ApiModelProperty(value = "优惠券售卖数据")
    private MarketDataStatisticsCouponSaleDTO marketDataStatisticsCouponSaleDTO;

    @ApiModelProperty(value = "优惠券兑换数据")
    private MarketDataStatisticsIntegralExchangeDTO marketDataStatisticsIntegralExchangeDTO;


}
