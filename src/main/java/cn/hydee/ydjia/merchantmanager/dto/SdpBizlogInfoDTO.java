package cn.hydee.ydjia.merchantmanager.dto;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sdp_bizlog_info")
public class SdpBizlogInfoDTO {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String merCode;

    private Integer logType;

    private String logBizTag;

    private String operationName;

    private String operationContent;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}