package cn.hydee.ydjia.merchantmanager.dto.commission;

import cn.hydee.starter.dto.PageBase;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/***
 * @ClassName: EmpStatisReqDTO
 * @Description: 店员业绩明细
 * @Author: koumingming
 * @Date: 2020-06-30 15:45
 * @version : V1.0
 */
@Data
@ToString(callSuper = true)
public class EmpPerformDetailReqDTO extends PageBase {

    @ApiModelProperty(value = "门店Id", required = true)
    @NotNull(message = "门店id不能为空")
    private String storeId;

    @ApiModelProperty(value = "员工Id", required = true)
    @NotNull(message = "员工id不能为空")
    private String userId;

    @ApiModelProperty(value = "开始时间", required = true)
    @Pattern(regexp = LocalConst.YYYY_MM_DD, message = "开始时间格式错误")
    private String startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    @Pattern(regexp = LocalConst.YYYY_MM_DD, message = "结束时间格式错误")
    private String endDate;

}
