package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/17
 */
@Data
@Accessors(chain = true)
public class IspCouponListReqDTO extends PageBase {
    @ApiModelProperty("单号")
    private String orderNumber;
    @ApiModelProperty("业务订单号")
    private String orderId;
    @ApiModelProperty("状态 1：待结算，2：已结算 3：已退款 5:待入账")
    private Integer orderState;
    /**
     * 活动编码集合
     */
    private List<Long> activityIdList;
    @ApiModelProperty("活动编码")
    private Long activityId;
    @ApiModelProperty("活动名称")
    private String activityName;
    @ApiModelProperty("商户编码，必填")
    @NotBlank
    private String merCode;
    @ApiModelProperty("服务商编码，必填")
    @NotBlank
    private String ispCode;

    private List<Integer> modeTypeList;

    private List<Integer> typeList;
}
