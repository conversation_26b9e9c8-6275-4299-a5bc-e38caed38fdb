package cn.hydee.ydjia.merchantmanager.dto.resp.activity.sms;

import cn.hydee.ydjia.merchantmanager.constants.MerchantManagerConstants;
import cn.hydee.ydjia.merchantmanager.dto.resp.MessageVariableDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.sms.MemberLifeCircleWithCalculateInfo;
import cn.hydee.ydjia.merchantmanager.enums.sms.MemberLifeCircleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/03/01
 */
@Data
public class LifeCircleDetailRspDTO extends LifeCircleCommonRspDTO {

    @ApiModelProperty(value = "title")
    private String title;

    @ApiModelProperty(value = "发送对象")
    private SendObjectInfo sendObject;

    @ApiModelProperty(value = "名称，如会员收购激活")
    private String sendContent;

    @ApiModelProperty(value = "发送时间描述")
    private String sendTimeDesc;

    @ApiModelProperty("短信变量")
    private List<MessageVariableDTO> messageVariables;

    @ApiModelProperty(value = "0-推荐人群未添加 1-人群已添加")
    private Integer addFlag;


    public static LifeCircleDetailRspDTO buildDefault(MemberLifeCircleWithCalculateInfo info, MemberLifeCircleTypeEnum lifeCircleTypeEnum, String autoSendTimeDesc) {
        LifeCircleDetailRspDTO lifeCircle = new LifeCircleDetailRspDTO();
        lifeCircle.setLifeCircleType(info.getLifeCircleType());
        SendObjectInfo sendObject = new SendObjectInfo();
        sendObject.setName(info.getName());
        sendObject.setDescription(info.getDescription());
        sendObject.setCalculateStatus(Optional.ofNullable(info.getCalculateStatus()).orElse(MerchantManagerConstants.INTEGER_ZERO));
        sendObject.setTotalCrowdNum(StringUtils.isEmpty(info.getTotalCrowdNum()) ? MerchantManagerConstants.STRING_ZERO : info.getTotalCrowdNum());
        lifeCircle.setSendObject(sendObject);
        lifeCircle.setTitle(lifeCircleTypeEnum.getTitle());

        lifeCircle.setSendTimeDesc(autoSendTimeDesc);
        lifeCircle.setCrowdId(info.getAddCrowId());
        lifeCircle.setCrowStatus(info.getCrowStatus());
        lifeCircle.setRecommendId(info.getRecommendId());
        if (Objects.isNull(info.getAddCrowId())) {
            lifeCircle.setSendContent(lifeCircleTypeEnum.getDefaultSmsContext());
        }

        lifeCircle.correct();
        return lifeCircle;
    }

    public void correct() {
        // 人群id为空，未添加推荐人群
        if(Objects.isNull(getCrowdId())){
            addFlag = MerchantManagerConstants.INTEGER_ZERO;
        }else{
            addFlag = MerchantManagerConstants.INTEGER_ONE;
        }
        // 数据纠正
        if (Objects.isNull(sendObject)) {
            return;
        }
        // 非计算完成，会员数初始化
        if (MerchantManagerConstants.INTEGER_TWO.compareTo(sendObject.getCalculateStatus()) != 0) {
            sendObject.setTotalCrowdNum(MerchantManagerConstants.STRING_ZERO);
        }
    }
}
