package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class OrgInAuthRespDTO {
    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty("机构编码")
    private String orCode;
    @ApiModelProperty("机构名称")
    private String orName;
    @ApiModelProperty("电话")
    private String mobile;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("机构类型")
    private Integer orType;
    @ApiModelProperty("配送方式")
    private List<String> distributionType;
    @ApiModelProperty(value = "是否支持门店自提0不支持，1支持")
    private Integer isSelf;
    @ApiModelProperty(value = "是否支持普通快递0不支持，1支持")
    private Integer isDelivery;
    @ApiModelProperty(value = "是否支持送药上门0不支持，1支持")
    private Integer isDistribution;

    public void setDistribution() {
        this.isDelivery = 0;
        this.isDistribution = 0;
        this.isSelf = 0;
        if (!CollectionUtils.isEmpty(this.distributionType)) {
            if (this.distributionType.contains("0")) {
                this.isDelivery = 1;
            }
            if (this.distributionType.contains("1")) {
                this.isDistribution = 1;
            }
            if (this.distributionType.contains("2")) {
                this.isSelf = 1;
            }
        }
    }

    public StoreResDTO convert() {
        StoreResDTO storeResDTO = new StoreResDTO();
        storeResDTO.setMerCode(this.merCode);
        storeResDTO.setStCode(this.orCode);
        storeResDTO.setStName(this.orName);
        storeResDTO.setMobile(this.mobile);
        storeResDTO.setAddress(this.address);
        storeResDTO.setIsself(this.isSelf);
        storeResDTO.setIsdelivery(this.isDelivery);
        storeResDTO.setIsdistribution(this.isDistribution);
        return storeResDTO;
    }
}
