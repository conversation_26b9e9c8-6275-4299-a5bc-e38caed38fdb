package cn.hydee.ydjia.merchantmanager.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CallTaskParam {

    @ApiModelProperty(value = "是否过滤会员：1、不过滤（默认） 2、过滤指定类型会员")
    private Integer isFilter;

    @ApiModelProperty(value = "过滤会员的天数")
    private Integer filterDay;

    @ApiModelProperty(value = "过滤会员类型：1、已外呼（默认） 2、已接通")
    private Integer filterType;
    
}
