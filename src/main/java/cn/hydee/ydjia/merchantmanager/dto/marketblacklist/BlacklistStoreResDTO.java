package cn.hydee.ydjia.merchantmanager.dto.marketblacklist;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店配置 Response DTO
 *
 * <AUTHOR>
 * @since 2024-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BlacklistStoreResDTO extends BlacklistBaseResDTO {

    @ApiModelProperty("黑名单门店ID")
    private Integer id;

    @ApiModelProperty("黑名单ID")
    private Integer blacklistId;

    @ApiModelProperty("门店ID")
    private String storeId;

    @ApiModelProperty("门店编码")
    private String stCode;

    @ApiModelProperty("门店名称")
    private String stName;

}
