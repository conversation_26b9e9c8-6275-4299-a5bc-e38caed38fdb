package cn.hydee.ydjia.merchantmanager.dto.commission;

import cn.hydee.ydjia.merchantmanager.util.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/6/17   17:00
 * @since 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionCommodityRspDTO {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("商户编号")
    private String merCode;

    @ApiModelProperty("提成方案ID")
    private Integer commissionId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品图片")
    private String commodityImg;

    @ApiModelProperty("商品规格ID")
    private Long specId;

    @ApiModelProperty(value = "规格名称")
    private String specName;

    @ApiModelProperty(value = "商品参考价")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal commodityPrice;

    @ApiModelProperty("提成类型：1-比例；2-金额")
    private Integer commissionType;

    @ApiModelProperty("提成值")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal commissionValue;
}
