package cn.hydee.ydjia.merchantmanager.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @version 1.0
 * @Author: lisp
 * @Description: 会员充值
 * @Date: 2020/8/12
 */
@Data
public class MemberBalancePageReqDTO extends PageBase {


    @ApiModelProperty(value = "商户编码")
    @NotNull(message = "商户编码不能为空")
    private String merCode;

    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "日期")
    private String date;

}
