package cn.hydee.ydjia.merchantmanager.dto.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ColumnWidth(value = 20)
public class LiveCommodityExcelRspDTO {

    @ExcelProperty(value = "序号")
    private Integer index;

    @ExcelProperty(value = "商品名称")
    private String commodityName;

    @ExcelProperty(value = "商品编码")
    private String erpCode;

    @ExcelProperty(value = "规格型号")
    private String specName;

    @ExcelProperty(value = "参考价")
    private BigDecimal maPrice;

    @ExcelProperty(value = "商品销量")
    private Integer totalQuantity;

    @ExcelProperty(value = "订单量")
    private Integer orderCount;

    @ExcelProperty(value = "商品销售额")
    private String totalAmountStr;

    @ExcelProperty(value = "商品点击人次")
    private String viewersNum;


}
