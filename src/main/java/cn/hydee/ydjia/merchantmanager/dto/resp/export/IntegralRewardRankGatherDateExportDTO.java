package cn.hydee.ydjia.merchantmanager.dto.resp.export;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/17 14:01
 */
@Data
public class IntegralRewardRankGatherDateExportDTO {

   @ExcelProperty(value = "激励日期")
   @ColumnWidth(value = 20)
    private String createTime;

   @ExcelProperty(value = "厂家(供应商)")
   @ColumnWidth(value = 20)
    private String ispName;

   @ExcelProperty(value = "激励积分")
   @ContentStyle(dataFormat = 2)
    private BigDecimal rewardBooking;

}
