package cn.hydee.ydjia.merchantmanager.dto.resp.chain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/24 10:24
 */
@Data
public class CourseStatisticDetailRespDTO {


    @ApiModelProperty("服务商编码")
    private String ispCode;

    @ApiModelProperty(value = "课程名称")
    private String activityDetailName;

    @ApiModelProperty(value = "课程介绍")
    private String activityNote;

    @ApiModelProperty(value = "课程封面")
    private String cover;

    @ApiModelProperty(value = "上架状态（1未上架、3已上架")
    private Integer status;

    @ApiModelProperty(value = "首次上架时间")
    private Date firstReleaseTime;

    @ApiModelProperty(value = "修改时间。未下架状态时，表示下架时间")
    private Date updateTime;

    @ApiModelProperty(value = "课时数")
    private Integer courseCount;


    @ApiModelProperty(value = "活动id", notes = "")
    private Integer activityId;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime releaseTime;

    /**
     * 服务商
     */
    @ApiModelProperty(value = "服务商Logo")
    private String ispLogo;

    /**
     * 服务商名称
     */
    @ApiModelProperty(value = "服务商名称")
    private String ispName;


    @ApiModelProperty(value = "获取课程学习概况统计数据")
    private IspCourseOverviewStatisticsRespDTO statisticsRespDTO;
}
