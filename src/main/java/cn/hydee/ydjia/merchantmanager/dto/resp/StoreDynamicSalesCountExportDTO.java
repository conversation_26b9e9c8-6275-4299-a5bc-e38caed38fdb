package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hydee.ydjia.merchantmanager.util.MathUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/14 14:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreDynamicSalesCountExportDTO {

    @Excel(name = "门店名称", orderNum = "0", width = 20)
    private String storeName;

    @Excel(name = "门店编码", orderNum = "1", width = 20)
    private String storeCode;

    @Excel(name = "参与店员数", orderNum = "2", width = 20)
    private Integer employeeQty;

    @Excel(name = "动销店员数", orderNum = "3", width = 20)
    private Integer dynamicSalesEmpQty;

    @Excel(name = "动销店员参与率（%）", orderNum = "4", width = 20)
    private Integer dynamicSalesEmpQtyRate;

    @Excel(name = "打开店员数", orderNum = "5", width = 20)
    private Integer enjoyLoginEmpQty;

    @Excel(name = "打开店员数占比（%）", orderNum = "6", width = 20)
    private Integer enjoyLoginEmpQtyRate;

    @Excel(name = "随心看连续3天未登录店员数", orderNum = "7", width = 20)
    private Integer enjoyNotLoginEmpQty;

    @Excel(name = "实名认证店员数", orderNum = "8", width = 20)
    private Integer authEmployeeNum;

    @Excel(name = "随心看商品分享店员数", orderNum = "9", width = 20)
    private Integer enjoyCommShareEmpQty;

    @Excel(name = "随心看商品分享率（%）", orderNum = "10", width = 20)
    private Integer enjoyCommShareEmpQtyRate;

    @Excel(name = "微商城商品分享店员数", orderNum = "11", width = 20)
    private Integer mallCommShareEmpQty;

    @Excel(name = "微商城商品分享率（%）", orderNum = "12", width = 20)
    private Integer mallCommShareEmpQtyRate;

//    @Excel(name = "随心看优惠券分享店员数", orderNum = "9", width = 20)
//    private Integer enjoyCouponShareEmpQty;

    @Excel(name = "获得奖励店员数", orderNum = "13", width = 20)
    private Integer rewardEmpNum;

    @Excel(name = "提现店员数", orderNum = "14", width = 20)
    private Integer withdrawEmpNum;

//    @Excel(name = "视频学习店员数（%）", orderNum = "15", width = 20)
//    private BigDecimal videoStudyQtyRate;
//
//    @Excel(name = "文档学习店员数（%）", orderNum = "16", width = 20)
//    private BigDecimal documentStudyQtyRate;

    public BigDecimal getDynamicSalesEmpQtyRate() {
        return MathUtils.calculatePropdortion(dynamicSalesEmpQty, employeeQty);
    }

    public BigDecimal getEnjoyLoginEmpQtyRate() {
        return MathUtils.calculatePropdortion(enjoyLoginEmpQty, employeeQty);
    }

    public BigDecimal getEnjoyCommShareEmpQtyRate() {
        return MathUtils.calculatePropdortion(enjoyCommShareEmpQty, employeeQty);
    }

    public BigDecimal getMallCommShareEmpQtyRate() {
        return MathUtils.calculatePropdortion(mallCommShareEmpQty, employeeQty);
    }
}
