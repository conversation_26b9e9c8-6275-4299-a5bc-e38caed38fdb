package cn.hydee.ydjia.merchantmanager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 门店锁定
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/25 9:28
 */
@Data
public class StoreSpecUpdate {
    @ApiModelProperty(value = "门店ID")
    private String storeId;
    @ApiModelProperty(value = "规格ID列表")
    private Long specId;
    @ApiModelProperty(value = "商品基本信息ID", hidden = true)
    private Long commodityId;
    @ApiModelProperty(value = "商城表主键id")
    private Long storeSpecId;
    @NotNull(message = "锁定标志不可为空")
    @ApiModelProperty(value = "锁定标志")
    private Integer lockFlag;
    @ApiModelProperty(value = "ERP商品编码")
    private String erpCode;
}
