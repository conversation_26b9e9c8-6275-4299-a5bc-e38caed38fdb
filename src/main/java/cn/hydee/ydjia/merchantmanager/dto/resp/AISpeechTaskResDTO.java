package cn.hydee.ydjia.merchantmanager.dto.resp;

import cn.hydee.ydjia.merchantmanager.util.LocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @version 1.0
 * @Author: pengyayun
 * @Description:
 * @Date: 2022/1/12
 */
@Data
public class AISpeechTaskResDTO {
    @ApiModelProperty("任务主键Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long missionId;
    @ApiModelProperty("任务名称")
    private String missionName;
    @ApiModelProperty(value = "任务状态 1-未执行；2-执行中；3-已完成；4-已取消")
    private Integer taskStatus;
    @ApiModelProperty("成员数")
    private Integer memberNum;
    @ApiModelProperty("实际拨打数")
    private Integer actualNum;
    @ApiModelProperty("接通数")
    private Integer arrivedNum;
    @ApiModelProperty("接通率")
    private String arrivedAvg;
    @ApiModelProperty("平均通话时长")
    private String avgCall;
    @ApiModelProperty(value = "平均通话时长(X小时X分X秒)")
    private String avgCallStr;
    @ApiModelProperty(value = "总通话时长(秒)")
    private Integer totalCall;
    @ApiModelProperty(value = "总通话时长(X小时X分X秒)")
    private String totalCallStr;
    @ApiModelProperty("周期开始")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime timeStart;
    @ApiModelProperty("周期结束")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime timeEnd;
    @ApiModelProperty(value = "效果监测开始时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime activityBeginTime;
    @ApiModelProperty(value = "效果监测结束时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime activityEndTime;
    @ApiModelProperty("短信发送成功人数")
    private Integer smsSendSucceedNum;
    @ApiModelProperty("短信消耗数")
    private Integer smsUsedNum;
    @ApiModelProperty("拨打话术")
    private String bizType;
    @ApiModelProperty("话术id")
    private String verbalId;
    @ApiModelProperty(value = "ROI（销售）")
    private BigDecimal ROISaleAmount;
    @ApiModelProperty(value = "ROI（毛利额）")
    private BigDecimal ROIProfit;
    @ApiModelProperty(value = "创建人名称")
    private String createName;
    @ApiModelProperty(value = "创建人编码")
    private String createCode;
    
    
}
