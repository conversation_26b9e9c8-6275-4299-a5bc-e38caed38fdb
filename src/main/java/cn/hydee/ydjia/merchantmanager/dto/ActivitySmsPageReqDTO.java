package cn.hydee.ydjia.merchantmanager.dto;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/5/16
 */
@Data
public class ActivitySmsPageReqDTO extends PageBase {

    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty("短信任务名称")
    private String taskName;

    @ApiModelProperty(value = "活动开始时间")
    private String beginTime;

    @ApiModelProperty(value = "活动结束时间")
    private String endTime;

    @ApiModelProperty(value = "活动id")
    private Integer activityId;



}
