package cn.hydee.ydjia.merchantmanager.excel;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * excel中转换成key-value键值
 * <AUTHOR>
 * @date 15:37 2019/11/8
 **/
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelKeyValue {
    int start() default -1;
    int end() default -1;
}
