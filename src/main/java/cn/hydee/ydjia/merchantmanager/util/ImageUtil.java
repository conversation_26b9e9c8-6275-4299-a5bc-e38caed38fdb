package cn.hydee.ydjia.merchantmanager.util;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片处理类
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/22 13:52
 */
public class ImageUtil {

    private ImageUtil () {

    }

    public static boolean isSquare(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        return width == height;
    }

    public static boolean isSquare(InputStream stream) throws IOException {
        BufferedImage image = ImageIO.read(stream);
        return isSquare(image);
    }
}
