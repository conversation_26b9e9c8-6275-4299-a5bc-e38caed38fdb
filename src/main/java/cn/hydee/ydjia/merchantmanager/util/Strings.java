package cn.hydee.ydjia.merchantmanager.util;


import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/1/12 20:21
 */
public class Strings {

    private static final Pattern SPLIT_VARIABLE_PATTERN = Pattern.compile("(\\{\\$)(\\w+)+(\\})");

    /**
     *  ex: {$king}abc,{$queue}
     *  return ["king","queue"]
     * @param str
     * @return
     */
    public static List<String> splitVariable(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        Matcher matcher = SPLIT_VARIABLE_PATTERN.matcher(str);
        List<String> variables = Lists.newArrayList();
        while (matcher.find()) {
            variables.add(matcher.group(2));
        }
        return variables;
    }
}
