package cn.hydee.ydjia.merchantmanager.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description DateUtil
 * @create 2020/05/15 15:17
 */
@Slf4j
public class DateUtil {

    /**
     * 日期格式，年月日时分秒，年月日用横杠分开，时分秒用冒号分开
     * 例如：2005-05-10 23：20：00，2008-08-08 20:08:08
     */
    public static final String DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS = "yyyy-MM-dd HH:mm:ss";

    public static Date parseToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) return null;
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * transfer to Date
     *
     * @param strDate
     * @param format
     * @return
     * @throws ParseException
     */
    public static Date parse(String strDate, String format) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.parse(strDate);
    }

    public static String parse(Date data) {
        return parse(data, "yyyy-MM-dd HH:mm:ss");
    }

    public static String parse(Date data, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(data);
    }

    public static String parseString(LocalDateTime time) {
        return DateUtil.parseString(time, "yyyy-MM-dd HH:mm:ss");
    }

    public static String parseString(LocalDateTime time, String pattern) {
        if (ObjectUtils.isEmpty(time)) {
            return "";
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        return df.format(time);
    }
    /**
     * get age by date
     *
     * @param dateOfBirth
     * @return
     */
    public static int getAge(Date dateOfBirth) {
        int age = 0;
        Calendar born = Calendar.getInstance();
        Calendar now = Calendar.getInstance();
        if (dateOfBirth != null) {
            now.setTime(new Date());
            born.setTime(dateOfBirth);
            if (born.after(now)) {
                throw new IllegalArgumentException("年龄不能超过当前日期");
            }
            age = now.get(Calendar.YEAR) - born.get(Calendar.YEAR);
            int nowDayOfYear = now.get(Calendar.DAY_OF_YEAR);
            int bornDayOfYear = born.get(Calendar.DAY_OF_YEAR);
            if (nowDayOfYear < bornDayOfYear) {
                age -= 1;
            }
        }
        return age;
    }

    public static int getTimeDifference(Date endTime) {
        Assert.notNull(endTime, "EndTime is null");
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        now.setTime(new Date());
        end.setTime(endTime);
        return end.get(Calendar.DAY_OF_YEAR) - now.get(Calendar.DAY_OF_YEAR);
    }

    public static int getDayDifference(Date startTime, Date endTime) {
        Assert.notNull(startTime, "StartTime is null");
        Assert.notNull(endTime, "EndTime is null");
        long disSes = endTime.getTime() - startTime.getTime();
        return (int) (disSes / (1000 * 60 * 60 * 24));
    }

    public static Date parseStrToDate(String time, String timeFormat) {
        if (time == null || Strings.isBlank(time)) {
            return null;
        }

        Date date=null;
        try{
            DateFormat dateFormat=new SimpleDateFormat(timeFormat);
            date=dateFormat.parse(time);
        }catch(Exception e){
            log.error("DateUtil", e);
        }
        return date;
    }

    public static String parseDateToStr(Date time, String timeFormat){
        if(null == time){
            return "";
        }
        DateFormat dateFormat=new SimpleDateFormat(timeFormat);
        return dateFormat.format(time);
    }

    public static Date getNeedTime(Date date,int hour,int minute,int second){

        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY,hour);
        c.set(Calendar.MINUTE,minute);
        c.set(Calendar.SECOND,second);
        // 解决小数点问题
        return DateUtil.parseStrToDate(DateUtil.parseDateToStr(c.getTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
    }

    public static void main(String[] args) throws ParseException {
        int dayDifference = cn.hydee.ydjia.merchantmanager.util.DateUtil.getDayDifference(DateUtil.parse("2021-12-27", "yyyy-MM-dd"), DateUtil.parse("2022-01-02", "yyyy-MM-dd"));
        Date startTimeMoM = DateUtils.addDays(DateUtil.parse("2021-12-27", "yyyy-MM-dd"), -dayDifference - 1);
        Date endTimeMoM = DateUtils.addDays(DateUtil.parse("2022-01-02", "yyyy-MM-dd"), -dayDifference - 1);
        String formatStart = DateFormatUtils.format(startTimeMoM, cn.hydee.starter.util.DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        String formatend = DateFormatUtils.format(endTimeMoM, cn.hydee.starter.util.DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        System.out.println(formatStart + "   " + formatend);
    }

    /**
     * @Description: 获取指定月份的最后一天，日期格式：yyyy-mm
     * @Author: Kaven
     * @Date: 2023/4/6 16:57
     * @param [monthStr]
     * @return java.lang.String
     * @Exception
     */
    public static String getLastDayForMonth(String monthStr) {
        // 解析出年份和月份
        String[] array = monthStr.split("-");
        if (array.length < 2)
            return null;
        int year = Integer.parseInt(array[0]);
        int month = Integer.parseInt(array[1]);
        Calendar cal = Calendar.getInstance();
        // 设置年份
        cal.set(Calendar.YEAR, year);
        // 设置月份
        cal.set(Calendar.MONTH, month - 1);
        // 获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        // 格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    public static List<String> getNerDate30(){
        List<String> dates = new ArrayList<>();
        LocalDate localDate = LocalDate.now().minusDays(0);
        LocalDate afterDate = localDate.minusDays(29);

        //获取倒数30天
        List<LocalDate> localDates = new ArrayList<>();

        for (LocalDate currentdate = afterDate;
             currentdate.isBefore(localDate) ||
                     currentdate.isEqual(localDate);
             currentdate = currentdate.plusDays(1)) {
            LocalDate of = LocalDate.of(currentdate.getYear(), currentdate.getMonth(), currentdate.getDayOfMonth());
            localDates.add(LocalDate.of(currentdate.getYear(), currentdate.getMonth(), currentdate.getDayOfMonth()));
            dates.add(of.toString());
        }
        return dates;
    }
}
