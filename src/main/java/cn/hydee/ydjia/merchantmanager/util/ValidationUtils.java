package cn.hydee.ydjia.merchantmanager.util;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Iterator;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class ValidationUtils {

    public static Validator getValidator() {
        return validator;
    }

    static Validator validator;

    static {
        ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
        validator = validatorFactory.getValidator();
    }

    /**
     * 校验fein调用是否失败
     *
     * @param responseBase 调用结果
     */
    public static void checkResult(ResponseBase responseBase) {
        if (!responseBase.checkSuccess()) {
            log.error("checkSuccess failed {}", JSONObject.toJSONString(responseBase));
            throw WarnException.builder().code(responseBase.getCode()).
                    tipMessage(responseBase.getMsg()).build();
        }
    }

    /**
     * 抛出异常
     *
     * @param b          boolean值
     * @param localError 全局错误参数
     */
    public static void throwError(boolean b, LocalError localError) {
        if (b) {
            throw WarnException.builder().code(localError.getCode())
                    .tipMessage(localError.getMsg()).build();
        }
    }

    /**
     * 获取类校验结果
     *
     * @param model 校验实体
     * @param <T>   实体类
     * @return 错误信息
     */
    public static <T> String getValidatorReason(T model) {
        Set<ConstraintViolation<T>> validate = getValidator().validate(model);
        if (!validate.isEmpty()) {
            return groupReason(validate);
        }
        return null;
    }

    /**
     * 使用正则校验参数
     * @param param 参数
     * @param rgex 正则表达式
     * @return 结果
     */
    public static boolean checkRgex(String param,String rgex){
        Pattern pattern = Pattern.compile(rgex);
        Matcher matcher = pattern.matcher(param);
        return matcher.matches();
    }

    /**
     * 格式化校验结果信息
     *
     * @param result 校验结果
     * @param <T>    校验类
     * @return 原因
     */
    private static <T> String groupReason(Set<ConstraintViolation<T>> result) {
        Iterator<ConstraintViolation<T>> iterator = result.iterator();
        StringBuilder builder = new StringBuilder();
        while (iterator.hasNext()) {
            builder.append(";" + iterator.next().getMessage());
        }
        return builder.toString().substring(1);
    }


    private static Pattern FilePattern = Pattern.compile("[\\\\/:*?\"<>|]");

    public static String fileNameFilter(String str) {
        return str == null ? null : FilePattern.matcher(str).replaceAll("");
    }


}