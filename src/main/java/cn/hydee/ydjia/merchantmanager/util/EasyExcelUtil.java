package cn.hydee.ydjia.merchantmanager.util;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import java.io.OutputStream;
import java.util.List;

/**
 * 专用于EasyExcel导出模板的类
 *
 * <AUTHOR>
 */
public class EasyExcelUtil {

    /**
     * 导出空白模板，供用户写入
     *
     * @param outputStream      outputStream
     * @param head              列头信息
     * @param sheetName         sheet名
     * @param cellWriteHandlers 列处理handler
     */
    public static void writeExcelWithModel(OutputStream outputStream, List<List<String>> head, String sheetName, CellWriteHandler cellWriteHandlers, List<Object> data) {

        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 单元格策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 初始化表格样式
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        // 这里需要设置不关闭流
        EasyExcel.write(outputStream)
                .head(head)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .registerWriteHandler(cellWriteHandlers)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .autoCloseStream(Boolean.FALSE).sheet(sheetName)
                .doWrite(data);
    }
}