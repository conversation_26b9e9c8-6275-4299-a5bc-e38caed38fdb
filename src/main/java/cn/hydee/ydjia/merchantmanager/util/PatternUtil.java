package cn.hydee.ydjia.merchantmanager.util;

import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/1 12:09
 */
public class PatternUtil {

    public static boolean validMerCode(String merCode) {
        if (StringUtils.isEmpty(merCode)) {
            return false;
        }
        return Pattern.matches(LocalConst.MER_CODE_PATTERN, merCode);
    }
}
