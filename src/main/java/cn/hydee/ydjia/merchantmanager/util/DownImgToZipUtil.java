package cn.hydee.ydjia.merchantmanager.util;

import cn.hydee.ydjia.merchantmanager.dto.SdpUserInfoDTO;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Classname DownImgToZipUtil
 * @Description 打包下载图片成zip
 * @Date 2020/9/15 16:53
 * @Created lizhaoyang-2695
 */
public class DownImgToZipUtil {

    /**
     *
     * @param qrCodeList 图片集合
     * @return
     */
    public static void downImgToZipUtil(List<SdpUserInfoDTO> qrCodeList, String zipName){
        ZipOutputStream zos = null;
        try {
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            // 文件的名称
            String downloadFilename = zipName+".zip";
            // 转换中文否则可能会产生乱码
            downloadFilename = URLEncoder.encode(downloadFilename, "UTF-8");
            // 指明response的返回对象是文件流
            response.setContentType("application/octet-stream");
            // 设置在下载框默认显示的文件名
            response.setHeader("Content-Disposition", "attachment;filename=" + downloadFilename);
             zos = new ZipOutputStream(response.getOutputStream());
            for (int i = 0; i < qrCodeList.size(); i++) {
                URL url = new URL(qrCodeList.get(i).getQrCode());
                //在压缩文件中建立名字为XXX的文件
                zos.putNextEntry(new ZipEntry(qrCodeList.get(i).getUserName()+qrCodeList.get(i).getUserMobile() + ".jpg"));
                //读取图片
                BufferedImage buffImg = ImageIO.read(url);
                BufferedImage image = new BufferedImage(200, 200, BufferedImage.TYPE_INT_BGR);
                Graphics graphics = image.createGraphics();
                //绘制缩小后的图
                graphics.drawImage(buffImg, 0, 0, 200, 200, null);
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(image, "jpg", os);
                InputStream fis = new ByteArrayInputStream(os.toByteArray());
                byte[] buffer = new byte[1024];
                int r = 0;
                while ((r = fis.read(buffer)) != -1) {
                    zos.write(buffer, 0, r);
                }
                fis.close();
            }
            zos.flush();
        } catch (Exception e) {
        }finally {
            try {
                zos.close();
            }catch (Exception e){

            }
        }



    }

}
