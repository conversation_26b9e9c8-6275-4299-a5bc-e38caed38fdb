package cn.hydee.ydjia.merchantmanager.model.vo;

import java.util.List;

import cn.hydee.ydjia.merchantmanager.model.base.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class CommodityTypeVO extends BaseVO {

    private String id;
    private String name;
    private String merCode;
    private String parentId;
    private Integer type;
    private Integer sort;
    private Integer level;
    private String dimensionId;
    private String pic;
    @ApiModelProperty("分类关联商品数量")
    private Integer itemRefs;

    private List<CommodityTypeVO> children;


}
