package cn.hydee.ydjia.merchantmanager.config.ymlval;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 新零售后台的权限配置项
 */
@Data
@Component
@Configuration
@ConfigurationProperties(prefix = "merchant.meal-info")
public class NrIndexAuthConfig {

    /**
     * 套餐集
     */
    private List<String> meals;
}
