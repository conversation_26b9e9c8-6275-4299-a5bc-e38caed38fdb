package cn.hydee.ydjia.merchantmanager.config;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/7
 */
@Component
public class CommonEasyExcelListener<CommonNameExcelDto> extends AnalysisEventListener<CommonNameExcelDto> {
    @Override
    public void invoke(Object o, AnalysisContext analysisContext) {

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
