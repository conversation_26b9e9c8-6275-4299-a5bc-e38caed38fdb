package cn.hydee.ydjia.merchantmanager.config.ymlval;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * 新零售后台的常用功能事项配置项
 */
@Data
@Component
@Configuration
@ConfigurationProperties(prefix = "merchant.meal-default")
public class NrIndexMostUseItemConfig {

    /**
     * 常用项
     */
    private List<String> items;
}
