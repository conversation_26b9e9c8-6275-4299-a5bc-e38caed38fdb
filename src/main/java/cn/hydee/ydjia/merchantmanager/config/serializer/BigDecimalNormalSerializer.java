package cn.hydee.ydjia.merchantmanager.config.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class BigDecimalNormalSerializer extends JsonSerializer<BigDecimal> {

    @Override
    public void serialize(BigDecimal value, JsonGenerator jsonGenerator, SerializerProvider serializers) throws IOException {
        if (value == null) {
            jsonGenerator.writeObject(BigDecimal.ZERO);
        }
    }

    @Override
    public Class<BigDecimal> handledType() {
        return super.handledType();
    }
}
