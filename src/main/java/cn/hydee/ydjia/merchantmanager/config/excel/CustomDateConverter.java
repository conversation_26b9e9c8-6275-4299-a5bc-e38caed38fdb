package cn.hydee.ydjia.merchantmanager.config.excel;

import cn.hydee.ydjia.merchantmanager.util.DateUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.DateUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class CustomDateConverter<T> implements Converter<T> {

    @Override
    public Class supportJavaTypeKey() {
        return Date.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.DIRECT_STRING;
    }

    @Override
    public T convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (cellData.getData() instanceof Date) {
            // Date日期转换
            return (T)DateUtil.parse(cellData.getStringValue(), DateUtils.DATE_FORMAT_10);
        } else if (cellData.getData() instanceof LocalDate) {
            // LocalDate日期转换
            return (T) LocalDate.parse(cellData.getStringValue(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_10));
        } else if (cellData.getData() instanceof LocalTime) {
            // LocalTime时间转换
            return (T) LocalTime.parse(cellData.getStringValue(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        } else if (cellData.getData() instanceof LocalDateTime) {
            // LocalDateTime时间日期转换
            return (T) LocalDateTime.parse(cellData.getStringValue(), DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_19));
        }
        return null;
    }

    @Override
    public CellData convertToExcelData(T obj, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (obj instanceof Date) {
            // Date日期转换
            return new CellData<>(DateUtil.parse((Date) obj, DateUtils.DATE_FORMAT_10));
        } else if (obj instanceof LocalDate) {
            // LocalDate日期转换
            return new CellData<>(((LocalDate) obj).format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_10)));
        } else if (obj instanceof LocalTime) {
            // LocalTime时间转换
            return new CellData<>(((LocalTime) obj).format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        } else if (obj instanceof LocalDateTime) {
            // LocalDateTime时间日期转换
            return new CellData<>(((LocalDateTime) obj).format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_19)));
        }
        return new CellData<>(obj.toString());
    }
}
