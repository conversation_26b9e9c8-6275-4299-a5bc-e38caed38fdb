package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.im.ChannelManager;
import cn.hydee.ydjia.merchantmanager.service.ChannelManagerService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/12 15:51
 */
@RestController
@RequestMapping(value = "/${api.version}/channelManager")
@Api(value="商户自定义渠道",description = "商户自定义渠道")
public class YdjMerChannelController extends AbstractController {

    @Autowired
    private ChannelManagerService channelManagerService;

    @GetMapping("/list")
    public ResponseBase<List<ChannelManager>> queryChannelManagerByCondition(@RequestParam String merCode, @RequestParam Integer state) {

        return generateSuccess(channelManagerService.queryList(merCode, state));
    }


}
