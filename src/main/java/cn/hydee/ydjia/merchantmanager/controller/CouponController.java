package cn.hydee.ydjia.merchantmanager.controller;


import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.service.OssFileService;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.UUIDUtil;
import cn.hydee.ydjia.merchantmanager.config.CouponConfig;
import cn.hydee.ydjia.merchantmanager.config.SpringContextHolder;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.*;
import cn.hydee.ydjia.merchantmanager.dto.coupon.BatchDeleteRequest;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.coupon.CouponProductQueryRequest;
import cn.hydee.ydjia.merchantmanager.dto.resp.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.market.coupon.CouponBaseResDTO;
import cn.hydee.ydjia.merchantmanager.enums.*;
import cn.hydee.ydjia.merchantmanager.excel.AnalysisRule;
import cn.hydee.ydjia.merchantmanager.excel.Correspondence;
import cn.hydee.ydjia.merchantmanager.excel.PositionCorrespondence;
import cn.hydee.ydjia.merchantmanager.excel.WDWUtil;
import cn.hydee.ydjia.merchantmanager.feign.*;
import cn.hydee.ydjia.merchantmanager.repository.YdjTaskRepo;
import cn.hydee.ydjia.merchantmanager.service.CouponService;
import cn.hydee.ydjia.merchantmanager.service.MerchantPlatformService;
import cn.hydee.ydjia.merchantmanager.util.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.*;
import static cn.hydee.ydjia.merchantmanager.util.LocalConst.HEAD_MER_CODE_KEY;
import static cn.hydee.ydjia.merchantmanager.util.LocalConst.HYDEE;
import static cn.hydee.ydjia.merchantmanager.util.ValidationUtils.checkResult;
import static com.baomidou.mybatisplus.core.toolkit.StringPool.SLASH;

/**
 * 优惠券管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/6 16:11
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/coupon")
@Api(value = "优惠券", description = "优惠券")
public class CouponController extends AbstractController {

    @Autowired
    private CouponService couponService;

    @Autowired
    private CommoditySpecClient commoditySpecClient;

    @Autowired
    private CommodityTypeClient commodityTypeClient;

    @Autowired
    private StoreClient storeClient;

    @Value("${excel.import.commodityLimit}")
    private Integer commodityLimit;

    @Value("${ydj-wee-chat.coupon-url}")
    private String domainName;

    @Autowired
    private MerchantPlatformService merchantService;

    @Autowired
    private OssFileService ossFileService;
    private static final String PUBLIC_PATH = "public";
    @Value("${spring.application.name}")
    private String appName;

    @Autowired
    private MemberDataClient memberDataClient;

    @Autowired
    private CouponConfig couponConfig;

    @Autowired
    private DataCenterClient dataCenterClient;

    @Autowired
    private CouponClient couponClient;


    @Autowired
    private ImportUtil importUtil;

    @Autowired
    private MarketExportClient marketExportClient;

    @Resource
    private MarketDataStatisticsClient marketDataStatisticsClient;
/*    @ApiOperation(
            value = "查优惠券列表",
            notes = "查优惠券列表",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_search")
    public ResponseBase<List<CouponResDTO>> queryCouponList(@Valid @RequestBody CouponQueryDTO couponQueryDTO, BindingResult result) {
        this.checkValid(result);
        return generateSuccess(couponService.queryCouponList(couponQueryDTO));
    }*/

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }


    // todo zyh
    @ApiOperation(
            value = "优惠券列表",
            notes = "优惠券列表",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_search")
    public ResponseBase queryCouponList(@Valid @RequestBody CouponQueryDTO couponQueryDTO, BindingResult result) {
        checkValid(result);
        if(Objects.isNull(couponQueryDTO.getBusinessScenario())){
            couponQueryDTO.setBusinessScenario(1);
        }
        return couponService.queryCouponList(couponQueryDTO);
    }

    @ApiOperation(value = "商城首页设置查询优惠券推广信息")
    @PostMapping(value = "/coupon-link")
    public ResponseBase<Page<CouponResDTO>> getCouponLink(
            @Valid @RequestBody MarketingActivitiesDTO req, BindingResult result) {
        checkValid(result);
        return generateSuccess(couponService.getCouponLink(req));
    }





    /*@ApiOperation(
            value = "根据id以及状态修改优惠券状态",
            notes = "根据id以及状态修改优惠券状态",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/updateCouponStauts/{id}/{status}")
    public ResponseBase updateCouponStauts(@PathVariable Long id,@PathVariable int status) {
        ResponseBase responseBase = couponService.updateCouponStauts(id,status);
        return responseBase;
    }*/


    //todo zyh
    @ApiOperation(value = "查询单个优惠券详情")
    @GetMapping("/{id}")
    public ResponseBase<CouponBaseExtendDTO> queryCoupon(@PathVariable Integer id) {
        ResponseBase<CouponBaseExtendDTO> responseBase = couponService.queryCoupon(id);
        if (responseBase.getData() != null) {
            CouponBaseExtendDTO couponBaseExtendDTO = responseBase.getData();
            if (StringUtils.isNotBlank(couponBaseExtendDTO.getGiftId())) {
                String giftId = couponBaseExtendDTO.getGiftId();
                String merCode = couponBaseExtendDTO.getMerCode();
                SpecSearchDTO specSearchDTO = new SpecSearchDTO();
                specSearchDTO.setErpCode(giftId);
                specSearchDTO.setMerCode(merCode);
                specSearchDTO.setHasSpec(true);
                ResponseBase<PageDTO<StoreSpec>> base = commoditySpecClient.getCommoditySpec(specSearchDTO);
                if (base.getData() != null && base.getData().getData() != null && base.getData().getData().size() > 0) {
                    List<StoreSpec> list = base.getData().getData();
                    GiftResDTO giftResDTO = new GiftResDTO();
                    log.info("[list_coupontest]:" + list);
                    if (list.size() > 0) {
                        giftResDTO.setErpCode(list.get(0).getErpCode());
                        giftResDTO.setBrandName(list.get(0).getBrandName());
                        giftResDTO.setManufacture(list.get(0).getManufacture());
                        giftResDTO.setMPrice(list.get(0).getMPrice());
                        giftResDTO.setName(list.get(0).getName());
                        giftResDTO.setPicUrl(list.get(0).getPicUrl());
                        giftResDTO.setPrice(list.get(0).getPrice());
                        giftResDTO.setSpecId(list.get(0).getId());
                        giftResDTO.setSpecSkuList(list.get(0).getSpecSkuList());

                    }
                    couponBaseExtendDTO.setGiftResDTO(giftResDTO);
                }
            }
            if(!ObjectUtils.isEmpty(couponBaseExtendDTO.getIspCode())){
                couponBaseExtendDTO.setMerchantRule(Integer.parseInt(couponBaseExtendDTO.getIspCode().substring(LocalConst.STATUS_ZERO, LocalConst.STATUS_ONE)));
                if(couponBaseExtendDTO.getIspCode().length() >= LocalConst.STATUS_TWO){
                    couponBaseExtendDTO.setSpCodes(Arrays.asList(couponBaseExtendDTO.getIspCode().substring(LocalConst.STATUS_TWO).split(LocalConst.COMMA_SPLIT)));
                }
            }
            initTypeCodes(couponBaseExtendDTO.getListCouponProductTypeEntity());
        }
        log.info("[responseBase_coupontest]:" + responseBase);
        return responseBase;
    }

    public void initTypeCodes(List<CouponProductTypeDTO> productTypeList) {
        if (CollectionUtils.isEmpty(productTypeList)) {
            return;
        }

        CommodityTypeQueryDTO queryDTO = new CommodityTypeQueryDTO();
        queryDTO.setMerCode(HYDEE);
        queryDTO.setType("1");
        queryDTO.setIds(productTypeList.parallelStream().map(CouponProductTypeDTO::getTypeCode).collect(Collectors.toList()));
        ResponseBase<Map<String, MultilevelTypeDTO>> typeResponseBase = commodityTypeClient.getParentTypeListByCondition(queryDTO);
        checkResult(typeResponseBase);
        Map<String, MultilevelTypeDTO> multilevelTypeMap;
        if (!CollectionUtils.isEmpty(multilevelTypeMap = typeResponseBase.getData())) {
            productTypeList.forEach(couponProductTypeDTO -> {
                MultilevelTypeDTO multilevelTypeDTO = multilevelTypeMap.get(couponProductTypeDTO.getTypeCode());
                if (Objects.nonNull(multilevelTypeDTO)) {
                    List<String> typeList = Lists.newArrayList(multilevelTypeDTO.getId());

                    while (multilevelTypeDTO.getChild() != null) {
                        multilevelTypeDTO = multilevelTypeDTO.getChild();
                        typeList.add(multilevelTypeDTO.getId());
                    }
                    couponProductTypeDTO.copyTypeCodes(typeList);
                }
            });
        }
    }

    @ApiOperation(
            value = "根据id删除优惠券",
            notes = "根据id删除优惠券",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/deleteCoupon")
    public ResponseBase deleteCouponExtend(@RequestParam Integer id,
                                           @RequestHeader String merCode) {
        return couponService.deleteCouponExtend(merCode, id);
    }

    @ApiOperation(
            value = "判断优惠券是否绑定活动",
            notes = "判断优惠券是否绑定活动",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/checkCouponBindActivity")
    public ResponseBase<Boolean> checkCouponBindActivity(@RequestParam Integer id) {
        return couponService.checkCouponBindActivity(id);
    }

    @ApiOperation(value = "判断优惠券是否绑定活动以及是否被领取")
    @GetMapping("/checkCouponBindActivityAndHasReceive/{id}")
    public ResponseBase<Boolean> checkCouponBindActivityAndHasReceive(@PathVariable("id") Integer id, @RequestHeader(HEAD_MER_CODE_KEY) String merCode) {
        return generateSuccess(couponService.checkCouponBindActivityAndHasReceive(merCode, id));
    }

    @ApiOperation(value = "查询优惠券绑定的活动列表")
    @GetMapping("/queryCouponBindActivity/{couponId}")
    public ResponseBase<List<ActivityDetailRespDTO>> queryCouponBindActivity(@PathVariable("couponId") Integer couponId) {
        return generateSuccess(couponService.queryCouponBindActivity(couponId));
    }


    //todo zyh
    @ApiOperation(
            value = "新增优惠券",
            notes = "新增优惠券",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/createCoupon")
    public ResponseBase createCoupon(@Valid @RequestBody CouponReqDTO couponReqDTO) {
        couponReqDTO.initGift();
        return couponService.createCoupon(couponReqDTO);
    }


    //todo zyh
    @ApiOperation(
            value = "修改优惠券",
            notes = "修改优惠券",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/updateCoupon")
    public ResponseBase updateCoupon(@Valid @RequestBody CouponReqDTO couponReqDTO,
                                     BindingResult result) {
        checkValid(result);
        couponReqDTO.initGift();
        return couponService.updateCoupon(couponReqDTO);
    }

    @ApiOperation(
            value = "(B端发券)历史记录列表",
            notes = "(B端发券)历史记录列表",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/couponHistoryList")
    public ResponseBase<Page<CouponSendHistoryDTO>> couponHistoryList(@Valid @RequestBody MercodeReqDTO mercodeReqDTO) {
        return couponService.couponHistoryList(mercodeReqDTO);
    }

    @ApiOperation(value = "导出历史发券记录")
    @PostMapping("/exportCouponHistoryList")
    public ResponseBase<Boolean> exportCouponHistoryList(@Valid @RequestBody MercodeReqDTO mercodeReqDTO, @RequestHeader String userName) {
        mercodeReqDTO.setUserName(userName);
        CommonExportReqDTO commonExport = new CommonExportReqDTO()
                .setBusinessType(COUPON_HISTORY_LIST_EXPORT)
                .setQueryDTO(mercodeReqDTO)
                .setMerCode(mercodeReqDTO.getMerCode())
                .setOperator(userName);
        return marketDataStatisticsClient.commonExport(commonExport);
    }

    @ApiOperation(
            value = "(B端发券)根据历史id查询规则列表",
            notes = "(B端发券)根据历史id查询规则列表",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/listCouponHistoryDetail")
    public ResponseBase listCouponHistoryDetail(@Valid @RequestBody CommonReqIntegerDTO commonReqIntegerDTO) {
        return couponService.listCouponHistoryDetail(commonReqIntegerDTO);
    }

    @ApiOperation(
            value = "(B端发券)删除历史记录",
            notes = "(B端发券)删除历史记录",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/deleteSendHistory")
    public ResponseBase<Boolean> deleteSendHistory(@Valid @RequestBody BatchDeleteRequest request,
                                                   @RequestHeader String merCode,
                                                   BindingResult result) {
        checkValid(result);
        request.setMerCode(merCode);
        return couponService.deleteSendHistory(request);
    }

    @ApiOperation(
            value = "(B端发券)查询历史发券信息",
            notes = "(B端发券)查询历史发券信息",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/getCouponHistoryInfo")
    public ResponseBase<CouponSendHistoryDTO> getCouponHistoryInfo(@RequestParam Integer id, @RequestHeader String merCode) {
        ResponseBase<CouponSendHistoryDTO> couponHistoryInfo = couponService.getCouponHistoryInfo(id, merCode);
        if (couponHistoryInfo.checkSuccess() && Objects.nonNull(couponHistoryInfo.getData()) && StringUtils.isNotBlank(couponHistoryInfo.getData().getSmsTaskBatchNo())) {
            // 查询发券短信信息
            DataCenterSmsStatisticsReq dataCenterSmsStatisticsReq = new DataCenterSmsStatisticsReq();
            dataCenterSmsStatisticsReq.setMerCode(merCode);
            dataCenterSmsStatisticsReq.setBatchNoList(Lists.newArrayList(couponHistoryInfo.getData().getSmsTaskBatchNo()));
            ResponseBase<List<BatchSmsStatisticsResDTO>> statisticsResponseBase = dataCenterClient.queryBatchSmsStatistics(dataCenterSmsStatisticsReq);
            log.info("查询发券详情, 查询数仓短信统计数据接口调用result={}", JSON.toJSONString(statisticsResponseBase));
            if (statisticsResponseBase.checkSuccess()) {
                if (!CollectionUtils.isEmpty(statisticsResponseBase.getData())) {
                    couponHistoryInfo.getData().setSmsUsedNum(statisticsResponseBase.getData().get(0).getSmsUsedNum());
                }
            }
        }
        return couponHistoryInfo;
    }

    @ApiOperation(
            value = "优惠卷推广获取二维码",
            notes = "参数：couponId（优惠卷id）、merCode（商户编码）、activityId（活动id）",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/queryQrCode")
    public ResponseBase<CouponQrCodeDto> queryQrCode(@RequestParam String couponId, @RequestParam String merCode, @RequestParam String activityId, @RequestParam String activityType,
                                                     @RequestParam(required = false) String sourceChannel) {

        CouponQrCodeDto couponQrCodeDto = new CouponQrCodeDto();
        String url = domainName;
        if (merchantService.isOpenProduct(MerchantProductEnum.MALL.getCode(), merCode) > 0) {
            url = url + "/h5/home/<USER>";
            couponQrCodeDto.setAppStr("home/index");
        } else {
            url = url + "/h5/couponCenter/getCoupon";
            couponQrCodeDto.setAppStr("couponCenter/getCoupon");
        }
        url = url + "?merCode=" + merCode + "&couponId=" + couponId + "&activityId=" + activityId + "&activityType=" + activityType;

        if (StringUtils.isNotBlank(sourceChannel)) {
            url = url + "&fromChannel=" + sourceChannel;
        }
        log.info("拼接地址：" + url);
        String imgUrl = QrCodeImgUtil.getQrCodeImg(url);
        imgUrl = imgUrl.replaceAll("\r\n", "");
        couponQrCodeDto.setQrCodeImg("data:image/png;base64," + imgUrl);
        couponQrCodeDto.setQrcodeUrl(url);
        //跟前端约定
        return generateSuccess(couponQrCodeDto);

    }

    @ApiOperation(
            value = "优惠卷推广获取二维码(小程序)",
            notes = "参数：couponId（优惠卷id）、merCode（商户编码）、activityId（活动id）、activityType（活动类型）、appUrl（小程序地址）",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/getAppletsQrCode")
    public ResponseBase<String> getAppletsQrCode(QrCodeDto qrCodeDto, @RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName) {
        qrCodeDto.setOperator(userName);
        return couponService.getAppletsQrCode(qrCodeDto);
    }

    @ApiOperation(value = "检验批量选择门店的excel文件数据正确性")
    @PostMapping("/checkStoreExcel")
    public ResponseBase checkStoreExcel(@RequestHeader(LocalConst.HEAD_MER_CODE_KEY) String merCode,
                                        @RequestPart("excelFile") MultipartFile multipartFile) throws IOException {
        ActivityStoreRespDTO storeRespDTO = new ActivityStoreRespDTO();
        storeRespDTO.setCorrectList(Collections.emptyList());
        storeRespDTO.setErrorList(Collections.emptyList());
        // 解析excel文件得到门店编码
        Correspondence<ExcelObject> correspondence = new PositionCorrespondence<>(ExcelObject.class);
        AnalysisRule rule = new AnalysisRule();
        List<List<String>> list = correspondence.resolveContent(multipartFile.getInputStream(), rule,
                WDWUtil.isExcel2003(Objects.requireNonNull(multipartFile.getOriginalFilename())));
        if (CollectionUtils.isEmpty(list)) {
            return generateSuccess(storeRespDTO);
        }
        list.remove(0);
        List<String> codeList = list.stream()
                .map(sonList -> sonList.get(0))
                .filter(code -> !StringUtils.isBlank(code))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codeList)) {
            return generateSuccess(storeRespDTO);
        }
        // 调用门店查询接口，校验门店编码集合
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        // 优惠券只查询门店，不查询仓库
        queryStoreDTO.setOnlyStore(1);
        queryStoreDTO.setMerCode(merCode);
        queryStoreDTO.setStCodeList(codeList);
        queryStoreDTO.setPageSize(codeList.size());
        ResponseBase<PageDTO<StoreResDTO>> response = storeClient.queryStoreByCondition(queryStoreDTO);
        List<StoreResDTO> storeRespList = response.getData().getData();
        if (CollectionUtils.isEmpty(storeRespList)) {
            storeRespDTO.setErrorList(codeList);
            return generateSuccess(storeRespDTO);
        }
        List<String> storeCodeRespList = storeRespList.stream().map(StoreResDTO::getStCode).collect(Collectors.toList());
        codeList.removeAll(storeCodeRespList);
        // 封装返回校验结果
        storeRespDTO.setCorrectList(storeRespList);
        storeRespDTO.setErrorList(codeList);
        return generateSuccess(storeRespDTO);
    }

    @ApiOperation(value = "解析批量选择商品excel文件数据")
    @PostMapping("/resolveCommodityExcel")
    public ResponseBase<ActivityCommodityRespDTO> checkStoreExcel(@RequestPart("file") MultipartFile multipartFile) throws IOException {
        // 解析excel文件得到商品编码
        Correspondence<ExcelObject> correspondence = new PositionCorrespondence<>(ExcelObject.class);
        AnalysisRule rule = new AnalysisRule();
        List<List<String>> list = correspondence.resolveContent(multipartFile.getInputStream(), rule,
                WDWUtil.isExcel2003(Objects.requireNonNull(multipartFile.getOriginalFilename())));
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_03);
        AssertUtils.state(list.get(0).size() >= 2
                        && list.get(0).get(0).equals("erpCode")
                        && list.get(0).get(1).equals("name"),
                ErrorType.COUPON_ERROR_02);
        list.remove(0);
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_03);
        AssertUtils.state(list.size() <= commodityLimit, ErrorType.COUPON_ERROR_04);
        List<ActivityCommodityRespDTO.ActivityCommodityImportDTO> commodityList = list.parallelStream()
                .map(sonList -> {
                    ActivityCommodityRespDTO.ActivityCommodityImportDTO commodityDTO =
                            new ActivityCommodityRespDTO.ActivityCommodityImportDTO();
                    commodityDTO.setErpCode(sonList.get(0));
                    commodityDTO.setName(sonList.get(1));
                    return commodityDTO;
                })
                .filter(dto -> !StringUtils.isBlank(dto.getErpCode()) && !StringUtils.isBlank(dto.getName()))
                .filter(distinctByKey(ActivityCommodityRespDTO.ActivityCommodityImportDTO::getErpCode))
                .collect(Collectors.toList());
        ActivityCommodityRespDTO respDTO = new ActivityCommodityRespDTO();
        respDTO.setList(commodityList);
        respDTO.setLength(commodityList.size());
        return generateSuccess(respDTO);
    }

    @ApiOperation("礼品券可用商品")
    @PostMapping("/commodityToGift")
    public ResponseBase<PageDTO<ActivitySpecDTO>> commodityToGift(
            @Valid @RequestBody CommodityQueryDTO req, BindingResult result) {
        checkValid(result);
        return generateSuccess(couponService.getGiftCouponCommodity(req));
    }

    @ApiOperation(value = "表格导入会员功能")
    @PostMapping("/importMemberInfo")
    public ResponseBase<List<String>> importMemberInfo(@RequestPart("excelFile") MultipartFile multipartFile,
                                                       @RequestHeader String userName,
                                                       @RequestHeader String merCode) throws IOException {
        // 解析excel文件得到门店编码
        Correspondence<ExcelObject> correspondence = new PositionCorrespondence<>(ExcelObject.class);
        AnalysisRule rule = new AnalysisRule();
        List<List<String>> list = correspondence.resolveContent(multipartFile.getInputStream(), rule,
                WDWUtil.isExcel2003(Objects.requireNonNull(multipartFile.getOriginalFilename())));
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_02);
        list.remove(0);
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_03);
        List<String> codeList = list.stream()
                .map(sonList -> sonList.get(0))
                .filter(code -> !StringUtils.isBlank(code))
                .distinct()
                .collect(Collectors.toList());
        if(codeList.size() >= couponConfig.getExportMemberLimitNum()){
            throw WarnException.builder().code(ErrorType.COUPON_ERROR_10.getCode()).tipMessage(String.format(ErrorType.COUPON_ERROR_10.getMsg(), couponConfig.getExportMemberLimitNum())).build();
        }

        CouponBatchSendRequest couponBatchSendRequest = new CouponBatchSendRequest();
        couponBatchSendRequest.setMerCode(merCode);
        couponBatchSendRequest.setUserName(userName);

        int min = Math.min(codeList.size(), couponConfig.getMemberQueryLimitNum());
        Iterator<String> iterator = codeList.iterator();
        List<String> memberCardList = Lists.newLinkedList();
        List<String> result = Lists.newLinkedList();

        do {

            for (int i = 0; i < min; i++) {
                memberCardList.add(iterator.next());
                iterator.remove();
            }
            couponBatchSendRequest.setMemberCardList(memberCardList);
            couponBatchSendRequest.setPageSize(min);
            PageDTO<MemberSimpleInfoDTO> pageDTO = couponService.queryMemberListByType(TBean.copy(couponBatchSendRequest,
                    QueryMemberByConditionOrCrowdDTO::new));
            if (!CollectionUtils.isEmpty(pageDTO.getData())) {
                result.addAll(pageDTO.getData().stream().map(MemberSimpleInfoDTO::getMemberCardNumber).collect(Collectors.toList()));
            }
            min = Math.min(codeList.size(), couponConfig.getMemberQueryLimitNum());
            memberCardList.clear();

        } while (codeList.size() > 0);

        return generateSuccess(result);
    }

    @ApiOperation(value = "获取表格导入会员人数")
    @PostMapping("/getImportMemberInfoSize")
    public ResponseBase<Map<String, String>> getImportMemberInfoSize(@RequestPart("excelFile") MultipartFile multipartFile,
                                                                     @RequestHeader String merCode) throws IOException {
        return generateSuccess(importUtil.importData(multipartFile, merCode, ImportMemberTypeEnum.MEMBER_CARD));
    }

    @ApiOperation(value = "获取表格导入会员人数")
    @PostMapping("/getImportMemberInfoSizeBac")
    public ResponseBase<Map<String, String>> getImportMemberInfoSizeBac(@RequestPart("excelFile") MultipartFile multipartFile,
                                                                     @RequestHeader String merCode) throws IOException {
        // 解析excel文件得到门店编码
        Correspondence<ExcelObject> correspondence = new PositionCorrespondence<>(ExcelObject.class);
        AnalysisRule rule = new AnalysisRule();
        List<List<String>> list = correspondence.resolveContent(multipartFile.getInputStream(), rule,
                WDWUtil.isExcel2003(Objects.requireNonNull(multipartFile.getOriginalFilename())));
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_02);
        List<String> subList = list.get(0);
        AssertUtils.notEmpty(subList, ErrorType.COUPON_ERROR_02);
        if(subList.size() > 1 || !subList.get(0).equals("会员卡号")){
            throw ExceptionUtil.getWarnException(ErrorType.IMPORT_TEMPLATE_TABLE_NAME_ERROR);
        }
        list.remove(0);
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_03);
        List<String> codeList = list.stream()
                .map(sonList -> sonList.get(0))
                .filter(code -> !StringUtils.isBlank(code))
                .distinct()
                .collect(Collectors.toList());

        if(codeList.size() >= couponConfig.getExportMemberLimitNum()){
            throw WarnException.builder().code(ErrorType.COUPON_ERROR_10.getCode()).tipMessage(String.format(ErrorType.COUPON_ERROR_10.getMsg(), couponConfig.getExportMemberLimitNum())).build();
        }

        CouponBatchSendRequest couponBatchSendRequest = new CouponBatchSendRequest();
        couponBatchSendRequest.setMerCode(merCode);
        int min = Math.min(codeList.size(), couponConfig.getMemberQueryLimitNum());
        int allSize = codeList.size();
        Iterator<String> iterator = codeList.iterator();
        List<String> memberCardList = Lists.newLinkedList();
        List<String> existMember = Lists.newLinkedList();
        List<String> notExitMember = Lists.newLinkedList();
        int size = 0;
        do {
            for (int i = 0; i < min; i++) {
                memberCardList.add(iterator.next());
                iterator.remove();
            }
            couponBatchSendRequest.setMemberCardList(memberCardList);
            couponBatchSendRequest.setPageSize(min * 2);// 设置大一点的页值，避免ES重复数据
            PageDTO<MemberSimpleInfoDTO> pageDTO = couponService.queryMemberSizeByType(TBean.copy(couponBatchSendRequest,
                    QueryMemberByConditionOrCrowdDTO::new));
            int count = 0;
            if (!CollectionUtils.isEmpty(pageDTO.getData())) {
                count = (int) pageDTO.getData().stream().distinct().count();
                List<String> exitMemberList = pageDTO.getData().stream().map(MemberSimpleInfoDTO::getMemberCardNumber).collect(Collectors.toList());
                existMember.addAll(exitMemberList);
                List<String> fail = memberCardList.stream().filter(e -> !exitMemberList.contains(e)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(fail)) {
                    notExitMember.addAll(fail);
                }
            } else {
                notExitMember.addAll(memberCardList);
            }
            size += count;
            log.info("getImportMemberInfoSize,查询ES会员人数size:{},count:{}", size, count);
            min = Math.min(codeList.size(), couponConfig.getMemberQueryLimitNum());
            memberCardList.clear();
        } while (codeList.size() > 0);
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("size", size + "");
        resultMap.put("failSize", (allSize - size) + "");

        // 上传文件
        if (org.springframework.util.StringUtils.isEmpty(merCode)) {
            merCode = PUBLIC_PATH;
        }
        String suffix = "";
        String originalFilename = multipartFile.getOriginalFilename();
        if (!org.springframework.util.StringUtils.isEmpty(originalFilename)) {
            int i = originalFilename.lastIndexOf(".");
            if (i < 1) {
                throw WarnException.builder()
                        .code(LocalError.FOLDER_FORMAT_ERROR.getCode())
                        .tipMessage(LocalError.FOLDER_FORMAT_ERROR.getMsg()).build();
            }
            int length = originalFilename.length();
            suffix = originalFilename.substring(i, length);
        }
        String fileName = appName + "/" + merCode + "/" + DateUtil.getTodayString() + "/" +
                UUIDUtil.generateUuid() + suffix;
        try {
            // 上传成功会员文件
            if (!CollectionUtils.isEmpty(existMember)) {
                existMember = existMember.stream().distinct().collect(Collectors.toList());
                List<MemberSuccessImportExcelRespDTO> respDTOS = existMember.stream().map(e -> new MemberSuccessImportExcelRespDTO()
                        .setKey(e)).collect(Collectors.toList());
                File file = FileUtil.writeExcel("导入成功数据", MemberSuccessImportExcelRespDTO.class, respDTOS, "", null);
                ossFileService.upload(file, fileName);
                FileUtil.deleteRecursively(file);
                resultMap.put("fileName", fileName);
            }
            // 上传失败会员文件
            if (!CollectionUtils.isEmpty(notExitMember)) {
                List<MemberImportExcelRespDTO> respDTOS = notExitMember.stream().map(e -> {
                    return new MemberImportExcelRespDTO()
                            .setKey(e)
                            .setReason("会员不存在");
                }).collect(Collectors.toList());
                File file = FileUtil.writeExcel("导入失败数据", MemberImportExcelRespDTO.class, respDTOS, "", null);
                String key = appName + SLASH + merCode + SLASH + System.currentTimeMillis() + SLASH + "导入失败数据" + suffix;
                ossFileService.upload(file, key);
                FileUtil.deleteRecursively(file);
                resultMap.put("failMemberFileName", key);
            }
        } catch (Exception e) {
            throw WarnException.builder().code(LocalError.FILE_UPLOAD_ERROR.getCode()).
                    tipMessage(LocalError.FILE_UPLOAD_ERROR.getMsg()).build();
        }
        return generateSuccess(resultMap);
    }

    @ApiOperation(value = "分页查询优惠券的可用商品列表")
    @PostMapping("/pageCouponProductInfo")
    ResponseBase<PageDTO<CouponProductImageCacheDTO>> pageCouponProductInfo(@RequestBody @Valid CouponProductQueryRequest request,
                                                                            @RequestHeader(HEAD_MER_CODE_KEY) String merCode,
                                                                            BindingResult bindingResult) {
        checkValid(bindingResult);
        request.setMerCode(merCode);
        return generateSuccess(couponService.pageCouponProductInfo(request));
    }

    @ApiOperation(value = "导出手动发券活动优惠券列表")
    @PostMapping("/sendCouponActivityExportCouponList")
    public ResponseBase sendCouponActivityExportCouponList(@RequestBody CouponDetailsQueryRequest request,
                                        @RequestHeader("userName") String userName) {
//        return reportUtil.validAndAddTask(() -> {
//            ResponseBase<PageDTO<CouponDetailDTO>> responseBase = couponClient.queryCouponDetailsPage(request);
//            if (!responseBase.checkSuccess()) {
//                log.warn("查询手动发券活动优惠券列表:{}", JSON.toJSONString(responseBase));
//                throw WarnException.builder().code(responseBase.getCode()).tipMessage(responseBase.getMsg()).build();
//            }
//            if (null == responseBase.getData() || CollectionUtils.isEmpty(responseBase.getData().getData())) {
//                log.warn("导出的数据为空");
//                throw WarnException.builder().code(LocalError.EXCEL_ROW_NULL.getCode()).tipMessage(LocalError.EXCEL_ROW_NULL.getMsg()).build();
//            }
//            return Integer.parseInt(responseBase.getData().getTotalCount() + "");
//        }, SEND_COUPON_ACTIVITY_COUPON_EXPORT, userName, request.getMerCode(), "sendCouponActivity", request);
        request.setUserName(userName);
        CouponManageController couponManageController = SpringContextHolder.getBean(CouponManageController.class);
        if (couponManageController.preCheckBlurAndStop(request)) {
            throw WarnException.builder().code(LocalError.EXCEL_ROW_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_ROW_NULL.getMsg()).build();
        }
        request.setSortDescByProvideTime(false);

        CommonExportReqDTO commonExport = new CommonExportReqDTO()
                .setBusinessType(SEND_COUPON_ACTIVITY_COUPON_EXPORT)
                .setQueryDTO(request)
                .setMerCode(request.getMerCode())
                .setOperator(userName)
                .setExtended1(request.getThirdSendId());
        return marketDataStatisticsClient.commonExport(commonExport);
    }

    @ApiOperation(value = "查询服务商列表")
    @PostMapping("/getSPMerchantList")
    ResponseBase<PageDTO<SPMerchantDetailInfoResDTO>> getSPMerchantList(@RequestBody SPMerchantListQryReqDto request){
        request.setAuthStatus(LocalConst.INTEGER_ONE);
        return couponService.getSPMerchantList(request);
    }

    @ApiOperation(value = "查询商品列表")
    @PostMapping("/getSPCommodityList")
    ResponseBase<PageDTO<ActivitySpecDTO>> getSPCommodityList(@RequestBody SPCommodityListReq request){
        return couponService.getSPCommodityList(request);
    }

}
