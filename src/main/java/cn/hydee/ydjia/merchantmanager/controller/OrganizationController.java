package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryOrgTreeDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OrgRespDTO;
import cn.hydee.ydjia.merchantmanager.feign.OrganizationClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织机构控制器
 * <AUTHOR>
 * @date 2020/4/22 16:07
 */
@RestController
@RequestMapping(value = "/${api.version}/organization")
@Api(value="组织机构",description = "组织机构")
public class OrganizationController {

    @Autowired
    private OrganizationClient organizationClient;


    @ApiOperation(value = "根据商家编码查询组织机构")
    @GetMapping("/{merCode}")
    public ResponseBase<List<OrgRespDTO>>  queryOrgByMerCode(@PathVariable String merCode){
        ResponseBase<List<OrgRespDTO>> base = organizationClient.queryOrgByMerCode(merCode);
        if (base == null || !base.checkSuccess()) {
            return base;
        }
        Map<String,OrgRespDTO> map = new HashMap<>();
        List<OrgRespDTO> superMap = new ArrayList<>();
        base.getData().forEach(model-> {
            map.put(model.getId(),model);
            if (StringUtils.isEmpty(model.getOrParent())) {
                superMap.add(model);
            }
        });
        base.getData().forEach(model-> {
            OrgRespDTO orgRespDTO = map.get(model.getOrParent());
            if (orgRespDTO != null) {
                orgRespDTO.addChildren(model);
            }
        });
        base.setData(superMap);
        return base;
    }
    @ApiOperation(value = "根据条件查询组织机构")
    @PostMapping("/search")
    public ResponseBase<List<OrgRespDTO>> search(@RequestBody QueryOrgTreeDTO reqDTO, @RequestHeader String userId){
        reqDTO.setUserId(userId);
        ResponseBase<List<OrgRespDTO>> base = organizationClient.search(reqDTO);
        return base;
    }

    @ApiOperation(value = "根据商家编码查询组织机构之商品任务")
    @GetMapping("/query/{merCode}")
    public ResponseBase<List<OrgRespDTO>>  queryOrgByMerCodeByComm(@PathVariable String merCode){
        ResponseBase<List<OrgRespDTO>> base = organizationClient.queryOrgByMerCode(merCode);
        if (base == null || !base.checkSuccess()) {
            return base;
        }
        List<String> c_0_p = base.getData().stream().filter(e -> e.getOrCode().equals("c_0")).map(OrgRespDTO::getId).collect(Collectors.toList());

        List<String> c_0 = base.getData().stream().filter(e -> e.getOrClass() == 1).map(OrgRespDTO::getId).collect(Collectors.toList());
        Map<String,OrgRespDTO> map = new HashMap<>();
        List<OrgRespDTO> superMap = new ArrayList<>();
        base.getData().forEach(model-> {
            //将集团级的数据添加
            if((c_0_p.contains(model.getOrParent()) || c_0.contains(model.getOrParent())) && model.getOrClass() != 1){
                superMap.add(model);
            }
            map.put(model.getId(),model);
        });
        base.getData().forEach(model-> {
            OrgRespDTO orgRespDTO = map.get(model.getOrParent());
            if (orgRespDTO != null) {
                orgRespDTO.addChildren(model);
            }
        });
        base.setData(superMap);
        return base;
    }
}
