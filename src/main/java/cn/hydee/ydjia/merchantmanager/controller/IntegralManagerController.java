package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.IntegralCleanRule;
import cn.hydee.ydjia.merchantmanager.feign.MemberIntegralManagerClient;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/11/29 17:37
 */
@RestController
@Api(tags = "心币管理")
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/${api.version}/integralManager")
public class IntegralManagerController extends AbstractController {

    @Autowired
    private MemberIntegralManagerClient memberIntegralManagerClient;

    @ApiOperation(value = "新增or修改活动配置")
    @PostMapping("/saveOrUpdate")
    public ResponseBase<Boolean> saveOrUpdate(@Valid @RequestBody IntegralCleanRule req, @RequestHeader String userName) {
        req.setCreateName(userName);
        req.setUpdateName(userName);
        log.info("IntegralManagerController  saveOrUpdate req:{}", JSON.toJSONString(req));
        return memberIntegralManagerClient.saveOrUpdate(req);
    }

    @ApiOperation(value = "获取心币清零规则详情")
    @GetMapping("/getIntegralManagerDetail")
    public ResponseBase<IntegralCleanRule> getIntegralManagerDetail(@RequestParam(value = "merCode") String merCode) {
        return memberIntegralManagerClient.getIntegralManagerDetail(merCode);
    }

    @ApiOperation(value = "获取商户剩余修改次数")
    @GetMapping("/getMerchantCleanCount")
    public ResponseBase<Integer> getMerchantCleanCount(@RequestParam(value = "merCode") String merCode) {
        return memberIntegralManagerClient.getMerchantCleanCount(merCode);
    }

}
