package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.MemberDayBasicReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberDayBasicRespDTO;
import cn.hydee.ydjia.merchantmanager.feign.MemberDayClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @description:
 * @author: HuangYiBo
 * @time: 2020/9/8 09:34
 */

@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/memberDay")
@Api(value = "会员日配置", description = "会员日配置控制器")
public class MemberDayController extends AbstractController {

    @Autowired
    private MemberDayClient memberDayClient;


    @ApiOperation(value = "会员日配置信息设置保存", notes = "会员日配置信息设置保存")
    @RequestMapping(value = "/addMemberDay", method = RequestMethod.POST)
    public ResponseBase addMemberDay(@Valid @RequestBody MemberDayBasicReqDTO memberDayBasicReqDTO, BindingResult result, @RequestHeader String merCode) {
        checkValid(result);
        memberDayBasicReqDTO.setMerCode(merCode);
        return memberDayClient.addMemberDay(memberDayBasicReqDTO);
    }

    @ApiOperation(value = "会员日配置信息设置修改", notes = "会员日配置信息设置修改")
    @RequestMapping(value = "/updateMemberDay", method = RequestMethod.POST)
    public ResponseBase updateMemberDay(@Valid @RequestBody MemberDayBasicReqDTO memberDayBasicReqDTO, BindingResult result, @RequestHeader String merCode) {
        checkValid(result);
        memberDayBasicReqDTO.setMerCode(merCode);
        return memberDayClient.updateMemberDay(memberDayBasicReqDTO);
    }


    @ApiOperation(value = "查询会员日配置信息", notes = "查询会员日配置信息")
    @RequestMapping(value = "/getMemberDay", method = RequestMethod.GET)
    public ResponseBase<MemberDayBasicRespDTO> getMemberDay(@NonNull @RequestParam("merCode") String merCode, @NonNull @RequestParam("basicType") Integer basicType) {
        return memberDayClient.getMemberDay(merCode, basicType);
    }
}
