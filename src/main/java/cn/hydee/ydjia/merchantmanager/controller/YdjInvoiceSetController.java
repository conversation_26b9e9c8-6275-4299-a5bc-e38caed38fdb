package cn.hydee.ydjia.merchantmanager.controller;


import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.YdjInvoiceSetReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjInvoiceSetResDTO;
import cn.hydee.ydjia.merchantmanager.service.YdjInvoiceSetService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 商户电子发票开关配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Slf4j
@RestController
@RequestMapping("/${api.version}/ydj-invoice-set")
@Api(value = "商户电子发票开关配置", tags = "商户电子发票开关配置")
public class YdjInvoiceSetController extends BaseController {
    @Autowired
    private YdjInvoiceSetService ydjInvoiceSetService;

    /**
     * @Description: 查询商户电子发票开关配置
     * @Author: Kaven
     * @Date: 2023/2/10 14:25
     * @param []
     * @return cn.hydee.starter.dto.ResponseBase<YdjInvoiceSetResDTO>
     * @Exception
     */
    @ApiOperation(value = "查询商户电子发票开关配置", notes = "查询商户电子发票开关配置")
    @PostMapping("/_searchByMerCode")
    public ResponseBase<YdjInvoiceSetResDTO> queryMessageByMerCode() {
        ResponseBase<YdjInvoiceSetResDTO> responseBase = ResponseBase.success();
        responseBase.setData(ydjInvoiceSetService.querySettingByMerCode(getMerCodeV2()));
        return responseBase;
    }

    /**
     * @Description: 查询商户电子发票开关配置（C端小前台使用：控制电子发票入口是否显示）
     * @Author: Kaven
     * @Date: 2023/2/13 10:36
     * @param [merCode]
     * @return cn.hydee.starter.dto.ResponseBase<cn.hydee.ydjia.merchantmanager.dto.resp.YdjInvoiceSetResDTO>
     * @Exception
     */
    @ApiOperation(value = "查询商户电子发票开关配置（C端小前台使用）", notes = "查询商户电子发票开关配置（C端小前台使用）")
    @GetMapping("/searchInvoiceStatus")
    public ResponseBase<YdjInvoiceSetResDTO> searchInvoiceStatus(@RequestParam("merCode") String merCode) {
        log.info("查询商户电子发票开关配置（C端小前台）--入参：{}", merCode);
        ResponseBase<YdjInvoiceSetResDTO> responseBase = ResponseBase.success();
        responseBase.setData(ydjInvoiceSetService.querySettingByMerCode(merCode));
        return responseBase;
    }

    /**
     * @Description: 新增或修改商户电子发票开关配置
     * @Author: Kaven
     * @Date: 2023/2/10 14:26
     * @param [userName, invoiceSetReqDTO, result]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @ApiOperation(value = "新增或修改商户电子发票开关配置", notes = "新增或修改商户电子发票开关配置")
    @PostMapping("/saveOrUpdate")
    public ResponseBase saveOrUpdate(@RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName, @Valid @RequestBody YdjInvoiceSetReqDTO invoiceSetReqDTO,
                                        BindingResult result) {
        // 参数校验
        checkValid(result);
        // 从header中获取到商户编码并赋值
        invoiceSetReqDTO.setMerCode(getMerCodeV2());
        return generateSuccess(ydjInvoiceSetService.saveOrUpdateInvoiceSet(invoiceSetReqDTO, userName));
    }
}
