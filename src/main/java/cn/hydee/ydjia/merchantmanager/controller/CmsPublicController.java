package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.CmsPublic;
import cn.hydee.ydjia.merchantmanager.dto.req.CmsPublicSearchReqDTO;
import cn.hydee.ydjia.merchantmanager.service.CmsPublicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 客服人员管理器
 * <AUTHOR>
 * @date 2019/11/28
 */
@Api(value = "商家公共配置控制器", description = "商家公共配置控制器，目前包含首页轮播图，公告", tags = {"商家公共配置控制器" })
@RestController
@RequestMapping(value = "/${api.version}/cms-pub")
public class CmsPublicController extends AbstractController {

    @Autowired
    private CmsPublicService cmsPublicService;

    @ApiOperation(value = "创建公共配置信息")
    @PostMapping
    public ResponseBase createCmsPublic(@Valid @RequestBody CmsPublic cmsPublic,
                                        BindingResult result,
                                        @RequestHeader String userName) {
        checkValid(result);
        cmsPublic.setCreateName(userName);
        int line = cmsPublicService.createCmsPublic(cmsPublic);
        return generateLineSuccess(line,0);
    }

    @ApiOperation(value = "修改公共配置信息")
    @PutMapping
    public ResponseBase updateCmsPublic(@Valid @RequestBody CmsPublic cmsPublic,
                                        BindingResult result,
                                        @RequestHeader String userName) {
        checkValid(result);
        cmsPublic.setModifyName(userName);
        int line = cmsPublicService.updateCmsPublic(cmsPublic);
        return generateLineSuccess(line,0);
    }

    @ApiOperation(value = "查询单个公共配置信息")
    @GetMapping("/{id}")
    public ResponseBase<CmsPublic> queryCmsPublic(@PathVariable String id) {
        if (StringUtils.isEmpty(id)) {
            throw WarnException.builder().code(cn.hydee.starter.dto.ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        CmsPublic cmsPublic = cmsPublicService.queryCmsPublic(id);
        return generateObjectSuccess(cmsPublic);
    }

    @ApiOperation(value = "删除公共配置信息")
    @DeleteMapping("/{id}")
    public ResponseBase deleteCmsPublic(@PathVariable String id,
                                        @RequestParam String merCode,
                                        @RequestHeader String userName) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(merCode)) {
            throw WarnException.builder().code(cn.hydee.starter.dto.ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        int line = cmsPublicService.deleteCmsPublic(id,merCode,userName);
        return generateLineSuccess(line,0);
    }

    @ApiOperation(value = "搜索公共配置信息")
    @PostMapping("/_search")
    public ResponseBase<PageDTO<CmsPublic>> searchCmsPublic(@Valid @RequestBody CmsPublicSearchReqDTO reqDTO,
                                                            BindingResult result) {
        checkValid(result);
        int total = cmsPublicService.countCmsPublic(reqDTO);
        List<CmsPublic> list = new ArrayList<>();
        if (total > 0) {
            list = cmsPublicService.searchCmsPublic(reqDTO);
        }
        return generatePageDtoSuccess(total,list);
    }

}
