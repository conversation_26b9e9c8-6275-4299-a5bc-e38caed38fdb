package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.commission.CommissionStoreReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.commission.CommissionStoreRspDTO;
import cn.hydee.ydjia.merchantmanager.dto.commission.StoreInfoRspDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryStoreByOrgIdDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OrgRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.trans.MerchantResDTO;
import cn.hydee.ydjia.merchantmanager.feign.AccountClient;
import cn.hydee.ydjia.merchantmanager.feign.CommissionClient;
import cn.hydee.ydjia.merchantmanager.service.CommissionService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 提成方案门店控制器 B端
 *
 * <AUTHOR>
 * @date 2020/06/17  16:49
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/${api.version}/commissionStore")
@Api(value = "提成方案门店控制器", produces = "application/json", tags = {"提成方案门店控制器"})
public class CommissionStoreController extends AbstractController {

    @Resource
    private CommissionClient commissionClient;
    @Resource
    private AccountClient accountClient;
    @Resource
    private CommissionService commissionService;

    @ApiOperation(value = "方案门店查询", notes = "方案门店查询(该方案下所有门店，有最大值限制)")
    @PostMapping("getStoreList")
    public ResponseBase<PageDTO<CommissionStoreRspDTO>> getStoreList(@RequestBody CommissionStoreReqDTO reqDTO) {
        log.debug("方案门店查询, req:{}", JSONObject.toJSONString(reqDTO));
        return commissionClient.getStoreList(reqDTO);
    }

    @ApiOperation(value = "方案门店查询 All", notes = "方案门店查询(该方案下所有门店，有最大值限制)")
    @PostMapping("getStoreListAll")
    public ResponseBase<List<CommissionStoreRspDTO>> getStoreListAll(@RequestBody CommissionStoreReqDTO reqDTO) {
        log.debug("方案门店查询, req:{}", JSONObject.toJSONString(reqDTO));
        return commissionClient.getStoreListAll(reqDTO);
    }

    @ApiOperation(value = "门店多条件查询", notes = "多条件查询")
    @PostMapping("/searchStore")
    public ResponseBase<PageDTO<CommissionStoreRspDTO>> queryStoreByCondition(@RequestBody QueryStoreByOrgIdDTO reqDTO, @RequestHeader String userId) {
        log.info("门店多条件查询,req:{}", reqDTO);
        ResponseBase<MerchantResDTO> responseBase = accountClient.queryMerchant(userId);
        if (responseBase == null || !responseBase.checkSuccess()) {
            return generateError(ErrorType.PARA_ERROR);
        }
        return generateSuccess(commissionService.queryStoreByOrgId(reqDTO, responseBase.getData().getMerCode()));
    }

    @ApiOperation(value = "根据商家编码查询组织机构")
    @GetMapping("/getMerchantOrg")
    public ResponseBase<List<OrgRespDTO>> queryOrgByMerCode(@RequestHeader String userId) {
        ResponseBase<MerchantResDTO> responseBase = accountClient.queryMerchant(userId);
        if (responseBase == null || !responseBase.checkSuccess()) {
            return generateError(ErrorType.PARA_ERROR);
        }
        return commissionService.queryOrgByMerCode(responseBase.getData().getMerCode());
    }

    @ApiOperation(value = "根据商家编码查询门店")
    @GetMapping("/getMerchantStore")
    public ResponseBase<List<StoreInfoRspDTO>> queryStoreByMerCode(@RequestHeader String userId) {
        ResponseBase<MerchantResDTO> responseBase = accountClient.queryMerchant(userId);
        if (responseBase == null || !responseBase.checkSuccess()) {
            return generateError(ErrorType.PARA_ERROR);
        }
        return this.generateSuccess(commissionService.queryStoreByMerCode(responseBase.getData().getMerCode()));
    }

}
