package cn.hydee.ydjia.merchantmanager.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.service.OssFileService;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.UUIDUtil;
import cn.hydee.ydjia.merchantmanager.config.WeeChatConfiguration;
import cn.hydee.ydjia.merchantmanager.dto.WxGenerateCodeRequest;
import cn.hydee.ydjia.merchantmanager.dto.resp.merchant.MerchantResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.third.WxCodeResDTO;
import cn.hydee.ydjia.merchantmanager.enums.MerchantProductEnum;
import cn.hydee.ydjia.merchantmanager.feign.BaseInfoClient;
import cn.hydee.ydjia.merchantmanager.feign.WeeChatMaClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.service.MerchantPlatformService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.apache.catalina.startup.ExpandWar.deleteDir;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/03/31 22:40
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/weeChatMa")
@Api(value = "微信小程序平台接口管理", description = "微信小程序平台接口控制器")
public class WeeChatMaController extends AbstractController {

    @Autowired
    private WeeChatMaClient weeChatMaClient;
    @Autowired
    private WeeChatConfiguration weeChatConfiguration;
    private static final String BASE64_PRE = "data:image/png;base64,";
    private static final String MER_CODE = "merCode=";
    @Autowired
    private BaseInfoClient baseInfoClient;

    @Autowired
    private MerchantPlatformService merchantPlatformService;
    @Autowired
    private EmpAuthHandler empAuthHandler;
    @Autowired
    private OssFileService ossFileService;

    @Value("${spring.application.name}")
    private String appName;

    @ApiOperation(value = "查询当前登录人所属权限的单个小程序二维码和地址")
    @GetMapping("/queryUrl/{merCode}")
    @Deprecated
    public ResponseBase<String[]> queryUrl(@PathVariable String merCode,
                                           @Nullable @RequestParam Integer appletUrlType,
                                           @RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName) {
        Integer type = merchantPlatformService.isOpenProduct(MerchantProductEnum.MALL.getCode(), merCode);
        String merCodeStr = MER_CODE + merCode;
        String miniPath = weeChatConfiguration.getMiniPagePath() + "?" + merCodeStr;
        String qrcodePath = weeChatConfiguration.getMiniPagePath();
        if(type == 1 && appletUrlType != null && appletUrlType == 1){
            miniPath = weeChatConfiguration.getMiniMallPagePath() + "?" + merCodeStr;
            qrcodePath = weeChatConfiguration.getMiniMallPagePath();
        }
        // 好物甄选首页 需要查询是否开通云仓服务
        if (appletUrlType != null && appletUrlType == 2) {
            ResponseBase<MerchantResDTO> merchantInfoResp = baseInfoClient.queryMerchantInfo(merCode);
            if (merchantInfoResp.checkSuccess() && null != merchantInfoResp.getData()
                    && merchantInfoResp.getData().getOpenCloudWareHouse() == 1) {
                miniPath = weeChatConfiguration.getMiniYcPagePath() + "?" + merCodeStr;
                qrcodePath = weeChatConfiguration.getMiniYcPagePath();
            }
        }

        String orgCode = empAuthHandler.getSubOrgCode(merCode, userName);
        WxGenerateCodeRequest wxGenerateCodeRequest = new WxGenerateCodeRequest();
        wxGenerateCodeRequest.setScene(merCodeStr);
        wxGenerateCodeRequest.setPage(qrcodePath);
        wxGenerateCodeRequest.setWidth(430);
        wxGenerateCodeRequest.setAutoColor(true);
        wxGenerateCodeRequest.setIsHyaline(false);
        ResponseBase<String> responseBase = weeChatMaClient.generateMiniCode(wxGenerateCodeRequest,
                null, orgCode, "1", merCode);
        if (!responseBase.checkSuccess()) {
            throw WarnException.builder().code(responseBase.getCode()).
                    tipMessage(responseBase.getMsg()).build();
        }
        ResponseBase<String[]> response = ResponseBase.success();
        response.setData(new String[]{miniPath, BASE64_PRE + responseBase.getData()});
        return response;
    }

    @ApiOperation(value = "查询当前登录人所属权限的全部小程序二维码和地址")
    @GetMapping("/queryUrlNew/{merCode}")
    public ResponseBase<List<WxCodeResDTO>> queryUrlNew(@PathVariable String merCode,
                                                        @RequestParam(value = "appletUrlType") Integer appletUrlType,
                                                        @RequestParam(value = "appName", required = false) String searchKey,
                                                        @RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName) {
        log.info("queryUrlNew merCode: {}, appletUrlType: {}, searchKey: {}, userName:{} ",
                merCode, appletUrlType, searchKey, userName);

        Integer type = merchantPlatformService.isOpenProduct(MerchantProductEnum.MALL.getCode(), merCode);
        String merCodeStr = MER_CODE + merCode;
        String miniPath = weeChatConfiguration.getMiniPagePath() + "?" + merCodeStr;
        String qrcodePath = weeChatConfiguration.getMiniPagePath();
        if (type == 1 && appletUrlType != null && appletUrlType == 1){
            miniPath = weeChatConfiguration.getMiniMallPagePath() + "?" + merCodeStr;
            qrcodePath = weeChatConfiguration.getMiniMallPagePath();
        }
        // 好物甄选首页 需要查询是否开通云仓服务
        if (appletUrlType != null && appletUrlType == 2) {
            ResponseBase<MerchantResDTO> merchantInfoResp = baseInfoClient.queryMerchantInfo(merCode);
            if (merchantInfoResp.checkSuccess() && null != merchantInfoResp.getData()
                    && merchantInfoResp.getData().getOpenCloudWareHouse() == 1) {
                miniPath = weeChatConfiguration.getMiniYcPagePath() + "?" + merCodeStr;
                qrcodePath = weeChatConfiguration.getMiniYcPagePath();
            }
        }

        String orgCode = empAuthHandler.getSubOrgCode(merCode, userName);
        WxGenerateCodeRequest wxGenerateCodeRequest = new WxGenerateCodeRequest();
        wxGenerateCodeRequest.setScene(merCodeStr);
        wxGenerateCodeRequest.setPage(qrcodePath);
        wxGenerateCodeRequest.setWidth(430);
        wxGenerateCodeRequest.setAutoColor(true);
        wxGenerateCodeRequest.setIsHyaline(false);
        ResponseBase<List<WxCodeResDTO>> wxResBase = weeChatMaClient.generateMiniCodeList(wxGenerateCodeRequest,
                merCode, Collections.singletonList(orgCode), false);
        if (!wxResBase.checkSuccess()) {
            throw WarnException.builder().code(wxResBase.getCode()).
                    tipMessage(wxResBase.getMsg()).build();
        }

        List<WxCodeResDTO> wxCodeList;
        if (!CollectionUtils.isEmpty(wxCodeList = wxResBase.getData())) {
            for (WxCodeResDTO data : wxResBase.getData()) {
                data.setUrl(miniPath);
                data.setCode(BASE64_PRE + data.getCode());
            }
        }

        if (StringUtils.hasText(searchKey)) {
            wxCodeList = wxCodeList.stream()
                    .filter(o -> StringUtils.hasText(o.getAppName()) && o.getAppName().contains(searchKey))
                    .collect(Collectors.toList());
        }

        ResponseBase<List<WxCodeResDTO>> response = ResponseBase.success();
        response.setData(wxCodeList);

        return response;
    }

    @ApiOperation(value = "下载查询当前登录人所属权限的全部小程序二维码和地址")
    @PostMapping("/downloadWxCode/{merCode}")
    public ResponseBase<String> downloadWxCode(@PathVariable(value = "merCode") String merCode,
                                               @RequestParam(value = "appletUrlType") Integer appletUrlType,
                                               @RequestParam(value = "appName", required = false) String searchKey,
                                               @RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName) {
        log.info("downloadWxCode merCode: {}, appletUrlType: {}, searchKey: {}, userName:{} ",
                merCode, appletUrlType, searchKey, userName);

        List<WxCodeResDTO> wxCodeList = this.queryUrlNew(merCode, appletUrlType, null, userName).getData();
        if (CollectionUtils.isEmpty(wxCodeList)) {
            generateObjectSuccess(null);
        }

        String zipFileName = "小程序二维码";
        String localPath = "/temp/" + zipFileName;
        wxCodeList.forEach(o -> {
            String filePath = localPath + "/" + o.getAppName() + ".png";
            byte[] decode = Base64.getDecoder().decode(o.getCode().replace(LocalConst.CODE_IMG_PATTERN, ""));
            FileUtil.writeBytes(decode, filePath);
        });
        ZipUtil.zip(localPath);

        String localZipFilePath = localPath + ".zip";
        String ossZipFilePath = appName + "/" + merCode
                + "/qrCode/" + DateUtil.getTodayString() + "/" + UUIDUtil.generateUuid()
                + "/" + zipFileName + ".zip";
        ossFileService.upload(new File(localZipFilePath), ossZipFilePath);

        deleteDir(new File(localPath));
        deleteDir(new File(localZipFilePath));

        return generateObjectSuccess(ossZipFilePath);
    }

    @ApiOperation(value = "解绑小程序/公众号")
    @GetMapping("/unbindWxAuthInfo/{merCode}")
    public ResponseBase<Boolean> unbindWxAuthInfo(
            @PathVariable String merCode,
            @RequestParam(required = false) String appid,
            @RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName){
        return weeChatMaClient.unbindWxAuthInfo(merCode, appid, userName);
    }
}
