package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.*;
import cn.hydee.ydjia.merchantmanager.dto.ActivitySpecDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.LiveOnlineCountRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.LiveSharerRecordRankRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.LiveSimpleRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.LiveViewerRespDTO;
import cn.hydee.ydjia.merchantmanager.enums.LiveCommodityType;
import cn.hydee.ydjia.merchantmanager.enums.TaskStatus;
import cn.hydee.ydjia.merchantmanager.enums.YesOrNoType;
import cn.hydee.ydjia.merchantmanager.feign.MiddleIdClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.repository.YdjTaskRepo;
import cn.hydee.ydjia.merchantmanager.service.LiveActivityService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.enums.ExportType.LIVE_SHARE_RECORD_EXPORT;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.EXPORT_COUNT_ERROR;


/**
 * 直播活动控制器
 *
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020/5/6   10:49
 * @since 1.0
 */
@Slf4j
@Api(value = "直播活动控制器", produces = "application/json", tags = {"直播活动控制器"})
@RestController
@RequestMapping("/${api.version}/activity")
public class LiveActivityController extends AbstractController {
    @Resource
    private LiveActivityService liveActivityService;

    @Autowired
    private YdjTaskRepo ydjTaskRepo;

    @Value("${exportNumLimitToday}")
    private Integer exportNumLimitToday;

    @Autowired
    private MiddleIdClient middleIdClient;

    @Autowired
    private EmpAuthHandler empAuthHandler;

    @ApiOperation(value = "直播活动分页查询", notes = "直播活动分页查询")
    @PostMapping("list")
    public ResponseBase<PageDTO<LiveActivityRspDTO>> pageLiveActivity(@Valid @RequestBody LiveActivityPageReqDTO reqDTO) {
        log.info("直播活动分页查询,req:{}", reqDTO);
        return liveActivityService.pageLiveActivity(reqDTO);
    }

    @ApiOperation(value = "直播活动-厂家直播分页查询", notes = "直播活动-厂家直播分页查询")
    @PostMapping("factoryLive/list")
    public ResponseBase<PageDTO<LiveActivityRspDTO>> pageFactoryLiveActivity(@Valid @RequestBody FactoryLivePageReqDTO reqDTO) {
        log.info("直播活动-厂家直播分页查询,req:{}", reqDTO);
        return liveActivityService.pageFactoryLiveActivity(reqDTO);
    }

    @ApiOperation(value = "直播活动商品查询", notes = "直播活动商品查询")
    @PostMapping("commodity/list")
    public ResponseBase<PageDTO<LiveCommodityRspDTO>> getCommodityList(@Valid @RequestBody LiveCommodityPageReqDTO reqDTO) {
        log.info("直播活动商品分页查询，req : {}", reqDTO);
        return liveActivityService.getCommodityList(reqDTO);
    }

    @ApiOperation(value = "直播活动-厂家直播商品分页查询", notes = "直播活动-厂家直播商品分页查询")
    @PostMapping("factoryCommodity/getList")
    public ResponseBase<PageDTO<LiveCommodityRspDTO>> pageFactoryCommodityList(@RequestBody LiveSubsCommodityPageReqDTO reqDTO) {
        log.info("直播活动-厂家直播商品分页查询，req : {}", reqDTO);
        return liveActivityService.pageFactoryCommodityList(reqDTO);
    }

    @ApiOperation(value = "新增直播活动", notes = "新增直播活动")
    @PostMapping("add")
    public ResponseBase add(@Valid @RequestBody LiveActivityAddReqDTO reqDTO) {
        log.info("新增直播活动,req: {}", reqDTO);
        return liveActivityService.add(reqDTO);
    }

    @ApiOperation(value = "直播活动编辑", notes = "直播活动编辑")
    @PostMapping("edit")
    public ResponseBase edit(@Valid @RequestBody LiveActivityEditReqDTO reqDTO) {
        log.info("直播活动编辑,req:{}", reqDTO);

        return liveActivityService.edit(reqDTO);
    }

    @ApiOperation(value = "厂家直播活动编辑", notes = "厂家直播活动编辑")
    @PostMapping("factoryEdit")
    public ResponseBase factoryEdit(@Valid @RequestBody LiveActivityEditReqDTO reqDTO) {
        log.info("厂家直播活动编辑,req:{}", reqDTO);

        return liveActivityService.factoryEdit(reqDTO);
    }

    @ApiOperation(value = "厂家直播提交审核", notes = "厂家直播提交审核")
    @PostMapping("submitAudit")
    public ResponseBase<Boolean> submitAudit(@Valid @RequestBody FactoryLiveAuditReqDTO reqDTO) {
        log.info("厂家直播提交审核，req : {}", reqDTO);

        return   liveActivityService.audited(reqDTO);
    }

    @ApiOperation(value = "厂家直播-奖励详情", notes = "厂家直播-奖励详情")
    @GetMapping("awardDetail")
    public ResponseBase<LiveAwardDetailRspDTO> awardDetail(@RequestParam String merCode,@RequestParam Long liveId) {
        log.info("厂家直播-奖励详情，liveId : {}", liveId);
        return liveActivityService.awardDetail(merCode,liveId);
    }

    @ApiOperation(value = "厂家直播-领取奖励", notes = "厂家直播-领取奖励")
    @GetMapping("receivedAward")
    public ResponseBase<Boolean> receivedAward(@RequestParam String merCode,@RequestParam Long liveId) {
        log.info("厂家直播-领取奖励，liveId : {}", liveId);
        return liveActivityService.receivedAward(merCode,liveId);
    }

    @ApiOperation(value = "通过直播id获取直播详情", notes = "通过直播id获取直播详情")
    @GetMapping("detail/{liveId}")
    public ResponseBase<LiveActivityDetailRspDTO> detail(@PathVariable Long liveId) {
        log.info("通过直播id查询，req：{}", liveId);
        return liveActivityService.detail(liveId);
    }

    @ApiOperation(value = "通过商户编码+直播id获取直播详情", notes = "通过商户编码+直播id获取直播详情")
    @GetMapping("detail/{merCode}/{liveId}")
    public ResponseBase<LiveActivityDetailRspDTO> detail(@PathVariable("merCode") String merCode,
                                                         @PathVariable("liveId") Long liveId) {
        log.info("通过直播id查询，req：{}", liveId);
        return liveActivityService.detail(merCode, liveId);
    }

    @ApiOperation(value = "通过直播id获取直播详情", notes = "通过直播id获取直播详情")
    @GetMapping("editDetail/{liveId}")
    public ResponseBase<LiveActivityEditDetailRspDTO> editDetail(@RequestHeader String merCode,
                                                                @PathVariable Long liveId) {
        log.info("通过直播id查询，req：{}", liveId);
        ResponseBase<LiveActivityEditDetailRspDTO> responseBase = liveActivityService.editDetail(liveId);
        if(responseBase.checkSuccess() && responseBase.getData() != null){
            LiveActivityEditDetailRspDTO detailRspDTO = responseBase.getData();
            List<ActivitySpecDTO> list = detailRspDTO.getCommoditys();
            //组合商品规格ids
            List<String> assembleSpecIds = list.stream().filter(comm -> comm.getCommodityType().equals(LiveCommodityType.COMBINATION.getCode())).map(ActivitySpecDTO::getSpecId).collect(Collectors.toList());
            liveActivityService.getCommodityInfo(list, assembleSpecIds, LiveCommodityType.COMBINATION.getCode(), merCode);
            //普通商品规格ids
            List<String> specIds = list.stream().filter(comm -> comm.getCommodityType().equals(LiveCommodityType.ORDINARY.getCode())).map(ActivitySpecDTO::getSpecId).collect(Collectors.toList());
            liveActivityService.getCommodityInfo(list, specIds, LiveCommodityType.ORDINARY.getCode(), merCode);
        }

        return responseBase;
    }

    @ApiOperation(value = "直播活动删除", notes = "直播活动删除")
    @GetMapping("delete")
    public ResponseBase delete(@RequestParam Long liveId) {
        log.info("直播活动删除,req:{}", liveId);
        return liveActivityService.delete(liveId);
    }

    @ApiOperation(value = "通过类型查询商户的直播活动列表（简要信息）",
            notes = "查询商户的直播活动列表（reqDTO.type：0-直播预告；1-直播中；2-直播历史），靠近当前时间的排前面，分页")
    @PostMapping("listSimple")
    public ResponseBase<PageDTO<LiveSimpleRspDTO>> pageLiveSimple(@Valid @RequestBody LiveActivityQueryReqDTO reqDTO) {
        log.info("通过类型查询商户的直播活动列表,req[{}]", reqDTO);
        return liveActivityService.pageLiveSimple(reqDTO);
    }

    @ApiOperation(value = "查询推荐的直播活动",
            notes = "查询推荐的直播活动（简要信息）")
    @GetMapping("getRecommend/{merCode}")
    public ResponseBase<LiveSimpleRespDTO> getRecommend(@PathVariable("merCode") String merCode) {
        log.info("通过类型查询商户的直播活动列表,merCode[{}]", merCode);
        return liveActivityService.getRecommend(merCode);
    }


    @ApiOperation(value = "获取C端播放地址", notes = "获取C端播放地址")
    @PostMapping("getPlayUrl/{merCode}/{liveId}")
    public ResponseBase<String> getPlayUrl(@PathVariable String merCode, @PathVariable Long liveId) {
        log.info("获取C端播放地址,liveId={}", liveId);

        return liveActivityService.getPlayUrl(merCode, liveId);
    }

    @ApiOperation(value = "获取小程序二维码", notes = "获取小程序二维码")
    @PostMapping("getAppletsQrCode")
    public ResponseBase<String> getAppletsQrCode(@Valid @RequestBody AppletQrCodeReqDTO reqDTO,
                                                 @RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName) {
        log.info("获取小程序二维码,reqDTO={}", reqDTO);
        reqDTO.setOperator(userName);
        return liveActivityService.getAppletsQrCode(reqDTO);
    }

    @ApiOperation(value = "根据liveId查询直播视频列表接口", notes = "根据liveId查询直播视频列表接口")
    @GetMapping("getVideos/{liveId}")
    public ResponseBase<List<LiveVideoRspDTO>> getVideos(@PathVariable Long liveId) {
        log.info("获取直播视频列表,liveId={}", liveId);

        return liveActivityService.getVideos(liveId);
    }

    @ApiOperation(value = "根据liveId查询直播状态", notes = "根据liveId查询直播状态，状态：0-未开播；1-开播中；2-直播完；3-暂停")
    @GetMapping("getLiveStatus/{liveId}")
    public ResponseBase<Integer> getLiveStatus(@PathVariable Long liveId) {
        log.info("根据liveId查询直播状态,liveId={}", liveId);
        return liveActivityService.getLiveStatus(liveId);
    }

    @ApiOperation(value = "直播观看人员信息分页",
            notes = "直播观看人员信息分页")
    @PostMapping("getLiveViewers")
    public ResponseBase<PageDTO<LiveViewerRespDTO>> getLiveViewers(@RequestBody LiveViewerPageReqDTO reqDTO) {
        log.info("直播观看人员信息分页,reqDTO={}", reqDTO);
        return liveActivityService.getLiveViewers(reqDTO);
    }

    @ApiOperation(value = "统计直播观看用户量",
            notes = "统计直播观看用户量")
    @GetMapping("countLiveViewers")
    public ResponseBase<String> countLiveViewers(@RequestParam(required = false) String name) {

        return liveActivityService.countLiveViewers(name);
    }

    @ApiOperation(value = "校验直播场次",
            notes = "校验直播场次")
    @PostMapping("checkLiveNum")
    public ResponseBase<Boolean> checkLiveNum(@RequestBody LiveCheckNumReqDTO reqDTO) {

        return liveActivityService.checkLiveNum(reqDTO);
    }

    @ApiOperation(value = "查询直播实时在线数据", notes = "查询直播实时在线数据")
    @GetMapping("getLiveOnline/{merCode}/{liveId}")
    public ResponseBase<LiveOnlineCountRespDTO> getLiveOnline(@PathVariable String merCode, @PathVariable Long liveId){
        return liveActivityService.getLiveOnline(merCode,liveId);
    }

    @ApiOperation(value = "分页查询分享记录",
            notes = "分页查询分享记录")
    @PostMapping("shareRecordPage")
    public ResponseBase<PageDTO<LiveSharerRecordRankRespDTO>> shareRecordPage(@Valid @RequestBody LiveShareRecordReqDTO reqDTO){
        ResponseBase<PageDTO<LiveSharerRecordRankRespDTO>> responseBase = liveActivityService.shareRecordPage(reqDTO);
        if(responseBase.checkSuccess() && responseBase.getData() != null){
            PageDTO<LiveSharerRecordRankRespDTO> pageDTO = responseBase.getData();
            List<LiveSharerRecordRankRespDTO> list = pageDTO.getData();
            if(!CollectionUtils.isEmpty(list)){
               for(int i =0;i<list.size();i++){
                   LiveSharerRecordRankRespDTO dto = list.get(i);
                   if(reqDTO.getCurrentPage() > 1){
                       dto.setShareRank((reqDTO.getCurrentPage() - 1) * reqDTO.getPageSize() + i + 1);
                   }else{
                       dto.setShareRank(i + 1);
                   }

               }
            }
        }
        return responseBase;
    }

    @ApiOperation(value = "导出分享排行列表")
    @PostMapping("/export")
    public ResponseBase export(@Valid @RequestBody LiveShareRecordReqDTO reqDTO, @RequestHeader String userName, BindingResult result) {
        this.checkValid(result);
        Date date = new Date();
        int count = ydjTaskRepo.selectCountByToday(reqDTO.getMerCode(), LIVE_SHARE_RECORD_EXPORT.toString(),
                DateUtil.getBeginOfDay(date), DateUtil.getEndOfDay(date));
        if (count >= exportNumLimitToday) {
            return ResponseBase.error(EXPORT_COUNT_ERROR.getCode(), String.format(EXPORT_COUNT_ERROR.getMsg(), exportNumLimitToday));
        }

        YdjTask ydjTask = new YdjTask();
        List<Long> ids = middleIdClient.getId(1);
        ydjTask.setId(ids.get(0).toString());
        ydjTask.setMerCode(reqDTO.getMerCode());
        ydjTask.setCommand(JSON.toJSONString(reqDTO));
        ydjTask.setStatus(TaskStatus.WAIT.getCode());
        ydjTask.setType(LIVE_SHARE_RECORD_EXPORT.toString());
        ydjTask.setIsDelete(YesOrNoType.NO.getCode());
        ydjTask.setCreateTime(new Date());
        ydjTask.setCreateName(userName);
        ydjTask.setOrgCode(empAuthHandler.queryInfoByAccount(userName, reqDTO.getMerCode()).getSubOrgCode());
        ydjTaskRepo.insert(ydjTask);
        return ResponseBase.success();
    }

    @ApiOperation(value = "工作台-最近直播", notes = "工作台-最近直播")
    @GetMapping("/recently")
    public ResponseBase<List<RecentlyLiveActivityRspDTO>> recentlyLiveActivity(@RequestParam(value = "merCode") String merCode) {
        return liveActivityService.recentlyLiveActivity(merCode);
    }

    @ApiOperation(value = "商品上架", notes = "商品上架")
    @PostMapping("/commodity/upShelf")
    public ResponseBase upShelf(@Valid @RequestBody CommodityDownUpShelfReqDTO req) {
        return liveActivityService.upShelf(req);
    }

    @ApiOperation(value = "商品下架", notes = "商品下架")
    @PostMapping("/commodity/downShelf")
    public ResponseBase downShelf(@Valid @RequestBody CommodityDownUpShelfReqDTO req) {
        return liveActivityService.downShelf(req);
    }

    @ApiOperation(value = "创建红包", notes = "创建红包")
    @PostMapping("/createRedPack")
    public ResponseBase<Integer> createRedPack(@RequestHeader("merCode") String merCode,
                                               @RequestBody LiveCreateRedPackReqDTO reqDTO) {
        return liveActivityService.createRedPack(merCode, reqDTO);
    }

    @ApiOperation(value = "判断是否可以发红包", notes = "判断是否可以发红包")
    @GetMapping("/checkSelfApp")
    public ResponseBase<Boolean> checkSelfApp(@RequestHeader("merCode") String merCode) {
        return liveActivityService.checkSelfApp(merCode);
    }

    @ApiOperation(value = "领取红包汇总", notes = "领取红包汇总")
    @PostMapping("/getRedPackRecord")
    public ResponseBase<List<ActivityRedpackReceiveRecordResDTO>> getRedPackRecord(@RequestHeader("merCode") String merCode,
                                                                                   @Valid @RequestBody LiveRedPackRecordReqDTO reqDTO) {
        return liveActivityService.getRedPackRecord(merCode, reqDTO);
    }

    @ApiOperation(value = "新增直播-优惠券", notes = "新增直播-优惠券")
    @PostMapping("/addCoupon")
    public ResponseBase<Boolean> saveLiveCoupon(@Valid @RequestBody LiveActivityCouponAddReqDTO reqDTO) {
        return liveActivityService.saveLiveCoupon(reqDTO);
    }

    @ApiOperation(value = "新增直播-活动", notes = "新增直播-活动")
    @PostMapping("/addGameActivity")
    public ResponseBase<Boolean> addActivityZbByGame(@Valid @RequestBody LiveActivityGameRelationAddReqDTO reqDTO) {
        return liveActivityService.addActivityZbByGame(reqDTO);
    }

}
