package cn.hydee.ydjia.merchantmanager.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.hydee.ydjia.merchantmanager.domain.base.Returner;
import cn.hydee.ydjia.merchantmanager.feign.CommodityTypeRelateClient;
import cn.hydee.ydjia.merchantmanager.model.dto.CommTypeRefCountDTO;
import cn.hydee.ydjia.merchantmanager.service.CommodityTypeRelateService;
import com.baomidou.mybatisplus.extension.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.CommodityType;
import cn.hydee.ydjia.merchantmanager.dto.req.DimensionSearchDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.TypeSearchReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.TypeDimensionRespDTO;
import cn.hydee.ydjia.merchantmanager.feign.CommodityTypeDimensionClient;
import cn.hydee.ydjia.merchantmanager.model.vo.CommodityTypeVO;
import cn.hydee.ydjia.merchantmanager.service.CommodityTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@RestController
@RequestMapping(value = "/2.0/comm-type")
@Api(value = "商品分类管理", tags = { "商品分类管理控制器" })
public class CommTypeController {
    @Resource
    private CommodityTypeService commodityTypeService;
	@Resource
	private CommodityTypeDimensionClient commodityTypeDimensionClient;
	@Resource
	private CommodityTypeRelateClient commodityTypeRelateClient;
	@Resource
	private CommodityTypeRelateService commodityTypeRelateService;

	@ApiOperation(value = "获取分类树", notes = "获取分类树 merCode-商家编码(默认分类树 merCode='hydee') type-1.分类 2.分组")
	@PostMapping("/getTypeTree")
	public ResponseBase<List<CommodityTypeVO>> getTypeTree(@RequestBody TypeSearchReqDTO typeSearchReqDTO) {

		if (typeSearchReqDTO.getUse() != null && typeSearchReqDTO.getUse()) {
			DimensionSearchDTO searchDTO = new DimensionSearchDTO();
			searchDTO.setMerCode(typeSearchReqDTO.getMerCode());
			searchDTO.setUseStatus(1);
			ResponseBase<List<TypeDimensionRespDTO>> list = commodityTypeDimensionClient
					.queryTypeDimensionTree(searchDTO);
			if (list != null && list.checkSuccess() && list.getData() != null
					&& !list.getData().isEmpty()) {
				typeSearchReqDTO.setDimensionId(list.getData().get(0).getId());
			}
		}

		List<CommodityType> pureTypeTree = commodityTypeService.getPureTypeTree(typeSearchReqDTO);
		if (CollUtil.isEmpty(pureTypeTree)) return Returner.ok();

		List<CommodityTypeVO> commTypeVOs = commodityTypeRelateService.getCommTypeVOWithCounts(typeSearchReqDTO.getMerCode(), pureTypeTree);

		return Returner.ok(commTypeVOs);
	}

}
