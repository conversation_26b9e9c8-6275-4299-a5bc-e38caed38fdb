package cn.hydee.ydjia.merchantmanager.controller.batchimport;

import cn.hydee.starter.configuration.DisLockConfiguration;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.batchimport.BatchTaskRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.BatchTaskQueryDTO;
import cn.hydee.ydjia.merchantmanager.feign.MemberDataClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * @version: V1.0
 * @author: duans
 * @className: BatchImportController
 * @packageName: cn.hydee.ydjia.merchantmanager.controller.batchimport
 * @description:
 * @data: 2023-06-30 17:49
 **/
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/batchImport")
@Api(value = "批量导入", description = "批量导入控制器")
public class BatchImportController extends AbstractController {
	
	@Autowired
	private MemberDataClient memberDataClient;
	
	@ApiOperation(value = "获取模板", notes = "获取模板")
	@GetMapping("/template")
	public ResponseBase<String> getTemplate(@RequestParam("type") String type) {
		return memberDataClient.getTemplate(type);
	}

	@ApiOperation(value = "批量导入", notes = "批量导入")
	@PostMapping("/import")
	@ResponseBody
	@DisLockConfiguration.DisLock(keyExpr = "'hydee:middle:market:import:sync:combinationExcitationPolicy:' + #merCode+ ':' + #type",
			lockType = DisLockConfiguration.DisLock.LockType.OPTIMISTIC, expireTime = 60, timeUnit = TimeUnit.SECONDS, tipMessage = "重复提交")
	public ResponseBase<Void> batchImport(@RequestParam("excelFile") String excelFile,
	                                      @RequestParam("type") String type,
	                                      @RequestHeader("userName") String userName,
	                                      @RequestHeader("merCode") String merCode,
	                                      @RequestParam(required = false) String busiJson,
	                                      @RequestParam("fileName") String fileName){

		return memberDataClient.batchImport(userName, merCode,type,excelFile,busiJson,fileName);
	}


	@ApiOperation(value = "分页查询导入任务执行结果列表", notes = "分页查询导入任务执行结果列表")
	@PostMapping("/page")
	public ResponseBase<PageDTO<BatchTaskRespDTO>> queryByPage(
			@RequestHeader("userName") String userName,
			@RequestHeader("merCode") String merCode,
			@Valid @RequestBody BatchTaskQueryDTO batchTaskQueryDTO,
			BindingResult result) {
		checkValid(result);
		batchTaskQueryDTO.setMerCode(merCode);
		return memberDataClient.queryByPage(userName,merCode,batchTaskQueryDTO);
	}
}