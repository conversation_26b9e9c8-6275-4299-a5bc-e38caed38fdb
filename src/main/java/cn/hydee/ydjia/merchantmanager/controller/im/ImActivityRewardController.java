package cn.hydee.ydjia.merchantmanager.controller.im;


import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.industry.ImRewardListQueryRequest;
import cn.hydee.ydjia.merchantmanager.dto.resp.industry.ImRewardDetailDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 工业活动奖励流水控制器
 *
 * <AUTHOR>
 * @date 2023/4/16 22:19
 */
@RestController
@Api(tags = "工业活动奖励流水控制器")
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/${api.version}/imActivityReward")
@Deprecated // 服务商业务
public class ImActivityRewardController extends AbstractController {

    @PostMapping("/queryRewardList")
    @ApiOperation(value = "查询商户工业奖励流水列表")
    public ResponseBase<PageDTO<ImRewardDetailDTO>> queryRewardList(@RequestBody @Valid ImRewardListQueryRequest request,
                                                                    @RequestHeader(LocalConst.HEAD_MER_CODE_KEY) String merCode) {
        return ResponseBase.success();
    }

    @ApiOperation(value = "导出商户工业奖励流水列表")
    @PostMapping("/exportRewardList")
    public ResponseBase exportRewardList(@RequestBody ImRewardListQueryRequest reqDTO,
                                         @RequestHeader(LocalConst.HEAD_MER_CODE_KEY) String merCode,
                                         @RequestHeader String userName) {
        return ResponseBase.success();

    }
}
