package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.dto.req.AssembleCommodityDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.AssembleSearchDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.commodity.ChildCoefficientReq;
import cn.hydee.ydjia.merchantmanager.dto.req.commodity.DeleteAssembleCommodityReq;
import cn.hydee.ydjia.merchantmanager.dto.resp.AssembleSearchRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.commodity.ChildCoefficientResp;
import cn.hydee.ydjia.merchantmanager.feign.AssembleCommClient;
import cn.hydee.ydjia.merchantmanager.service.AssembleCommService;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 组合商品管理
 *
 * <AUTHOR>
 * @date 2019/11/12
 */
@Api(value = "组合商品管理器", description = "组合商品管理器")
@RestController
@RequestMapping(value = "/${api.version}/assemble-comm")
public class AssembleCommodityController extends AbstractController {

    @Resource
    private AssembleCommClient assembleCommClient;

    @Autowired
    private AssembleCommService assembleCommService;

    @PostMapping
    @ApiOperation(value = "组合商品新增", notes = "组合商品新增")
    public ResponseBase<String> addAssembleComm(@Valid @RequestBody AssembleCommodityDTO assemble,
                                                BindingResult result,
                                                @RequestHeader(value = "userName") String userName) {
        if (!StringUtils.isEmpty(userName)) {
            assemble.setUserName(userName);
        }
        checkValid(result);
        return assembleCommClient.addAssembleComm(assemble, userName);
    }

    @PutMapping
    @ApiOperation(value = "组合商品更新", notes = "组合商品更新")
    public ResponseBase<Boolean> updateAssembleComm(@Valid @RequestBody AssembleCommodityDTO assemble,
                                                    BindingResult result,
                                                    @RequestHeader(value = "userName") String userName) {

        if (!StringUtils.isEmpty(userName)) {
            assemble.setUserName(userName);
        }
        checkValid(result);
        return assembleCommClient.updateAssembleComm(assemble, userName);
    }

    @PostMapping("/search")
    @ApiOperation(value = "组合商品分页列表查询", notes = "组合商品分页列表查询")
    public ResponseBase<PageDTO<AssembleSearchRespDTO>> getAssemblePage(@Valid @RequestBody AssembleSearchDTO searchDTO, BindingResult result) {
        checkValid(result);
        return assembleCommClient.getAssemblePage(searchDTO);
    }

    @GetMapping("/{merCode}/{id}")
    @ApiOperation(value = "查询单个组合商品信息", notes = "查询单个组合商品信息")
    public ResponseBase<AssembleCommodityDTO> getAssembleCommodity(@PathVariable String merCode, @PathVariable String id) {

        if (StringUtils.isEmpty(merCode)) {
            throw WarnException.builder().code(LocalError.MERCODE_IS_NULL.getCode()).tipMessage(LocalError.MERCODE_IS_NULL.getMsg()).build();
        }
        if (StringUtils.isEmpty(id)) {
            throw WarnException.builder().code(LocalError.ID_IS_NULL.getCode()).tipMessage(LocalError.ID_IS_NULL.getMsg()).build();
        }
        return assembleCommService.getAssembleCommodity(merCode, id);
    }

    @ApiOperation(value = "组合商品删除接口", notes = "组合商品删除接口")
    @DeleteMapping("/delete")
    public ResponseBase<Integer> deleteAssembleCommodity(@Valid @RequestBody DeleteAssembleCommodityReq req,
                                                    BindingResult result) {
        checkValid(result);
        return assembleCommClient.deleteAssembleCommodity(req);
    }

    @PostMapping("/childCoefficient")
    @ApiOperation(value = "查询商品ERP拆零系数", notes = "查询商品ERP拆零系数")
    public ResponseBase<List<ChildCoefficientResp>> getChildCoefficient(@Valid @RequestBody ChildCoefficientReq req, BindingResult result) {
        checkValid(result);
        return assembleCommClient.getChildCoefficient(req);
    }
}
