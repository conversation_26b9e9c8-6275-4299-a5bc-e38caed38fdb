package cn.hydee.ydjia.merchantmanager.controller.customer;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.csd.CsdGroupReq;
import cn.hydee.ydjia.merchantmanager.dto.csd.CsdGroupResp;
import cn.hydee.ydjia.merchantmanager.feign.MiddlePushClient;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 客服分组表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@RestController
@RequestMapping("/${api.version}/staff-group")
public class CsdStaffGroupController extends AbstractController {
    private final MiddlePushClient pushClient;

    public CsdStaffGroupController(MiddlePushClient pushClient) {
        this.pushClient = pushClient;
    }

    @ApiOperation(value = "简单查询客服组集合（添加客服用）")
    @GetMapping(value = "/list/{merCode}")
    public ResponseBase<List<CsdGroupResp>> getList(@PathVariable String merCode) {
        return pushClient.getList(merCode);
    }

    @ApiOperation(value = "添加或修改客服信息")
    @PostMapping(value = "/_saveOrUpdate")
    public ResponseBase<Boolean> saveOrUpdate(@RequestBody CsdGroupReq req) {
        return pushClient.saveOrUpdateGroup(req);
    }

    @ApiOperation(value = "删除客服分组")
    @DeleteMapping(value = "/_deleteGroup/{merCode}/{groupId}")
    public ResponseBase<Boolean> deleteGroup(@PathVariable String merCode, @PathVariable Long groupId) {
        CsdGroupReq req = new CsdGroupReq();
        req.setGroupId(groupId);
        req.setMerCode(merCode);
        return pushClient.deleteGroup(req);
    }

    @ApiOperation(value = "查询客服组集合(连表查询客服分组下客服数量）")
    @PostMapping(value = "/list")
    public ResponseBase<List<CsdGroupResp>> queryList(@RequestBody CsdGroupReq req) {
        return pushClient.queryGroupList(req);
    }

}

