package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.req.MemberValueCardBalanceReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.MemberValueCardReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberValueCardBalanceRspDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberValueCardRespDTO;
import cn.hydee.ydjia.merchantmanager.enums.ErrorType;
import cn.hydee.ydjia.merchantmanager.enums.TaskStatus;
import cn.hydee.ydjia.merchantmanager.enums.YesOrNoType;
import cn.hydee.ydjia.merchantmanager.feign.MemberBatchTaskClient;
import cn.hydee.ydjia.merchantmanager.feign.MemberValueCardClient;
import cn.hydee.ydjia.merchantmanager.feign.MiddleIdClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.repository.YdjTaskRepo;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static cn.hydee.ydjia.merchantmanager.enums.ExportType.MEMBER_VALUE_CARD_BALANCE_EXPORT;
import static cn.hydee.ydjia.merchantmanager.enums.ExportType.MEMBER_VALUE_CARD_CONSUME_DETAIL_EXPORT;

@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/memberValueCard")
@Api(value = "充值礼包设置", description = "充值礼包设置控制器")
public class MemberValueCardController extends AbstractController {


    @Autowired
    private MemberValueCardClient memberValueCardClient;
    @Autowired
    private MiddleIdClient middleIdClient;
    @Autowired
    private YdjTaskRepo ydjTaskRepo;
    @Autowired
    private EmpAuthHandler empAuthHandler;

    @Autowired
    private MemberBatchTaskClient memberBatchTaskClient;


    @ApiOperation(value = "充值礼包设置保存", notes = "充值礼包设置保存")
    @RequestMapping(value = "/addMemberValueCard", method = RequestMethod.POST)
    public ResponseBase addMemberValueCard(@RequestHeader("userName") String userName,@Valid @RequestBody MemberValueCardReqDTO memberValueCardReqDTO,
                                           @RequestHeader String merCode,
                                           BindingResult result) {
        //参数验证
        checkValid(result);
        memberValueCardReqDTO.setMerCode(merCode);
        memberValueCardReqDTO.setUserName(userName);
        return memberValueCardClient.addMemberValueCard(memberValueCardReqDTO);
    }

    @ApiOperation(value = "充值礼包设置修改", notes = "充值礼包设置修改")
    @RequestMapping(value = "/updateMemberValueCard", method = RequestMethod.POST)
    public ResponseBase updateMemberValueCard(@RequestHeader("userName") String userName,@Valid @RequestBody MemberValueCardReqDTO memberValueCardReqDTO,
                                              @RequestHeader String merCode,
                                              BindingResult result) {
        //参数验证
        checkValid(result);
        memberValueCardReqDTO.setMerCode(merCode);
        memberValueCardReqDTO.setUserName(userName);
        return memberValueCardClient.updateMemberValueCard(memberValueCardReqDTO);
    }

    @ApiOperation(value = "查询充值礼包设置信息", notes = "查询充值礼包设置信息")
    @RequestMapping(value = "/getMemberValueCard", method = RequestMethod.GET)
    public ResponseBase<MemberValueCardRespDTO> getMemberValueCard(@NonNull @RequestParam("merCode") String merCode) {
        return memberValueCardClient.getMemberValueCard(merCode);
    }

    @ApiOperation(value = "查询充值礼包编号", notes = "查询充值礼包编号")
    @RequestMapping(value = "/getCardCodes", method = RequestMethod.GET)
    public ResponseBase<List<Integer>> getCardCodes(@NonNull @RequestParam("merCode") String merCode) {
        return memberValueCardClient.getCardCodes(merCode);
    }

    @ApiOperation(value = "查询会员余额列表")
    @PostMapping("/balance")
    public ResponseBase<PageDTO<MemberValueCardBalanceRspDTO>> queryMemberBalance(@RequestBody MemberValueCardBalanceReqDTO reqDTO, @RequestHeader String merCode) {
        reqDTO.setMerCode(merCode);
        if(reqDTO.getCurrentPage()>1000){
            return ResponseBase.error(ErrorType.PARA_ERROR.getCode(),"当前数据量过大，建议重新筛选后再做翻页（翻页尽量控制在1000页以内）");
        }
        return memberValueCardClient.queryMemberBalance(reqDTO);
    }

    @ApiOperation(value = "导出会员余额列表")
    @PostMapping("/balance/export")
    public ResponseBase export(@Valid @RequestBody MemberValueCardBalanceReqDTO reqDTO, @RequestHeader String userName, BindingResult result, @RequestHeader String merCode) {
        this.checkValid(result);
        reqDTO.setMerCode(merCode);
        reqDTO.setUserName(userName);
        return memberBatchTaskClient.exportMemberValueCardBalance(reqDTO);
    }

    @ApiOperation(value = "导出会员储值卡消费明细列表")
    @PostMapping("/consumeDetail/export")
    public ResponseBase exportConsumeDetail(@Valid @RequestBody MemberValueCardBalanceReqDTO reqDTO, @RequestHeader String userName, BindingResult result, @RequestHeader String merCode) {
        this.checkValid(result);
        reqDTO.setMerCode(merCode);
        reqDTO.setUserName(userName);
        return memberBatchTaskClient.exportMemberValueCardConsumeDetail(reqDTO);
    }
}
