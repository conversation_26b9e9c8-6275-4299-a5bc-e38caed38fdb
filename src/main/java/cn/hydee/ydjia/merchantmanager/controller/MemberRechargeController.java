package cn.hydee.ydjia.merchantmanager.controller;


import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.service.OssFileService;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.UUIDUtil;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.ExcelObject;
import cn.hydee.ydjia.merchantmanager.dto.MemberSimpleInfoDTO;
import cn.hydee.ydjia.merchantmanager.dto.payment.OrderPaymentLogDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.*;
import cn.hydee.ydjia.merchantmanager.enums.ErrorType;
import cn.hydee.ydjia.merchantmanager.enums.ExportType;
import cn.hydee.ydjia.merchantmanager.enums.TaskStatus;
import cn.hydee.ydjia.merchantmanager.enums.YesOrNoType;
import cn.hydee.ydjia.merchantmanager.excel.AnalysisRule;
import cn.hydee.ydjia.merchantmanager.excel.Correspondence;
import cn.hydee.ydjia.merchantmanager.excel.PositionCorrespondence;
import cn.hydee.ydjia.merchantmanager.excel.WDWUtil;
import cn.hydee.ydjia.merchantmanager.feign.MemberBatchTaskClient;
import cn.hydee.ydjia.merchantmanager.feign.MemberRechargeClient;
import cn.hydee.ydjia.merchantmanager.feign.MiddleIdClient;
import cn.hydee.ydjia.merchantmanager.feign.PayAfterClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.repository.YdjTaskRepo;
import cn.hydee.ydjia.merchantmanager.util.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.enums.ExportType.*;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.EXPORT_NUM_EXCEEDS_LIMIT;
import static com.baomidou.mybatisplus.core.toolkit.StringPool.SLASH;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;


/**
 * @version 1.0
 * @Author: lisp
 * @Description: 会员充值管理
 * @Date: 2020/11/02
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/memberRecharge")
@Api(value = "会员充值管理", description = "会员充值管理控制器")
public class MemberRechargeController extends AbstractController {

    @Autowired
    private MemberRechargeClient rechargeClient;

    @Autowired
    private YdjTaskRepo ydjTaskRepo;
    @Value("${exportNumLimitToday}")
    private Integer exportNumLimitToday;
    @Value(("${memberExportLimit}"))
    private Integer memberExportLimit;

    @Autowired
    private MiddleIdClient middleIdClient;

    @Autowired
    private EmpAuthHandler empAuthHandler;

    @Autowired
    private PayAfterClient payAfterClient;

    @Autowired
    private OssFileService ossFileService;
    private static final String PUBLIC_PATH = "public";
    @Value("${spring.application.name}")
    private String appName;

    @Autowired
    private MemberBatchTaskClient memberBatchTaskClient;

    @ApiOperation(value = "充值记录明细列表", notes = "充值记录明细列表")
    @PostMapping("/list")
    public ResponseBase<PageDTO<MemberRechargeDetailRespDTO>> list(@Valid @RequestBody MemberRechargePageReqDTO reqDTO, @RequestHeader String merCode) {
        reqDTO.setMerCode(merCode);
        ResponseBase<PageDTO<MemberRechargeDetailRespDTO>> responseBase = rechargeClient.list(reqDTO);
        if(responseBase.checkSuccess() && responseBase.getData( )!= null){
            PageDTO<MemberRechargeDetailRespDTO> pageDTO = responseBase.getData();
            List<MemberRechargeDetailRespDTO> list = pageDTO.getData();
            if(!CollectionUtils.isEmpty(list)){
                // 补充支付流水号
                replenishPaymentNo(list);
            }
        }
        return responseBase;
    }

    @ApiOperation(value = "会员批量充值", notes = "会员批量充值")
    @PostMapping("/batch/recharge")
    public ResponseBase<Boolean> batchRecharge(@Valid @RequestBody MemberBatchRechargeAddReqDTO reqDTO, @RequestHeader String userName, @RequestHeader String merCode) {
        reqDTO.setCreateName(userName);
        reqDTO.setMerCode(merCode);
        return rechargeClient.batchRecharge(reqDTO);
    }


    @ApiOperation(value = "批量充值记录列表", notes = "批量充值记录列表")
    @PostMapping("/batch/list")
    public ResponseBase<PageDTO<MemberBatchRechargeRespDTO>> batchList(@Valid @RequestBody MemberBatchRechargePageReqDTO reqDTO, @RequestHeader String merCode) {
        reqDTO.setMerCode(merCode);
        return rechargeClient.batchList(reqDTO);
    }

    @ApiOperation(value = "查询批量充值会员记录", notes = "查询批量充值会员记录")
    @PostMapping("/batch/detail")
    public ResponseBase<PageDTO<MemberBatchRechargeDetailRespDTO>> batchDetail(@Valid @RequestBody MemberRechargeDetailPageReqDTO reqDTO, @RequestHeader String merCode) {
        reqDTO.setMerCode(merCode);
        return rechargeClient.batchDetail(reqDTO);
    }


    private void replenishPaymentNo(List<MemberRechargeDetailRespDTO> list) {
        ResponseBase<List<OrderPaymentLogDTO>> responseBase = payAfterClient.getPaymentLogs(
                list.stream().filter(dto -> Objects.nonNull(dto.getId())).map(dto -> dto.getId().toString()).collect(Collectors.toList()));
        if (responseBase != null && responseBase.checkSuccess() && !CollectionUtils.isEmpty(responseBase.getData())) {
            Map<String, String> paymentNoMap = responseBase.getData().stream().filter(dto -> !StringUtils.isEmpty(dto.getBusinessId()) && !StringUtils.isEmpty(dto.getPaymentNo()))
                    .collect(Collectors.toMap(OrderPaymentLogDTO::getBusinessId, OrderPaymentLogDTO::getPaymentNo));
            list.forEach(dto -> {
                if(Objects.nonNull(dto.getId()) && paymentNoMap.containsKey(dto.getId().toString())) {
                    dto.setPaymentNo(paymentNoMap.get(dto.getId().toString()));
                }
            });
        }
    }

    @ApiOperation(value = "导出充值记录列表")
    @PostMapping("/export")
    public ResponseBase export(@Valid @RequestBody MemberRechargePageReqDTO reqDTO, @RequestHeader String userName, BindingResult result, @RequestHeader String merCode) {
        this.checkValid(result);
        reqDTO.setMerCode(merCode);
        reqDTO.setUserName(userName);
        if(reqDTO.getType() != null && reqDTO.getType() == 2) {
//            YdjTask ydjTask = new YdjTask();
//            List<Long> ids = middleIdClient.getId(1);
//            ydjTask.setId(ids.get(0).toString());
//            ydjTask.setMerCode(reqDTO.getMerCode());
//            ydjTask.setCommand(JSON.toJSONString(reqDTO));
//            ydjTask.setStatus(TaskStatus.WAIT.getCode());
//            ydjTask.setType(MEMBER_RECHARGE_REPORT_EXPORT.toString());
//            ydjTask.setIsDelete(YesOrNoType.NO.getCode());
//            ydjTask.setCreateTime(new Date());
//            ydjTask.setCreateName(userName);
//            ydjTask.setOrgCode(empAuthHandler.queryInfoByAccount(userName, reqDTO.getMerCode()).getSubOrgCode());
//            ydjTaskRepo.insert(ydjTask);
            CommonExportReqDTO commonExport = new CommonExportReqDTO()
                    .setBusinessType(ExportType.MEMBER_RECHARGE_REPORT_EXPORT.toString())
                    .setQueryDTO(reqDTO)
                    .setMerCode(reqDTO.getMerCode())
                    .setOperator(userName);
            return memberBatchTaskClient.commonExport(commonExport);
        }
        return memberBatchTaskClient.exportMemberRecharge(reqDTO);
    }

    @ApiOperation(value = "导出批量充值记录列表")
    @PostMapping("/batch/export")
    public ResponseBase exportBatch(@Valid @RequestBody MemberRechargeDetailPageReqDTO reqDTO, @RequestHeader String userName, BindingResult result, @RequestHeader String merCode) {
        this.checkValid(result);
        if (reqDTO.getPageSize() > memberExportLimit) {
            return ResponseBase.error(EXPORT_NUM_EXCEEDS_LIMIT.getCode(),
                    String.format(EXPORT_NUM_EXCEEDS_LIMIT.getMsg(), memberExportLimit));
        }
        reqDTO.setMerCode(merCode);
//        YdjTask ydjTask = new YdjTask();
//        List<Long> ids = middleIdClient.getId(1);
//        ydjTask.setId(ids.get(0).toString());
//        ydjTask.setMerCode(reqDTO.getMerCode());
//        ydjTask.setCommand(JSON.toJSONString(reqDTO));
//        ydjTask.setStatus(TaskStatus.WAIT.getCode());
//        ydjTask.setType(MEMBER_BATCH_RECHARGE_EXPORT.toString());
//        ydjTask.setIsDelete(YesOrNoType.NO.getCode());
//        ydjTask.setCreateTime(new Date());
//        ydjTask.setCreateName(userName);
//        ydjTask.setOrgCode(empAuthHandler.queryInfoByAccount(userName, reqDTO.getMerCode()).getSubOrgCode());
//        ydjTaskRepo.insert(ydjTask);
        CommonExportReqDTO commonExport = new CommonExportReqDTO()
                .setBusinessType(ExportType.MEMBER_BATCH_RECHARGE_EXPORT.toString())
                .setQueryDTO(reqDTO)
                .setExtended1(reqDTO.getId().toString())
                .setMerCode(reqDTO.getMerCode())
                .setOperator(userName);
        return memberBatchTaskClient.commonExport(commonExport);
    }

    @ApiOperation(value = "导入批量充值会员")
    @PostMapping("/importBatchRechargeMember")
    public ResponseBase<Map<String, Object>> importBatchRechargeMember(@RequestPart("excelFile") MultipartFile multipartFile,
                                                                     @RequestHeader String merCode) throws IOException {
        // 解析excel文件得到门店编码
        Correspondence<ExcelObject> correspondence = new PositionCorrespondence<>(ExcelObject.class);
        AnalysisRule rule = new AnalysisRule();
        List<List<String>> list = correspondence.resolveContent(multipartFile.getInputStream(), rule,
                WDWUtil.isExcel2003(Objects.requireNonNull(multipartFile.getOriginalFilename())));
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_02);
        list.remove(0);
        //产品要求从第三行开始导入？？？
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_03);
        list.remove(0);
        AssertUtils.notEmpty(list, ErrorType.COUPON_ERROR_03);
        List<MemberBatchRechargeImportDTO> codeList = list.stream()
                .map(sonList -> MemberBatchRechargeImportDTO.builder().memberCard(sonList.get(0).trim()).amount(sonList.get(1).trim()).build()).collect(Collectors.toList());
        AssertUtils.state(codeList.size() <= 1000, ErrorType.MEMBER_BATCH_RECHARGE_IMPORT_ERROR);

        CouponBatchSendRequest couponBatchSendRequest = new CouponBatchSendRequest();
        couponBatchSendRequest.setMerCode(merCode);

        int allSize = codeList.size();
        Map<String, Object> resultMap = new HashMap<>();
        List<String> notExistMember = Lists.newLinkedList();
        int size = 0;
        MemberUsableAmountListReqDTO reqDTO = new MemberUsableAmountListReqDTO();
        reqDTO.setMerCode(merCode);
        reqDTO.setMemberCards(codeList.stream().filter(code -> !StringUtils.isEmpty(code)).map(MemberBatchRechargeImportDTO::getMemberCard).distinct().collect(Collectors.toList()));
        ResponseBase<List<MemberBatchRechargeDetailDTO>> responseBase = rechargeClient.getUsableAmountInfoList(reqDTO);
        Map<String, MemberBatchRechargeDetailDTO> map = new HashMap<>();
        if(responseBase.checkSuccess() && !CollectionUtils.isEmpty(responseBase.getData())){

            map = responseBase.getData().stream().collect(Collectors.toMap(MemberBatchRechargeDetailDTO::getMemberCard, Function.identity()));
            List<String> exitMemberList = responseBase.getData().stream().map(MemberBatchRechargeDetailDTO::getMemberCard).collect(Collectors.toList());
            List<String> fail = codeList.stream().filter(code -> !StringUtils.isEmpty(code.getMemberCard()) && !exitMemberList.contains(code.getMemberCard())).map(MemberBatchRechargeImportDTO::getMemberCard).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(fail)) {
                notExistMember.addAll(fail);
            }
        }else{
            notExistMember.addAll(codeList.stream().map(MemberBatchRechargeImportDTO::getMemberCard).filter(memberCard -> !StringUtils.isEmpty(memberCard)).collect(Collectors.toList()));
        }
        List<String> memberCards = codeList.stream().map(MemberBatchRechargeImportDTO::getMemberCard).filter(memberCard -> !StringUtils.isEmpty(memberCard)).collect(Collectors.toList());
        List<String> repeatMemberCards = memberCards.stream().filter(e->Collections.frequency(memberCards,e)>1).distinct().collect(Collectors.toList());

        String suffix = "";
        String originalFilename = multipartFile.getOriginalFilename();
        if (!StringUtils.isEmpty(originalFilename)) {
            int i = originalFilename.lastIndexOf(".");
            if (i < 1) {
                throw WarnException.builder()
                        .code(LocalError.FOLDER_FORMAT_ERROR.getCode())
                        .tipMessage(LocalError.FOLDER_FORMAT_ERROR.getMsg()).build();
            }
            int length = originalFilename.length();
            suffix = originalFilename.substring(i, length);
        }

        List<MemberBatchRechargeImportExcelRespDTO> failList = new ArrayList<>();
        List<MemberBatchRechargeImportExcelRespDTO> successList = new ArrayList<>();
        List<MemberBatchRechargeImportExcelRespDTO> finalFailList = failList;
        codeList.forEach(code ->{
            MemberBatchRechargeImportExcelRespDTO respDTO = new MemberBatchRechargeImportExcelRespDTO()
                    .setMemberCard(code.getMemberCard()).setAmount(code.getAmount());
            List<String> reasons = new ArrayList<>();
            if(StringUtils.isEmpty(code.getMemberCard())) {
                reasons.add("会员卡号必填");
            }else{
                if(notExistMember.contains(code.getMemberCard())){
                    reasons.add("会员卡号不存在");
                }
                if(repeatMemberCards.contains(code.getMemberCard())){
                    reasons.add("会员卡号重复");
                }
            }
            if(StringUtils.isEmpty(code.getAmount())) {
                reasons.add("充值金额必填");
            }else{
                if(StringUtil.isNumber(code.getAmount()) && new BigDecimal(code.getAmount()).compareTo(new BigDecimal("999999")) <=0){
                    if(code.getAmount().indexOf(".") > 0 && code.getAmount().split("\\.")[1].length() > 2){
                        reasons.add("充值金额只能输入大于0，小于等于999,999的数字，最多2位小数");
                    }
                }else{
                    reasons.add("充值金额只能输入大于0，小于等于999,999的数字，最多2位小数");
                }
            }
            if(!CollectionUtils.isEmpty(reasons)) {
                respDTO.setResult(String.join(";", reasons));
                finalFailList.add(respDTO);
            }else{
                successList.add(respDTO);
            }
        });
        if(!CollectionUtils.isEmpty(successList)){
            Map<String, MemberBatchRechargeDetailDTO> finalMap = map;
            List<MemberBatchRechargeDetailDTO> batchRechargeDetails = successList.stream().map(success ->{
                if(finalMap.containsKey(success.getMemberCard())){
                    MemberBatchRechargeDetailDTO detailDTO = ModelConvertUtils.convert(finalMap.get(success.getMemberCard()),MemberBatchRechargeDetailDTO.class);
                    if(detailDTO != null && !StringUtils.isEmpty(success.getAmount())) {
                        detailDTO.setRechargeAmount(new BigDecimal(success.getAmount()));
                    }
                    return detailDTO;
                }
                return null;
            }).collect(Collectors.toList());
            resultMap.put("successList", batchRechargeDetails.stream().filter(Objects::nonNull).collect(Collectors.toList()));
            size = batchRechargeDetails.size();
        }
        if(!CollectionUtils.isEmpty(failList)) {
            // 上传失败会员文件
            File file = FileUtil.writeExcel("导入失败数据", MemberBatchRechargeImportExcelRespDTO.class, failList, "", null);
            String key = appName + SLASH + merCode + SLASH + System.currentTimeMillis() + SLASH + "导入失败数据" + suffix;
            ossFileService.upload(file, key);
            FileUtil.deleteRecursively(file);
            resultMap.put("failMemberFileName", key);
        }
        resultMap.put("successSize", size + "");
        resultMap.put("failSize", (allSize - size) + "");

        return generateSuccess(resultMap);
    }
}
