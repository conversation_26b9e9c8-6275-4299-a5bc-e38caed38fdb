package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.commodity.CommodityReportReq;
import cn.hydee.ydjia.merchantmanager.dto.req.commodity.PlatformDetailReq;
import cn.hydee.ydjia.merchantmanager.dto.resp.commodity.CommodityReportResp;
import cn.hydee.ydjia.merchantmanager.dto.resp.commodity.PlatformDetailResp;
import cn.hydee.ydjia.merchantmanager.feign.CommodityReportClient;
import cn.hydee.ydjia.merchantmanager.service.CommodityReportService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import cn.hydee.ydjia.merchantmanager.util.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.Set;
import java.util.regex.Pattern;

/**
 * <p>
 * 商品报表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-29
 */
@Api(value = "商品报表管理控制器")
@RestController
@RequestMapping("/${api.version}/report")
public class CommodityReportController extends AbstractController {
    private final CommodityReportClient reportClient;
    private final CommodityReportService commodityReportService;

    public CommodityReportController(CommodityReportClient reportClient, CommodityReportService commodityReportService) {
        this.reportClient = reportClient;
        this.commodityReportService = commodityReportService;
    }


    @ApiOperation(value = "根据条件查询报表", notes = "根据条件查询报表")
    @PostMapping("/_search")
    public ResponseBase<PageDTO<CommodityReportResp>> searchByParam(@RequestBody CommodityReportReq param, BindingResult result) {
        checkValid(result);
        return commodityReportService.searchByParam(param);
    }


    @ApiOperation(value = "根据商品条码获取平台详情", notes = "根据商品条码获取平台详情")
    @PostMapping("/detail")
    public ResponseBase<PlatformDetailResp> getPlatformDetail(@RequestBody PlatformDetailReq req, BindingResult result) {
        checkValid(result);
        return reportClient.getPlatformDetail(req);
    }


    @ApiOperation(value = "获取商户有门店的地区列表", notes = "获取商户有门店的地区列表")
    @GetMapping("/citys/{merCode}")
    public ResponseBase<Set<String>> getMerchantsCity(@PathVariable("merCode") String merCode) {
        ValidationUtils.throwError(!Pattern.matches(LocalConst.MER_CODE_PATTERN, merCode), LocalError.MERCODE_IS_ERROR);
        return generateObjectSuccess(commodityReportService.getMerchantsCity(merCode));
    }

}

