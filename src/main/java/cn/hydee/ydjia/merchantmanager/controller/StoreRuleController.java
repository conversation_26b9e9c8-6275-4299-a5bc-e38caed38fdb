package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.StoreRuleReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreRuleResDTO;
import cn.hydee.ydjia.merchantmanager.service.StoreRuleService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 门店规则控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/2
 */
@RestController
@Api(tags = "门店规则控制器")
@RequestMapping("/${api.version}/storeRule")
@AllArgsConstructor
public class StoreRuleController extends AbstractController {

    private final StoreRuleService storeRuleService;

    @ApiOperation(value = "新增or修改门店规则配置")
    @PostMapping("/saveOrUpdate")
    public ResponseBase<Boolean> saveOrUpdate(
            @Valid @RequestBody StoreRuleReqDTO reqDTO,
            @RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName) {
        return generateObjectSuccess(storeRuleService.saveOrUpdate(reqDTO, userName));
    }

    @ApiOperation(value = "查询门店规则配置", notes = "merCode：商户编码")
    @GetMapping("/getDetail")
    public ResponseBase<StoreRuleResDTO> getDetail(@RequestParam(value = "merCode") String merCode) {
        return generateObjectSuccess(storeRuleService.getDetail(merCode));
    }

}
