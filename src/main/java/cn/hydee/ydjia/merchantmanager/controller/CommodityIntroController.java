package cn.hydee.ydjia.merchantmanager.controller;


import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.dto.CommodityIntroDTO;
import cn.hydee.ydjia.merchantmanager.feign.CommodityIntroClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 商家商品说明控制器 OK
 * <AUTHOR>
 * @date 2019/10/9 16:41
 */
@RestController
@RequestMapping("/${api.version}/comm-intro")
@Api(value="商品说明管理",description = "商品说明")
public class CommodityIntroController extends AbstractController {

    @Autowired
    private CommodityIntroClient commodityIntroClient;

    @ApiOperation(value = "新增或者更新商品说明", notes = "新增或者更新商品说明")
    @PostMapping
    public ResponseBase addOrUpdate(@Valid @RequestBody CommodityIntroDTO intro, BindingResult result) {
        checkValid(result);
        return commodityIntroClient.addOrUpdate(intro);

    }
    @ApiOperation(value = "查询商品说明", notes = "根据商品ID,查询商品说明")
    @GetMapping("{commodityId}")
    public ResponseBase<CommodityIntroDTO> getCommodityDetail(@PathVariable String commodityId){
        if(StringUtils.isEmpty(commodityId)){
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        return commodityIntroClient.getCommodityDetail(commodityId);

    }

}
