package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.ProductAttrKey;
import cn.hydee.ydjia.merchantmanager.feign.ProductAttrKeyClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
@Api(value = "海典标库属性键管理", description = "海典标库属性键管理")
@RestController
@RequestMapping("/${api.version}/attr-key")
public class ProductAttrKeyController extends AbstractController {

    @Resource
    private ProductAttrKeyClient productAttrKeyClient;

    @ApiOperation(value = "属性键列表信息", notes = "属性键列表信息")
    @GetMapping(value = "/list")
    public ResponseBase<List<ProductAttrKey>> getProductAttrKeyList() {
        return productAttrKeyClient.getProductAttrKeyList();
    }
}
