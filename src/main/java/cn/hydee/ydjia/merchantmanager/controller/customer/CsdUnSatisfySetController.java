package cn.hydee.ydjia.merchantmanager.controller.customer;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.csd.CsdUnSatisfyReq;
import cn.hydee.ydjia.merchantmanager.dto.csd.CsdUnSatisfySet;
import cn.hydee.ydjia.merchantmanager.feign.MiddlePushClient;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 客服不满意设置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@RestController
@RequestMapping("/${api.version}/unSatisfy-set")
public class CsdUnSatisfySetController extends AbstractController {
    private final MiddlePushClient pushClient;

    public CsdUnSatisfySetController(MiddlePushClient pushClient) {
        this.pushClient = pushClient;
    }

    @ApiOperation(value = "添加或修改不满意原因设置")
    @PostMapping(value = "/_saveOrUpdate")
    public ResponseBase<Boolean> saveOrUpdate(@RequestBody CsdUnSatisfyReq req) {
        return pushClient.saveOrUpdateSet(req);
    }

    @ApiOperation(value = "删除设置选项")
    @DeleteMapping(value = "/_deleteSet/{merCode}/{unSatisfyId}")
    public ResponseBase<Boolean> deleteSet(@PathVariable String merCode, @PathVariable Long unSatisfyId) {
        CsdUnSatisfyReq req = new CsdUnSatisfyReq();
        req.setMerCode(merCode);
        req.setUnSatisfyId(unSatisfyId);
        return pushClient.deleteSet(req);
    }

    @ApiOperation(value = "获取不满意设置选项列表")
    @PostMapping(value = "/list")
    public ResponseBase<List<CsdUnSatisfySet>> queryList(@RequestBody CsdUnSatisfyReq req) {
        return pushClient.queryUnSatisfyList(req);
    }
}

