package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageBase;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.wx.WxMsgReplyRuleDTO;
import cn.hydee.ydjia.merchantmanager.dto.wx.WxMsgReplySettingDTO;
import cn.hydee.ydjia.merchantmanager.feign.WeChatMpMsgReplyClient;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2021/1/25
 **/
@Slf4j
@RestController
@AllArgsConstructor
@Api("微信公众号平台消息回复接口管理")
@RequestMapping(value = "/${api.version}/weChatMp/msgReply")
public class WeChatMpMsgReplyController extends AbstractController {
    private final WeChatMpMsgReplyClient weChatMpMsgReplyClient;

    @GetMapping("/switch")
    @ApiOperation(value = "查看消息回复开关", notes = "")
    public ResponseBase<WxMsgReplySettingDTO> getReplySwitch(@RequestHeader(LocalConst.HEAD_MER_CODE_KEY) String merCode,
                                                             @RequestParam("ruleType") Integer ruleType) {
        return weChatMpMsgReplyClient.checkReplySwitch(merCode, ruleType);
    }

    /**
     * 开启或关闭消息回复1:关键词回复2:收到消息回复3:被关注回复
     *
     * @param wxMsgReplySettingDTO
     * @return
     */
    @PostMapping("/switch")
    @ApiOperation(value = "开启或关闭消息回复开关", notes = "")
    public ResponseBase<Boolean> changeReplySwitch(@RequestHeader(LocalConst.HEAD_MER_CODE_KEY) String merCode,
                                                   @Valid @RequestBody WxMsgReplySettingDTO wxMsgReplySettingDTO) {
        wxMsgReplySettingDTO.setMerCode(merCode);
        return weChatMpMsgReplyClient.changeReplySwitch(wxMsgReplySettingDTO);
    }

    /**
     * 新增或修改规则
     *
     * @param wxMsgReplyRuleDTO
     * @return
     */
    @PostMapping
    @ApiOperation(value = "保存消息回复规则", notes = "")
    public ResponseBase<Boolean> saveMsgReplyRule(@RequestHeader(LocalConst.HEAD_MER_CODE_KEY) String merCode,
                                                  @Valid @RequestBody WxMsgReplyRuleDTO wxMsgReplyRuleDTO) {
        wxMsgReplyRuleDTO.setMerCode(merCode);
        return weChatMpMsgReplyClient.saveMsgReplyRule(wxMsgReplyRuleDTO);
    }

    /**
     * 分页获取消息回复规则
     *
     * @param merCode
     * @param ruleType
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "获取消息回复规则", notes = "")
    public ResponseBase<PageDTO<WxMsgReplyRuleDTO>> getMsgReplyRule(@RequestHeader(LocalConst.HEAD_MER_CODE_KEY) String merCode,
                                                                    @RequestParam("ruleType") Integer ruleType,
                                                                    @RequestBody PageBase pageDTO) {
        return weChatMpMsgReplyClient.getMsgReplyRule(merCode, ruleType, pageDTO.getPageSize(), pageDTO.getCurrentPage());
    }

    /**
     * 删除消息回复规则
     *
     * @param ruleId
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除消息回复规则", notes = "")
    public ResponseBase<Boolean> deleteMsgReplyRule(@RequestParam("ruleId") Integer ruleId) {
        return weChatMpMsgReplyClient.deleteMsgReplyRule(ruleId);
    }
}
