package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.config.WeeChatConfiguration;
import cn.hydee.ydjia.merchantmanager.dto.MemberWeappRegSettingDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.merchant.MerchantResDTO;
import cn.hydee.ydjia.merchantmanager.enums.MerchantProductEnum;
import cn.hydee.ydjia.merchantmanager.enums.page.WeChatPageEnum;
import cn.hydee.ydjia.merchantmanager.feign.BaseInfoClient;
import cn.hydee.ydjia.merchantmanager.feign.MemberWeAppRegSettingClient;
import cn.hydee.ydjia.merchantmanager.service.MerchantPlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/weappRegSetting")
@Api(value = "小程序注册设置管理控制器", description = "小程序注册设置管理控制器")
public class MemberWeappRegSettingController extends AbstractController {

    @Autowired
    private MemberWeAppRegSettingClient weappRegSettingService;

    @Autowired
    private WeeChatConfiguration weeChatConfiguration;

    @Autowired
    private MerchantPlatformService merchantPlatformService;

    @Autowired
    private BaseInfoClient baseInfoClient;

    private static final String MER_CODE = "merCode=";


    @ApiOperation(value = "小程序注册设置保存", notes = "小程序注册设置保存")
    @PostMapping("/save")
    public ResponseBase<Boolean> save(@RequestHeader("userName") String userName, @RequestBody MemberWeappRegSettingDTO reqDTO, @RequestHeader String merCode) {
        reqDTO.setUserName(userName);
        reqDTO.setMerCode(merCode);
        return weappRegSettingService.save(reqDTO);
    }

    @ApiOperation(value = "查询会员卡基本信息设置", notes = "查询商户会员卡基本信息设置")
    @GetMapping("/queryWeappRegSetting")
    public ResponseBase<MemberWeappRegSettingDTO> queryWeappRegSetting(@RequestParam String merCode) {
        List<MemberWeappRegSettingDTO.AppletUrl> appletUrls = new ArrayList<>();
        Integer type = merchantPlatformService.isOpenProduct(MerchantProductEnum.MALL.getCode(), merCode);
        ResponseBase<MerchantResDTO> merchantInfoResp = baseInfoClient.queryMerchantInfo(merCode);
        String merCodeStr = MER_CODE + merCode;
        MemberWeappRegSettingDTO.AppletUrl miniPagePath = new MemberWeappRegSettingDTO.AppletUrl()
                .setType(WeChatPageEnum.MINI_PAGE_PATH.getType())
                .setName(WeChatPageEnum.MINI_PAGE_PATH.getName())
                .setUrl(weeChatConfiguration.getMiniPagePath() + "?" + merCodeStr);
        appletUrls.add(miniPagePath);
        if (1 == type) {
            MemberWeappRegSettingDTO.AppletUrl miniMallPagePath = new MemberWeappRegSettingDTO.AppletUrl()
                    .setType(WeChatPageEnum.MINI_MALL_PAGE_PATH.getType())
                    .setName(WeChatPageEnum.MINI_MALL_PAGE_PATH.getName())
                    .setUrl(weeChatConfiguration.getMiniMallPagePath() + "?" + merCodeStr);
            appletUrls.add(miniMallPagePath);
        }
        if (merchantInfoResp.checkSuccess() && null != merchantInfoResp.getData()
                && null != merchantInfoResp.getData().getOpenCloudWareHouse()
                && merchantInfoResp.getData().getOpenCloudWareHouse() == 1) {
            MemberWeappRegSettingDTO.AppletUrl miniYcPagePath = new MemberWeappRegSettingDTO.AppletUrl()
                    .setType(WeChatPageEnum.MINI_YC_PAGE_PATH.getType())
                    .setName(WeChatPageEnum.MINI_YC_PAGE_PATH.getName())
                    .setUrl(weeChatConfiguration.getMiniYcPagePath() + "?" + merCodeStr);
            appletUrls.add(miniYcPagePath);
        }
        ResponseBase<MemberWeappRegSettingDTO> responseBase = weappRegSettingService.queryWeappRegSetting(merCode);
        MemberWeappRegSettingDTO settingDTO = responseBase.getData();
        if(settingDTO != null){
            settingDTO.setAppletUrls(appletUrls);
        }
        return responseBase;
    }
}
