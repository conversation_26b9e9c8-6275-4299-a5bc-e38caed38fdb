package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.MerChantSetReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.QuerySetReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjMerSetResDTO;
import cn.hydee.ydjia.merchantmanager.service.YdjMerSetService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020/5/13 15:36
 */
@RestController
@RequestMapping(value = "/${api.version}/merSet")
@Api(value="配置商户参数",description = "配置商户参数")
public class YDJMerSetController extends AbstractController {

    @Autowired
    private YdjMerSetService ydjMerSetService;

    @ApiOperation(
            value = "商户配置参数列表",
            notes = "商户配置参数列表",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_search")
    public ResponseBase<PageDTO<YdjMerSetResDTO>> queryMessageListByCondition(@Valid @RequestBody QuerySetReqDTO querySetReqDTO, BindingResult result) throws IllegalAccessException, InstantiationException {
        ResponseBase<PageDTO<YdjMerSetResDTO>> responseBase = ResponseBase.success();
        responseBase.setData(ydjMerSetService.querySetListByCondition(querySetReqDTO));
        return responseBase;
    }

    @ApiOperation(
            value = "商户配置参数",
            notes = "商户配置参数",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_searchByMerCode")
    public ResponseBase<YdjMerSetResDTO> queryMessageByMerCode(@RequestParam String sysKey,@RequestParam String mercode) {
        ResponseBase<YdjMerSetResDTO> responseBase = ResponseBase.success();
        responseBase.setData(ydjMerSetService.queryMessageByMerCode(sysKey,mercode));
        return responseBase;
    }


    @ApiOperation(value = "新增或修改商户配置信息", notes = "新增或修改商户配置信息")
    @PostMapping("/editMerChantSet")
    public ResponseBase editMerChantSet(@RequestHeader(LocalConst.HEAD_USER_NAME_KEY) String userName, @Valid @RequestBody MerChantSetReqDTO merChantSetReqDTO,
                                       BindingResult result) {
        return generateSuccess(ydjMerSetService.editMerChantSet(merChantSetReqDTO,userName));
    }


}
