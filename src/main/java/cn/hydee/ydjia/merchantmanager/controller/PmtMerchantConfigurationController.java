package cn.hydee.ydjia.merchantmanager.controller;


import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.PmtMerchantConfigurationReqDTO;
import cn.hydee.ydjia.merchantmanager.feign.PromoteClient;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;

/**
 * <p>
 * 商户活动配置表，商户控制活动的开关 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-19
 */
@RestController
@RequestMapping("/${api.version}/pmtMerchantConfiguration")
public class PmtMerchantConfigurationController extends AbstractController {

    @Autowired
    private PromoteClient promoteClient;

    /**
     * 查询互动开关状态
     * @param dto 请求dto
     * @param result 校验参数
     * @return
     * @throws IOException
     */
    @PostMapping("/searchActivityStatus")
    @ApiOperation(value = "查询活动状态")
    public ResponseBase searchActivityStatus(@Valid @RequestBody PmtMerchantConfigurationReqDTO dto, BindingResult result) throws IOException {
        checkValid(result);
        return promoteClient.searchActivityStatus(dto);
    }

    /**
     * 活动开关控制
     * @param dto 请求dto
     * @param result 参数校验
     * @return
     * @throws IOException
     */
    @PostMapping("/activityOpenOrClose")
    @ApiOperation(value = "活动开关")
    public ResponseBase activityOpenOrClose(@Valid @RequestBody PmtMerchantConfigurationReqDTO dto, BindingResult result) throws IOException {
        checkValid(result);
        return promoteClient.activityOpenOrClose(dto);
    }

}

