package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.*;
import cn.hydee.ydjia.merchantmanager.dto.req.QualifiedActivityStoreListReq;
import cn.hydee.ydjia.merchantmanager.dto.req.isp.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.chain.EmpActuateRankReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.chain.MerEmpRewardRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.chain.RewardRankGatherRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.isp.CommodityMatchFail;
import cn.hydee.ydjia.merchantmanager.dto.sms.feign.ExpectSmsCostRequest;
import cn.hydee.ydjia.merchantmanager.dto.sms.feign.ExpectSmsCostResponse;
import cn.hydee.ydjia.merchantmanager.feign.*;
import cn.hydee.ydjia.merchantmanager.util.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import static cn.hydee.ydjia.merchantmanager.util.LocalConst.HEAD_USER_NAME_KEY;


/**
 * 活动广场
 * <AUTHOR>
 * @date 2022/5/18
 */
@Slf4j
@RestController
@Api(tags = "厂家买单-活动广场控制器")
@RequestMapping("/${api.version}/activitySquare")
@Deprecated // 服务商业务
public class ActivitySquareController extends AbstractController {


    @Value("${activityIsp.singleSmsFee}")
    private Long singleSmsFee;


    @Autowired
    private SmsServiceClient smsServiceClient;

    @Autowired
    private BaseInfoClient baseInfoClient;


    @PostMapping("/list")
    @ApiOperation(value = "活动列表")
    public ResponseBase<PageDTO<ISPActivityListResDTO>> list(@RequestHeader String merCode,
                                                             @RequestHeader(HEAD_USER_NAME_KEY) String userName, @RequestBody ActivityIspPageReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());
    }

    @PostMapping("/detail/baseInfo")
    @ApiOperation(value = "活动详情-基础信息")
    public ResponseBase<ISPActivityBaseInfoResDTO> detail(@RequestHeader String merCode,
                                                          @Valid @RequestBody ActivityIspDetailReqDTO reqDTO) {
        return ResponseBase.success();

    }

    @PostMapping("/detail/couponList")
    @ApiOperation(value = "活动详情-优惠券列表")
    public ResponseBase<PageDTO<ActivityCouponIspRespDTO>> couponList(@RequestHeader String merCode,
                                                                      @Valid @RequestBody ActivityIspDetailReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());

    }

    @ApiOperation("分页获取工业活动单品激励、目标达成激励信息")
    @PostMapping("/detail/getSingleExcitationPage")
    public ResponseBase<PageDTO<SingleExcitationDetailRespDTO>> getSingleExcitationPage(@Valid @RequestBody ExcitationPageReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());

    }

    @ApiOperation("分页获取工业活动组合商品销售激励信息")
    @PostMapping("/getCombinationExcitationPage")
    public ResponseBase<PageDTO<CombinationExcitationDetailRespDTO>> getCombinationExcitationPage(@Valid @RequestBody ExcitationPageReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());

    }

    @ApiOperation(value = "分页获取工业活动单品动销排名激励")
    @PostMapping("/detail/policySingleQuery")
    public ResponseBase<CommoditySimpleRankQueryRespDTO> queryIspActivityExcitationList(@RequestBody CommoditySimpleRankQueryReqDTO reqDTO) {
        return ResponseBase.success();

    }

    @ApiOperation(value = "分页获取销售周、月排名激励")
    @PostMapping("/policy/saleAmount/query")
    public ResponseBase<CommoditySaleAmountRankQueryRespDTO> policySaleAmountQuery(@Valid @RequestBody CommoditySaleAmountRankQueryReqDTO req) {
        return ResponseBase.success();

    }

    @PostMapping("/detail/smsReachList")
    @ApiOperation(value = "活动详情-短信触达列表")
    public ResponseBase<PageDTO<ActivityMessageIspRespDTO>> smsReachList(@RequestHeader String merCode,
                                                                         @Valid @RequestBody ActivityIspDetailReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());

    }

    @PostMapping("/getSendSmsAndCouponInfo")
    @ApiOperation(value = "获取群发短信和优惠券信息")
    public ResponseBase<ActivitySendSmsAndCouponRespDTO> getSendSmsAndCouponInfo(@RequestHeader String merCode,
                                                                                 @Valid @RequestBody ActivitySendSmsAndCouponReqDTO reqDTO) {
        return ResponseBase.success();

    }

    @PostMapping("/beforeJoin/checkCommodity")
    @ApiOperation(value = "参加活动前校验商户参与商品")
    public ResponseBase<ActivityIspPrepJoinDTO> checkCommodity(@RequestHeader String merCode,
                                                               @Valid @RequestBody ActivityIspJoinPrepReqDTO reqDTO) {
        return ResponseBase.success();

    }

    @PostMapping("/beforeJoin/checkArea")
    @ApiOperation(value = "参加活动前校验商户参与区域")
    public ResponseBase<ActivityIspPrepJoinDTO> checkArea(@RequestHeader String merCode,
                                                          @Valid @RequestBody ActivityIspJoinPrepReqDTO reqDTO) {
        return ResponseBase.success();

    }

    @PostMapping("/join")
    @ApiOperation(value = "参加活动")
    public ResponseBase<ActivityIspJoinRespDTO> join(@RequestHeader String merCode,
                                                     @RequestHeader String userName,
                                                     @Valid @RequestBody ActivityIspJoinReqDTO reqDTO) {
        return ResponseBase.success();

    }

    @PostMapping("/getSendSmsFee")
    @ApiOperation(value = "获取短信发送费用")
    public ResponseBase<ActivitySendSmsFeeRespDTO> getSendSmsFee(@RequestHeader String merCode,
                                                                 @Valid @RequestBody ActivitySendSmsFeeReqDTO reqDTO) {
        //获取短信签名
        ResponseBase<MerchantSmsStatusDTO> responseBase = smsServiceClient.queryMerchantStatus(merCode);
        MerchantSmsStatusDTO merchantSmsStatus = responseBase.getData();
        String sign = "";
        if(merchantSmsStatus != null && StringUtils.isNotBlank(merchantSmsStatus.getSign())){
            sign = merchantSmsStatus.getSign();
        }
        ExpectSmsCostRequest request = new ExpectSmsCostRequest();
        request.setSmsContent(sign + reqDTO.getMessageContent() + LocalConst.SMS_CONTENT_SUFFIX);
        ResponseBase<ExpectSmsCostResponse> response = smsServiceClient.expectSmsCost(request);
        ValidationUtils.checkResult(response);
        Integer messageNum = response.getData().getSmsSendNum() * reqDTO.getMemberNum();
        ActivitySendSmsFeeRespDTO respDTO = ActivitySendSmsFeeRespDTO.builder().messageNum(messageNum)
                .messageFee(singleSmsFee * messageNum).build();
        return generateSuccess(respDTO);
    }

    @PostMapping("/queryStoreDynamicSalesCount")
    @ApiOperation(value = "门店动销执行统计")
    public ResponseBase<PageDTO<ActivityStoreDynamicSalesRespDTO>> queryStoreDynamicSalesCount(@RequestHeader String merCode,
                                                                                               @Valid @RequestBody ActivityDynamicSalesReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());

    }

    @PostMapping("/queryEmpDynamicSalesCount")
    @ApiOperation(value = "店员动销执行统计")
    public ResponseBase<PageDTO<ActivityEmpDynamicSalesRespDTO>> queryEmpDynamicSalesCount(@RequestHeader String merCode,
                                                                                           @RequestHeader String userName,
                                                                                           @Valid @RequestBody ActivityEmpDynamicSalesReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());

    }

    @PostMapping("/queryUnfilledAreaStoreNum")
    @ApiOperation(value = "查询未维护地址的门店数量")
    public ResponseBase<Integer> queryUnfilledAreaStoreNum(@RequestHeader String merCode) {
       return baseInfoClient.queryStoreNumNotAddress(merCode);
    }

    @ApiOperation(value = "获取导入的参与门店")
    @PostMapping("/getImportJoinStore")
    public ResponseBase<ActivityIspImportStoreRespDTO> getImportMemberInfoSize(@RequestPart("excelFile") MultipartFile multipartFile,
                                                                     @RequestParam Long activityIspId,
                                                                     @RequestParam String ispCode,
                                                                     @RequestHeader String merCode) throws IOException {
        return ResponseBase.success();
    }


    @ApiOperation(value = "导出店员动销执行统计")
    @PostMapping("/empDynamicSalesExport")
    public ResponseBase empDynamicSalesExport(@RequestHeader String merCode,
                                              @Valid @RequestBody ActivityEmpDynamicSalesReqDTO reqDTO, @RequestHeader String userName, BindingResult result) {
        return ResponseBase.success();
    }

    @ApiOperation(value = "导出门店动销执行统计")
    @PostMapping("/storeDynamicSalesExport")
    public ResponseBase storeDynamicSalesExport(@RequestHeader String merCode,
                                                @Valid @RequestBody ActivityDynamicSalesReqDTO reqDTO, @RequestHeader String userName, BindingResult result) {
        return ResponseBase.success();
    }


    @ApiOperation("门店销售排行")
    @PostMapping("/storeSaleRank")
    public ResponseBase<PageDTO<StoreSaleRankRespDTO>> storeSaleRank(@RequestHeader String merCode,
                                                                     @RequestBody SaleRankReqDTO reqDTO,
                                                                     @RequestHeader String userName) {
        return generateSuccess(new PageDTO<>());
    }

    @ApiOperation("店员销售排行")
    @PostMapping("/empSaleRank")
    public ResponseBase<PageDTO<EmpSaleRankRespDTO>> empSaleRank(@RequestHeader String merCode,
                                                                 @RequestBody SaleRankReqDTO reqDTO,
                                                                 @RequestHeader String userName) {
        return generateSuccess(new PageDTO<>());
    }

    @ApiOperation("查询专属巡店建议")
    @PostMapping("/exclusiveStoreAdviceList")
    public ResponseBase<List<ExclusiveStoreAdviceRespDto>> exclusiveStoreAdviceList(@RequestHeader String merCode,
                                                                                    @Valid @RequestBody ExclusiveStoreAdviceReqDto reqDto) {
        return ResponseBase.success();

    }


    @ApiOperation(value = "导出专属巡店建议")
    @PostMapping("/exportExclusiveStoreAdvice")
    public ResponseBase exportDynamicSales(@RequestHeader String merCode,
                                           @Valid @RequestBody ExclusiveStoreAdviceReqDto request,
                                           @RequestHeader String userName) {
        return ResponseBase.success();
    }

    @ApiOperation(value = "导出门店销售排行")
    @PostMapping("/storeSaleRankExport")
    public ResponseBase storeSaleRankExport(@RequestHeader String merCode,
                                            @Valid @RequestBody SaleRankReqDTO reqDTO,
                                            @RequestHeader String userName, BindingResult result) {
        return ResponseBase.success();
    }

    @ApiOperation(value = "导出店员销售排行")
    @PostMapping("/empSaleRankExport")
    public ResponseBase empSaleRankExport(@RequestHeader String merCode,
                                          @Valid @RequestBody SaleRankReqDTO reqDTO,
                                          @RequestHeader String userName, BindingResult result) {
        return ResponseBase.success();
    }

    @ApiOperation(value = "查询活动销售排行激励政策集合")
    @GetMapping("/excitationList")
    public ResponseBase<List<ActivityExcitationIspRespDTO>> queryIspActivityExcitationList(@RequestParam(value = "ispCode") String ispCode,
                                                                                           @RequestParam(value = "id") Long id) {
        return ResponseBase.success();
    }

    @ApiOperation(value = "激励明细列表")
    @PostMapping("/saleRewardRank")
    ResponseBase<MerEmpRewardRespDTO> saleRewardRank(@RequestHeader String merCode,
                                                     @RequestHeader String userName,
                                                     @RequestBody EmpActuateRankReqDTO reqDTO) {
        return ResponseBase.success();

    }

    @ApiOperation(value = "导出激励明细")
    @PostMapping("/exportSaleRewardRank")
    ResponseBase exportSaleRewardRank(@RequestHeader String merCode,
                                      @RequestBody EmpActuateRankReqDTO reqDTO,
                                      @RequestHeader String userName) {
        return ResponseBase.success();
    }


    @ApiOperation(value = "导出激励明细(按日期汇总)")
    @PostMapping("/exportSaleRewardRankDate")
    @Deprecated // 服务商业务
    ResponseBase exportSaleRewardRankDate(@RequestHeader String merCode,
                                          @RequestBody EmpActuateRankReqDTO reqDTO,
                                          @RequestHeader String userName) {
        return ResponseBase.success();
    }


    @PostMapping("/qualifiedActivityStoreList")
    @ApiOperation(value = "查询活动可添加门店列表，剔除已参与的门店")
    public ResponseBase<List<AreaStoreResDTO>> qualifiedActivityStoreList(@RequestHeader String merCode,
                                                                          @RequestBody QualifiedActivityStoreListReq req) {
        return ResponseBase.success();

    }

    /**
     * 商品中台商品与标库批量对码
     */
    @PostMapping("/matchPlatform")
    public ResponseBase<List<CommodityMatchFail>> batchMatchPlat(@Valid @RequestBody ProductMatch productMatch) {
        return ResponseBase.success();
    }


    @ApiOperation("激励厂家排行汇总")
    @PostMapping("/queryRewardRankGather")
    public ResponseBase<PageDTO<RewardRankGatherRespDTO>> queryRewardRankGather(@RequestHeader String merCode,
                                                                                @RequestBody EmpActuateRankReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());

    }


    @ApiOperation(value = "导出激励厂家汇总")
    @PostMapping("/exportRewardRankGather")
    ResponseBase exportRewardRankGather(@RequestHeader String merCode,
                                        @RequestBody EmpActuateRankReqDTO reqDTO,
                                        @RequestHeader String userName) {
        return ResponseBase.success();
    }

    @ApiOperation(value = "导出激励厂家汇总(按激励日期)")
    @PostMapping("/exportRewardRankGatherDate")
    ResponseBase exportRewardRankGatherDate(@RequestHeader String merCode,
                                            @RequestBody EmpActuateRankReqDTO reqDTO,
                                            @RequestHeader String userName) {
        return ResponseBase.success();
    }


    @ApiOperation("分页获取工业活动疗程商品销售激励信息")
    @PostMapping("/getTreatmentExcitationPage")
    public ResponseBase<PageDTO<TreatmentExcitationDetailRespDTO>> getTreatmentExcitationPage(@Valid @RequestBody ExcitationPageReqDTO reqDTO) {
        return generateSuccess(new PageDTO<>());
    }
}
