package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.dto.csd.*;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.EmployeeResDTO;
import cn.hydee.ydjia.merchantmanager.enums.YesOrNoType;
import cn.hydee.ydjia.merchantmanager.feign.MiddlePushClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.service.CsdStaffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 客服人员管理器
 *
 * <AUTHOR>
 * @date 2019/11/28
 */
@Api(value = "客服人员管理器", description = "客服人员管理器")
@RestController
@RequestMapping(value = "/${api.version}/csd-staff")
public class CsdStaffController extends AbstractController {

    @Autowired
    private CsdStaffService csdStaffService;

    @Autowired
    private MiddlePushClient middlePushClient;

    @Autowired
    private EmpAuthHandler empAuthHandler;

    /**
     * 提供给登陆服务使用，假接口，后续考虑删除
     * @param userId
     * @return
     */
    @ApiOperation(value = "查询单个客服信息", notes = "根据客服Id查询单个客服信息")
    @GetMapping
    @Deprecated
    public ResponseBase queryCsdStaff(@RequestHeader String userId) {
        return generateObjectSuccess(null);
    }

    @ApiOperation(value = "添加客服")
    @PostMapping(value = "/_addStaff")
    public ResponseBase<Boolean> addStaff(@RequestBody CsdStaffAddReq req) {
        return csdStaffService.addStaff(req);
    }

    @ApiOperation(value = "查询客服列表")
    @PostMapping(value = "/staff-list")
    public ResponseBase<PageDTO<CsdStaffResp>> getStaffList(@RequestBody CsdStaffReq req, @RequestHeader String userId, @RequestHeader String userName) {
        if (StringUtils.isBlank(req.getMerCode())) {
            throw WarnException.builder().tipMessage(ErrorType.PARA_ERROR.getMsg())
                    .code(ErrorType.PARA_ERROR.getCode()).build();
        }
        ResponseBase<PageDTO<CsdStaffResp>> pageDTO = middlePushClient.getStaffList(req);
        if(pageDTO.checkSuccess() && !CollectionUtils.isEmpty(pageDTO.getData().getData())){
            pageDTO.getData().getData().forEach(e -> {
                if(StringUtils.equals(userId, e.getStaffId())){
                    e.setOnlineStatus(YesOrNoType.YES.getCode());
                }
            });
        }
        return pageDTO;
    }

    @ApiOperation(value = "批量查询员工信息（新增客服时使用）")
    @PostMapping(value = "/_searchEmployee")
    public ResponseBase<PageDTO<EmployeeResDTO>> batchQuery(@RequestBody QueryEmpDTO req) {
        return csdStaffService.searchEmployee(req);
    }

    @ApiOperation(value = "查看客服详情")
    @PostMapping(value = "/staff-detail")
    public ResponseBase<CsdStaffResp> getStaffDetail(@RequestBody CsdStaffReq req) {
        return middlePushClient.getStaffDetail(req);
    }

    @ApiOperation(value = "客服数据统计")
    @PostMapping(value = "/staff-statistics")
    public ResponseBase<CsdStatisticsResp> staffStatistics(@RequestBody CsdStatisticsReq req) {
        if (!org.springframework.util.StringUtils.hasText(req.getMerCode())
                || !org.springframework.util.StringUtils.hasText(req.getStatisticsMonth())) {
            throw WarnException.builder().tipMessage(ErrorType.PARA_ERROR.getMsg())
                    .code(ErrorType.PARA_ERROR.getCode()).build();
        }
        if (YesOrNoType.YES.getCode().equals(req.getIsSuper())
                || org.springframework.util.StringUtils.hasText(req.getAll())) {
            req.setStaffId(null);
        }
        return middlePushClient.staffStatistics(req);
    }

    @ApiOperation(value = "删除客服")
    @DeleteMapping(value = "/{merCode}/{staffId}")
    public ResponseBase<Boolean> deleteStaff(@PathVariable String merCode, @PathVariable String staffId) {
        return middlePushClient.deleteStaff(merCode, staffId);
    }

    @ApiOperation(value = "修改客服信息")
    @PostMapping(value = "/_modifyStaff")
    public ResponseBase<Boolean> modifyStaff(@RequestBody CsdStaffReq req) {
        return csdStaffService.modifyStaffStatus(req);
    }

    @ApiOperation(value = "查看单个客服")
    @GetMapping(value = "/single-staff")
    public ResponseBase<CsdStaffDTO> singleStaff(@RequestParam String merCode, @RequestParam String userId) {
        return middlePushClient.singleStaff(merCode, userId);
    }

    @ApiOperation(value = "客服分组占比统计")
    @PostMapping(value = "/group-statistics")
    public ResponseBase<List<CsdGroupResp>> groupStatistics(@RequestBody CsdStatisticsReq req) {
        return middlePushClient.groupStatistics(req);
    }

    @ApiOperation(value = "客服工作量统计")
    @PostMapping(value = "/workload-statistics")
    public ResponseBase<PageDTO<StaffWorkloadData>> workloadStatistics(@RequestBody CsdStatisticsReq req) {
        return middlePushClient.workloadStatistics(req);
    }

    @ApiOperation(value = "客服工作质量统计")
    @PostMapping(value = "/work-quality")
    public ResponseBase<PageDTO<StaffWorkQualityData>> workQualityStatistics(@RequestBody CsdStatisticsReq req) {
        return middlePushClient.workQualityStatistics(req);
    }

}
