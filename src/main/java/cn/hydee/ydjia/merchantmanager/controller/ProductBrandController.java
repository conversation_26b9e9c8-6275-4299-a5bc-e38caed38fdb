package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.ProductBrand;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryBrandDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.BrandRespDTO;
import cn.hydee.ydjia.merchantmanager.feign.ProductBrandClient;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 品牌控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/9/23 16:26
 */
@RestController
@RequestMapping("/${api.version}/brand")
public class ProductBrandController extends AbstractController {

    @Autowired
    private ProductBrandClient productBrandClient;

    @ApiOperation(
            value = "品牌多条件查询",
            notes = "多条件查询",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/_search")
    public ResponseBase<PageDTO<BrandRespDTO>> queryBrandByCondition(@Valid @RequestBody QueryBrandDTO queryBrandDTO, BindingResult result) throws Exception {
        this.checkValid(result);
        return productBrandClient.queryBrandByCondition(queryBrandDTO);
    }

    @ApiOperation(value = "根据品牌ID查询品牌", notes = "根据品牌ID查询品牌")
    @GetMapping("{id}")
    public ResponseBase<BrandRespDTO> getBrandById(@PathVariable String id) {

        if (StringUtils.isEmpty(id)) {
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }

        ProductBrand brand = productBrandClient.getBrandById(id);

        BrandRespDTO brandRespDTO = new BrandRespDTO();
        if (brand != null) {
            BeanUtils.copyProperties(brand, brandRespDTO);
        }

        return this.generateSuccess(brandRespDTO);
    }
}
