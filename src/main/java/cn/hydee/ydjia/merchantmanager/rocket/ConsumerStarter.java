package cn.hydee.ydjia.merchantmanager.rocket;

import cn.hydee.ydjia.merchantmanager.config.RocketConsumerConfig;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


@Component
@Slf4j
public class ConsumerStarter {

    @Autowired
    private PushMessageConsumerService pushMessageConsumerService;

    @Autowired
    private CallBackMessageConsumerService callBackMessageConsumerService;

    @Value("${message-notify.merchant-platform-topic:TOPIC_MERCHANT_PLATFORM_DATA_DEV}")
    private String merchantPlatformTopic;

    @PostConstruct
    public void startConsumer() {
        try {
            log.info("startConsumer topic="+ merchantPlatformTopic);
            pushMessageConsumerService.listener(merchantPlatformTopic, LocalConst.PUSH_MESSAGE_TAG, false);

            callBackMessageConsumerService.listener(merchantPlatformTopic, LocalConst.CALLBACK_MESSAGE_TAG, false);
        } catch (MQClientException e) {
            log.error("消息Consumer启动失败", e);
        }
    }

}
