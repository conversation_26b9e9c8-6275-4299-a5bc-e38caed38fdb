package cn.hydee.ydjia.merchantmanager.enums;


/**
 * 组织机构类型统一枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/18 14:30
 */

public enum StatusEnums {
    STOP_USE(0, "停用"),
    ENABLING(1, "启用"),

    ;

    private Integer code;
    private String msg;

    StatusEnums(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }
    public String getMsg() {
        return msg;
    }

}
