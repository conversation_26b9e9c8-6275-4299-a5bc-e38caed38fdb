package cn.hydee.ydjia.merchantmanager.enums;

/**
 * <AUTHOR>
 * @date 2019/12/31 17:04
 */
public enum  ExpressType {
    SEND_EXPRESS(1, "发货物流"),
    RETURN_EXPRESS(2, "退货物流"),    ;

    private Integer code;
    private String msg;

    ExpressType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
