package cn.hydee.ydjia.merchantmanager.enums;

import lombok.Getter;

/**
 * 下单可用优惠类型枚举
 *
 * <AUTHOR>
 * @create 2020-07-29
 */
@Getter
public enum OrderCouponType {

    COUPON(0, "优惠券"),
    TIME_LIMITED_COUPON(1, "限时优惠"),
    FULL_COUPON(2, "满减活动"),
    ADD_PRICE_COUPON(3, "加价购"),
    MORE_DISCOUNT_COUPON(4, "多买优惠"),
    ;

    private Integer code;
    private String msg;

    OrderCouponType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
