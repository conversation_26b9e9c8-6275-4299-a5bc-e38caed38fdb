package cn.hydee.ydjia.merchantmanager.enums;

import cn.hydee.ydjia.merchantmanager.service.ExportTaskService;

/**
 *
 * <AUTHOR>
 * @date 2022-01-07 14:03
 * @since 1.0
 */
public enum OnlineOrderReportGroupType {

    BY_STORE(ExportTaskService.ONLINE_ORDER_STORE_EXPORT),
    BY_COMMODITY_TYPE(ExportTaskService.ONLINE_ORDER_COMMODITY_TYPE_EXPORT),
    BY_COMMODITY_GROUP(ExportTaskService.ONLINE_ORDER_COMMODITY_GROUP_EXPORT),
    BY_COMMODITY(ExportTaskService.ONLINE_ORDER_COMMODITY_EXPORT),
    ;

    private String exportType;
    OnlineOrderReportGroupType(String exportType){
        this.exportType = exportType;
    }

    private String getExportType(){
        return exportType;
    }

    public static OnlineOrderReportGroupType getGroupTypeByExport(String exportType){
        for(OnlineOrderReportGroupType groupType : OnlineOrderReportGroupType.values()){
            if(groupType.getExportType().equals(exportType)){
                return groupType;
            }
        }
        return null;
    }
}
