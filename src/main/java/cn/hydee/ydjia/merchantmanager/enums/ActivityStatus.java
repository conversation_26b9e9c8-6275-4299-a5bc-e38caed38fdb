package cn.hydee.ydjia.merchantmanager.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/4/15 19:49
 */
public enum ActivityStatus {
    UN_START(0, "未开始"),
    START(1, "正在进行"),
    END(2, "已结束"),

    MARK_DOWN(0, "减价"),
    DISCOUNT(1, "打折"),

    GIFT_YES(1, "送赠品"),
    GIFT_NO(0, "不送赠品"),


    ;

    private Integer code;
    private String msg;

    ActivityStatus(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }
    public String getMsg() {
        return msg;
    }
}
