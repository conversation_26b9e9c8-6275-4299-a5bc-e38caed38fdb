package cn.hydee.ydjia.merchantmanager.enums.shelf;

/**
 * <AUTHOR>
 * @ClassName ProfitType.java
 * @Description 云货架B端首页商品搜索分润比入参枚举类
 * @createTime 2020年10月22日 19:23:00
 */
public enum ProfitType {
    //分润比类型
    TOP_TO_BOTTOM(1, "从高到低"),
    BOTTOM_TO_TOP(2, "从低到高");

    private final Integer code;
    private final String msg;

    ProfitType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
