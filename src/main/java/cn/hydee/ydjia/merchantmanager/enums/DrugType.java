package cn.hydee.ydjia.merchantmanager.enums;


/**
 * 药品类型枚举
 */

public enum DrugType {

    /**
     * 0：甲类OTC，1：处方药，2：乙类OTC，3：非处方药
     */
    A_OTC(0, "甲类OTC"),
    PRESCRIPTION(1, "处方药"),
    B_OTC(2, "乙类OTC"),
    OTC(3, "非处方药"),
    ;


    private Integer code;
    private String name;

    DrugType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
