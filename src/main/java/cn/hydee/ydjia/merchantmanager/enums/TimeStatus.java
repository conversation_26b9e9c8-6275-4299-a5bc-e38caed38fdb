package cn.hydee.ydjia.merchantmanager.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 活动.时间状态
 * 
 * -1: 未开始, 1: 进行中, 0: 已结束
 * 
 * 根据当前时间和活动的时间范围比较，计算出来的时间状态，不需要保存到数据库
 *
 */
public enum TimeStatus {

    PENDING(-1, "未开始"),

    STARTED(1, "进行中"),

    CLOSED(0, "已结束");

//	@EnumValue // 标记数据库存的值是code
    private final Integer code;
    
    private final String name;

    TimeStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    @JsonCreator
    public static TimeStatus getItem(int code){
        for(TimeStatus item : values()){
            if(item.getCode() == code){
                return item;
            }
        }
        
        return null;
    }

	@JsonValue
    public Integer getCode() {
        return code;
    }

	public String getName() {
		return name;
	}

}
