package cn.hydee.ydjia.merchantmanager.enums.sms;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @description: 短信业务场景枚举
 * @date 2022/1/11 18:47
 */

@Getter
public enum BizTypeEnum {

    send_coupons("send_coupons", "手动发券"),
    crowd_marketing("crowd_marketing", "人群营销"),
    bulk_SMS("bulk_SMS", "短信群发"),
    member_notification("member_notification", "会员通知"),
    pharmacy_services("pharmacy_services", "药学服务"),
    order_notification("order_notification", "订单通知"),
    live_notification("live_notification", "直播通知"),
    other_type("other_type", "其他"),
    voice_mission("voice_mission", "智能语音"),
    life_circle("life_circle", "会员生命周期自动维护");

    private String code;

    private String name;

    private static final Map<String, BizTypeEnum> MAPPING = Maps.newHashMapWithExpectedSize(BizTypeEnum.values().length);

    BizTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    static {
        for (BizTypeEnum value : BizTypeEnum.values()) {
            MAPPING.put(value.name(), value);
        }
    }

    public static BizTypeEnum getEnumBy(String name) {
        return MAPPING.get(name);
    }

}
