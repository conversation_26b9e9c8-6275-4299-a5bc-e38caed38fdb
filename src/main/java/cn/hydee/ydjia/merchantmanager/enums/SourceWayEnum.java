package cn.hydee.ydjia.merchantmanager.enums;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: ch<PERSON><PERSON>bin
 * @Description: 注册来源方式枚举
 * @Date: 2021/3/23
 */
public enum SourceWayEnum {

    /**
     * 0其他，1扫描员工推荐码，2扫描门店推荐码，4扫员工企微二维码，5自然流量，6填写员工编码，7填写门店编码，8员工分享商品，9员工分享活动，10员工分享优惠券
     * 11后台修改为员工，12后台修改为门店，13扫员工企微二维码 14门店活码 15门店活码
     */
    OTHER(0, "其他"),//推荐来源:无
    EMPLOYEES(1, "扫描员工推荐码"),//推荐来源:员工
    STORES(2, "扫描门店推荐码"),//推荐来源:门店
    WELCOME(4, "扫员工企微二维码"),//推荐来源:无
    NATURE(5, "自然流量"),//推荐来源:无
    WRITE_EMPLOYEES_CODE(6, "填写员工编码"),//推荐来源:员工
    WRITE_STORES_CODE(7, "填写门店编码"),//推荐来源:门店
    EMPLOYEES_SHARE_GOOD(8, "员工分享商品"),//推荐来源:员工
    EMPLOYEES_SHARE_ACTIVITY(9, "员工分享活动"),//推荐来源:员工
    EMPLOYEES_SHARE_TICKET(10, "员工分享优惠券"),//推荐来源:员工
    WELCOME_TO_EMPLOYEES(13, "扫员工企微二维码"),//推荐来源:员工
    STORE_ACTIVITY(14, "扫门店活码"),
    STORE_ACTIVITY_TO_EMPLOYEES(15, "扫门店活码"),
    INTERFACE_TRANSMISSION(16, "接口传输"),

    SCENE_CODE_1027(1027, "搜索小程序"),
    SCENE_CODE_1035(1035, "公众号菜单栏"),
    SCENE_CODE_1047(1047, "扫描小程序二维码"),
    SCENE_CODE_1058(1058, "公众号文章"),;

    private Integer code;
    private String msg;

    public static List<String> sourceChannels = Arrays.asList("5", "0", "11", "6");

    SourceWayEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 获取来源方式名称
     *
     * @param code
     * @return
     */
    public static String getSourceWayNameByCode(String code) {
        for (SourceWayEnum item : SourceWayEnum.values()) {
            if (String.valueOf(item.getCode()).equals(code)) {
                return item.getMsg();
            }
        }
        return null;
    }

    public static String getSourceWayNameByCode(String code, String sourceChannel) {
        if (sourceChannels.contains(sourceChannel)) {
            return INTERFACE_TRANSMISSION.getMsg();
        } else {
            for (SourceWayEnum item : SourceWayEnum.values()) {
                if (String.valueOf(item.getCode()).equals(code)) {
                    return item.getMsg();
                }
            }
        }
        return null;
    }

    /**
     * 将所有枚举列举出来的方法
     */
    public static List listSourceWay() {
        List list = Lists.newArrayList();
        for (SourceWayEnum item : SourceWayEnum.values()) {
            if (item.getCode() == 13 || item.getCode() == 15) {
                continue;
            }
            Map map = new HashMap();
            map.put("code", item.getCode());
            map.put("msg", item.getMsg());
            list.add(map);
        }
        return list;
    }

    /**
     * 根据来源方式获取推荐来源
     */
    public static Integer getRegSource(String code) {
        if (String.valueOf(SourceWayEnum.EMPLOYEES.code).equals(code)
                || String.valueOf(SourceWayEnum.WRITE_EMPLOYEES_CODE.code).equals(code)
                || String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_GOOD.code).equals(code)
                || String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_ACTIVITY.code).equals(code)
                || String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_TICKET.code).equals(code)
                || String.valueOf(SourceWayEnum.WELCOME_TO_EMPLOYEES.code).equals(code)) {
            // 推荐来源为员工
            return 1;
        } else if (String.valueOf(SourceWayEnum.STORES.code).equals(code)
                || String.valueOf(SourceWayEnum.WRITE_STORES_CODE.code).equals(code)
                || String.valueOf(SourceWayEnum.STORE_ACTIVITY.code).equals(code)
                || String.valueOf(SourceWayEnum.STORE_ACTIVITY_TO_EMPLOYEES.code).equals(code)) {

            // 推荐来源为门店
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 根据推荐来源获取来源方式
     */
    public static List listSourceWayOfRegSource(Integer parent) {
        List list = Lists.newArrayList();
        Map map;
        if (parent.intValue() == 1) {
            // 推荐来源为员工
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.WELCOME.getCode()));
            map.put("msg", SourceWayEnum.WELCOME.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.WRITE_EMPLOYEES_CODE.getCode()));
            map.put("msg", SourceWayEnum.WRITE_EMPLOYEES_CODE.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_GOOD.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES_SHARE_GOOD.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_ACTIVITY.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES_SHARE_ACTIVITY.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_TICKET.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES_SHARE_TICKET.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.INTERFACE_TRANSMISSION.getCode()));
            map.put("msg", SourceWayEnum.INTERFACE_TRANSMISSION.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1027.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1027.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1035.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1035.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1047.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1047.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1058.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1058.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.STORE_ACTIVITY.getCode()));
            map.put("msg", SourceWayEnum.STORE_ACTIVITY.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.OTHER.getCode()));
            map.put("msg", SourceWayEnum.OTHER.getMsg());
            list.add(map);

        } else if (parent.intValue() == 2) {
            // 推荐来源为门店
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.STORES.getCode()));
            map.put("msg", SourceWayEnum.STORES.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.WRITE_STORES_CODE.getCode()));
            map.put("msg", SourceWayEnum.WRITE_STORES_CODE.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.INTERFACE_TRANSMISSION.getCode()));
            map.put("msg", SourceWayEnum.INTERFACE_TRANSMISSION.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1027.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1027.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1035.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1035.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1047.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1047.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1058.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1058.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.OTHER.getCode()));
            map.put("msg", SourceWayEnum.OTHER.getMsg());
            list.add(map);
        } else if (parent.intValue() == 3) {
            // 自然来源

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.WELCOME.getCode()));
            map.put("msg", SourceWayEnum.WELCOME.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.WRITE_EMPLOYEES_CODE.getCode()));
            map.put("msg", SourceWayEnum.WRITE_EMPLOYEES_CODE.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_GOOD.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES_SHARE_GOOD.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_ACTIVITY.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES_SHARE_ACTIVITY.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.EMPLOYEES_SHARE_TICKET.getCode()));
            map.put("msg", SourceWayEnum.EMPLOYEES_SHARE_TICKET.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.INTERFACE_TRANSMISSION.getCode()));
            map.put("msg", SourceWayEnum.INTERFACE_TRANSMISSION.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1027.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1027.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1035.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1035.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1047.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1047.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.SCENE_CODE_1058.getCode()));
            map.put("msg", SourceWayEnum.SCENE_CODE_1058.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.STORE_ACTIVITY.getCode()));
            map.put("msg", SourceWayEnum.STORE_ACTIVITY.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.STORES.getCode()));
            map.put("msg", SourceWayEnum.STORES.getMsg());
            list.add(map);
            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.WRITE_STORES_CODE.getCode()));
            map.put("msg", SourceWayEnum.WRITE_STORES_CODE.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.NATURE.getCode()));
            map.put("msg", SourceWayEnum.NATURE.getMsg());
            list.add(map);

            map = new HashMap();
            map.put("code", String.valueOf(SourceWayEnum.OTHER.getCode()));
            map.put("msg", SourceWayEnum.OTHER.getMsg());
            list.add(map);

        }
        return list;
    }


    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
