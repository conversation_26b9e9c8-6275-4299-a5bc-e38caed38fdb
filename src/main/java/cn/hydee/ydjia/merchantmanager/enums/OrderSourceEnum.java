package cn.hydee.ydjia.merchantmanager.enums;

/**
 * <AUTHOR>
 * @description: 订单来源枚举
 * @date 2022/4/14 16:17
 */

public enum OrderSourceEnum {

    ERP(0, "ERP"),
    WE_MALL(1, "微商城"),
    ;

    private Integer code;
    private String msg;

    OrderSourceEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsgByCode(Integer code){
        for(OrderSourceEnum status : OrderSourceEnum.values()){
            if(status.getCode().equals(code)){
                return status.getMsg();
            }
        }
        return null;
    }
}
