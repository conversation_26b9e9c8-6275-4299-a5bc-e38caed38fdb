package cn.hydee.ydjia.merchantmanager.enums;

import cn.hydee.ydjia.merchantmanager.dto.MemberCardExcelDto;
import cn.hydee.ydjia.merchantmanager.dto.MemberPhoneExcelDto;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberImportExcelRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberPhoneImportFailExcelRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberPhoneImportSucExcelRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberSuccessImportExcelRespDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导入会员信息的类别
 */
@AllArgsConstructor
@Getter
public enum ImportMemberTypeEnum {

    MEMBER_CARD(1, "会员卡号", MemberCardExcelDto.class, MemberImportExcelRespDTO.class, MemberSuccessImportExcelRespDTO.class),
    MEMBER_PHONE(2, "会员手机号", MemberPhoneExcelDto.class, MemberPhoneImportFailExcelRespDTO.class, MemberPhoneImportSucExcelRespDTO.class);

    private final Integer code;
    private final String msg;
    // 上传表头匹配类
    private final Class titleMatchCls;
    // 失败上传文件类
    private final Class failUploadCls;
    // 成功上传文件类
    private final Class sucUploadCls;
}
