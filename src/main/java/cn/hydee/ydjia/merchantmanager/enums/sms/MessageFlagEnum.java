package cn.hydee.ydjia.merchantmanager.enums.sms;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum MessageFlagEnum {
    /**
     * 会员卡绑定成功通知
     */
    user_msg(1, BizTypeEnum.member_notification),
    /**
     * 优惠券到账通知
     */
    coupon_msg(2, BizTypeEnum.member_notification),
    /**
     * 优惠券过期通知
     */
    coupon_remind(2, BizTypeEnum.member_notification),
    /**
     * 线上下单成功通知
     */
    online_order_msg(3, BizTypeEnum.order_notification),
    /**
     * 线下下单成功通知
     */
    offline_order_msg(3, BizTypeEnum.order_notification),
    /**
     * 订单付款通知
     */
    order_pay(3, BizTypeEnum.order_notification),
    /**
     * 订单待支付通知
     */
    order_waiting_pay_remind(3, BizTypeEnum.order_notification),
    /**
     * 订单配送提醒
     */
    order_delivery(3, BizTypeEnum.order_notification),
    /**
     * 订单取消通知
     */
    order_cancel_msg(3, BizTypeEnum.order_notification),
    /**
     * 线下订单取消通知
     */
    offline_order_cancel(3, BizTypeEnum.order_notification),
    /**
     * 处方单审核结果提醒
     */
    order_audit(7, BizTypeEnum.order_notification),
    /**
     * 处方单审核成功提醒
     */
    order_audit_success_msg(7, BizTypeEnum.order_notification),
    /**
     * 服务预约提醒
     */
    group_remind(4, BizTypeEnum.order_notification),
    /**
     * 开团成功
     */
    open_group_success(4, BizTypeEnum.order_notification),
    /**
     * 拼团成功提醒
     */
    group_success(4, BizTypeEnum.order_notification),
    /**
     * 拼团失败提醒
     */
    group_fail(4, BizTypeEnum.order_notification),
    /**
     * 生日礼包通知
     */
    gift_msg(5, BizTypeEnum.member_notification),
    /**
     * 评价消息通知
     */
    comment_msg(5, BizTypeEnum.order_notification),
    /**
     * 配送有礼通知
     */
    delivery_gift_msg(5, BizTypeEnum.member_notification),
    /**
     * 海贝到账提醒
     */
    hb_msg(6, BizTypeEnum.member_notification),
    /**
     * 线下积分变化提醒
     */
    integral_msg(1, BizTypeEnum.member_notification),
    /**
     * 线下积分清零提醒
     */
    integral_clear_msg(1, BizTypeEnum.member_notification),
    /**
     * 线上心币清零提醒
     */
    online_integral_clean_msg(1, BizTypeEnum.member_notification),
    /**
     * 活动抽奖次数到账提醒
     */
    activity_draw_msg(5, BizTypeEnum.member_notification),
    /**
     * 会员等级变化提醒
     */
    grade_msg(1, BizTypeEnum.member_notification),
    /**
     * 退款申请提醒
     */
    refund_submit_msg(3, BizTypeEnum.order_notification),
    /**
     * 退款成功提醒
     */
    refund_success_msg(3, BizTypeEnum.order_notification),
    /**
     * 退款失败提醒
     */
    refund_failed_msg(3, BizTypeEnum.order_notification),
    /**
     * 确认收货提醒
     */
    confirm_receipt_msg(3, BizTypeEnum.order_notification),
    /**
     * 自动确认收货提醒
     */
    auto_confirm_receipt_msg(3, BizTypeEnum.order_notification),
    /**
     * 提货提醒
     */
    pick_up_remind(3, BizTypeEnum.order_notification),
    /**
     * 尾款支付提醒
     */
    final_payment_remind_msg(3, BizTypeEnum.order_notification),
    /**
     * 直播开播提醒
     */
    live_remind(8, BizTypeEnum.live_notification),
    /**
     * 会员卡种变更提醒
     */
    category_msg(1, BizTypeEnum.member_notification),
    /**
     * 会员储值卡充值消息
     */
    recharge_msg(1, BizTypeEnum.member_notification),
    /**
     * 用药指导消息
     */
    medicine_guide_msg(9, BizTypeEnum.pharmacy_services),
    /**
     * 用药提醒消息
     */
    medicine_remind_msg(10, BizTypeEnum.pharmacy_services),
    /**
     * 用药依从消息
     */
    medicine_compliance_msg(11, BizTypeEnum.pharmacy_services),
    /**
     * 复购提醒消息
     */
    medicine_repurchase_msg(12, BizTypeEnum.pharmacy_services),
    /**
     * 设置用药提醒通知
     */
    medicine_remind_setting_msg(13, BizTypeEnum.pharmacy_services),
    /**
     * 会员首购激活
     */
    member_shop_first(1, BizTypeEnum.life_circle),
    /**
     * 会员复购激活
     */
    member_shop_again(2, BizTypeEnum.life_circle),
    /**
     * 沉睡会员唤醒
     */
    member_sleep_wake(5, BizTypeEnum.life_circle),
    /**
     * 流失会员召回
     */
    member_lose_return(6, BizTypeEnum.life_circle),
    ;

    private Integer iconCode;

    private BizTypeEnum bizType;

    MessageFlagEnum(Integer iconCode, BizTypeEnum bizType) {
        this.iconCode = iconCode;
        this.bizType = bizType;
    }

    private static final Map<String, MessageFlagEnum> MAPPING = Maps.newHashMapWithExpectedSize(MessageFlagEnum.values().length);

    static {
        for (MessageFlagEnum value : MessageFlagEnum.values()) {
            MAPPING.put(value.name(), value);
        }
    }

    public static Integer getIconCodeBy(String flagName) {
        MessageFlagEnum flagEnum = getEnumBy(flagName);
        if (flagEnum != null) {
            return flagEnum.getIconCode();
        }
        return null;
    }

    public static BizTypeEnum getBizTypeEnum(String flagName) {
        MessageFlagEnum flagEnum = getEnumBy(flagName);
        if (flagEnum != null) {
            return flagEnum.getBizType();
        }
        return null;
    }

    public static MessageFlagEnum getEnumBy(String name) {
        return MAPPING.get(name);
    }

}
