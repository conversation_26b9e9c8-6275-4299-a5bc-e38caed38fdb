package cn.hydee.ydjia.merchantmanager.enums;

import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: chenzhibin
 * @Description: 注册来源取渠道枚举
 * @Date: 2021/3/23
 */
public enum SourceChannelEnum {

    ERP("0", "ERP", "其他"),
    WECHAT("1", "微信", "社交媒体"),
    WEAPP("2", "一心到家注册", "线上发展"),
    H5("3", "H5", "线上发展"),
    WX_PAY("4", "门店微信", "门店发展"),
    ZFB_PAY("5", "支付宝扫码", "门店发展"),
    MEMBER_WEAPP("6", "一心堂会员小程序注册", "线上发展"),
    MEDIA_DOUYIN("7", "抖音", "社交媒体"),
    WORK_WECHAT("8", "企业微信", "社交媒体"),
    MEDIA_WEIBO("9", "微博", "社交媒体"),
    BATCH("10", "后台发展", "导入"),
    CRM("11", "CRM注册", "门店发展"),
    IMPORT_ACQUISITION("12", "收购", "导入"),
    IMPORT_VENTURE("13", "合资", "导入"),
    POS("14", "POS注册", "门店发展"),
    ASSIST("15", "一心助手", "门店发展"),
    IMPORT_JOIN("16", "加盟", "导入"),
    IMPORT_GROUP("17", "集团会员", "导入"),
    IMPORT_COOPERATION("18", "合作单位", "导入"),
    IMPORT_AGENT("19", "代导入", "导入"),
    IMPORT_OTHER("96", "其他导入", "导入"),
    MEDIA_OTHER("97", "其他社交媒体", "社交媒体"),
    OTHER("98", "其他", "其他"),
    CUSTOM("99", "自定义渠道", "其他"),
    ;

    private String code;
    private String msg;
    private String group;

    SourceChannelEnum(String code, String msg, String group) {
        this.code = code;
        this.msg = msg;
        this.group = group;
    }


    /**
     * 获取来源渠道名称
     *
     * @param code
     * @return
     */
    public static String getSourceChannelNameByCode(String code) {
        for (SourceChannelEnum item : SourceChannelEnum.values()) {
            if (String.valueOf(item.getCode()).equals(code)) {
                return item.getMsg();
            }
        }
        return null;
    }


    /**
     * 将所有枚举列举出来的方法
     */
    public static List listSourceChannel(Integer parent) {
        List list = Lists.newArrayList();
        for (SourceChannelEnum item : SourceChannelEnum.values()) {
            Map temp = new HashMap<>();
            if (parent == 1) {
                if (!SourceChannelEnum.CRM.code.equals(item.code)
                        && !SourceChannelEnum.ERP.code.equals(item.code)) {
                    temp.put("code", item.getCode());
                    temp.put("msg", item.getMsg());
                }else {
                    continue;
                }
            } else if (parent == 2) {
                // 存健康  APP CRM HC ERP
                if (SourceChannelEnum.CRM.code.equals(item.code)
                        || SourceChannelEnum.ERP.code.equals(item.code)) {
                    temp.put("code", item.getCode());
                    temp.put("msg", item.getMsg());
                }else {
                    continue;
                }
            }
            list.add(temp);
        }
        return list;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getGroup() {
        return group;
    }
}
