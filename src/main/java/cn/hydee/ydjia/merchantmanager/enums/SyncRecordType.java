package cn.hydee.ydjia.merchantmanager.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/7/22 10:52
 */
public enum SyncRecordType {
    STORE_TYPE("store", "门店维度"),
    GROUP_TYPE("group", "分组维度"),
    COMMODITY_TYPE("commodity", "商品维度"),

    ;

    private String type;
    private String msg;

    SyncRecordType(String type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public String getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }
}

