package cn.hydee.ydjia.merchantmanager.enums;

/**
 * @version:
 * @author:cyq
 * @className:LiveStatus
 * @packageName:cn.hydee.ydjia.merchantmanager.enums
 * @description:直播的状态
 * @date:2020/8/25 15:01
 */
public enum  LiveStatus {
    //直播状态
    UNSTART(0, "未开始开播"),
    START(1, "正在直播"),
    ENDSTART(2, "已结束直播"),
    ;

    private Integer code;
    private String msg;

    LiveStatus(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
