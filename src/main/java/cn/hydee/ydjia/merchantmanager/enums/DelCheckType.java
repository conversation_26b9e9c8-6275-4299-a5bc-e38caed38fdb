package cn.hydee.ydjia.merchantmanager.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/6/18 11:36
 */
public enum DelCheckType {
    DELETE_ONE(1, "单个操作冲突"),
    DELETE_BATCH(2, "批量操作冲突"),
    ;

    private Integer code;
    private String msg;

    DelCheckType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }
    public String getMsg() {
        return msg;
    }
}
