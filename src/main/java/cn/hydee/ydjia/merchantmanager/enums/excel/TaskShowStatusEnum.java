package cn.hydee.ydjia.merchantmanager.enums.excel;

/**
 * <AUTHOR>
 * @description TODO 任务显示状态
 * @date 2023/7/11
 */
public enum TaskShowStatusEnum {

    IMPORT_PROCESSING(0, "进行中"),
    IMPORT_ALL_SUCCESS(1, "全部成功"),
    IMPORT_PARTIAL_FAILURE(2, "部分成功"),
    IMPORT_ALL_FAILURE(3, "全部失败"),

    EXPORT_WAITING(0, "等待数据打包"),
    EXPORT_FINISHED(1, "数据打包完成"),
    EXPORT_FAILURE(2, "数据打包失败"),


    ;

    private Integer code;
    private String desc;

    TaskShowStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
