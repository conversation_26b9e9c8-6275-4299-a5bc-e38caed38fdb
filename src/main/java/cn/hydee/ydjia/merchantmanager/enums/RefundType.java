package cn.hydee.ydjia.merchantmanager.enums;

/**
 * <AUTHOR>
 * @date 2019/12/25 11:14
 */
public enum RefundType {
    SEND(1, "快递寄回"),
    RETURN(2, "送回门店"),

    ;

    private Integer code;
    private String msg;

    RefundType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
