package cn.hydee.ydjia.merchantmanager.enums;

/**
 * 产品信息完整性枚举
 *
 * <AUTHOR>
 * @date 2019/11/12
 */
public enum CommodityTypeEnum {

    GENERAL_COMMODITY(1, "普通商品"),
    ASSEMBLE_COMMODITY(2, "组合商品"),
    GIFT_COMMODITY(3, "赠品"),
    OPEN_STOCK_COMMODITY(4, "拆零商品"),
    ;

    private Integer code;
    private String msg;

    CommodityTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
