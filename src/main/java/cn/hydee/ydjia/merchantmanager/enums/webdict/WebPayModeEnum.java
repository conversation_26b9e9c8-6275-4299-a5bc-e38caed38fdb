package cn.hydee.ydjia.merchantmanager.enums.webdict;

import lombok.Data;

/**
 * @version: V1.0
 * @author: duans
 * @className: WebPayModeEnum
 * @packageName: cn.hydee.ydjia.merchantmanager.enums.webdict
 * @description:支付方式
 * @data: 2023-01-05 10:04
 **/
public enum WebPayModeEnum {

	ALL("", "全部"),
	/**
	 * 线上支付 包括：微信支付、余额支付、医保支付等方式
	 */
	ONLINE_PAYMENT("0", "在线支付"),
	/**
	 * 线下支付，包括：到店支付、货到付款
	 */
	CASH_ON_DELIVERY("1", "货到付款"),

	
	;

	private String value;
	private String label;

	WebPayModeEnum(String value, String label) {
		this.value = value;
		this.label = label;
	}

	public String getValue() {
		return value;
	}

	public String getLabel() {
		return label;
	}
}