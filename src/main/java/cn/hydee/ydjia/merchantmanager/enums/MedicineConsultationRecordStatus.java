package cn.hydee.ydjia.merchantmanager.enums;


/**
 * 问诊记录状态统一枚举
 * 尽可能与之前的处方状态保持一致
 *
 * <AUTHOR>
 */

public enum MedicineConsultationRecordStatus {
    /**
     * 作废记录
     */
    INVALID(-1, "已作废"),
    /**
     * 初始化（等待回调通知）
     */
    INIT(6, "初始化"),
    /**
     * 第三方回调成功，未拿到开方最终结果
     */
    PRESCRIBING(7, "待开方"),
    /**
     * 开方拒绝
     */
    PRESCRIBE_REJECT(10, "开方拒绝"),
    /**
     * 已拿到开方结果，等待审方结果
     */
    APPROVALING(1, "待审方"),
    /**
     * 审方通过
     */
    APPROVAL_SUCCESS(2, "审方通过"),
    /**
     * 审方拒绝
     */
    APPROVAL_REJECT(3, "审方拒绝"),
    ;

    private Integer code;
    private String msg;

    MedicineConsultationRecordStatus(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
