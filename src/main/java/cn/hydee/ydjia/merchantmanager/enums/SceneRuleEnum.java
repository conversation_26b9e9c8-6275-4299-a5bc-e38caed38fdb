package cn.hydee.ydjia.merchantmanager.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/09/04 14:38
 */
public enum SceneRuleEnum {
    ONLINE(1, "线上自营商品"),
    OFFLINE(2, "线下"),
    BOTH(3, "线上线下通用"),
    ONLINE_CLOUD(4, "线上云仓商品"),
    ;
    private Integer code;
    private String msg;

    SceneRuleEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static SceneRuleEnum getByCode(Integer code) {
        for (SceneRuleEnum item : SceneRuleEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static boolean isOffline(Integer code) {
        return OFFLINE.getCode().equals(code);
    }

    public static boolean isOnline(Integer code) {
        return ONLINE.getCode().equals(code);
    }
}
