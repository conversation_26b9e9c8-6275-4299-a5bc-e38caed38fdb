package cn.hydee.ydjia.merchantmanager.enums.sms;


import lombok.Getter;
import lombok.RequiredArgsConstructor;


/**
 * @author: Jack<PERSON>i
 * @methodsName:
 * @description:
 * @param:
 * @return:
 * @throws:
 **/
@Getter
@RequiredArgsConstructor
public enum SmsSecondBizTypeEnum {
    /**
     * 会员卡绑定成功通知
     */
    user_msg(1, MessageFlagEnum.user_msg),
    /**
     * 优惠券到账通知
     */
    coupon_msg(2, MessageFlagEnum.coupon_msg),
    /**
     * 优惠券过期通知
     */
    coupon_remind(3, MessageFlagEnum.coupon_remind),
    /**
     * 线上下单成功通知
     */
    online_order_msg(4, MessageFlagEnum.online_order_msg),
    /**
     * 线下下单成功通知
     */
    offline_order_msg(5, MessageFlagEnum.offline_order_msg),
    /**
     * 订单付款通知
     */
    order_pay(6, MessageFlagEnum.order_pay),
    /**
     * 订单待支付通知
     */
    order_waiting_pay_remind(7, MessageFlagEnum.order_waiting_pay_remind),
    /**
     * 订单配送提醒
     */
    order_delivery(8, MessageFlagEnum.order_delivery),
    /**
     * 订单取消通知
     */
    order_cancel_msg(9, MessageFlagEnum.order_cancel_msg),
    /**
     * 线下订单取消通知
     */
    offline_order_cancel(10, MessageFlagEnum.offline_order_cancel),
    /**
     * 处方单审核结果提醒
     */
    order_audit(11, MessageFlagEnum.order_audit),
    /**
     * 处方单审核成功提醒
     */
    order_audit_success_msg(12, MessageFlagEnum.order_audit_success_msg),
    /**
     * 服务预约提醒
     */
    group_remind(13, MessageFlagEnum.group_remind),
    /**
     * 开团成功
     */
    open_group_success(14, MessageFlagEnum.open_group_success),
    /**
     * 拼团成功提醒
     */
    group_success(15, MessageFlagEnum.group_success),
    /**
     * 拼团失败提醒
     */
    group_fail(16, MessageFlagEnum.group_fail),
    /**
     * 生日礼包通知
     */
    gift_msg(17, MessageFlagEnum.gift_msg),
    /**
     * 评价消息通知
     */
    comment_msg(18, MessageFlagEnum.comment_msg),
    /**
     * 配送有礼通知
     */
    delivery_gift_msg(19, MessageFlagEnum.delivery_gift_msg),
    /**
     * 海贝到账提醒
     */
    hb_msg(20, MessageFlagEnum.hb_msg),
    /**
     * 线下积分变化提醒
     */
    integral_msg(21, MessageFlagEnum.integral_msg),
    /**
     * 线下积分清零提醒
     */
    integral_clear_msg(22, MessageFlagEnum.integral_clear_msg),
    /**
     * 线上心币清零提醒
     */
    online_integral_clean_msg(23, MessageFlagEnum.online_integral_clean_msg),
    /**
     * 活动抽奖次数到账提醒
     */
    activity_draw_msg(24, MessageFlagEnum.activity_draw_msg),
    /**
     * 会员等级变化提醒
     */
    grade_msg(25, MessageFlagEnum.grade_msg),
    /**
     * 退款申请提醒
     */
    refund_submit_msg(26, MessageFlagEnum.refund_submit_msg),
    /**
     * 退款成功提醒
     */
    refund_success_msg(27, MessageFlagEnum.refund_success_msg),
    /**
     * 退款失败提醒
     */
    refund_failed_msg(28, MessageFlagEnum.refund_success_msg),
    /**
     * 确认收货提醒
     */
    confirm_receipt_msg(29, MessageFlagEnum.confirm_receipt_msg),
    /**
     * 自动确认收货提醒
     */
    auto_confirm_receipt_msg(30, MessageFlagEnum.auto_confirm_receipt_msg),
    /**
     * 提货提醒
     */
    pick_up_remind(31, MessageFlagEnum.pick_up_remind),
    /**
     * 直播开播提醒
     */
    live_remind(32, MessageFlagEnum.live_remind),
    /**
     * 会员卡种变更提醒
     */
    category_msg(33, MessageFlagEnum.category_msg),
    /**
     * 会员储值卡充值消息
     */
    recharge_msg(34, MessageFlagEnum.recharge_msg),
    /**
     * 用药指导消息
     */
    medicine_guide_msg(35, MessageFlagEnum.medicine_guide_msg),
    /**
     * 用药提醒消息
     */
    medicine_remind_msg(36, MessageFlagEnum.medicine_remind_msg),
    /**
     * 用药依从消息
     */
    medicine_compliance_msg(37, MessageFlagEnum.medicine_compliance_msg),
    /**
     * 复购提醒消息
     */
    medicine_repurchase_msg(38, MessageFlagEnum.medicine_repurchase_msg),
    /**
     * 设置用药提醒通知
     */
    medicine_remind_setting_msg(39, MessageFlagEnum.medicine_remind_setting_msg),
    /**
     * 会员首购激活
     */
    member_shop_first(40, MessageFlagEnum.member_shop_first),
    /**
     * 会员复购激活
     */
    member_shop_again(41, MessageFlagEnum.member_shop_again),
    /**
     * 沉睡会员唤醒
     */
    member_sleep_wake(42, MessageFlagEnum.member_sleep_wake),
    /**
     * 流失会员召回
     */
    member_lose_return(43, MessageFlagEnum.member_lose_return),
    /**
     * 尾款支付提醒
     */
    final_payment_remind_msg(44, MessageFlagEnum.final_payment_remind_msg),
    ;

    private Integer code;

    private MessageFlagEnum messageFlagEnum;

    SmsSecondBizTypeEnum(Integer code, MessageFlagEnum messageFlagEnum) {
        this.code = code;
        this.messageFlagEnum = messageFlagEnum;
    }
}
