package cn.hydee.ydjia.merchantmanager.enums;

/**
 * @Name: DataFlowOrderColumnEnum
 * @Description: 流量数据排序字段枚举类
 * @Author: Kaven
 * @Date: 2023/4/23 10:52
 */
public enum DataFlowOrderColumnEnum {
    UV(1, "uv", "uv"),
    PV(2, "pv", "pv"),
    SALE_AMT(3, "saleAmt", "销售额"),
    UV_VAULE(4, "uvValue", "uv价值"),
    LINK_CLINKS(5, "guideClicks", "引导点击次数"),
    SHARE_COUNT(6, "shareCount", "分享次数"),
    ORDER_COUNT(7, "orderCount", "成交订单"),
    GUIDE_PAY_AMT(8, "guidePayAmt", "引导支付金额"),
    GUIDE_PAY_NUM(9, "guidePayNum", "引导支付人数"),
    SALE_TRANSFER(10, "saleTransfer", "销售转化率"),
    GUIDE_ORDERS(11, "guideOrders", "引导下单数"),
    ;

    private Integer code;
    private String name;
    private String msg;

    DataFlowOrderColumnEnum(Integer code, String name, String msg) {
        this.code = code;
        this.name = name;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getMsg() {
        return msg;
    }
}
