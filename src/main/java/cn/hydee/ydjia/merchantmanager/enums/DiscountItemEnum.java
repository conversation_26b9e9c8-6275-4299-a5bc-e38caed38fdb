package cn.hydee.ydjia.merchantmanager.enums;

/**
 * 订单折价项
 *
 * <AUTHOR>
 * @date 2020-07-01
 */
public enum DiscountItemEnum {
    //优惠类型
    FLASH_PRICE(1, "限时优惠", 0),
    FULL(2, "满减优惠", 1),
    MORE_DISCOUNT(3, "多买优惠", 2),
    COUPON(4, "优惠券抵扣", 3),
    SUPER_MEMBER_DAY(5, "超级会员日优惠", 4),
    MEMBER_DAY(6, "会员日优惠", 5),
    MEMBER_RIGHT(7, "会员价优惠", 6),
    MEMBER_PLUS(8, "会员优享优惠", 7),
    ;

    private Integer code;
    private String msg;
    private Integer sort;

    DiscountItemEnum(Integer code, String msg, Integer sort) {
        this.code = code;
        this.msg = msg;
        this.sort = sort;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getSort() {
        return sort;
    }
}
