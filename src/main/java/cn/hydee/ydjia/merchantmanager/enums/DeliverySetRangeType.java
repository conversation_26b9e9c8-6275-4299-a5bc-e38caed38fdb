package cn.hydee.ydjia.merchantmanager.enums;


/**
 * 组织机构类型统一枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/18 14:30
 */

public enum DeliverySetRangeType {
    DELIVERY(0, "快递配置"),
    DISTRIBUTE(1, "配送配置"),
    CLOUD_DELIVERY(2, "服务商快递配置"),
    ;

    private Integer code;
    private String msg;

    DeliverySetRangeType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
