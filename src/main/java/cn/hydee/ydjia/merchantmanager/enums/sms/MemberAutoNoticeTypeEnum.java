package cn.hydee.ydjia.merchantmanager.enums.sms;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

/**
 * 短信自动通知-会员权益自动关怀、自动药学服务、订单交易自动服务
 *
 * <AUTHOR>
 * @date 2023/03/20
 */
@Getter
@AllArgsConstructor
public enum MemberAutoNoticeTypeEnum {

    /** 会员权益自动关怀 */
    /**
     * 注册会员通知
     */
    user_msg(1, 1, "注册会员通知", SmsSecondBizTypeEnum.user_msg),
    /**
     * 会员卡种变更提醒
     */
    category_msg(2, 2, "会员卡种变化提醒", SmsSecondBizTypeEnum.category_msg),
    /**
     * 会员等级变化提醒
     */
    grade_msg(3, 3, "会员等级变化提醒", SmsSecondBizTypeEnum.grade_msg),
    /**
     * 充值成功提醒
     */
    recharge_msg(4, 4, "充值成功提醒", SmsSecondBizTypeEnum.recharge_msg),
    /**
     * 会员积分变化提醒
     */
    integral_msg(5, 5, "积分变动提醒", SmsSecondBizTypeEnum.integral_msg),
    /**
     * 心币变动提醒
     */
    hb_msg(6, 6, "心币变动提醒", SmsSecondBizTypeEnum.hb_msg),
    /**
     * 优惠券到账通知
     */
    coupon_msg(7, 7, "优惠券到账通知", SmsSecondBizTypeEnum.coupon_msg),
    /**
     * 优惠券即将过期提醒
     */
    coupon_remind(8, 8, "优惠券即将过期提醒", SmsSecondBizTypeEnum.coupon_remind),
    /**
     * 配送礼包通知
     */
    delivery_gift_msg(9, 9, "配送礼包通知", SmsSecondBizTypeEnum.delivery_gift_msg),


    /**  自动药学服务 */
    /**
     * 用药指导通知
     */
    medicine_guide_msg(30, 1, "用药指导通知", SmsSecondBizTypeEnum.medicine_guide_msg),
    /**
     * 用药提醒通知
     */
    medicine_remind_msg(33, 2, "用药提醒通知", SmsSecondBizTypeEnum.medicine_remind_msg),
    /**
     * 用药依从通知
     */
    medicine_compliance_msg(31, 3, "用药依从通知", SmsSecondBizTypeEnum.medicine_compliance_msg),
    /**
     * 备药提醒通知
     */
    medicine_repurchase_msg(32, 4, "备药提醒通知", SmsSecondBizTypeEnum.medicine_repurchase_msg),


    /**  订单交易自动服务 */
    /**
     * 线上下单成功通知
     */
    online_order_msg(51, 1, "线上下单成功通知", SmsSecondBizTypeEnum.online_order_msg),
    /**
     * 评价消息通知
     */
    comment_msg(50, 2, "订单评价通知", SmsSecondBizTypeEnum.comment_msg),
    /**
     * 退款成功提醒
     */
    refund_success_msg(52, 3, "退款成功通知", SmsSecondBizTypeEnum.refund_success_msg),
    /**
     * 处方生成通知
     */
    order_audit_success_msg(53, 4, "处方生成通知", SmsSecondBizTypeEnum.order_audit_success_msg),
    /**
     * 尾款支付提醒
     */
    final_payment_remind_msg(54,5, "尾款支付提醒",  SmsSecondBizTypeEnum.final_payment_remind_msg),
    ;

    private Integer code;
    private int sort;
    private String title;
    private SmsSecondBizTypeEnum smsSecondBizType;

    private final static HashMap<String, MemberAutoNoticeTypeEnum> ALL_MAP
            = Maps.newHashMapWithExpectedSize(MemberAutoNoticeTypeEnum.values().length);
    private final static HashMap<String, MemberAutoNoticeTypeEnum> MSG_FLAG_MAP_AUTO_CARE
            = Maps.newHashMapWithExpectedSize(9);
    private final static HashMap<String, MemberAutoNoticeTypeEnum> MSG_FLAG_MAP_PHARMACY
            = Maps.newHashMapWithExpectedSize(4);
    private final static HashMap<String, MemberAutoNoticeTypeEnum> MSG_FLAG_MAP_ORDER_TRADE
            = Maps.newHashMapWithExpectedSize(5);

    static {
        for (MemberAutoNoticeTypeEnum value : MemberAutoNoticeTypeEnum.values()) {
            ALL_MAP.put(value.smsSecondBizType.getMessageFlagEnum().name(), value);
            // 会员权益自动关怀
            if(BizTypeEnum.member_notification.getCode().equalsIgnoreCase(value.smsSecondBizType.getMessageFlagEnum().getBizType().getCode())){
                MSG_FLAG_MAP_AUTO_CARE.put(value.smsSecondBizType.getMessageFlagEnum().name(), value);
            }
            // 自动药学服务
            if(BizTypeEnum.pharmacy_services.getCode().equalsIgnoreCase(value.smsSecondBizType.getMessageFlagEnum().getBizType().getCode())){
                MSG_FLAG_MAP_PHARMACY.put(value.smsSecondBizType.getMessageFlagEnum().name(), value);
            }
            // 订单交易自动服务
            if(BizTypeEnum.order_notification.getCode().equalsIgnoreCase(value.smsSecondBizType.getMessageFlagEnum().getBizType().getCode())){
                MSG_FLAG_MAP_ORDER_TRADE.put(value.smsSecondBizType.getMessageFlagEnum().name(), value);
            }
        }
    }
    public static HashMap<String, MemberAutoNoticeTypeEnum> allMap(){
        return ALL_MAP;
    }

    public static HashMap<String, MemberAutoNoticeTypeEnum> msgFlagMapAutoCare(){
        return MSG_FLAG_MAP_AUTO_CARE;
    }

    public static HashMap<String, MemberAutoNoticeTypeEnum> msgFlagMapPharmacy(){
        return MSG_FLAG_MAP_PHARMACY;
    }

    public static HashMap<String, MemberAutoNoticeTypeEnum> msgFlagMapOrderTrade(){
        return MSG_FLAG_MAP_ORDER_TRADE;
    }
}
