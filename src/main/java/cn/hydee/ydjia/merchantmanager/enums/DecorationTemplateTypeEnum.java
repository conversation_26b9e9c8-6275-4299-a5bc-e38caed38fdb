package cn.hydee.ydjia.merchantmanager.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @version: V1.0
 * @author: duans
 * @className: DecorationTemplateType
 * @packageName: cn.hydee.ydjia.merchantmanager.enums
 * @description:
 * @data: 2022-08-19 17:40
 **/

@Getter
@AllArgsConstructor
public enum DecorationTemplateTypeEnum {
	
	MALL(1,"微商城模板"),
	SP_MALL(2,"好物甄选模板"),
	DM(3,"DM单模板");
	
	private Integer code;
	private String msg;
	
	public static String getName(Integer code){
		for (DecorationTemplateTypeEnum templateType:DecorationTemplateTypeEnum.values()){
			if(templateType.getCode().equals(code)){
				return templateType.getMsg();
			}
		}
		return StringUtils.EMPTY;
	}
}