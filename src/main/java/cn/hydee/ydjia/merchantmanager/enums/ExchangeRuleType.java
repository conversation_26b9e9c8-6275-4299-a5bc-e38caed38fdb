package cn.hydee.ydjia.merchantmanager.enums;


/**
 * <AUTHOR>
 *  * @date 2020-06-22
 */

public enum ExchangeRuleType {
    NUMBER(0, "按次数兑换"),
    AMOUNT(1, "按区间消费金额兑换"),
    RATE(2,"按兑换倍率兑换"),
    FIXED_AMOUNT(3,"按定额消费兑换"),
    SPEC(4, "适用商品"),

    ;

    private Integer code;
    private String msg;

    ExchangeRuleType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
