package cn.hydee.ydjia.merchantmanager.enums;

/**
 * 产品信息完整性枚举
 *
 * <AUTHOR>
 * @date 2019/11/12
 */
public enum CommTypeEnum {

    ZXYP_LEVEL_TYPE(1, "1065279ca65a4a529109f82472f11053", "中西药品"),
    YYBJ_LEVEL_TYPE(2, "fb5e6c99d2a24eb79dae4350d9bfa837", "营养保健"),
    YLQX_LEVEL_TYPE(2, "a99917a7c7254ac281e844acf1610657", "医疗器械");

    private Integer id;
    private String code;
    private String name;

    CommTypeEnum(Integer id, String code, String name) {
        this.id = id;
        this.code = code;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
