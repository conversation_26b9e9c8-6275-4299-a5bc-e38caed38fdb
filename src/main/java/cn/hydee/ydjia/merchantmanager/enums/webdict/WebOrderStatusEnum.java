package cn.hydee.ydjia.merchantmanager.enums.webdict;

/**
 * @version: V1.0
 * @author: duans
 * @className: WebPayModeEnum
 * @packageName: cn.hydee.ydjia.merchantmanager.enums.webdict
 * @description:订单状态
 * @data: 2023-01-05 10:04
 **/
public enum WebOrderStatusEnum {

	UNPAID("2", "待付款"),
	PAID_DEPOSIT("3", "待付款(已付定金)"),
	UN_RECEIVED("4", "待发货"),
	RECEIVED("6", "已发货"),
	UN_DELIVER("7", "待提货"),
	COMPLETED("12", "已完成"),
	UN_REFUND("10", "待退款"),
	UN_RETURN_GOODS("8", "待退货"),
	REFUND_COMPLETED("30", "退款完成"),
	CANCEL("20", "已取消"),
	
	;

	private String value;
	private String label;

	WebOrderStatusEnum(String value, String label) {
		this.value = value;
		this.label = label;
	}

	public String getValue() {
		return value;
	}

	public String getLabel() {
		return label;
	}
}