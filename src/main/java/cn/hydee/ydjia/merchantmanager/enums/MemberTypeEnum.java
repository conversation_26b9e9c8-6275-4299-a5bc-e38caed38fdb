package cn.hydee.ydjia.merchantmanager.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @version 1.0
 * @Author: pengyayun
 * @Description: 注册来源枚举
 * @Date: 2020/3/24
 */
public enum MemberTypeEnum {

    /**
     * 活跃会员：三个月有消费记录且有3次及以上通过企业微信交流
     * 新增会员：30天内注册的会员
     * 沉寂会员：3个月及以上没有消费记录的会员
     * 普通会员：三个月有消费记录的会员
     * 优质会员：3个月消费3次及以上，并且消费金额500元及以上
     */
    NORMAL(1, "普通会员"),
    ADD(2, "新增会员"),
    GOOD(3, "优质会员"),
    ACTIVE(4, "活跃会员"),
    SLEEP(9, "沉寂会员"),
    ;

    private Integer code;
    private String msg;

    MemberTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMemberType(Integer code) {
        for (MemberTypeEnum value : MemberTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
