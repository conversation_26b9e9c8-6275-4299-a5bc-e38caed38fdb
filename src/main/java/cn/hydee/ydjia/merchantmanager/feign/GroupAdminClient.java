package cn.hydee.ydjia.merchantmanager.feign;

import java.util.List;

import javax.validation.Valid;

import cn.hydee.ydjia.merchantmanager.dto.req.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.ActivityGroupProductAddStockDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.ActivityGroupProductDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.ActivityInfoGroupDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.GroupActivityInfoQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.GroupProductInfoQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.PmtGroupActivityInfo;
import cn.hydee.ydjia.merchantmanager.dto.group.PmtGroupProduct;
import cn.hydee.ydjia.merchantmanager.dto.group.QueryGroupIngPageRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.QueryGroupIngReqDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;

/**
 * <AUTHOR>
 * 拼团B端管理
 * **/
@FeignClient(value= LocalConst.PROMOTE_APP_NAME)
public interface GroupAdminClient {

    @PostMapping("/${api.version}/admin/activityGroup/add")
    ResponseBase<String> addGroupActivity(@Valid @RequestBody ActivityInfoGroupDTO dto);

    @PostMapping("/${api.version}/admin/activityGroup/modify")
    ResponseBase<String> modifyGroupActivity(@Valid @RequestBody ActivityInfoGroupDTO dto);

    @PostMapping("/${api.version}/admin/activityGroup/deleteByIds")
    ResponseBase<String>  deleteByIds(@RequestBody List<Long> ids);

    @PutMapping("/${api.version}/act-rule")
    ResponseBase modifyProduct(@RequestBody RuleUpdateReqDTO dto);

    @PostMapping("/${api.version}/admin/activityGroup/getProductsByActivityId")
    ResponseBase<PageDTO<PmtGroupProduct>> getProductsByActivityId(@RequestBody GroupProductInfoQueryDTO dto);

    @GetMapping("/${api.version}/admin/activityGroup/getProductInfo/{specId}")
    ResponseBase<PmtGroupProduct> getProductInfo(@PathVariable(value = "specId") Long specId);

    @PostMapping("/${api.version}/admin/activityGroup/addProduct")
    ResponseBase<String> addProduct(@RequestBody List<ActivityGroupProductDTO> dtos);

    @PostMapping("/${api.version}/admin/activityGroup")
    ResponseBase<PageDTO<PmtGroupActivityInfo>> getGroupActivityInfo(@RequestBody GroupActivityInfoQueryDTO condition);

    @PostMapping("/${api.version}/admin/activityGroup/updateStock")
    ResponseBase<String> updateStock(@RequestBody List<ActivityGroupProductDTO> dtos);

    @GetMapping("${api.version}/admin/activityGroup/getActivityInfo/{activityId}")
    ResponseBase<PmtGroupActivityInfo> getActivityInfo(@PathVariable(value = "activityId") Long activityId);

    @PostMapping("/${api.version}/admin/activityGroup/deleteProductInfo")
    ResponseBase<Boolean> deleteProductInfo(@RequestBody List<Long> activityspecIds);

    @PostMapping("/${api.version}/admin/activityGroup/getGroupIngPage")
    ResponseBase<PageDTO<QueryGroupIngPageRespDTO>> getGroupIngPage(@RequestBody QueryGroupIngReqDTO dto);

    @PostMapping("/${api.version}/admin/activityGroup/oneTimeGroup")
    ResponseBase<Boolean> oneTimeGroup(@RequestParam(value = "groupCode") String groupCode);

    @PostMapping("/${api.version}/admin/activityGroup/clearStock")
    ResponseBase<Integer> clearProductStock(@RequestBody ClearStockReqDTO reqDTO);

    @PutMapping("/${api.version}/admin/activityGroup/addStock")
    ResponseBase addStock(GroupAddStockDTO dto);

    @PostMapping("/${api.version}/act-rule/_search")
    ResponseBase<List<PmtGroupRuleDto>> searchDetailRule(RuleQueryReqDTO dto);

    @PostMapping("/${api.version}/admin/activityGroup/_sort")
    ResponseBase updateSortGroupRule(@RequestBody List<SortReqDTO> reqDTO);
}
