package cn.hydee.ydjia.merchantmanager.feign;

import java.util.List;

import javax.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.SumGroupAmountReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.SumGroupAmountResDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;

/**
 * <AUTHOR>
 * @date 2019/11/14 15:20
 */
@FeignClient(value = LocalConst.ORDER_APP_NAME)
public interface GroupOrderInfoClient {

    /**
     * 通过团编码查团实付总金额
     * @param sumGroupAmountReqDTO
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-group/sumGroupAmount")
    ResponseBase<List<SumGroupAmountResDTO>> sumGroupAmount(@Valid @RequestBody SumGroupAmountReqDTO sumGroupAmountReqDTO);

   

}
