package cn.hydee.ydjia.merchantmanager.feign.bigdata;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.activity.sms.SmsBizReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.activity.sms.SmsChildBizReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.activity.sms.SmsTouchBaseInfo;
import cn.hydee.ydjia.merchantmanager.dto.resp.activity.sms.SmsTouchChildInfo;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 数智接口
* @Author: syu
* @Date: 2023-3-2
*/
@FeignClient(value = LocalConst.DATA_CENTER_TRANSMIT, url = "${dataCenterTransform.url}")
public interface BigDataClient {

    /**
    *从数仓查询各场景的会员维护人数（父类型）
    * @param reqDTO
    * @return: cn.hydee.starter.dto.ResponseBase<java.util.List<cn.hydee.ydjia.merchantmanager.dto.resp.activity.sms.SmsTouchBaseInfo>>
    * @Author: syu
    * @Date: 2023-3-2
    */
    @PostMapping("/dc-transmit/sms-voice/queryBizTypeMemberNum")
    ResponseBase<List<SmsTouchBaseInfo>> querySmsTouchBaseInfo(@RequestBody SmsBizReqDTO reqDTO);

    /**
    * 从数仓查询各场景的会员维护人数（子类型）
    * @param reqDTO
    * @return: cn.hydee.starter.dto.ResponseBase<java.util.List<cn.hydee.ydjia.merchantmanager.dto.resp.activity.sms.SmsTouchChildInfo>>
    * @Author: syu
    * @Date: 2023-3-2
    */
    @PostMapping("/dc-transmit/sms-voice/querySubTypeMemberNum")
    ResponseBase<List<SmsTouchChildInfo>> queryChildSmsTouchBaseInfo(@RequestBody SmsChildBizReqDTO reqDTO);

}
