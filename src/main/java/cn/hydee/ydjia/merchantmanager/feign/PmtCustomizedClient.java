package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.pmtcustomized.PartnerEnumRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.pmtcustomized.PmtCustomizedReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.pmtcustomized.PmtCustomizedRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 定制促销活动 Client
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@FeignClient(value = LocalConst.PROMOTE_APP_NAME)
public interface PmtCustomizedClient {

    /**
     * 获取合作商列表
     *
     * @return 合作商列表
     */
    @GetMapping(value = "/${api.version}/pmt-customized/getPartnerList")
    ResponseBase<List<PartnerEnumRespDTO>> getPartnerList();

    /**
     * 新增定制促销活动
     *
     * @param reqDTO 新增数据
     * @return 新增结果
     */
    @PostMapping("/${api.version}/pmt-customized/create")
    ResponseBase<Boolean> create(@RequestBody PmtCustomizedReqDTO reqDTO);

    /**
     * 更新定制促销活动
     *
     * @param reqDTO 更新数据
     * @return 更新结果
     */
    @PostMapping("/${api.version}/pmt-customized/update")
    ResponseBase<Boolean> update(@RequestBody PmtCustomizedReqDTO reqDTO);

    /**
     * 查看定制促销活动详情
     *
     * @param id 定制促销活动ID
     * @return 定制促销活动详情
     */
    @GetMapping("/${api.version}/pmt-customized/detail")
    ResponseBase<PmtCustomizedRespDTO> detail(@RequestParam("id") Integer id);

    /**
     * 分页查询定制促销活动
     *
     * @param reqDTO 查询条件
     * @return 定制促销活动分页列表
     */
    @PostMapping("/${api.version}/pmt-customized/page")
    ResponseBase<PageDTO<PmtCustomizedRespDTO>> page(@RequestBody PmtCustomizedReqDTO reqDTO);

}
