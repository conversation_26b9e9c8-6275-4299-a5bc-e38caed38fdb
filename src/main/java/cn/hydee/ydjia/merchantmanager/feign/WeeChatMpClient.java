package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.WeeChatMenuReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.WeeChatMenuResDTO;
import cn.hydee.ydjia.merchantmanager.dto.wx.WeeChatCustomerMessageDTO;
import cn.hydee.ydjia.merchantmanager.dto.wx.WxMpMemberCardActivatedMessage;
import cn.hydee.ydjia.merchantmanager.dto.wx.WxMpMessageNotifyRequestDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信公众平台服务接口
 * @create 2020/03/31 19:12
 */
@FeignClient(LocalConst.THIRD)
public interface WeeChatMpClient {
    /**
     * 删除公众号菜单栏
     *
     * @param menuId
     * @param merCode
     * @return
     */
    @GetMapping("/${api.base-info-version}/mp/menu/delete/{menuId}")
    ResponseBase deleteMenu(@PathVariable("menuId") String menuId,
                            @RequestParam("merCode") String merCode);

    /**
     * 删除所有公众号菜单栏
     *
     * @param merCode
     * @return
     */
    @GetMapping("/${api.base-info-version}/mp/menu/deleteAll")
    ResponseBase deleteAll(@RequestParam("merCode") String merCode, @RequestParam(required = false, name = "createName") String createName);

    /**
     * 查询自定义菜单栏信息
     *
     * @param merCode
     * @return
     */
    @GetMapping("/${api.base-info-version}/mp/menu/get")
    ResponseBase<WeeChatMenuResDTO> get(@RequestParam("merCode") String merCode);

    @GetMapping("/${api.base-info-version}/mp/menu/getSelfMenuInfo")
    ResponseBase<WeeChatMenuResDTO> getSelfMenuInfo(@RequestParam("merCode") String merCode);

    @PostMapping("/${api.base-info-version}/mp/menu/create")
    ResponseBase create(@RequestBody WeeChatMenuReqDTO weeChatMenuResDTO, @RequestParam("merCode") String merCode,
                        @RequestParam(required = false, name = "createName") String createName);

    @PostMapping(value = "/${api.base-info-version}/mp/material/wWxMediaImgUpload")
    ResponseBase<String> upload(@RequestParam("path") String path, @RequestParam("merCode") String merCode);

    /**
     * 用户微信会员卡解绑
     *
     * @param merCode
     * @param activatedMessage
     * @return
     */
    @PostMapping(value = "/${api.base-info-version}/mp/card/unactivate")
    ResponseBase unActivate(@RequestParam("merCode") String merCode, @RequestBody WxMpMemberCardActivatedMessage activatedMessage);

    /**
     * 用户微信会员卡激活
     *
     * @param merCode
     * @param activatedMessage
     * @return
     */
    @PostMapping(value = "/${api.base-info-version}/mp/card/activate")
    ResponseBase activate(@RequestParam("merCode") String merCode, @RequestBody WxMpMemberCardActivatedMessage activatedMessage);


    /**
     * 发送客服消息
     *
     * @param message
     * @param accessToken
     * @return
     */
    @PostMapping("/${api.base-info-version}/mp/kefu/sendKefuMessage")
    ResponseBase<Boolean> sendCustomerMessage(@RequestBody WeeChatCustomerMessageDTO message,
                                              @RequestParam("accessToken") String accessToken,
                                              @RequestParam("appId") String appId);

    /**
     * 处理微信回调消息
     *
     * @param request
     * @return
     */
    @PostMapping("/${api.base-info-version}/mp/msg")
    ResponseBase<String> post(@RequestBody WxMpMessageNotifyRequestDTO request);

    /**
     * [二维码]生成永久二维码(10万数量限制)
     *
     * @param sceneStr
     * @param merCode
     * @param expireSeconds
     * @return
     */
    @PostMapping("/${api.base-info-version}/mp/Qrcode/qrCodePictureUrlStr")
    ResponseBase<String> qrCodePictureUrl(@RequestParam("sceneStr") String sceneStr,
                                          @RequestParam("merCode") String merCode,
                                          @RequestParam(value = "expireSeconds", required = false) Integer expireSeconds);

    @PostMapping("/${api.base-info-version}/mp/Qrcode/qrCodePictureUrlStr")
    ResponseBase<String> createQrcode(@RequestParam("sceneStr") String sceneStr,
                                      @RequestParam("merCode") String merCode,
                                      @RequestParam(value = "expireSeconds", required = false) Integer expireSeconds);

    /**
     * 获取独立小程序appid，为空表示非独立小程序
     *
     * @param merCode 商户编码
     * @return
     */
    @GetMapping(value = "/${api.version}/ma/getSelfMinAppid")
    ResponseBase<String> getSelfMinAppId(@RequestParam(value = "merCode") String merCode);
}
