package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.MerchantHomePageSetReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MerchantHomePageSetRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/03/30 21:54
 */
@FeignClient(LocalConst.MEMBER_APP_NAME)
public interface MerchantHomePageSetClient {

    @GetMapping("/${api.version}/homePage/detail")
    ResponseBase<MerchantHomePageSetRespDTO> detail(@RequestParam String merCode);

    @PostMapping("/${api.version}/homePage/set")
    ResponseBase<Boolean> homePageSet(@RequestBody MerchantHomePageSetReqDTO reqDTO);
}
