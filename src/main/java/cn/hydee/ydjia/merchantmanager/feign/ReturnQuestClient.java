package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.OrderDetail;
import cn.hydee.ydjia.merchantmanager.domain.ReturnQuest;
import cn.hydee.ydjia.merchantmanager.domain.ReturnQuestDetail;
import cn.hydee.ydjia.merchantmanager.dto.order.req.ReturnQuestReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.ReturnQuestRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/11/19 17:57
 */
@FeignClient(value = LocalConst.ORDER_APP_NAME)
public interface ReturnQuestClient {

    /**
     * 根据商家编码获取商家订单配置信息
     * @param returnQuest 退款申请对象
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-return")
    ResponseBase<Integer> saveOrUpdate(@RequestBody ReturnQuest returnQuest);

    /**
     * 根据订单明细id获取退款申请单
     * @param detailId 订单明细id
     * @return ResponseBase
     **/
    @GetMapping("/${api.version}/order-return/{detailId}")
    ResponseBase<ReturnQuest> selectByDetailId(@PathVariable(value = "detailId") String detailId);

    /**
     * 根据订单明细id获取退款申请单
     * @param returnRequestId 售后申请单id
     * @return ResponseBase
     **/
    @GetMapping("/${api.version}/order-return/_get/{returnRequestId}")
    ResponseBase<ReturnQuest> selectById(@PathVariable("returnRequestId") String returnRequestId);

    /**
     * 查询退货退款申请
     * @param queryReturnQuestDTO 退款申请对象
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-return/_query")
    ResponseBase<List<ReturnQuestRespDTO>> queryByCondition(@RequestBody QueryReturnQuestDTO queryReturnQuestDTO);


    /**
     * 同意退款申请
     * @param refundReqDTO
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-return/_agree")
    ResponseBase<Integer> agree(@RequestBody RefundReqDTO refundReqDTO, @RequestHeader(value = "userName") String userName);

    /**
     * 同意退款申请
     * @param rejectReqDTO
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-return/_reject")
    ResponseBase<Integer> reject(@RequestBody RejectReqDTO rejectReqDTO, @RequestHeader(value = "userName") String userName);


    /**
     * 验证明细退款是否是最后一笔退款明细
     * @param checkDetailReqDTO
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-return/_check")
    ResponseBase<Integer> checkDetail(@RequestBody CheckDetailReqDTO checkDetailReqDTO);

    /**
     * 根据条件查询售后明细列表
     * @param queryReturnQuestDetailDTO
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-return/_queryReturnDetailList")
    ResponseBase<List<ReturnQuestDetail>> queryReturnDetailList(@RequestBody QueryReturnQuestDetailDTO queryReturnQuestDetailDTO);


    /**
     * 退货退款申请
     *
     * @param returnQuestReqDTO
     * @return ResponseBase
     **/
    @PostMapping("/${api.version}/order-return/_apply")
    ResponseBase<String> apply(@RequestBody ReturnQuestReqDTO returnQuestReqDTO);

    @PostMapping("/${api.version}/order-return/_cancelReturn")
    ResponseBase cancelReturn(@RequestBody CancelReturnReqDTO cancelReturnReqDTO);
}
