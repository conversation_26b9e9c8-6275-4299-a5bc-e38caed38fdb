package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.*;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/03/31 10:23
 */
@FeignClient(name = LocalConst.MEMBER_APP_NAME)
public interface MemberRechargeClient {

    @PostMapping("/${api.version}/memberRecharge/list")
    ResponseBase<PageDTO<MemberRechargeDetailRespDTO>> list(@Valid @RequestBody MemberRechargePageReqDTO reqDTO);

    @PostMapping("/${api.version}/memberRecharge/batch/recharge")
    ResponseBase<Boolean> batchRecharge(@Valid @RequestBody MemberBatchRechargeAddReqDTO reqDTO);

    @PostMapping("/${api.version}/memberRecharge/batch/list")
    ResponseBase<PageDTO<MemberBatchRechargeRespDTO>> batchList(@Valid @RequestBody MemberBatchRechargePageReqDTO reqDTO);

    @PostMapping("/${api.version}/memberRecharge/batch/detail")
    ResponseBase<PageDTO<MemberBatchRechargeDetailRespDTO>> batchDetail(@Valid @RequestBody MemberRechargeDetailPageReqDTO reqDTO);

    @PostMapping("/${api.version}/member/getUsableAmountInfoList")
    ResponseBase<List<MemberBatchRechargeDetailDTO>> getUsableAmountInfoList(@Valid @RequestBody MemberUsableAmountListReqDTO reqDTO);


}
