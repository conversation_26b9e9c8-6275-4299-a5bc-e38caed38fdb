package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.MessageSendReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.merchant.WxAuthDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 调用发号器
 *
 * <AUTHOR>
 * @date 2019/9/26
 */
@FeignClient(value = "hydee-middle-third")
public interface MiddleThirdClient {

    @PostMapping("/${api.version}/sms/send")
    @ApiOperation(value = "发送短信")
    ResponseBase<Boolean> send(@RequestBody @Valid MessageSendReqDTO messageSendReqDTO);

    /**
     * 检测商户小程序公众号设置
     *
     * @param merCode
     * @return
     */
    @GetMapping("/1.0/api/getWxAuthByMerCode")
    ResponseBase<WxAuthDTO> getWxAuthByMerCode(@RequestParam("merCode") String merCode);

}
