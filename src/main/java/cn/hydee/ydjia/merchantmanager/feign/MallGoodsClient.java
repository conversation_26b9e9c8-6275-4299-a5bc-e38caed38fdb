package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.base.Return;
import cn.hydee.ydjia.merchantmanager.dto.AllCommUpReq;
import cn.hydee.ydjia.merchantmanager.dto.BatchTaskReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.CommNumStatisticDTO;
import cn.hydee.ydjia.merchantmanager.dto.SpecStatisticsDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.CommEditBatchVO;
import cn.hydee.ydjia.merchantmanager.dto.resp.SpecStatisticsQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreSpec;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * @Name: YdjMallGoodsClient
 * @Description: 微商城商品管理接口类
 * @Author: Kaven
 * @Date: 2023/2/24 15:34
 */
@FeignClient(name = LocalConst.GOODS_APP_NAME)
public interface MallGoodsClient {
    /**
     * @Description: 微商城商品-统计列表
     * @Author: Kaven
     * @Date: 2023/2/24 16:20
     * @param [queryDTO]
     * @return cn.hydee.starter.dto.ResponseBase<cn.hydee.starter.dto.PageDTO<cn.hydee.ydjia.merchantmanager.dto.SpecStatisticsDTO>>
     * @Exception
     */
    @PostMapping("/${api.version}/mall-goods/statistic")
    ResponseBase<PageDTO<SpecStatisticsDTO>> specStatistics(@RequestBody SpecStatisticsQueryDTO queryDTO);

    /**
     * @Description: 微商城商品-批量上下架（当前搜索页）
     * @Author: Kaven
     * @Date: 2023/2/24 16:20
     * @param [dto, userName]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @PostMapping("/${api.version}/mall-goods/update/status")
    ResponseBase batchUpdateStoreCommStatus(@Valid @RequestBody StoreCommStatusReq dto, @RequestHeader String userName);

    @PostMapping("/${api.version}/mall-goods/all/up")
    ResponseBase allCommodityUpAllStore(@RequestBody AllCommUpReq allCommUpReq);

    /**
     * 批量复制指定门店的商品上下架状态到其他的门店（复制上下架）
     * @param statusCopyReq
     * @param userName
     * @return
     */
    @PostMapping("/${api.version}/mall-goods/copy/status")
    ResponseBase batchCopyStoreCommStatus(@Valid @RequestBody StoreStatusCopyReq statusCopyReq, @RequestHeader String userName);

    /**
     * @Description: 微商城商品-编辑库存价格，必须配置consumes = MediaType.MULTIPART_FORM_DATA_VALUE，否则无法传输MultipartFile
     * @Author: Kaven
     * @Date: 2023/2/24 09:40
     * @param [file, isLock, isYdj, merCode, userName, userId]
     * @return Return<CommEditBatchVO>
     * @Exception
     */
    @ApiOperation(value = "批量修改商品价格", notes = "批量修改商品价格")
    @PostMapping(value = "/${api.version}/mall-goods/batchEdit/price", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Return<CommEditBatchVO> batchEditPriceAndStock(@RequestPart MultipartFile file, @SpringQueryMap CommEditStockPriceVO dto);

    /**
     * @Description: 微商城商品列表-单个编辑库存价格（支持批量）
     * @Author: Kaven
     * @Date: 2023/4/14 10:28
     * @param [specList, userName]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @PutMapping("/${api.version}/mall-goods/editStockAndPrice")
    ResponseBase batchUpdate(@RequestBody List<StoreSpecUpdateReqDTO> specList, @RequestHeader String userName);

    /**
     * @Description: 全量校准库存价格
     * @Author: Kaven
     * @Date: 2023/2/27 09:25
     * @param [merCode, userName]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @GetMapping("/${api.version}/mall-goods/storeSpec")
    ResponseBase syncCommStockAndPriceFromStoreSpec(@RequestHeader(value = "merCode") String merCode, @RequestHeader(value = "userName") String userName);

    /**
     * 获取产品规格信息
     *
     * @param dto 规格查询dto
     * @return ResponseBase<CommodityDTO>
     */
    @PostMapping("/${api.version}/mall-goods/comm-spec/_search")
    ResponseBase<PageDTO<StoreSpec>> getCommoditySpec(@RequestBody SpecSearchDTO dto);


    /**
     * @Description: 微商城商品-锁定、解锁库存价格（已勾选）
     * @param dto, userName, userId
     * @return cn.hydee.starter.dto.ResponseBase
     */
    @PutMapping("${api.version}/mall-goods/lock")
    ResponseBase updateStoreSpecLock(@RequestBody StoreLockReqDTO dto, @RequestHeader String userName, @RequestHeader String userId);

    /**
     * @Description: 微商城商品-锁定、解锁库存价格（当前搜索）
     * @Author: Kaven
     * @Date: 2023/4/14 16:13
     * @param [dto, userName, userId]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @PostMapping("${api.version}/mall-goods/batchStoreSpecLock")
    ResponseBase batchStoreSpecLock(@RequestBody BatchLockReqDTO dto, @RequestHeader String userName, @RequestHeader String userId);

    /**
     * 导出门店商品
     * @param dto
     * @param userName
     * @return
     */
    @PostMapping(value = "/${api.version}/mall-goods/comm-spec/exportStoreSpecTask")
    ResponseBase exportStoreSpecTask(@RequestBody SpecSearchDTO dto, @RequestHeader String userName);

    /**
     * @Description: 微商城商品-门店商品统计导出任务
     * @Author: Kaven
     * @Date: 2023/4/14 09:53
     * @param [queryDTO, userName]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @PostMapping("/${api.version}/mall-goods/statistic-export")
    ResponseBase specStatisticsExport(@RequestBody SpecStatisticsQueryDTO queryDTO, @RequestHeader String userName);

    /**
     * @Description: 统计指定商家的商品数据同步门店数量
     * @Author: Kaven
     * @Date: 2023/4/14 10:51
     * @param [merCode]
     * @return cn.hydee.starter.dto.ResponseBase<cn.hydee.ydjia.merchantmanager.dto.CommNumStatisticDTO>
     * @Exception
     */
    @GetMapping("/${api.version}/mall-goods/statistic-sync/{merCode}")
    ResponseBase<CommNumStatisticDTO> specSyncStoreStatistics(@PathVariable(value = "merCode") String merCode);

    /**
     * @Description: 微商城商品-批量上下架（已勾选）
     * @Author: Kaven
     * @Date: 2023/3/8 15:40
     * @param [dto]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @PutMapping(value = "/${api.version}/mall-goods/store-spec")
    ResponseBase updateStoreAndSpecStatus(StoreAndSpecStatusReqDTO dto);

    /**
     * @Description: 同步微问诊
     * @Author: Kaven
     * @Date: 2023/4/14 11:11
     * @param [merCode, userName]
     * @return cn.hydee.starter.dto.ResponseBase<java.lang.Object>
     * @Exception
     */
    @GetMapping("/${api.version}/mall-goods/videoConsultationMicro/syncComm")
    ResponseBase<Object> syncMicroConsultation(@RequestHeader String merCode, @RequestHeader String userName);

    /**
     * @Description: 保存操作任务
     * @Author: Kaven
     * @Date: 2023/7/11 10:39
     * @param [dto, userName]
     * @return cn.hydee.starter.dto.ResponseBase<cn.hydee.batch.domain.BatchTask>
     * @Exception
     */
    @PostMapping("/${api.version}/mall-goods/task/save-batch-task")
    ResponseBase<BatchTaskReqDTO> saveBatchTask(@Valid @RequestBody BatchTaskReqDTO dto, @RequestHeader String userName);

    /**
     * @Description: 更新任务状态
     * @Author: Kaven
     * @Date: 2023/7/11 09:25
     * @param [dto, userName]
     * @return cn.hydee.starter.dto.ResponseBase
     * @Exception
     */
    @PostMapping("/${api.version}/mall-goods/task/update-batch-task")
    ResponseBase updateTaskById(@Valid @RequestBody BatchTaskReqDTO dto, @RequestHeader String userName);

    /**
     * @Description: 根据specIds批量查询门店商品
     * @Author: Kaven
     * @Date: 2023/9/7 18:15
     * @param [specDTO]
     * @return cn.hydee.starter.dto.ResponseBase<java.util.List<cn.hydee.ydjia.merchantmanager.dto.resp.StoreSpec>>
     * @Exception
     */
    @PostMapping("/${api.version}/mall-goods/queryOnlineSpecList")
    ResponseBase<List<StoreSpec>> queryOnlineSpecList(@RequestBody SpecStoreQueryDTO specDTO);
}
