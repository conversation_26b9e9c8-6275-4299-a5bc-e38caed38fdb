package cn.hydee.ydjia.merchantmanager.feign;


import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.MarketActivityTemplateReq;
import cn.hydee.ydjia.merchantmanager.dto.resp.ExpertDetailDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.ExpertProgrammeInfo;
import cn.hydee.ydjia.merchantmanager.dto.resp.FestivalInfoDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MarketActivityTemplateDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 营销专家方案
 * <AUTHOR>
 */

@FeignClient(LocalConst.MARKETING_APP_NAME)
public interface MarketExpertActivityClient {



    /**
     * 查询营销方案
     * @param req req
     * @return 营销专家方案对象
     */
    @PostMapping("/${api.version}/expertActivity/queryMarKetActivityTemplateList")
    ResponseBase<List<MarketActivityTemplateDTO>> queryMarKetActivityTemplateList(@RequestBody MarketActivityTemplateReq req);


    /**
     * 查询营销方案库
     * @return 查询营销方案库
     */
    @PostMapping("/${api.version}/expertActivity/queryExpertProgrammeList")
    ResponseBase<ExpertProgrammeInfo> queryExpertProgrammeList();


    /**
     * 查询专家日历集合
     * @param month 1上月 2本月
     * @return 查询专家日历集合
     */
    @GetMapping("/${api.version}/expertActivity/queryFestivalInfoList")
    ResponseBase<FestivalInfoDTO> queryFestivalInfoList(@RequestParam(value = "merCode") String merCode,@RequestParam(value = "month")  Integer month);


    /**
     * 专家方案详情
     * @param id id
     * @return 专家方案详情
     */
    @GetMapping("/${api.version}/expertActivity/getExpertDetail")
    ResponseBase<ExpertDetailDTO> getExpertDetail(@RequestParam(value = "id")  Integer id);


    /**d
     * 距离下个月解锁时间(天数)
     * @return 距离下个月解锁时间(天数)
     */
    @GetMapping("/${api.version}/expertActivity/getExpertNextMonthDay")
    ResponseBase<Integer> getExpertNextMonthDay();

}
