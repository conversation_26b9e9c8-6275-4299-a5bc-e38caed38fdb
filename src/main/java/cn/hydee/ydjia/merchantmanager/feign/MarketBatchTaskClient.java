package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.coupon.CouponBatchTaskQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.req.market.batchTask.CommonExportReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketdata.MarketDataStatisticsGameReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketdata.MarketDataStatisticsPayReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketdata.MarketDataStatisticsShareReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.BatchTaskDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 营销批量任务相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/6
 */
@FeignClient(LocalConst.MARKETING_APP_NAME)
public interface MarketBatchTaskClient {

    /**
     * 分页查询批量任务列表
     */
    @PostMapping("/${api.version}/batchTask/page")
    ResponseBase<PageDTO<BatchTaskDTO>> page(@Valid @RequestBody BatchTaskQueryDTO queryDTO);

    @PostMapping("/${api.version}/batchTask/pageAuth")
    ResponseBase<PageDTO<BatchTaskDTO>> pageAuth(@Valid @RequestBody CouponBatchTaskQueryDTO queryDTO);

    /**
     * 通用导出
     */
    @PostMapping(value = "/${api.version}/batchTask/commonExport")
    ResponseBase<Object> commonExport(@Valid @RequestBody CommonExportReqDTO reqDTO);

    /**
     * 查看任务
     */
    @PostMapping("/${api.version}/batch-task/query-one")
    ResponseBase<BatchTaskDTO> queryByOne(@RequestParam Long batchTaskId);

    /**
     * 导出支付有礼营销统计数据
     */
    @PostMapping("/${api.version}/batchTask/exportActivityPayStatistics")
    ResponseBase<Object> exportActivityPayStatistics(@Valid @RequestBody MarketDataStatisticsPayReqDTO queryDTO);

    /**
     * 导出趣味游戏营销统计数据
     */
    @PostMapping("/${api.version}/batchTask/exportActivityGameStatistics")
    ResponseBase<Object> exportActivityGameStatistics(@Valid @RequestBody MarketDataStatisticsGameReqDTO queryDTO);

    /**
     * 导出分享有礼营销统计数据
     */
    @PostMapping("/${api.version}/batchTask/exportShareGiftStatistics")
    ResponseBase<Object> exportShareGiftStatistics(@Valid @RequestBody MarketDataStatisticsShareReqDTO queryDTO);

    /**
     * 导出优惠券统计数据
     */
    @PostMapping("/${api.version}/batchTask/exportCouponStatistics")
    ResponseBase<Object> exportCouponStatistics(@Valid @RequestBody CouponStatisticsQueryRequest queryDTO);

    /**
     * 导出优惠券投放渠道统计数据
     * @param request
     * @return
     */
    @PostMapping(value = "/${api.version}/batchTask/exportCouponStatisticSource")
    ResponseBase<Boolean> exportCouponStatisticSource(@RequestBody CouponStatisticsQueryRequest request);

    /**
     * 导出现金券/券礼包订单营销统计数据
     * @param request
     * @return
     */
    @PostMapping(value = "/${api.version}/batchTask/exportCouponOrderStatistics")
    ResponseBase<Boolean> exportCouponOrderStatistics(@RequestBody CouponOrderQueryReqDTO request);

    /**
     * 导出现金券/券礼包退款单营销统计数据
     * @param request
     * @return
     */
    @PostMapping(value = "/${api.version}/batchTask/exportCouponRefundStatistics")
    ResponseBase<Boolean> exportCouponRefundStatistics(@RequestBody CouponOrderQueryReqDTO request);


    @ApiOperation(value = "(普通活动)新增")
    @PostMapping("/${api.version}/activity/createNormalActivity")
    ResponseBase<Boolean> createNormalActivity(@RequestBody ActivityAddNormalReqDto activityAddCouponNormalReqDto);

    @ApiOperation(value = "(短信-普通活动)新增")
    @PostMapping("/${api.version}/activity/createSmsActivity")
    ResponseBase<Integer> createSmsActivity(@RequestBody ActivityAddNormalReqDto activityAddCouponNormalReqDto);

    /**
     * 保存群发任务
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/memberBatchSendNotice/save")
    ResponseBase<MemberBatchSendNoticeTaskReqDTO> saveTask(@RequestBody MemberBatchSendNoticeTaskReqDTO reqDTO);

    /**
     * 修改群发任务
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/memberBatchSendNotice/update")
    ResponseBase<MemberBatchSendNoticeTaskReqDTO> update(@RequestBody MemberBatchSendNoticeTaskReqDTO reqDTO);

    /**
     * 智能语音导出
     * @param queryDTO
     */
    @PostMapping("/${api.version}/batchTask/exportSpeechTask")
    ResponseBase<Boolean> exportSpeechTask(@RequestBody AISpeechTaskQueryDTO queryDTO);
}
