package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.SdpConfigInfoDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.ActMoreDiscountRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 分销设置
 * @Date 2020/9/10 19:32
 */
@FeignClient(LocalConst.HYDEE_MIDDLE_SDP)
public interface SdpConfigInfoClient {

    @GetMapping("/sdp/sdp-config/{merCode}")
    ResponseBase<SdpConfigInfoDTO> queryConfig(@PathVariable String merCode);

}
