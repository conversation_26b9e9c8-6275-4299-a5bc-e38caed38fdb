package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.MaterialGroupDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: ChengZW
 * @date: 2021/4/13
 **/
@FeignClient(LocalConst.THIRD)
public interface MaterialGroupManagerClient {
    /**
     * 新增分组
     *
     * @param groupDTO
     * @return
     */
    @PostMapping("/${api.version}/material/group/save")
    ResponseBase<Boolean> saveGroup(@RequestBody MaterialGroupDTO groupDTO);

    /**
     * 查询分组列表
     *
     * @param groupType
     * @param merCode
     * @return
     */
    @GetMapping("/${api.version}/material/group")
    ResponseBase<List<MaterialGroupDTO>> getGroup(@RequestParam("groupType") String groupType,
                                                  @RequestParam("merCode") String merCode);

    /**
     * 编辑分组(排序, 删除, 更新组名)
     *
     * @param merCode
     * @param groupType
     * @param groupList
     * @return
     */
    @PostMapping("/${api.version}/material/group/sort")
    ResponseBase<Boolean> sortGroup(@RequestParam String merCode,
                                    @RequestParam("groupType") String groupType,
                                    @RequestBody List<MaterialGroupDTO> groupList);

}
