package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.ProductAttrKey;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
@FeignClient(LocalConst.MERCHANDISE_APP_NAME)
public interface ProductAttrKeyClient {

    /**
     * 根据分类ID查询产品属性键
     *
     * @return ResponseBase<PageDTO   <   ProductDTO>>
     */
    @GetMapping(value = "/${api.version}/attr-key/list")
    ResponseBase<List<ProductAttrKey>> getProductAttrKeyList();
}
