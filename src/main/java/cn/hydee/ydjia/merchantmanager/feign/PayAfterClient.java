package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.PayDTO;
import cn.hydee.ydjia.merchantmanager.dto.order.req.PaymentDetailQueryReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.payment.OrderPaymentLogDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OrderPaymentDetailDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/23 19:39
 */
@FeignClient(value = LocalConst.PAYMENT_APP_NAME)
public interface PayAfterClient {

    @PostMapping("/${api.version}/payAfter/refund")
    ResponseBase<Map<String, String>> refund(@Valid @RequestBody PayDTO dto);

    @PostMapping("/${api.version}/payAfter/getPaymentLogs")
    ResponseBase<List<OrderPaymentLogDTO>> getPaymentLogs(@RequestBody List<String> businessIds);
}
