package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.MemberValueCardBalanceReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.MemberValueCardReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberValueCardBalanceRspDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberValueCardRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import lombok.NonNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @description:
 * @author: HuangYiBo
 * @time: 2020/11/2 16:22
 */

@FeignClient(LocalConst.MEMBER_APP_NAME)
public interface MemberValueCardClient {

    /**
     * 功能描述: <br>
     * 〈充值礼包设置保存〉
     * @Param: [memberValueCardReqDTO]
     * @Return: cn.hydee.starter.dto.ResponseBase
     * @Author: huangyibo
     * @Date: 2020/11/2 16:28
     */
    @RequestMapping(value = "/${api.version}/memberValueCard/addMemberValueCard", method = RequestMethod.POST)
    ResponseBase addMemberValueCard(@Valid @RequestBody MemberValueCardReqDTO memberValueCardReqDTO);

    /**
     * 功能描述: <br>
     * 〈充值礼包设置修改〉
     * @Param: [memberValueCardReqDTO]
     * @Return: cn.hydee.starter.dto.ResponseBase
     * @Author: huangyibo
     * @Date: 2020/11/2 16:28
     */
    @RequestMapping(value = "/${api.version}/memberValueCard/updateMemberValueCard", method = RequestMethod.POST)
    ResponseBase updateMemberValueCard(@Valid @RequestBody MemberValueCardReqDTO memberValueCardReqDTO);

    /**
     * 功能描述: <br>
     * 〈查询充值礼包设置信息〉
     * @Param: [merCode]
     * @Return: cn.hydee.starter.dto.ResponseBase<cn.hydee.ydjia.merchantmanager.dto.resp.MemberValueCardRespDTO>
     * @Author: huangyibo
     * @Date: 2020/11/2 16:28
     */
    @RequestMapping(value = "/${api.version}/memberValueCard/getMemberValueCard", method = RequestMethod.GET)
    ResponseBase<MemberValueCardRespDTO> getMemberValueCard(@NonNull @RequestParam("merCode") String merCode);

    @RequestMapping(value = "/${api.version}/memberValueCard/getCardCodes", method = RequestMethod.GET)
    ResponseBase<List<Integer>> getCardCodes(@NonNull @RequestParam("merCode") String merCode);

    @PostMapping("/${api.version}/memberValueCard/balance")
    ResponseBase<PageDTO<MemberValueCardBalanceRspDTO>> queryMemberBalance(@RequestBody MemberValueCardBalanceReqDTO reqDTO);
}
