package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.CommentCountSearchReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.CommentReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.CommentSearchReqDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/06/10 16:06
 */
@FeignClient(LocalConst.HYDEE_ES_SYNC)
public interface ESCommentClient {

    /**
     * 新增评论
     * @param comment
     * @return
     */
    @PostMapping("/${api.version}/comment")
    ResponseBase addComment(@Valid CommentReqDTO comment);


    @PostMapping("/${api.version}/comment/_batch")
    ResponseBase batchAddComment(@Valid List<CommentReqDTO> list);

    /**
     * 修改评论
     * @param comment
     * @return
     */
    @PutMapping("/${api.version}/comment")
     ResponseBase updateComment(@Valid CommentReqDTO comment);


    /**
     * 删除评论
     * @param id
     * @return
     */
    @DeleteMapping("/${api.version}/comment/{id}")
     ResponseBase deleteComment(@PathVariable(value = "id") String id);

    /**
     * 统计数量
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/comment/_count")
     ResponseBase<Map<Long, Long>> countComment(@Valid CommentCountSearchReqDTO reqDTO);

    /**
     * 评论查询
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/comment/_search")
     ResponseBase<PageDTO<CommentReqDTO>> searchComment(@Valid CommentSearchReqDTO reqDTO);
}
