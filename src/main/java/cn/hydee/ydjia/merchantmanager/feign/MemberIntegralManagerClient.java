package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.IntegralCleanRule;
import cn.hydee.ydjia.merchantmanager.dto.MemberBaseInfoDTO;
import cn.hydee.ydjia.merchantmanager.dto.OnlineIntegralDTO;
import cn.hydee.ydjia.merchantmanager.dto.QueryOnlineIntegralReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OnlineIntegralRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.merchant.MemberCardAndHoneSetRespDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static cn.hydee.ydjia.merchantmanager.util.LocalConst.MEMBER_APP_NAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2022/11/29
 */
@FeignClient(value = MEMBER_APP_NAME)
public interface MemberIntegralManagerClient {


    /**
     * 新增or修改活动配置
     *
     * @param integralCleanRule
     * @return
     */
    @PostMapping("/${api.version}/integralManager/saveOrUpdate")
    ResponseBase<Boolean> saveOrUpdate(@RequestBody IntegralCleanRule integralCleanRule);

    /**
     * 获取心币清零规则详情
     * @param merCode
     * @return
     */
    @GetMapping("/${api.version}/integralManager/getIntegralManagerDetail")
    ResponseBase<IntegralCleanRule> getIntegralManagerDetail(@RequestParam(value = "merCode") String merCode);

    /**
     * 获取商户还剩余修改次数
     * @param merCode
     * @return
     */
    @GetMapping("/${api.version}/integralManager/getMerchantCleanCount")
    ResponseBase<Integer> getMerchantCleanCount(@RequestParam(value = "merCode") String merCode);


}
