package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.ConsultationPlatformConfigReq;
import cn.hydee.ydjia.merchantmanager.dto.req.ConsultationRecordReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.ConsultationPlatformConfigResp;
import cn.hydee.ydjia.merchantmanager.dto.resp.MedicineConsultationRecordDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 视频问诊（微问诊）相关接口
 *
 * <AUTHOR>
 * @date 2021-08-09
 */
@FeignClient(value = LocalConst.ORDER_APP_NAME)
public interface VideoConsultationClient {

    /**
     * 查询商户配置信息
     * @param merCode
     * @param platformType
     * @return
     */
    @GetMapping("/${api.version}/video-consultation/config/{merCode}/{platformType}")
    ResponseBase<ConsultationPlatformConfigResp> getConfig(@PathVariable String merCode, @PathVariable Integer platformType);

    /**
     * 更新商户配置信息
     * @param config
     * @return
     */
    @PostMapping("/${api.version}/video-consultation/config")
    ResponseBase<Boolean> updateConfig(@RequestBody ConsultationPlatformConfigReq config);

    /**
     * 分页查询问诊记录
     *
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/video-consultation/record/list")
    ResponseBase<PageDTO<MedicineConsultationRecordDTO>> getRecordList(@RequestBody ConsultationRecordReqDTO reqDTO);

    /**
     * 查询问诊记录
     *
     * @param consultationRecordId
     * @param isContainDrugs       是否查询明细
     * @return
     */
    @GetMapping("/${api.version}/video-consultation/record/{consultationRecordId}")
    ResponseBase<MedicineConsultationRecordDTO> getRecord(@PathVariable Long consultationRecordId, @RequestParam(defaultValue = "false") Boolean isContainDrugs);

}
