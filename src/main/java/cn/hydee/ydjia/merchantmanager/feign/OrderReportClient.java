package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.OrderPaymentReportReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.RefundPaymentReportReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OrderPaymentReportRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.RefundPaymentReportRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/02/14 15:20
 */
@FeignClient(value = LocalConst.ORDER_APP_NAME )
public interface OrderReportClient {


    /**
     * 订单支付列表
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/order-payment-report/orderList")
    ResponseBase<PageDTO<OrderPaymentReportRespDTO>> orderList(@RequestBody OrderPaymentReportReqDTO reqDTO);

    /**
     * 订单退款列表
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/order-payment-report/refundList")
    ResponseBase<PageDTO<RefundPaymentReportRespDTO>> refundList(@RequestBody RefundPaymentReportReqDTO reqDTO);

}
