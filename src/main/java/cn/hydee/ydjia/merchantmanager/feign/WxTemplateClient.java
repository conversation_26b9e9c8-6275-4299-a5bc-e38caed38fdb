package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.MessageTemplate;
import cn.hydee.ydjia.merchantmanager.dto.TemplateReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.SetTempReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.wx.WxSetTemplateReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.WxMessageMerchantResDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/14 15:56
 */
@FeignClient(LocalConst.CHANNEL_APP_NAME)
public interface WxTemplateClient {

    /**
     * 查询模板列表
     *
     * @param modelType
     * @param merCode
     * @param messageType
     * @return
     */
    @GetMapping("/${api.version}/api/listWxMessageTemplateMercode")
    ResponseBase<List<MessageTemplate>> queryWxTemplateByType(
            @RequestParam(value = "modelType") Integer modelType,
            @RequestParam("merCode") String merCode,
            @RequestParam("messageType") String messageType);

    @GetMapping("/${api.version}/api/wxMessageTemplateModelFlag")
    ResponseBase<MessageTemplate> wxMessageTemplateModelFlag(
            @RequestParam(value = "modelType") Integer modelType,
            @RequestParam("merCode") String merCode,
            @RequestParam("messageType") String messageType,
            @RequestParam(value = "modelFlag") String modelFlag);

    @GetMapping("/${api.version}/api/wxMessageTemplateMerchantExist")
    ResponseBase<List<String>> wxMessageTemplateMerchantExist();

    /**
     * 公众号模板和小程序消息模板提供一键重置
     * @param modelType
     * @param merCode
     * @param messageType
     * @return
     */
    @PostMapping("/${api.version}/api/templateReset")
    ResponseBase<Boolean> templateReset(
            @RequestParam(value = "modelType") Integer modelType,
            @RequestParam("merCode") String merCode,
            @RequestParam("messageType") String messageType);

    /**
     * 商户修改模板ModelHead和ModelNote
     *
     * @param request
     * @return
     */
    @PostMapping("/${api.version}/api/setMerchantHN")
    ResponseBase setMerchantHN(@RequestBody SetTempReqDTO request);

    /**
     * 给商户设置微信模板,type:1启用,0禁用
     *
     * @param setTempReqDTO
     * @return
     */
    @PostMapping("/${api.version}/api/setTemplate")
    ResponseBase setTemplate(@Valid @RequestBody SetTempReqDTO setTempReqDTO);


    /**
     * 给商户设置微信模板 (多小程序)
     *
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.version}/api/wxSetTemplate")
    ResponseBase wxSetTemplate(@Valid @RequestBody WxSetTemplateReqDTO reqDTO);

    @GetMapping("/${api.version}/api/getAppIdByMercode")
    ResponseBase<String> getAppIdByMercode(@RequestParam("mercode") String merCode);

    /**
     * 根据modelFlag查询短信消息模板记录
     *
     * @param reqDTO
     * @title: queryOneSmsMessageByModelFlag
     * @author: canliang.nie
     * @date: 2023-03-20 11:01
     * @return: cn.hydee.starter.dto.ResponseBase<cn.hydee.ydjia.merchantmanager.dto.resp.WxMessageMerchantResDTO>
     * @throws:
     */
    @PostMapping("/${api.version}/api/querySmsMessageByModelFlag")
    ResponseBase<List<WxMessageMerchantResDTO>> querySmsMessageByModelFlag(@RequestBody TemplateReqDTO reqDTO);
}
