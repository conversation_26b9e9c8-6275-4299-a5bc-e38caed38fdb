package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.AreaRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.AreaStoreQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.AreaStoreResDTO;
import cn.hydee.ydjia.merchantmanager.dto.StoreQueryByCodeReq;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.*;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/10/12 15:41
 */
@FeignClient(LocalConst.BASEINFO_APP_NAME)
public interface StoreClient {

    /**
     * 根据条件查门店信息
     *
     * @param queryStoreDTO 入参
     * @return PageDTO<StoreResDTO>
     */
    @PostMapping("/${api.version}/store/_search")
    ResponseBase<PageDTO<StoreResDTO>> queryStoreByCondition(@Valid @RequestBody QueryStoreDTO queryStoreDTO);

    @PostMapping("/${api.version}/store/_search")
    ResponseBase<PageDTO<StoreSimpleDTO>> queryStoreByConditionSimple(@Valid @RequestBody QueryStoreDTO queryStoreDTO);

    /**
     * 根据条件查门店信息，包括组织机构参数
     *
     * @param queryStoreByOrgIdDTO 入参
     * @return ResponseBase
     */
    @PostMapping("/${api.version}/store/queryStoreByOrgId")
    ResponseBase<PageDTO<StoreResDTO>> queryStoreByOrgId(@Valid @RequestBody QueryStoreByOrgIdDTO queryStoreByOrgIdDTO);

    @PostMapping("/${api.version}/store/myUsingCache")
    ResponseBase<PageDTO<StoreResDTO>> queryStoreByUserId(@Valid @RequestBody MyStoreDTO storeDTO);

    @PostMapping("/${api.version}/store/myUsingCache")
    ResponseBase<PageDTO<SmallStoreRes>> myUsingCache(@Valid @RequestBody MyStoreDTO storeDTO);


    /**
     * 根据门店id集合返回门店信息
     *
     * @param list 入参
     * @return List<StoreResDTO>
     */
    @PostMapping("/${api.version}/store/list")
    ResponseBase<List<StoreResDTO>> queryStoreByIds(@RequestBody Set<String> list);


    /**
     * 根据条件查门店信息
     *
     * @param id 门店id
     * @return PageDTO<StoreResDTO>
     */
    @GetMapping("/${api.version}/store/{id}")
    ResponseBase<StoreResDTO> queryStore(@PathVariable(value = "id") String id);

    @PostMapping("/${api.version}/store/onOffStore")
    ResponseBase<Integer> onOffStore(@RequestBody OnOffStoreReqDTO onOffStoreReqDTO);

    /**
     * 按省分组统计门店数量
     *
     * @param provinceReqDTO 对象
     * @return ResponseBase<List < StroeGroupByProvinceResDTO>>
     */
    @PostMapping("/${api.version}/store/groupByProvince")
    ResponseBase<List<StroeGroupByProvinceResDTO>> groupByProvince(@RequestBody StroeGroupByProvinceReqDTO provinceReqDTO);

    /**
     * 统计上线门店数量
     *
     * @param merCode 商家编码
     * @return ResponseBase<Integer>
     */
    @GetMapping("/${api.version}/store/countOnlineStore/{merCode}")
    ResponseBase<Integer> countOnlineStore(@PathVariable(value = "merCode") String merCode);

    /**
     * 获取excel导出的数据
     *
     * @param queryStoreDTO
     * @return ResponseBase<List < StoreResDTO>>
     */
    @PostMapping("/${api.version}/store/_export")
    ResponseBase<List<StoreResDTO>> queryListForExcel(@RequestBody QueryStoreDTO queryStoreDTO);

    /**
     * 获取erp统一接口Url
     */
    @GetMapping("/${api.base-info-version}/mer-conf/{merCode}/{type}")
    ResponseBase<HuditConfigRespDTO> getHuditConfigByMerCode(@PathVariable String merCode, @PathVariable String type);


    @GetMapping("/${api.version}/store/_queryMerchantCityList")
    ResponseBase<List<String>> queryCityListByMerCode(@RequestParam("merCode") String merCode);

    @PostMapping("/${api.version}/store/_queryStoreByCity")
    ResponseBase<PageDTO<StoreAddressInfoRspDTO>> queryStoreByCity(@Valid @RequestBody QueryStoreByCityReqDTO queryStoreByCityReqDTO);

    /**
     * 门店多条件查询
     *
     * @param queryStoreByOrgIdDTO 入参
     * @return ResponseBase
     */
    @PostMapping("/${api.version}/store/searchAllStoreByOrgId")
    ResponseBase<PageDTO<StoreResDTO>> searchAllStoreByOrgId(@Valid @RequestBody QueryStoreByOrgIdDTO queryStoreByOrgIdDTO);

    /**
     * 查商户下有门店的区域信息
     *
     * @param merCode
     * @return
     */
    @PostMapping("/${api.version}/area/_searchByMerCode")
    ResponseBase<List<AreaRespDTO>> queryAreaByMercode(@RequestParam("merCode") String merCode);

    @PostMapping("/${api.version}/area/_storeByAreas")
    ResponseBase<PageDTO<AreaStoreResDTO>> queryMerStoreWithAreas(@Valid @RequestBody AreaStoreQueryDTO areaQuery);


    /**
     * 门店关键字精确查询
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.base-info-version}/store/_queryStoreByKey")
    ResponseBase<StoreSearchKeyResDTO> queryStoreByKey(@RequestBody StoreSearchKeyReqDTO reqDTO);

    /**
     * 通过门店编码精确查询
     */
    @PostMapping("/${api.version}/store/queryStoreByCond")
    ResponseBase<StoreResDTO> queryStoreByCond(@RequestBody StoreQueryByCodeReq req);


    /**
     * 根据门店编码、商户编号查询门店信息
     * @param reqDTO
     * @return
     */
    @PostMapping("/${api.base-info-version}/store/batchQueryByStCodes/{merCode}")
    ResponseBase<List<StoreResDTO>> batchQueryByStCodes(@PathVariable String merCode,@RequestBody List<String> list);
}
