package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberLabelDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberLabelGroupRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberLabelRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberLabelSelectRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/03/30 21:54
 */
@FeignClient(LocalConst.MEMBER_APP_NAME)
public interface MemberLabelClient {


    @PostMapping("/${api.version}/memberLabel/list")
    ResponseBase<PageDTO<MemberLabelRespDTO>> list(@RequestBody MemberLabelPageReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/groupList")
    ResponseBase<PageDTO<MemberLabelGroupRespDTO>> groupList(@RequestBody MemberLabelGroupReqDTO reqDTO);

    @GetMapping("/${api.version}/memberLabel/platform/labelList")
    ResponseBase<List<MemberLabelRespDTO>> platformLabelList(@RequestParam String merCode);

    @GetMapping("/${api.version}/memberLabel/selectLabelList")
    ResponseBase<MemberLabelSelectRespDTO> selectLabelList(@RequestParam String merCode,@RequestParam(required = false) Boolean filter);

    @PostMapping("/${api.version}/memberLabel/add")
    ResponseBase<Boolean> add(@RequestBody MemberLabelAddReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/addGroup")
    ResponseBase<Boolean> addGroup(@RequestBody MemberLabelGroupAddReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/edit")
    ResponseBase<Boolean> edit(@RequestBody MemberLabelEditReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/editGroup")
    ResponseBase<Boolean> editGroup(@RequestBody MemberLabelGroupEditReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/usePlatformLabel")
    ResponseBase<Boolean> usePlatformLabel(@RequestBody MemberLabelUseReqDTO reqDTO);

    @GetMapping("/${api.version}/memberLabel/checkLabelRepeat")
    ResponseBase<Boolean> checkLabelRepeat(@RequestParam String merCode, @RequestParam String labelName);

    @PostMapping("/${api.version}/memberLabel/changeGroup")
    ResponseBase<Boolean> changeGroup(@RequestBody MemberLabelChangeGroupReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/delete")
    ResponseBase<Boolean> delete(@RequestBody MemberLabelRemoveReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/deleteGroup")
    ResponseBase<Boolean> deleteGroup(@RequestBody MemberLabelGroupRemoveReqDTO reqDTO);

    @PostMapping("/${api.version}/memberLabel/deleteMemLabel")
    ResponseBase<Boolean> deleteMemLabel(@RequestBody MemberLabelMemRemoveReqDTO reqDTO);

    /**
     * 获取会员标签集合
     *
     * @param queryReqDTO
     * @return
     */
    @PostMapping("/${api.version}/memberLabel/getLabelList")
    ResponseBase<List<MemberLabelDTO>> getLabelList(@RequestBody MemberLabelQueryReqDTO queryReqDTO);

    @PostMapping(value = "/${api.version}/memberLabel/importLabel")
    ResponseBase<Boolean> importLabel(@RequestBody MemberLabelImportReqDTO reqDTO);
}
