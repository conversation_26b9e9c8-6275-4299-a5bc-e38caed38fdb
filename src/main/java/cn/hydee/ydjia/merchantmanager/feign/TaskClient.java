package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/13 15:23
 */
@FeignClient(LocalConst.ORDER_APP_NAME)
public interface TaskClient {


    /**
     * 执行统计sql
     * @param sql
     * @return int
     **/
    @PostMapping("/${api.version}/task/_executeSqlCount")
    int executeSqlCount(String sql);

    /**
     * 执行导出sql
     * @param sql
     * @return List<Map></>
     **/
    @PostMapping("/${api.version}/task/_executeSql")
    List<Map> executeSql(String sql);
}
