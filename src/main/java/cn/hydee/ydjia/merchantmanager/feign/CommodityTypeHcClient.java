package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.CommodityType;
import cn.hydee.ydjia.merchantmanager.domain.CommodityTypeHc;
import cn.hydee.ydjia.merchantmanager.dto.CommodityTypeDimension;
import cn.hydee.ydjia.merchantmanager.dto.req.CommodityTypeDimensionReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.DimensionSearchDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.TypeSearchReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.TypeDimensionRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/17 14:06
 */
@FeignClient(LocalConst.MERCHANDISE_APP_NAME)
public interface CommodityTypeHcClient {

    /**
     * 查询分类树形
     * @param dto 分类树形dto
     * @return ResponseBase
     * */
    @PostMapping("/1.0/comm-type-hc/getTypeTree")
    ResponseBase<List<CommodityTypeHc>> getTypeTree(@Valid @RequestBody TypeSearchReqDTO dto);

}
