package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.sms.MessageVariableConfigRspDTO;
import cn.hydee.ydjia.merchantmanager.dto.*;
import cn.hydee.ydjia.merchantmanager.dto.SmsOrderHistoryReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.SmsReportReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.SmsTemplateReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.SmsTemplateRspDTO;
import cn.hydee.ydjia.merchantmanager.dto.sms.QuerySmsDetailRequest;
import cn.hydee.ydjia.merchantmanager.dto.sms.SmsBatchDetailDTO;
import cn.hydee.ydjia.merchantmanager.dto.sms.SmsDetailDTO;
import cn.hydee.ydjia.merchantmanager.dto.sms.feign.ExpectSmsCostRequest;
import cn.hydee.ydjia.merchantmanager.dto.sms.feign.ExpectSmsCostResponse;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Lee
 * @Create : 2022/1/11 15:46
 * @Description :
 */

@FeignClient(LocalConst.THIRD)
public interface SmsServiceClient {

    @PostMapping("/${api.version}/sms/template/addOrUpdate")
    @ApiOperation(value = "增加或修改模板")
    ResponseBase<Boolean> addOrUpdateTemplate(@RequestBody @Valid SmsTemplateReqDTO smsTemplateReqDTO);

    @PostMapping("/${api.version}/sms/template/list")
    @ApiOperation(value = "查询模板列表")
    ResponseBase<PageDTO<SmsTemplateRspDTO>> list(@RequestBody @Valid SmsTemplateReqDTO smsTemplateReqDTO);

    @GetMapping("/${api.version}/sms/template/detail")
    @ApiOperation(value = "查询模板详情")
    ResponseBase<SmsTemplateRspDTO> detail(@RequestParam("id") Long id);

    @GetMapping("/${api.version}/sms/variable")
    @ApiOperation(value = "查询模板变量")
    ResponseBase<List<MessageVariableConfigRspDTO>> variable(@RequestParam("merCode") String merCode, @RequestParam(value = "busType", required = false) String busType, @RequestParam(value = "isCoupon", required = false) Boolean isCoupon);

    @PostMapping("/${api.version}/sms/sign/update")
    @ApiOperation(value = "设置签名")
    ResponseBase<Boolean> updateSign(@RequestParam("merCode") String merCode, @RequestParam(value = "sign", required = false) String sign, @RequestParam(value = "userName", required = false) String userName);

    @GetMapping("/${api.version}/sms/status")
    @ApiOperation(value = "查询商户短信状态")
    ResponseBase<MerchantSmsStatusDTO> queryMerchantStatus(@RequestParam("merCode") String merCode);

    @PostMapping("/${api.version}/sms/order/history/list")
    @ApiOperation(value = "查询订购历史")
    ResponseBase<PageDTO<SmsOrderHistRspDTO>> orderHistoryList(@RequestBody @Valid SmsOrderHistoryReqDTO smsOrderHistoryReqDTO);


    @PostMapping("/${api.version}/sms/testSend")
    @ApiOperation(value = "测试模板发送")
    ResponseBase<Boolean> testSend(@RequestBody @Valid SmsTemplateReqDTO smsTemplateReqDTO);

    /**
     * 套餐余额 - 没有数据表示没有开通套餐
     *
     * @param merCode
     * @return
     */
    @GetMapping(value = "/${api.version}/sms/combo/balance")
    ResponseBase<ComboBalanceRspDTO> comboBalance(@RequestParam("merCode") String merCode, @RequestParam(value = "appType") String appType);

    /**
     * 短信套餐列表
     *
     * @param merCode
     * @return
     */
    @GetMapping(value = "/${api.version}/sms/combo/list")
    ResponseBase<ComboListRspDTO> comboList(@RequestParam("merCode") String merCode, @RequestParam(value = "appType") String appType);

    /**
     * 套餐订购
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/${api.version}/sms/combo/subscribe")
    ResponseBase<ComboSubscribeRspDTO> comboSubscribe(@Valid @RequestBody ComboSubscribeReqDTO req);

    /**
     * 短信分析 - 统计
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/${api.version}/sms/report/statistics")
    ResponseBase<ReportStatisticsRspDTO> reportStatistics(@Valid @RequestBody SmsReportReqDTO req);

    /**
     * 短信分析 - 曲线
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/${api.version}/sms/report/chart")
    ResponseBase<List<ReportChartRspDTO>> reportChart(@Valid @RequestBody SmsReportReqDTO req);

    /**
     * 短信分析 - 表格
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/${api.version}/sms/report/table")
    ResponseBase<PageDTO<ReportTableRspDTO>> reportTable(@Valid @RequestBody SmsReportReqDTO req);

    /**
     * 查询单个批次的发送详情
     *
     * @param merCode
     * @param request
     * @return
     */
    @PostMapping(value = "/${api.version}/sms/batchDetails")
    ResponseBase<PageDTO<SmsDetailDTO>> batchDetails(@RequestHeader String merCode, @RequestBody QuerySmsDetailRequest request);

    /**
     * 查询多个批次的短信发送情况
     *
     * @param merCode
     * @param bizBatchNoList
     * @return
     */
    @PostMapping(value = "/${api.version}/sms/batchList")
    ResponseBase<List<SmsBatchDetailDTO>> batchList(@RequestHeader String merCode, @RequestBody List<String> bizBatchNoList);

    /**
     * 预计短信发送量
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/${api.version}/sms/expectSmsCost")
    ResponseBase<ExpectSmsCostResponse> expectSmsCost(@RequestBody ExpectSmsCostRequest request);

    /**
     * 查询业务场景类型名称
     *
     * @param
     * @title: queryBizTypeNameMap
     * @author: canliang.nie
     * @date: 2023-03-14 14:00
     * @return: cn.hydee.starter.dto.ResponseBase<java.util.Map < java.lang.String, java.lang.String>>
     * @throws:
     */
    @GetMapping(value = "/${api.version}/sms/bizTypeNameMap")
    ResponseBase<Map<String, String>> queryBizTypeNameMap();
}
