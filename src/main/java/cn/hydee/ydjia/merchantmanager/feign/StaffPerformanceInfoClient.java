package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.resp.PromoteRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.sdp.req.QueryStaffReqDto;
import cn.hydee.ydjia.merchantmanager.dto.sdp.resp.SdpStaffPerformanceInfoRes;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2020/9/7 14:43
 */
@FeignClient(value = LocalConst.HYDEE_MIDDLE_SDP)
public interface StaffPerformanceInfoClient {

    @PostMapping("/${api.version}/sdp/staff/_query")
    ResponseBase<PageDTO<SdpStaffPerformanceInfoRes>> query(@RequestBody QueryStaffReqDto dto);
}
