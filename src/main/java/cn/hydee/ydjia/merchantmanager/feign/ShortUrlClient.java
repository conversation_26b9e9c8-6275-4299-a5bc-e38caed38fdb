package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;

import cn.hydee.ydjia.merchantmanager.dto.ShortUrlRequest;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/11/09 20:20
 */
@FeignClient(LocalConst.THIRD)
public interface ShortUrlClient {
    /**
     * queryOriginUrl
     *
     * @param shortUri
     * @return
     */
    @GetMapping("/${api.version}/thirdApi/queryOriginUrl/{shortUri}")
    ResponseBase<String> queryOriginUrl(@PathVariable("shortUri") String shortUri);

    /**
     * 长链转短链
     * @param request
     * @return
     * @return
     */
    @PostMapping("/${api.version}/thirdApi/transferShortUrl/{shortUri}")
    ResponseBase<String> transferShortUrl(@RequestBody ShortUrlRequest request);

}
