package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/15 11:07
 */
@FeignClient(LocalConst.THIRD)
public interface WeeChatApiClient {
    /**
     * 根据appId获取merCode
     *
     * @param appId
     * @return
     */
    @GetMapping("/${api.base-info-version}/api/getMerCodeByAppId")
    ResponseBase<String> getMerCodeByAppId(@RequestParam("appId") String appId);
}
