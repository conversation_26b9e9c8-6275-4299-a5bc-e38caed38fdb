package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.MemberCurrencyCountDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.MemberCurrencyPageReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberCurrencyPageRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberCurrencySourceRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.ReportFormInfoRespDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/11/20 16:50
 */
@FeignClient(value = LocalConst.MEMBER_APP_NAME)
public interface MemberCurrencyStatisticClient {

    @PostMapping("/${api.version}/currencyStatistic/reportForm/info")
    ResponseBase<ReportFormInfoRespDTO> queryReportFormInfo(@RequestBody MemberCurrencyCountDTO countDTO);

    @PostMapping("/${api.version}/currencyStatistic/page")
    ResponseBase<PageDTO<MemberCurrencyPageRespDTO>> queryCurrencyPage(@RequestBody MemberCurrencyPageReqDTO reqDTO);

    @GetMapping("/${api.version}/currencyStatistic/sourceList")
    ResponseBase<List<MemberCurrencySourceRespDTO>> querySourceList();

}
