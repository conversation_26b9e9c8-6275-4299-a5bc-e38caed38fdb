package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.WxGenerateCodeRequest;
import cn.hydee.ydjia.merchantmanager.dto.WxMiniAppDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.third.WxCodeResDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 微信小程序服务接口
 * @create 2020/03/31 22:38
 */
@FeignClient(LocalConst.THIRD)
public interface WeeChatMaClient {

    /**
     * 生成小程序码,会压缩scene长度,可自定义路径page
     *
     * @return
     */
    @PostMapping(value = "/${api.base-info-version}/ma/generateMiniCode")
    @Deprecated
    ResponseBase<String> generateMiniCode(@RequestParam String appType, @RequestBody WxGenerateCodeRequest request, @RequestParam String merCode);

    /**
     * 生成当前机构编码orgCode对应的小程序码，orgCode为空或未单独授权，则生成兜底二维码
     */
    @PostMapping(value = "/${api.version}/ma/generateMiniCode")
    ResponseBase<String> generateMiniCode(@RequestBody WxGenerateCodeRequest request,
                                          @RequestParam(value = "appid", required = false) String appid,
                                          @RequestParam(value = "organization", required = false) String orgCode,
                                          @RequestParam(value = "appType", required = false) String appType,
                                          @RequestParam(value = "merCode") String merCode);

    /**
     * 生成当前机构编码orgList对应的一批小程序码
     * 若orgList中机构未单独授权，则生成兜底小程序二维码
     * orgList为空，生成商户所有已授权的小程序二维码
     */
    @PostMapping(value = "/${api.version}/ma/generateMiniCodeList")
    ResponseBase<List<WxCodeResDTO>> generateMiniCodeList(@RequestBody WxGenerateCodeRequest request,
                                                          @RequestParam(value = "merCode") String merCode,
                                                          @RequestParam(value = "orgList", required = false) List<String> orgList,
                                                          @RequestParam(value = "returnBase", required = false) Boolean returnBase);

    /**
     * 根据merCode拿小程序appId和secret
     *
     * @param merCode
     * @return
     */
    @GetMapping("/${api.version}/api/getMiniAppIdByMerCode")
    ResponseBase<WxMiniAppDTO> getAppIdByMerCode(@RequestParam("merCode") String merCode);

    /**
     * 根据merCode获取appId,
     *  如果fromType=2,且商户没有独立小程序则返回海典公共小程序appId
     * @param merCode
     * @param fromType 1:公众号(默认) 2:小程序
     * @return
     */
    @GetMapping("/${api.version}/api/getAppIdByMerCodeWithDefault")
    ResponseBase<String> getAppIdByMerCodeWithDefault(@RequestParam("merCode") String merCode,
                                                      @RequestParam(value = "formType") Integer fromType);


    /**
     * 功能描述: <br>
     * 〈解除小程序/公众号授权关系〉
     * @Param: [merCode]
     * @Return: cn.hydee.starter.dto.ResponseBase<java.lang.String>
     * @Author: huangyibo
     * @Date: 2021/11/4 14:07
     */
    @GetMapping("/${api.version}/ma/unbindWxAuthInfo")
    ResponseBase<Boolean> unbindWxAuthInfo(@RequestParam("merCode") String merCode, @RequestParam(value = "appid", required = false) String appid, @RequestParam(value = "operator", required = false) String operator);

    /**
     * 获取独立小程序appid，为空表示非独立小程序
     *
     * @param merCode 商户编码
     * @return
     */
    @GetMapping(value = "/${api.version}/ma/getSelfMinAppid")
    ResponseBase<String> getSelfMinAppId(@RequestParam(value = "merCode") String merCode);
}
