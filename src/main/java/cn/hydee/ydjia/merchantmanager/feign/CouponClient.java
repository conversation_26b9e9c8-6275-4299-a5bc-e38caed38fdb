package cn.hydee.ydjia.merchantmanager.feign;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.ActivityDetailRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.CouponDetailDTO;
import cn.hydee.ydjia.merchantmanager.dto.coupon.BatchDeleteRequest;
import cn.hydee.ydjia.merchantmanager.dto.coupon.CouponDmAdd;
import cn.hydee.ydjia.merchantmanager.dto.coupon.CouponRefundDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.req.coupon.CouponProductQueryRequest;
import cn.hydee.ydjia.merchantmanager.dto.resp.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.market.coupon.CouponBaseResDTO;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/17 11:40
 */
@FeignClient(LocalConst.MARKETING_APP_NAME)
public interface CouponClient {

    /**
     * 查询优惠券列表
     * @param queryDTO 对象
     * @return List<CouponResDTO>
     */
    /*List<CouponResDTO> queryCouponList(CouponQueryDTO queryDTO);*/

    /**
     * 查询优惠券列表
     *
     * @param
     * @return
     */
    @PostMapping("/${api.version}/coupon/listCoupon")
    ResponseBase queryCouponList(@Valid @RequestBody CouponQueryDTO queryDTO);

    /**
     * 根据id获取单个优惠券详情
     *
     * @param id
     * @return
     */
    @GetMapping("/${api.version}/coupon/getCouponExtendDetail")
    ResponseBase<CouponBaseExtendDTO> getCouponExtendDetail(@RequestParam("id") Integer id);


    /**
     * 判断优惠券是否绑定活动
     *
     * @param id
     * @return
     */
    @PostMapping("/${api.version}/coupon/checkCouponBindActivity")
    ResponseBase<Boolean> checkCouponBindActivity(@RequestParam("id") Integer id);

    /**
     * 检查优惠券规则id是否被领取
     *
     * @param couponId
     * @param merCode
     * @return
     */
    @PostMapping("/${api.version}/coupon/checkCouponHasReceive")
    ResponseBase<Boolean> checkCouponHasReceive(@RequestParam("couponId") Integer couponId, @RequestParam String merCode);

    /**
     * (B端发券)检查优惠券是否被发放过
     *
     * @param merCode
     * @param couponId
     * @return
     */
    @PostMapping("/${api.version}/coupon/checkCouponHasSend")
    ResponseBase<Boolean> checkCouponHasSend(@RequestParam(value = "merCode") String merCode,
                                             @RequestParam(value = "couponId") Integer couponId);

    /**
     * 查询优惠券绑定的活动列表
     *
     * @param couponId
     * @return
     */
    @PostMapping("/${api.version}/coupon/queryCouponBindActivity")
    ResponseBase<List<ActivityDetailRespDTO>> queryCouponBindActivity(@RequestParam("couponId") Integer couponId);

    /**
     * 根据优惠券id以及状态修改优惠券状态
     * @param id
     * @param status
     * @return
     */
    /*
    @PostMapping("/${api.version}/coupon/updateCouponStauts/{id}/{status}")
    ResponseBase updateCouponStauts(@PathVariable(value = "id")Long id,@PathVariable(value = "id")int status);
    */

    /**
     * 新增优惠券
     *
     * @param
     * @return
     */
    @PostMapping("/${api.version}/coupon/addCouponExtend")
    ResponseBase createCoupon(@Valid @RequestBody CouponReqDTO couponReqDTO);

    /**
     * 修改优惠券
     *
     * @param
     * @return
     */
    @PostMapping("/${api.version}/coupon/updateCouponExtend")
    ResponseBase updateCoupon(@Valid @RequestBody CouponReqDTO couponReqDTO);

    /**
     * 根据id删除优惠券
     *
     * @param id
     * @return
     */
    @PostMapping("/${api.version}/coupon/deleteCouponExtend")
    ResponseBase deleteCouponExtend(@RequestParam("merCode") String merCode, @RequestParam("id") Integer id);


    /**
     * 为活动增加优惠卷
     *
     * @return 活动id
     */
    @PostMapping("/1.0/activity/addActivityFreeByCouponRules")
    ResponseBase<Integer> addCouponForActivity(@RequestBody CouponDmAdd dmCoupon);


    /**
     * (B端发券)历史记录列表
     *
     * @param mercodeReqDTO
     * @return
     */
    @PostMapping("/1.0/coupon/listCouponHistoryList")
    ResponseBase<Page<CouponSendHistoryDTO>> couponHistoryList(@Valid @RequestBody MercodeReqDTO mercodeReqDTO);

    @ApiOperation(value = "导出(B端发券)历史记录列表")
    @PostMapping("/1.0/coupon/exportListCouponHistoryList")
    ResponseBase<Boolean> exportListCouponHistoryList(@Valid @RequestBody MercodeReqDTO merInfo);

    /**
     * (B端发券)根据历史id查询规则列表
     *
     * @param commonReqIntegerDTO
     * @return
     */
    @PostMapping("/1.0/coupon/listCouponHistoryDetail")
    ResponseBase listCouponHistoryDetail(@Valid @RequestBody CommonReqIntegerDTO commonReqIntegerDTO);

    /**
     * (B端发券)删除历史记录
     *
     * @param
     * @return
     */
    @PostMapping("/1.0/coupon/deleteSendHistory")
    ResponseBase<Boolean> deleteSendHistory(@Valid @RequestBody BatchDeleteRequest request);

    /**
     * (B端发券)查询历史发券信息
     *
     * @param id
     * @return
     */
    @PostMapping("/1.0/coupon/getCouponHistoryInfo")
    ResponseBase<CouponSendHistoryDTO> getCouponHistoryInfo(@RequestParam("id") Integer id, @RequestParam("merCode") String merCode);

    /**
     * (B端发券)更新历史状态,1进行中 2已完成
     *
     * @param
     * @return
     */
    @PostMapping("/1.0/coupon/updateCouponHistoryDetail")
    ResponseBase<Boolean> updateCouponHistoryDetail(@RequestParam("remark") String remark, @RequestParam("status") Integer status);

    /**
     * B端批量发券(生成历史记录)
     *
     * @param batchSendCouponHistoryReqDTO
     * @return
     */
    @PostMapping("/1.0/activityCoupon/batchSendCouponHistory")
    ResponseBase<Boolean> batchSendCouponHistory(@Valid @RequestBody BatchSendCouponHistoryReqDTO batchSendCouponHistoryReqDTO);

    @PostMapping("/1.0/activityCoupon/sendCoupon")
    ResponseBase<Boolean> sendCoupon(@RequestBody CouponBatchSendRequest request);

    /**
     * 根据活动id集合查询优惠券列表
     *
     * @param activityIdList
     * @return
     */
    @PostMapping("/${api.version}/activity/activityCouponList")
    ResponseBase<List<ActivityCouponList>> queryCouponList(@RequestBody List<Integer> activityIdList);

    /**
     * 根据优惠券id集合批量查询规则
     *
     * @param ids
     * @param merCode
     * @return
     */
    @PostMapping("/${api.version}/coupon/queryBatchCouponBaseByIds")
    ResponseBase<List<CouponBaseDTO>> queryBatchCouponBaseByIds(@RequestParam(value = "ids") List<Integer> ids,
                                                                @RequestParam(value = "merCode") String merCode);

    /**
     * 分页查询券码列表
     *
     * @param request
     * @return
     */
    @PostMapping("/${api.version}/coupon/queryCouponDetailsPage")
    ResponseBase<PageDTO<CouponDetailDTO>> queryCouponDetailsPage(@RequestBody CouponDetailsQueryRequest request);

    @ApiOperation(value = "导出优惠券领取列表")
    @PostMapping("/${api.version}/coupon/exportReceiveCoupon")
    ResponseBase<Boolean> exportReceiveCoupon(@RequestBody CouponDetailsQueryRequest request);



    /**
     * 作废优惠券
     *
     * @param ids
     * @return
     */
    @PostMapping("/${api.version}/coupon/cancelCouponById")
    ResponseBase<Boolean> cancelCouponByIds(@RequestParam String merCode, @RequestBody List<Long> ids);

    /**
     * B端退回优惠券
     *
     * @param couponDetailsId
     * @param merCode
     * @return
     */
    @PostMapping("/${api.version}/coupon/returnCoupon")
    ResponseBase returnCoupon(@RequestParam Long couponDetailsId, @RequestParam String merCode);

    /**
     * 查询优惠券列表(主页设置选取内链使用）
     *
     * @param req c
     * @return c
     */
    @PostMapping("/${api.version}/activity/listActivityDetail")
    ResponseBase<Page<CouponResDTO>> queryCouponForHome(@Valid @RequestBody MarketingActivitiesDTO req);

    /**
     * 批量查询优惠券分享链接（B端首页编辑内链）
     *
     * @param request c
     * @return c
     */
    @PostMapping(value = "/1.0/couponbus/buildQrCode")
    ResponseBase<List<CouponResDTO>> queryCouponLink(@Valid @RequestBody List<CouponQueryDTO> request);

    /**
     * 查询带优惠券的订单列表
     *
     * @param request
     * @return
     */
    @PostMapping("/${api.version}/coupon/queryOrderWithCouponPages")
    ResponseBase<PageDTO<OrderWithCouponDTO>> queryOrderWithCouponPages(@Valid @RequestBody OrderWithCouponReqDTO request);

    /**
     * 获取优惠券渠道类型集合
     *
     * @return 优惠券渠道类型集合
     */
    @GetMapping("/${api.version}/coupon/getReceiveChannel")
    ResponseBase<List<Map<String, Object>>> getReceiveChannel();

    @PostMapping("/${api.version}/couponPayment/refundRecords")
    ResponseBase<PageDTO<CouponRefundDTO>> queryRefundRecords(@RequestBody CouponRefundReqDTO dto);

    @PostMapping("/${api.version}/couponPayment/refund")
    ResponseBase<CouponRefundRespDTO> refund(@RequestBody CouponOrderRefundReqDTO dto);

    @PostMapping("/${api.version}/couponPayment/merchantAudit")
    ResponseBase<Boolean> merchantAudit(@RequestBody CouponRefundAuditDTO dto);

    @PostMapping("/${api.version}/couponGift/queryPurchaseRecord")
    ResponseBase<PageDTO<CouponOrderDTO>> queryPurchaseRecord(@RequestBody CouponOrderQueryReqDTO request);

    @PostMapping("/${api.version}/couponGift/queryPurchaseTotal")
    ResponseBase<BigDecimal> queryPurchaseTotal(@RequestBody CouponOrderQueryReqDTO request);

    /**
     * 分页查询该优惠券可用商品信息列表
     * @param request
     * @return
     */
    @PostMapping("/${api.version}/coupon/pageCouponProductInfo")
    ResponseBase<PageDTO<CouponProductImageCacheDTO>> pageCouponProductInfo(@RequestBody CouponProductQueryRequest request);


    /**
     * 获取优惠券张数
     *
     * @param request
     * @return
     */
    @PostMapping("/${api.version}/coupon/countCoupon")
    ResponseBase<Integer> countCoupon(@RequestBody CouponDetailsQueryRequest request);

    /**
     * 查询成本中心报表
     * @param req
     * @return
     */
    @PostMapping("/${api.version}/couponbus/queryCostReportList")
    ResponseBase<PageDTO<CouponCostReportRespDTO>> costReportList(@RequestBody @Valid CouponCostReportReqDTO req, @RequestHeader("merCode") String merCode);


    /**
     * 导出成本中心报表
     * @param req
     * @return
     */
    @PostMapping("/${api.version}/couponbus/exportCostReportList")
    ResponseBase<Boolean> exportCostReportList(@RequestBody @Valid CouponCostReportReqDTO req);
}
