package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.ydjia.merchantmanager.domain.WindowDiagramTemplate;
import cn.hydee.ydjia.merchantmanager.dto.WindowDiagramDTO;
import cn.hydee.ydjia.merchantmanager.repository.WindowDiagramTemplateRepo;
import cn.hydee.ydjia.merchantmanager.service.WindowDiagramService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-05-11
 */
@Slf4j
@Service
public class WindowDiagramServiceImpl implements WindowDiagramService {

    @Autowired
    private WindowDiagramTemplateRepo windowDiagramTemplateRepo;

    @DS(value = LocalConst.DB_MANAGER_SLAVE)
    @Override
    public IPage<WindowDiagramTemplate> listWindowDiagramTemplate(WindowDiagramDTO reqDTO) {
        Page<WindowDiagramTemplate> p = new Page<>(reqDTO.getCurrentPage(), reqDTO.getPageSize());
        QueryWrapper<WindowDiagramTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("template_id,type,detail");
        queryWrapper.lambda().eq(WindowDiagramTemplate::getType, "preview");
        return this.windowDiagramTemplateRepo.selectPage(p, queryWrapper);
    }

    @DS(value = LocalConst.DB_MANAGER_SLAVE)
    @Override
    public WindowDiagramTemplate getMaterial(String templateId) {
        WindowDiagramTemplate windowDiagramTemplate = new WindowDiagramTemplate();
        windowDiagramTemplate.setTemplateId(templateId);
        QueryWrapper<WindowDiagramTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("template_id,type,detail");
        queryWrapper.lambda().eq(WindowDiagramTemplate::getTemplateId, templateId)
                .eq(WindowDiagramTemplate::getType, "material");
        List<WindowDiagramTemplate> windowDiagramTemplates = this.windowDiagramTemplateRepo.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(windowDiagramTemplates)) {
            windowDiagramTemplate.setDetail(windowDiagramTemplates.get(0).getDetail());
        }
        return windowDiagramTemplate;
    }
}
