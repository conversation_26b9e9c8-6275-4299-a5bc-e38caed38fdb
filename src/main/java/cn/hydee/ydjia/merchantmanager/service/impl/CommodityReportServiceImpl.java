package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryStoreDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.commodity.CommodityReportReq;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.commodity.CommodityReportResp;
import cn.hydee.ydjia.merchantmanager.enums.IsvalidStatus;
import cn.hydee.ydjia.merchantmanager.enums.StoreOnLineStatus;
import cn.hydee.ydjia.merchantmanager.feign.CommodityReportClient;
import cn.hydee.ydjia.merchantmanager.feign.StoreClient;
import cn.hydee.ydjia.merchantmanager.service.CommodityReportService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2021/1/11 15:45
 */
@Service
@Slf4j
public class CommodityReportServiceImpl implements CommodityReportService {
    private final StoreClient storeClient;
    private final CommodityReportClient reportClient;

    public CommodityReportServiceImpl(StoreClient storeClient, CommodityReportClient reportClient) {
        this.storeClient = storeClient;
        this.reportClient = reportClient;
    }

    @Override
    public Set<String> getMerchantsCity(String merCode) {
        Set<String> citys = new HashSet<>();
        QueryStoreDTO dto = new QueryStoreDTO();
        dto.setMerCode(merCode);
        dto.setStatus(IsvalidStatus.EFFECTIVE.getCode());
        dto.setOnlineStatus(StoreOnLineStatus.ON_LINE.getCode());
        dto.setExcelFlag(true);
        ResponseBase<PageDTO<StoreResDTO>> base = storeClient.queryStoreByCondition(dto);
        PageDTO<StoreResDTO> data = base.getData();
        if (StringUtils.isEmpty(data) || CollectionUtils.isEmpty(data.getData())) {
            log.info("获取门店地址信息失败{},入参为{}", base.getMsg(), JSON.toJSONString(dto));
            return citys;
        }
        data.getData().forEach(item -> {
            if (!StringUtils.isEmpty(item.getCity())) {
                citys.add(item.getCity());
            }
        });
        return citys;
    }

    @Override
    public ResponseBase<PageDTO<CommodityReportResp>> searchByParam(CommodityReportReq param) {
        if (!StringUtils.isEmpty(param.getCity())) {
            String city = fitterCity(param.getCity());
            param.setCity(city);
        }
        return reportClient.searchByParam(param);
    }

    private String fitterCity(String city) {
        return city.replace("市", "").replace("县", "");
    }
}
