package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.ydjia.merchantmanager.domain.YdjPaySet;
import cn.hydee.ydjia.merchantmanager.dto.payment.PayCommonConfigDTO;
import cn.hydee.ydjia.merchantmanager.dto.ThirdPayConfigDTO;
import cn.hydee.ydjia.merchantmanager.dto.payment.MedicalInsurancePaymentConfigDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.YdjPaySetSearchDTO;

import java.util.List;

/**
 * 药店加订单支付方式服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/21 11:20
 */
public interface YdjPaySetService {
    /**
     * 新增或修改订单支付方式
     * @param YdjPaySet 对象
     * @return String
     */
    String addPaySet(YdjPaySet YdjPaySet,String userId);

    /**
     * 查询订单支付方式
     * @param ydjPaySetSearchDTO 商家编码
     * @return YdjOrderPaySetReqDTO 对象
     */
    List<YdjPaySet> queryPaySet(YdjPaySetSearchDTO ydjPaySetSearchDTO);

    /**
     * 批量新增门店-支付设置
     *
     * @param reqDTO
     * @return
     */
    Boolean batchAdd(ThirdPayConfigDTO reqDTO);

    /**
     * 更新单个门店支付设置配置
     *
     * @param reqDTO
     * @return
     */
    Boolean update(PayCommonConfigDTO reqDTO);

    /**
     * 批量更新门店支付设置配置
     *
     * @param reqDTO
     * @return
     */
    Boolean batchUpdate(ThirdPayConfigDTO reqDTO);

    /**
     * 更新门店医保配置
     *
     * @param reqDTO
     * @return
     */
    Boolean updateMedicalInsuranceConfig(MedicalInsurancePaymentConfigDTO reqDTO);

    /**
     * 回收门店医保配置缓存
     *
     * @param merCode
     * @param storeId
     * @return
     */
    Boolean evictMedicalInsuranceConfig(String merCode, String storeId);
}
