package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.UUIDUtil;
import cn.hydee.ydjia.merchantmanager.domain.YdjStore;
import cn.hydee.ydjia.merchantmanager.dto.AreaRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreNumResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreRuleResDTO;
import cn.hydee.ydjia.merchantmanager.enums.*;
import cn.hydee.ydjia.merchantmanager.feign.ESCommodityClient;
import cn.hydee.ydjia.merchantmanager.feign.PackageClient;
import cn.hydee.ydjia.merchantmanager.feign.StoreClient;
import cn.hydee.ydjia.merchantmanager.repository.YdjStoreRepo;
import cn.hydee.ydjia.merchantmanager.service.StoreRuleService;
import cn.hydee.ydjia.merchantmanager.service.YdjStoreService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 药店加门店服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/21 11:20
 */
@Service
public class YdjStoreServiceImpl implements YdjStoreService {
    @Autowired
    private YdjStoreRepo ydjStoreRepo;
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private PackageClient packageClient;
    @Autowired
    private ESCommodityClient esCommodityClient;
    @Autowired
    private StoreRuleService storeRuleService;


    @Override
    public StoreResDTO queryCenterStore(String merCode) {
        QueryWrapper<YdjStore> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(YdjStore::getMerCode, merCode);
        queryWrapper.lambda().eq(YdjStore::getCenterStore, IsCenterStore.IS_CENTER_SOTRE.getCode());
        queryWrapper.lambda().eq(YdjStore::getIsvalid, IsvalidStatus.EFFECTIVE.getCode());
        List<YdjStore> list = ydjStoreRepo.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setMerCode(merCode);
        queryStoreDTO.setStCode(list.get(0).getStCode());
        queryStoreDTO.setExcelFlag(true);
        ResponseBase<PageDTO<StoreResDTO>> res = storeClient.queryStoreByCondition(queryStoreDTO);
        return CollectionUtils.isEmpty(res.getData().getData()) ? null :
                res.getData().getData().get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addCenterStore(YdjStore ydjStore, String userId) {
        //删除中心店
        ydjStoreRepo.deleteCenterStore(ydjStore.getMerCode(), userId);
        //新增中心店
        ydjStore.setId(UUIDUtil.generateUuid());
        ydjStore.setCenterStore(IsCenterStore.IS_CENTER_SOTRE.getCode());
        ydjStore.setIsvalid(IsvalidStatus.EFFECTIVE.getCode());
        ydjStoreRepo.insert(ydjStore);
        return ydjStore.getId();
    }

    @Override
    public ResponseBase onOffStore(OnOffStoreReqDTO onOffStoreReqDTO, String userId) {
        //如果是上线操作，则判断是否超过套餐可上线门店数量
        //如果是下线，判断是否下线了中心店
        if (StoreOnLineStatus.OFF_LINE.getCode().equals(onOffStoreReqDTO.getOnlineStatus())) {
            YdjStore centerStore = ydjStoreRepo.queryCenterStore(onOffStoreReqDTO.getMerCode());
            if (centerStore != null && !CollectionUtils.isEmpty(onOffStoreReqDTO.getList().stream()
                    .filter(o -> o.equals(centerStore.getStCode())).collect(Collectors.toList()))) {
                throw WarnException.builder().code(ErrorType.CENTER_STORE_CANT_OFFLINE.getCode()).
                        tipMessage(ErrorType.CENTER_STORE_CANT_OFFLINE.getMsg()).build();
            }
        }
        //批量修改上下线
        onOffStoreReqDTO.setUserId(userId);
        ResponseBase<Integer> responseBase = storeClient.onOffStore(onOffStoreReqDTO);
        if (responseBase != null && responseBase.checkSuccess()) {
            Integer line = responseBase.getData();
            if (line > 0) {
                //操作成功，写入ES
                StoreChangeReq req = new StoreChangeReq();
                req.setMerCode(onOffStoreReqDTO.getMerCode());
                req.setStoreIds(onOffStoreReqDTO.getStoreIds());
                esCommodityClient.storeChange(req);
            }
        }
        return responseBase;
    }

    @Override
    public StoreNumResDTO queryStoreNum(String merCode, String sys) {
        StoreNumResDTO storeNumResDTO = new StoreNumResDTO();
        PkgStoreReqDTO pkgStoreReqDTO = new PkgStoreReqDTO();
        pkgStoreReqDTO.setMerCode(merCode);
        pkgStoreReqDTO.setSys(sys);
        storeNumResDTO.setPkgOnlineStore(packageClient.queryStoreNum(pkgStoreReqDTO).getData());
        storeNumResDTO.setOnlineStore(storeClient.countOnlineStore(merCode).getData());
        return storeNumResDTO;
    }

    @Override
    public Set<String> buildStore(String merCode, String userId) {
        MyStoreDTO myStoreDTO = new MyStoreDTO();
//        myStoreDTO.setOnlineStatus(1);
        myStoreDTO.setMerCode(merCode);
        myStoreDTO.setStatus(1);
        myStoreDTO.setCurrentPage(1);
        myStoreDTO.setPageSize(100000);
        myStoreDTO.setUserId(userId);
        ResponseBase<PageDTO<StoreResDTO>> storeBase = storeClient.queryStoreByUserId(myStoreDTO);

        Map<String, StoreResDTO> storeMap = new HashMap<>();

        if (storeBase != null && storeBase.checkSuccess()) {
            if (storeBase.getData().getData() == null) {
                throw WarnException.builder().code(LocalError.STORE_NONE.getCode()).
                        tipMessage(LocalError.STORE_NONE.getMsg()).build();
            }
            storeBase.getData().getData().forEach(store -> {
                //云仓特殊处理，微商城云仓订单的门店id和门店code都是写死的WSC1111
                if (LocalConst.CLOUD_FIXED_STORE.equals(store.getStCode())) {
                    store.setId(LocalConst.CLOUD_FIXED_STORE);
                }
                storeMap.put(store.getId(), store);
            });
        }
        return storeMap.keySet();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteFlagshipStore(String merCode, String userName) {
        StoreRuleResDTO detail = storeRuleService.getDetail(merCode);
        if (detail != null) {
            if (NonStoreRuleEnum.ENTER_CENTER_STORE.getCode().equals(detail.getNonStoreRule())) {
                throw WarnException.builder().code(ErrorType.CENTER_STORE_USED_ERROR.getCode())
                        .tipMessage(ErrorType.CENTER_STORE_USED_ERROR.getMsg()).build();
            }
        }
        ydjStoreRepo.deleteCenterStore(merCode, userName);
        return true;
    }

    @Override
    public List<AreaRespDTO> queryAreaByMercode(String merCode) {
        List<AreaRespDTO> treeList = new ArrayList<>();
        ResponseBase<List<AreaRespDTO>> listResponseBase = storeClient.queryAreaByMercode(merCode);
        if (listResponseBase != null && listResponseBase.getData() != null) {
            List<AreaRespDTO> data = listResponseBase.getData();
            Map<Integer, List<AreaRespDTO>> parentMap = data.stream().filter(type -> !type.getAreaType().equals(2)).collect(Collectors.groupingBy(AreaRespDTO::getParentId));
            for (AreaRespDTO tree : data) {
                //找到根
                if (tree.getAreaType().equals(2)) {
                    treeList.add(tree);
                }
                //找到子集合o
                List<AreaRespDTO> child = parentMap.get(tree.getId());
                if (!CollectionUtils.isEmpty(child)) {
                    tree.setChildren(child);
                }
            }
        }
        return treeList;
    }
}
