package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.ActivitySpecImportResp;
import cn.hydee.ydjia.merchantmanager.dto.resp.ActivityStoreImportResp;
import cn.hydee.ydjia.merchantmanager.dto.resp.aggregate.ActAggPreferenceRes;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/4/17 9:52
 */
public interface ActivityInfoService {

    ResponseBase<PageDTO<ActiInfoRespDTO>> queryActivityCommByCondition(ActiSearchReqDTO conditionDTO);

    ResponseBase deleteActivity(Long id, String userName);

    ResponseBase inValidActivity(Long id, String userName);

    ResponseBase setEffectiveActivity(Long id);

    /**
     * 活动复制
     *
     * @param actCopyReqDTO
     * @return
     */
    ResponseBase copyActivity(ActCopyReqDTO actCopyReqDTO);

    ResponseBase launchActivity(ActLaunchReqDTO actLaunchReqDTO);

    /**
     * 导入活动商品excel，返回信息集合
     * @param merCode
     * @param isHasDrug
     * @param excelFile
     * @return
     */
    ActivitySpecImportResp importCommodityExcel(String merCode,Integer isHasDrug, MultipartFile excelFile);
    List<ActAggPreferenceRes> querySecKillData(ActivityMainReqDTO req);

    /**
     * 导入限时优惠活动商品excel，返回数据
     *
     * @param merCode
     * @param isHasDrug
     * @param excelFile
     * @return
     */
    ActivitySpecImportResp importLimitedExcel(String merCode, Integer isHasDrug, MultipartFile excelFile);

    /**
     * 导入门店
     *
     * @param merCode
     * @param excelFile
     * @return
     */
    ActivityStoreImportResp importStoreExcel(String merCode, MultipartFile excelFile);

    /**
     * @Description: 导入定金预售活动商品excel，返回数据
     * @Author: Kaven
     * @Date: 2023/1/5 13:43
     * @param [merCode, isHasDrug, excelFile, presaleType]
     * @return cn.hydee.ydjia.merchantmanager.dto.resp.ActivitySpecImportResp
     * @Exception
     */
    ActivitySpecImportResp importDepositPresaleExcel(String merCode, Integer isHasDrug, MultipartFile excelFile, String presaleType);

    /**
     * @Description: 导入全款预售活动商品excel，返回数据
     * @Author: Kaven
     * @Date: 2023/1/5 14:54
     * @param [merCode, isHasDrug, excelFile, presaleType]
     * @return cn.hydee.ydjia.merchantmanager.dto.resp.ActivitySpecImportResp
     * @Exception
     */
    ActivitySpecImportResp importFullPayPresaleExcel(String merCode, Integer isHasDrug, MultipartFile excelFile, String presaleType);

    /**
     * @Description: 分销活动商品导入
     * @Author: Kaven
     * @Date: 2023/8/24 10:59
     * @param [ merCode, commType, isHasDrug, excelFile, storeId]
     * @return cn.hydee.ydjia.merchantmanager.dto.resp.ActivitySpecImportResp
     * @Exception
     */
    ActivitySpecImportResp importDistributionExcel(String merCode, String commType, Integer isHasDrug, MultipartFile excelFile, String storeId);
}
