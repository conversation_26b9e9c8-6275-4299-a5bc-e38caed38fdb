package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.ExLogger;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryStoreDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketreport.MarketMemberPageReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketreport.MarketMemberReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketreport.MarketReportPageReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketreport.MarketReportReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketreport.MarketReportSaleStatisticPageReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.AccountInfoRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.marketreport.*;
import cn.hydee.ydjia.merchantmanager.enums.CouponTypeEnum;
import cn.hydee.ydjia.merchantmanager.enums.MarketReportHxOrderNum;
import cn.hydee.ydjia.merchantmanager.enums.MarketReportMemberOrderNum;
import cn.hydee.ydjia.merchantmanager.feign.BaseInfoClient;
import cn.hydee.ydjia.merchantmanager.feign.MarketMemberReportClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.service.MarketReportService;
import cn.hydee.ydjia.merchantmanager.util.CollectorsUtil;
import cn.hydee.ydjia.merchantmanager.util.StringUtil;
import cn.hydee.ydjia.merchantmanager.util.TBean;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: HuangYiBo
 * @time: 2021/11/23 14:47
 */

@Service
@Slf4j
public class MarketReportServiceImpl implements MarketReportService {

    @Autowired
    private MarketMemberReportClient marketMemberReportClient;

    @Autowired
    private EmpAuthHandler empAuthHandler;

    @Autowired
    private BaseInfoClient baseInfoClient;

    @Override
    public MarketReportAllDataRespDTO getAllData(MarketReportReqDTO marketReportReqDTO) {
        MarketReportAllDataRespDTO allDataRespDTO = new MarketReportAllDataRespDTO();

        MarketMemberReqDTO marketMemberReqDTO = buildParamDTO(marketReportReqDTO);
        log.info("getInfo reqDTO === {}", marketMemberReqDTO);
        ResponseBase<List<RespDTO>> reportBasicInfoRespDTOResponseBase = marketMemberReportClient.getInfo(marketMemberReqDTO);
        if (!reportBasicInfoRespDTOResponseBase.checkSuccess()) {
            throw WarnException.builder().code(reportBasicInfoRespDTOResponseBase.getCode()).
                    tipMessage(reportBasicInfoRespDTOResponseBase.getMsg()).build();
        }
        List<RespDTO> respDTOList = reportBasicInfoRespDTOResponseBase.getData();

        String startTime = marketMemberReqDTO.getStartTime();
        String endTime = marketMemberReqDTO.getEndTime();

        //环比
        setTimeMoM(marketMemberReqDTO, startTime, endTime, marketReportReqDTO.getTimeType());
        log.info("mom getInfo reqDTO === {}", marketMemberReqDTO);
        ResponseBase<List<RespDTO>> mom = marketMemberReportClient.getInfo(marketMemberReqDTO);
        if (!mom.checkSuccess()) {
            throw WarnException.builder().code(mom.getCode()).
                    tipMessage(mom.getMsg()).build();
        }
        List<RespDTO> respDTOListMoM = mom.getData();

        //同比
        setTimeYoY(marketMemberReqDTO, startTime, endTime);
        log.info("yoy getInfo reqDTO === {}", marketMemberReqDTO);
        ResponseBase<List<RespDTO>> yoy = marketMemberReportClient.getInfo(marketMemberReqDTO);
        if (!yoy.checkSuccess()) {
            throw WarnException.builder().code(yoy.getCode()).
                    tipMessage(yoy.getMsg()).build();
        }
        List<RespDTO> respDTOListYoY = yoy.getData();

        allDataRespDTO.setBasicDataRespDTO(getBasicData(respDTOList, respDTOListMoM, respDTOListYoY));

        allDataRespDTO.setSaleDataRespDTO(getSaleData(respDTOList));

        allDataRespDTO.setCompareDataRespDTO(getCompareData(respDTOList, respDTOListMoM, respDTOListYoY));

        return allDataRespDTO;
    }

    @Override
    public List<MarketReportRepurchaseDataRespDTO> getRepurchaseData(MarketReportReqDTO marketReportReqDTO) {
        MarketMemberReqDTO marketMemberReqDTO = buildParamDTO(marketReportReqDTO);
        ResponseBase<List<RespDTO>> marketMemberRepurchaseResp = marketMemberReportClient.getRepurchaseInfo(marketMemberReqDTO);
        if (!marketMemberRepurchaseResp.checkSuccess()) {
            throw WarnException.builder().code(marketMemberRepurchaseResp.getCode()).
                    tipMessage(marketMemberRepurchaseResp.getMsg()).build();
        }
        List<RespDTO> repurchaseRespDTOList = marketMemberRepurchaseResp.getData();
        return getRepurchase(repurchaseRespDTOList);
    }

    private void setTimeMoM(MarketMemberReqDTO marketMemberReqDTO, String startTime, String endTime, String timeType) {
        Date startDate = DateUtil.parseDate(startTime, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        Date endDate = DateUtil.parseDate(endTime, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        if ("1".equals(timeType) || "5".equals(timeType)|| StringUtils.isEmpty(timeType)) {
            int dayDifference = cn.hydee.ydjia.merchantmanager.util.DateUtil.getDayDifference(startDate, endDate);
            Date startTimeMoM = DateUtils.addDays(startDate, -dayDifference - 1);
            Date endTimeMoM = DateUtils.addDays(endDate, -dayDifference - 1);
            marketMemberReqDTO.setStartTime(DateFormatUtils.format(startTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
            marketMemberReqDTO.setEndTime(DateFormatUtils.format(endTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
        }
        if ("2".equals(timeType)) {
            Date startTimeMoM = DateUtils.addMonths(startDate, -1);
            Date endTimeMoM = DateUtils.addDays(DateUtils.addMonths(startTimeMoM, 1), -1);
            marketMemberReqDTO.setStartTime(DateFormatUtils.format(startTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
            marketMemberReqDTO.setEndTime(DateFormatUtils.format(endTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
        }
        if ("3".equals(timeType)) {
            Date startTimeMoM = DateUtils.addMonths(startDate, -3);
            Date endTimeMoM = DateUtils.addDays(DateUtils.addMonths(startTimeMoM, 3), -1);
            marketMemberReqDTO.setStartTime(DateFormatUtils.format(startTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
            marketMemberReqDTO.setEndTime(DateFormatUtils.format(endTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
        }
        if ("4".equals(timeType)) {
            Date startTimeMoM = DateUtils.addYears(startDate, -1);
            Date endTimeMoM = DateUtils.addYears(endDate, -1);
            marketMemberReqDTO.setStartTime(DateFormatUtils.format(startTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
            marketMemberReqDTO.setEndTime(DateFormatUtils.format(endTimeMoM, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
        }

    }

    private void setTimeYoY(MarketMemberReqDTO marketMemberReqDTO, String startTime, String endTime) {
        Date startDate = DateUtil.parseDate(startTime, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        Date endDate = DateUtil.parseDate(endTime, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        Date startTimeYoY = DateUtils.addYears(startDate, -1);
        Date endTimeYoY = DateUtils.addYears(endDate, -1);
        marketMemberReqDTO.setStartTime(DateFormatUtils.format(startTimeYoY, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
        marketMemberReqDTO.setEndTime(DateFormatUtils.format(endTimeYoY, DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
    }

    @Override
    public MarketReportNumDataRespDTO getNumData(MarketReportReqDTO marketReportReqDTO) {
        MarketMemberReqDTO marketMemberReqDTO = buildParamDTO(marketReportReqDTO);
        log.info("getNumData reqDTO === {}", marketMemberReqDTO);
        ResponseBase<BasicCountRespDTO> memberNumResp = marketMemberReportClient.getMemberNum(marketMemberReqDTO);
        if (!memberNumResp.checkSuccess()) {
            throw WarnException.builder().code(memberNumResp.getCode()).
                    tipMessage(memberNumResp.getMsg()).build();
        }
        BasicCountRespDTO basicCountRespDTO = memberNumResp.getData();

        ResponseBase<RepurchaseCountRespDTO> repurchaseMemberNumResp = marketMemberReportClient.getRepurchaseMemberNum(marketMemberReqDTO);
        if (!repurchaseMemberNumResp.checkSuccess()) {
            throw WarnException.builder().code(repurchaseMemberNumResp.getCode()).
                    tipMessage(repurchaseMemberNumResp.getMsg()).build();
        }
        RepurchaseCountRespDTO repurchaseCountRespDTO = repurchaseMemberNumResp.getData();

        String startTime = marketMemberReqDTO.getStartTime();
        String endTime = marketMemberReqDTO.getEndTime();

        //环比
        setTimeMoM(marketMemberReqDTO, startTime, endTime, marketReportReqDTO.getTimeType());
        log.info("mom getNumData reqDTO === {}", marketMemberReqDTO);
        ResponseBase<BasicCountRespDTO> momNum = marketMemberReportClient.getMemberNum(marketMemberReqDTO);
        if (!momNum.checkSuccess()) {
            throw WarnException.builder().code(momNum.getCode()).
                    tipMessage(momNum.getMsg()).build();
        }
        BasicCountRespDTO momNumData = momNum.getData();
        basicCountRespDTO.setMarketMemberNumRespDTOMoM(momNumData.getMarketMemberNumRespDTO());
        ResponseBase<RepurchaseCountRespDTO> momRepurchaseNum = marketMemberReportClient.getRepurchaseMemberNum(marketMemberReqDTO);
        if (!momRepurchaseNum.checkSuccess()) {
            throw WarnException.builder().code(momRepurchaseNum.getCode()).
                    tipMessage(momRepurchaseNum.getMsg()).build();
        }
        RepurchaseCountRespDTO momRepurchaseNumData = momRepurchaseNum.getData();
        repurchaseCountRespDTO.setRepurchaseNumRespDTOMoM(momRepurchaseNumData.getRepurchaseNumRespDTO());

        //同比
        setTimeYoY(marketMemberReqDTO, startTime, endTime);
        log.info("yoy getNumData reqDTO === {}", marketMemberReqDTO);
        ResponseBase<BasicCountRespDTO> yoyNum = marketMemberReportClient.getMemberNum(marketMemberReqDTO);
        if (!yoyNum.checkSuccess()) {
            throw WarnException.builder().code(yoyNum.getCode()).
                    tipMessage(yoyNum.getMsg()).build();
        }
        BasicCountRespDTO yoyNumData = yoyNum.getData();
        basicCountRespDTO.setMarketMemberNumRespDTOYoY(yoyNumData.getMarketMemberNumRespDTO());
        ResponseBase<RepurchaseCountRespDTO> yoyRepurchaseNum = marketMemberReportClient.getRepurchaseMemberNum(marketMemberReqDTO);
        if (!yoyRepurchaseNum.checkSuccess()) {
            throw WarnException.builder().code(yoyRepurchaseNum.getCode()).
                    tipMessage(yoyRepurchaseNum.getMsg()).build();
        }

        RepurchaseCountRespDTO yoyRepurchaseNumData = yoyRepurchaseNum.getData();
        repurchaseCountRespDTO.setRepurchaseNumRespDTOYoY(yoyRepurchaseNumData.getRepurchaseNumRespDTO());

        NumRespDTO numRespDTO = basicCountRespDTO.getMarketMemberNumRespDTO();
        RepurchaseNumRespDTO repurchaseNumRespDTO = repurchaseCountRespDTO.getRepurchaseNumRespDTO();

        NumRespDTO numRespDTOMoM = basicCountRespDTO.getMarketMemberNumRespDTOMoM();
        RepurchaseNumRespDTO repurchaseNumRespDTOMoM = repurchaseCountRespDTO.getRepurchaseNumRespDTOMoM();

        NumRespDTO numRespDTOYoY = basicCountRespDTO.getMarketMemberNumRespDTOYoY();
        RepurchaseNumRespDTO repurchaseNumRespDTOYoY = repurchaseCountRespDTO.getRepurchaseNumRespDTOYoY();

        //本期用券会员数
        Integer memberNumUse = numRespDTO.getMemberNumUse();
        //本期未用券会员数
        Integer memberNumNotUse = numRespDTO.getMemberNumNotUse();
        //本期用券复购会员数
        Integer repurchaseNumUse = repurchaseNumRespDTO.getMemberRepurchaseNumUse();
        //本期未用券复购会员数
        Integer repurchaseNumNotUse = repurchaseNumRespDTO.getMemberRepurchaseNumNotUse();
        //本期用券复购会员率
        BigDecimal repurchaseNumUseRate = new BigDecimal("0");
        if (memberNumUse != 0) {
            repurchaseNumUseRate = new BigDecimal(String.valueOf(repurchaseNumUse)).divide(new BigDecimal(String.valueOf(memberNumUse)), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        //本期未用券复购会员率
        BigDecimal repurchaseNumNotUseRate = new BigDecimal("0");
        if (memberNumNotUse != 0) {
            repurchaseNumNotUseRate = new BigDecimal(String.valueOf(repurchaseNumNotUse)).divide(new BigDecimal(String.valueOf(memberNumNotUse)), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        //环比用券复购会员数
        Integer repurchaseNumMoMUse = repurchaseNumRespDTOMoM.getMemberRepurchaseNumUse();
        //环比用券会员数
        Integer memberNumMoMUse = numRespDTOMoM.getMemberNumUse();
        //环比用券复购会员率
        BigDecimal repurchaseNumMoMUseRate = new BigDecimal("0");
        if (memberNumMoMUse != 0) {
            repurchaseNumMoMUseRate = new BigDecimal(String.valueOf(repurchaseNumMoMUse)).divide(new BigDecimal(String.valueOf(memberNumMoMUse)), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        //环比用券复购会员率增长率
        BigDecimal repurchaseNumMoMUseRateRate = new BigDecimal("0");
        if (repurchaseNumMoMUseRate.compareTo(BigDecimal.ZERO) != 0) {
            repurchaseNumMoMUseRateRate = repurchaseNumUseRate.subtract(repurchaseNumMoMUseRate).divide(repurchaseNumMoMUseRate, 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        //同比用券复购会员数
        Integer repurchaseNumYoYUse = repurchaseNumRespDTOYoY.getMemberRepurchaseNumUse();
        //同比用券会员数
        Integer memberNumYoYUse = numRespDTOYoY.getMemberNumUse();
        //同比用券复购会员率
        BigDecimal repurchaseNumYoYUseRate = new BigDecimal("0");
        if (memberNumYoYUse != 0) {
            repurchaseNumYoYUseRate = new BigDecimal(String.valueOf(repurchaseNumYoYUse)).divide(new BigDecimal(String.valueOf(memberNumYoYUse)), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        //同比用券复购会员率增长率
        BigDecimal repurchaseNumYoYUseRateRate = new BigDecimal("0");
        if (repurchaseNumYoYUseRate.compareTo(BigDecimal.ZERO) != 0) {
            repurchaseNumYoYUseRateRate = repurchaseNumUseRate.subtract(repurchaseNumYoYUseRate).divide(repurchaseNumYoYUseRate, 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }

        MarketReportNumDataRespDTO numDataRespDTO = new MarketReportNumDataRespDTO();
        numDataRespDTO.setRepurchaseNumUseRate(repurchaseNumUseRate.setScale(2, RoundingMode.HALF_UP));
        numDataRespDTO.setRepurchaseNumNotUseRate(repurchaseNumNotUseRate.setScale(2, RoundingMode.HALF_UP));
        numDataRespDTO.setRepurchaseNumMoMUseRateRate(repurchaseNumMoMUseRateRate.setScale(2, RoundingMode.HALF_UP));
        numDataRespDTO.setRepurchaseNumYoYUseRateRate(repurchaseNumYoYUseRateRate.setScale(2, RoundingMode.HALF_UP));
        return numDataRespDTO;
    }

    @Override
    public PageDTO<MarketReportSaleDataPageRespDTO> querySalePage(MarketReportPageReqDTO marketReportPageReqDTO) {
        MarketMemberPageReqDTO marketMemberPageReqDTO = buildParamDTOPage(marketReportPageReqDTO);
        ResponseBase<PageDTO<PageRespDTO>> pageRespDTOResponseBase = marketMemberReportClient.queryInfoPage(marketMemberPageReqDTO);
        if (!pageRespDTOResponseBase.checkSuccess()) {
            throw WarnException.builder().code(pageRespDTOResponseBase.getCode()).
                    tipMessage(pageRespDTOResponseBase.getMsg()).build();
        }
        PageDTO<PageRespDTO> pageRespDTO = pageRespDTOResponseBase.getData();
        if (Objects.isNull(pageRespDTO) || CollectionUtils.isEmpty(pageRespDTO.getData())) {
            return null;
        }
        List<PageRespDTO> pageRespDTOList = pageRespDTO.getData();

        //查询门店名称
        List<String> stCodeList = pageRespDTOList.stream().map(PageRespDTO::getBusinessId).collect(Collectors.toList());
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setMerCode(String.valueOf(marketReportPageReqDTO.getMerCode()));
        queryStoreDTO.setStCodeList(stCodeList);
        queryStoreDTO.setPageSize(marketMemberPageReqDTO.getPageSize());
        ResponseBase<PageDTO<StoreResDTO>> responseBase = baseInfoClient.queryStoreByCondition(queryStoreDTO);
        if (!responseBase.checkSuccess()) {
            throw WarnException.builder().code(responseBase.getCode()).
                    tipMessage(responseBase.getMsg()).build();
        }
        PageDTO<StoreResDTO> pageDTO = responseBase.getData();
        Map<String, StoreResDTO> storeResDTOMap = new HashMap<>();
        if (pageDTO != null && !CollectionUtils.isEmpty(pageDTO.getData())) {
            List<StoreResDTO> storeResDTOList = pageDTO.getData();
            storeResDTOMap.putAll(storeResDTOList.stream().collect(Collectors.toMap(a -> a.getStCode(), Function.identity(), (k1, k2) -> k1)));
        }

        List<MarketReportSaleDataPageRespDTO> resultList = Lists.newArrayList();
        pageRespDTOList.forEach(item -> {
            String business = item.getBusinessId();
            List<PageRespDTO.RespDTO> respDTOList = item.getRespDTOList();
            //会员合计
            splitList(business, respDTOList);
            Map<String, PageRespDTO.RespDTO> respDTOMap = respDTOList.stream().collect(Collectors.toMap(a -> a.getMemberOrderNum() + "&" + a.getHxOrderNum(), Function.identity(), (k1, k2) -> k1));
            List<AllDataPageDTO> allDataPageDTOList = initDefaultData(business);
            allDataPageDTOList.forEach(allDataPageDTO -> {
                MarketReportSaleDataPageRespDTO saleDataPageRespDTO = new MarketReportSaleDataPageRespDTO();
                StoreResDTO storeResDTO = storeResDTOMap.get(allDataPageDTO.getBusinessId());
                if (storeResDTO != null) {
                    saleDataPageRespDTO.setBusinessId(storeResDTO.getStName());
                } else {
                    saleDataPageRespDTO.setBusinessId(allDataPageDTO.getBusinessId());
                }
                saleDataPageRespDTO.setMemberOrderNum(allDataPageDTO.getMemberOrderNum());
                saleDataPageRespDTO.setHxOrderNum(allDataPageDTO.getHxOrderNum());
                PageRespDTO.RespDTO dto = respDTOMap.get(allDataPageDTO.getMemberOrderNum() + "&" + allDataPageDTO.getHxOrderNum());
                if (dto != null) {
                    RespDTO tar = new RespDTO();
                    BeanUtils.copyProperties(dto, tar);
                    AllDataDTO allDataDTO = buildData(tar);
                    saleDataPageRespDTO.setSaleAmount(allDataDTO.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setProfit(allDataDTO.getProfit().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setProfitRate(allDataDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setSaleCut(allDataDTO.getSaleCut().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setSaleCutRate(allDataDTO.getSaleCutRate().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setPerCusTra(allDataDTO.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setProUnitPrice(allDataDTO.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setPerCusNum(allDataDTO.getPerCusNum().setScale(2, RoundingMode.HALF_UP));
                } else {
                    saleDataPageRespDTO.setSaleAmount(allDataPageDTO.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setProfit(allDataPageDTO.getProfit().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setProfitRate(allDataPageDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setSaleCut(allDataPageDTO.getSaleCut().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setSaleCutRate(allDataPageDTO.getSaleCutRate().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setPerCusTra(allDataPageDTO.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setProUnitPrice(allDataPageDTO.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
                    saleDataPageRespDTO.setPerCusNum(allDataPageDTO.getPerCusNum().setScale(2, RoundingMode.HALF_UP));
                }
                resultList.add(saleDataPageRespDTO);
            });
        });

        PageDTO<MarketReportSaleDataPageRespDTO> result = new PageDTO<>();
        result.setCurrentPage(pageRespDTO.getCurrentPage());
        result.setPageSize(pageRespDTO.getPageSize());
        result.setTotalPage(pageRespDTO.getTotalPage());
        result.setTotalCount(pageRespDTO.getTotalCount());
        result.setData(resultList);

        return result;
    }

    @Override
    public PageDTO<MarketReportCompareDataPageRespDTO> queryComparePage(MarketReportPageReqDTO marketReportPageReqDTO) {
        List<MarketReportCompareDataPageRespDTO> resultList = Lists.newArrayList();
        List<PageRespDTO> data = Lists.newArrayList();
        PageDTO<PageRespDTO> pageRespDTO = new PageDTO<>();

        String startTime = marketReportPageReqDTO.getStartTime();
        String endTime = marketReportPageReqDTO.getEndTime();

        MarketMemberPageReqDTO marketMemberPageReqDTO = buildParamDTOPage(marketReportPageReqDTO);
        log.info("queryComparePage reqDTO === {}", marketMemberPageReqDTO);
        ResponseBase<PageDTO<PageRespDTO>> pageRespDTOResponseBase = marketMemberReportClient.queryInfoPage(marketMemberPageReqDTO);
        if (!pageRespDTOResponseBase.checkSuccess()) {
            throw WarnException.builder().code(pageRespDTOResponseBase.getCode()).
                    tipMessage(pageRespDTOResponseBase.getMsg()).build();
        }


        //环比
        MarketMemberReqDTO reqDTO = new MarketMemberReqDTO();
        setTimeMoM(reqDTO, startTime, endTime, marketReportPageReqDTO.getTimeType());
        marketMemberPageReqDTO.setStartTime(reqDTO.getStartTime());
        marketMemberPageReqDTO.setEndTime(reqDTO.getEndTime());
        log.info("mom queryComparePage reqDTO === {}", marketMemberPageReqDTO);
        ResponseBase<PageDTO<PageRespDTO>> mom = marketMemberReportClient.queryInfoPage(marketMemberPageReqDTO);
        if (!mom.checkSuccess()) {
            throw WarnException.builder().code(mom.getCode()).
                    tipMessage(mom.getMsg()).build();
        }

        //同比
        setTimeYoY(reqDTO, startTime, endTime);
        marketMemberPageReqDTO.setStartTime(reqDTO.getStartTime());
        marketMemberPageReqDTO.setEndTime(reqDTO.getEndTime());
        log.info("yoy queryComparePage reqDTO === {}", marketMemberPageReqDTO);
        ResponseBase<PageDTO<PageRespDTO>> yoy = marketMemberReportClient.queryInfoPage(marketMemberPageReqDTO);
        if (!yoy.checkSuccess()) {
            throw WarnException.builder().code(yoy.getCode()).
                    tipMessage(yoy.getMsg()).build();
        }

        Map<String, PageRespDTO> map = new HashMap<>();
        if (pageRespDTOResponseBase.getData() != null && !CollectionUtils.isEmpty(pageRespDTOResponseBase.getData().getData())) {
            List<PageRespDTO> pageRespDTOList = pageRespDTOResponseBase.getData().getData();
            map.putAll(pageRespDTOList.stream().collect(Collectors.toMap(item -> item.getBusinessId(), Function.identity(), (k1, k2) -> k1)));
            data = pageRespDTOResponseBase.getData().getData();
            pageRespDTO = pageRespDTOResponseBase.getData();
        }

        Map<String, PageRespDTO> mapMoM = new HashMap<>();
        if (mom.getData() != null && !CollectionUtils.isEmpty(mom.getData().getData())) {
            List<PageRespDTO> pageRespDTOListMoM = mom.getData().getData();
            mapMoM.putAll(pageRespDTOListMoM.stream().collect(Collectors.toMap(item -> item.getBusinessId(), Function.identity(), (k1, k2) -> k1)));
        }

        Map<String, PageRespDTO> mapYoY = new HashMap<>();
        if (yoy.getData() != null && !CollectionUtils.isEmpty(yoy.getData().getData())) {
            List<PageRespDTO> pageRespDTOListYoY = yoy.getData().getData();
            mapYoY.putAll(pageRespDTOListYoY.stream().collect(Collectors.toMap(item -> item.getBusinessId(), Function.identity(), (k1, k2) -> k1)));
        }


        if (!CollectionUtils.isEmpty(data)) {
            //查询门店名称
            List<String> stCodeList = data.stream().map(PageRespDTO::getBusinessId).collect(Collectors.toList());
            QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
            queryStoreDTO.setMerCode(String.valueOf(marketReportPageReqDTO.getMerCode()));
            queryStoreDTO.setStCodeList(stCodeList);
            queryStoreDTO.setPageSize(marketMemberPageReqDTO.getPageSize());
            ResponseBase<PageDTO<StoreResDTO>> responseBase = baseInfoClient.queryStoreByCondition(queryStoreDTO);
            if (!responseBase.checkSuccess()) {
                throw WarnException.builder().code(responseBase.getCode()).
                        tipMessage(responseBase.getMsg()).build();
            }
            PageDTO<StoreResDTO> pageDTO = responseBase.getData();
            Map<String, StoreResDTO> storeResDTOMap = new HashMap<>();
            if (pageDTO != null && !CollectionUtils.isEmpty(pageDTO.getData())) {
                List<StoreResDTO> storeResDTOList = pageDTO.getData();
                storeResDTOMap.putAll(storeResDTOList.stream().collect(Collectors.toMap(a -> a.getStCode(), Function.identity(), (k1, k2) -> k1)));
            }
            data.forEach(item -> {
                String business = item.getBusinessId();

                Map<String, PageRespDTO.RespDTO> respDTOMap = new HashMap<>();
                if (Objects.nonNull(map.get(business))) {
                    List<PageRespDTO.RespDTO> respDTOList = map.get(business).getRespDTOList();
                    splitList(business, respDTOList);
                    respDTOMap.putAll(respDTOList.stream().collect(Collectors.toMap(a -> a.getMemberOrderNum() + "&" + a.getHxOrderNum(), Function.identity(), (k1, k2) -> k1)));
                }
                Map<String, PageRespDTO.RespDTO> respDTOMapMoM = new HashMap<>();
                if (Objects.nonNull(mapMoM.get(business))) {
                    List<PageRespDTO.RespDTO> respDTOListMoM = mapMoM.get(business).getRespDTOList();
                    splitList(business, respDTOListMoM);
                    respDTOMapMoM.putAll(respDTOListMoM.stream().collect(Collectors.toMap(a -> a.getMemberOrderNum() + "&" + a.getHxOrderNum(), Function.identity(), (k1, k2) -> k1)));
                }
                Map<String, PageRespDTO.RespDTO> respDTOMapYoY = new HashMap<>();
                if (Objects.nonNull(mapYoY.get(business))) {
                    List<PageRespDTO.RespDTO> respDTOListYoY = mapYoY.get(business).getRespDTOList();
                    splitList(business, respDTOListYoY);
                    respDTOMapYoY.putAll(respDTOListYoY.stream().collect(Collectors.toMap(a -> a.getMemberOrderNum() + "&" + a.getHxOrderNum(), Function.identity(), (k1, k2) -> k1)));
                }


                List<AllDataPageDTO> allDataPageDTOList = initDefaultData(business);
                allDataPageDTOList.forEach(allDataPageDTO -> {
                    MarketReportCompareDataPageRespDTO compareDataPageRespDTO = new MarketReportCompareDataPageRespDTO();
                    StoreResDTO storeResDTO = storeResDTOMap.get(allDataPageDTO.getBusinessId());
                    if (storeResDTO != null) {
                        compareDataPageRespDTO.setBusinessId(storeResDTO.getStName());
                    } else {
                        compareDataPageRespDTO.setBusinessId(allDataPageDTO.getBusinessId());
                    }
                    compareDataPageRespDTO.setMemberOrderNum(allDataPageDTO.getMemberOrderNum());
                    compareDataPageRespDTO.setHxOrderNum(allDataPageDTO.getHxOrderNum());
                    PageRespDTO.RespDTO dto = respDTOMap.get(allDataPageDTO.getMemberOrderNum() + "&" + allDataPageDTO.getHxOrderNum());
                    AllDataDTO common = init();
                    if (dto != null) {
                        RespDTO tar = new RespDTO();
                        BeanUtils.copyProperties(dto, tar);
                        common = buildData(tar);
                        compareDataPageRespDTO.setSaleAmount(common.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfit(common.getProfit().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitRate(common.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusTra(common.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProUnitPrice(common.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusNum(common.getPerCusNum().setScale(2, RoundingMode.HALF_UP));
                    } else {
                        compareDataPageRespDTO.setSaleAmount(allDataPageDTO.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfit(allDataPageDTO.getProfit().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitRate(allDataPageDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusTra(allDataPageDTO.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProUnitPrice(allDataPageDTO.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusNum(allDataPageDTO.getPerCusNum().setScale(2, RoundingMode.HALF_UP));
                    }

                    PageRespDTO.RespDTO dtoMoM = respDTOMapMoM.get(allDataPageDTO.getMemberOrderNum() + "&" + allDataPageDTO.getHxOrderNum());
                    if (dtoMoM != null) {
                        RespDTO tar = new RespDTO();
                        BeanUtils.copyProperties(dtoMoM, tar);
                        AllDataDTO allDataDTO = buildData(tar);
                        AllRateDataDTO allRateDataDTO = rate(allDataDTO, common);
                        compareDataPageRespDTO.setSaleAmountMoMRate(allRateDataDTO.getSaleAmountRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitMoMRate(allRateDataDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitMoMRateRate(allRateDataDTO.getProfitRateRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusTraMoMRate(allRateDataDTO.getPerCusTraRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProUnitPriceMoMRate(allRateDataDTO.getProUnitPriceRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusNumMoMRate(allRateDataDTO.getPerCusNumRate().setScale(2, RoundingMode.HALF_UP));
                    } else {
                        compareDataPageRespDTO.setSaleAmountMoMRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitMoMRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitMoMRateRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusTraMoMRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProUnitPriceMoMRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusNumMoMRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));

                    }

                    PageRespDTO.RespDTO dtoYoY = respDTOMapYoY.get(allDataPageDTO.getMemberOrderNum() + "&" + allDataPageDTO.getHxOrderNum());
                    if (dtoYoY != null) {
                        RespDTO tar = new RespDTO();
                        BeanUtils.copyProperties(dtoYoY, tar);
                        AllDataDTO allDataDTO = buildData(tar);
                        AllRateDataDTO allRateDataDTO = rate(allDataDTO, common);
                        compareDataPageRespDTO.setSaleAmountYoYRate(allRateDataDTO.getSaleAmountRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitYoYRate(allRateDataDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitYoYRateRate(allRateDataDTO.getProfitRateRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusTraYoYRate(allRateDataDTO.getPerCusTraRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProUnitPriceYoYRate(allRateDataDTO.getProUnitPriceRate().setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusNumYoYRate(allRateDataDTO.getPerCusNumRate().setScale(2, RoundingMode.HALF_UP));
                    } else {
                        compareDataPageRespDTO.setSaleAmountYoYRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitYoYRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProfitYoYRateRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusTraYoYRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setProUnitPriceYoYRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                        compareDataPageRespDTO.setPerCusNumYoYRate(new BigDecimal("0").setScale(2, RoundingMode.HALF_UP));
                    }

                    resultList.add(compareDataPageRespDTO);
                });
            });
            PageDTO<MarketReportCompareDataPageRespDTO> result = new PageDTO<>();
            result.setCurrentPage(pageRespDTO.getCurrentPage());
            result.setPageSize(pageRespDTO.getPageSize());
            result.setTotalPage(pageRespDTO.getTotalPage());
            result.setTotalCount(pageRespDTO.getTotalCount());
            result.setData(resultList);
            return result;
        } else {
            return null;
        }
    }

    @Override
    public PageDTO<MarketReportRepurchaseDataPageRespDTO> queryRepurchasePage(MarketReportPageReqDTO marketReportPageReqDTO) {
        MarketMemberPageReqDTO marketMemberPageReqDTO = buildParamDTOPage(marketReportPageReqDTO);
        ResponseBase<PageDTO<PageRespDTO>> repurchaseInfoPage = marketMemberReportClient.queryRepurchaseInfoPage(marketMemberPageReqDTO);
        if (!repurchaseInfoPage.checkSuccess()) {
            throw WarnException.builder().code(repurchaseInfoPage.getCode()).
                    tipMessage(repurchaseInfoPage.getMsg()).build();
        }
        PageDTO<PageRespDTO> respDTOPageDTO = repurchaseInfoPage.getData();
        if (Objects.isNull(respDTOPageDTO) || CollectionUtils.isEmpty(respDTOPageDTO.getData())) {
            return null;
        }
        List<PageRespDTO> pageRespDTOList = respDTOPageDTO.getData();

        //查询门店名称
        List<String> stCodeList = pageRespDTOList.stream().map(PageRespDTO::getBusinessId).collect(Collectors.toList());
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setMerCode(String.valueOf(marketReportPageReqDTO.getMerCode()));
        queryStoreDTO.setStCodeList(stCodeList);
        queryStoreDTO.setPageSize(marketMemberPageReqDTO.getPageSize());
        ResponseBase<PageDTO<StoreResDTO>> responseBase = baseInfoClient.queryStoreByCondition(queryStoreDTO);
        if (!responseBase.checkSuccess()) {
            throw WarnException.builder().code(responseBase.getCode()).
                    tipMessage(responseBase.getMsg()).build();
        }
        PageDTO<StoreResDTO> pageDTO = responseBase.getData();
        Map<String, StoreResDTO> storeResDTOMap = new HashMap<>();
        if (pageDTO != null && !CollectionUtils.isEmpty(pageDTO.getData())) {
            List<StoreResDTO> storeResDTOList = pageDTO.getData();
            storeResDTOMap.putAll(storeResDTOList.stream().collect(Collectors.toMap(a -> a.getStCode(), Function.identity(), (k1, k2) -> k1)));
        }

        List<MarketReportRepurchaseDataPageRespDTO> resultList = Lists.newArrayList();
        pageRespDTOList.forEach(item -> {
            String business = item.getBusinessId();
            List<PageRespDTO.RespDTO> respDTOList = item.getRespDTOList();

            List<AllDataPageDTO> allDataPageDTOList = Lists.newArrayList();
            AllDataPageDTO allDataPageDTO = new AllDataPageDTO();
            allDataPageDTO.setBusinessId(business);
            allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.USE.getCode());
            initBuildDate(allDataPageDTO);
            allDataPageDTOList.add(allDataPageDTO);
            allDataPageDTO = new AllDataPageDTO();
            allDataPageDTO.setBusinessId(business);
            allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.NOT_USE.getCode());
            initBuildDate(allDataPageDTO);
            allDataPageDTOList.add(allDataPageDTO);
            allDataPageDTO = new AllDataPageDTO();
            allDataPageDTO.setBusinessId(business);
            allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.ALL.getCode());
            initBuildDate(allDataPageDTO);
            allDataPageDTOList.add(allDataPageDTO);

            allDataPageDTOList.forEach(allDataPageDTO1 -> {
                MarketReportRepurchaseDataPageRespDTO reportRepurchaseDataPageRespDTO = new MarketReportRepurchaseDataPageRespDTO();
                Map<Integer, PageRespDTO.RespDTO> respDTOMap = respDTOList.stream().collect(Collectors.toMap(a -> a.getHxOrderNum(), Function.identity(), (k1, k2) -> k1));
                PageRespDTO.RespDTO dto = respDTOMap.get(allDataPageDTO1.getHxOrderNum());
                StoreResDTO storeResDTO = storeResDTOMap.get(allDataPageDTO1.getBusinessId());
                if (storeResDTO != null) {
                    reportRepurchaseDataPageRespDTO.setBusinessId(storeResDTO.getStName());
                } else {
                    reportRepurchaseDataPageRespDTO.setBusinessId(allDataPageDTO1.getBusinessId());
                }
                reportRepurchaseDataPageRespDTO.setHxOrderNum(allDataPageDTO1.getHxOrderNum());
                if (dto != null) {
                    RespDTO tar = new RespDTO();
                    BeanUtils.copyProperties(dto, tar);
                    AllDataDTO allDataDTO = buildData(tar);

                    reportRepurchaseDataPageRespDTO.setRepurchaseNumTotal(tar.getNum());
                    reportRepurchaseDataPageRespDTO.setOrdersRepTotal(allDataDTO.getOrders());
                    reportRepurchaseDataPageRespDTO.setSaleAmountRepTotal(allDataDTO.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setProfitRepTotal(allDataDTO.getProfit().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setProfitRepTotalRate(allDataDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setPerCusTraTotalRep(allDataDTO.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setProUnitPriceRepTotal(allDataDTO.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setPerCusNumRepTotal(allDataDTO.getPerCusNum().setScale(2, RoundingMode.HALF_UP));

                } else {
                    reportRepurchaseDataPageRespDTO.setRepurchaseNumTotal(allDataPageDTO1.getRepurchaseNumTotal());
                    reportRepurchaseDataPageRespDTO.setOrdersRepTotal(allDataPageDTO1.getOrdersRepTotal());
                    reportRepurchaseDataPageRespDTO.setSaleAmountRepTotal(allDataPageDTO1.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setProfitRepTotal(allDataPageDTO1.getProfit().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setProfitRepTotalRate(allDataPageDTO1.getProfitRate().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setPerCusTraTotalRep(allDataPageDTO1.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setProUnitPriceRepTotal(allDataPageDTO1.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
                    reportRepurchaseDataPageRespDTO.setPerCusNumRepTotal(allDataPageDTO1.getPerCusNum().setScale(2, RoundingMode.HALF_UP));
                }
                resultList.add(reportRepurchaseDataPageRespDTO);
            });

        });

        PageDTO<MarketReportRepurchaseDataPageRespDTO> result = new PageDTO<>();
        result.setCurrentPage(respDTOPageDTO.getCurrentPage());
        result.setPageSize(respDTOPageDTO.getPageSize());
        result.setTotalPage(respDTOPageDTO.getTotalPage());
        result.setTotalCount(respDTOPageDTO.getTotalCount());
        result.setData(resultList);
        return result;
    }

    @Override
    public PageDTO<MarketReportSaleStatisticPageRespDTO> getCouponSaleStatisticDataPage(
            MarketReportSaleStatisticPageReqDTO reqDTO) {
        ExLogger.logger().field("getCouponSaleStatisticDataPage").field(reqDTO.getMerCode()).field(reqDTO.getUserName()).info("param: {}", reqDTO);
        reqDTO.setBusinessIdList(empAuthHandler.getMyStoreList(reqDTO.getBusinessIdList(), reqDTO.getUserName(), reqDTO.getMerCode()));

        if (CollectionUtils.isEmpty(reqDTO.getCouponTypeList())) {
            reqDTO.setCouponTypeList(Arrays.asList(CouponTypeEnum.ZK.getCode().toString(), CouponTypeEnum.MJ.getCode().toString(), CouponTypeEnum.CASH.getCode().toString()));
        }

        ResponseBase<PageDTO<MarketReportSaleStatisticPageRespDTO>> pageResponseBase = marketMemberReportClient.getCouponSaleStatisticDataPage(reqDTO);
        if (!pageResponseBase.checkSuccess()) {
            // TODO: 2024/4/11 上线临时解决
//            throw WarnException.builder().code(pageResponseBase.getCode()).
//                    tipMessage(pageResponseBase.getMsg()).build();
            return new PageDTO<>();
        }
        PageDTO<MarketReportSaleStatisticPageRespDTO> respDTOPageDTO = pageResponseBase.getData();
        if (Objects.isNull(respDTOPageDTO) || CollectionUtils.isEmpty(respDTOPageDTO.getData())) {
            return null;
        }

        List<MarketReportSaleStatisticPageRespDTO> statisticList = respDTOPageDTO.getData();

        //查询门店名称
        List<String> stCodeList = statisticList.stream().filter(e -> !StringUtils.isEmpty(e.getBusinessId())).map(MarketReportSaleStatisticPageRespDTO::getBusinessId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stCodeList)) {
            QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
            queryStoreDTO.setMerCode(String.valueOf(reqDTO.getMerCode()));
            queryStoreDTO.setStCodeList(stCodeList);
            queryStoreDTO.setPageSize(stCodeList.size());
            ResponseBase<PageDTO<StoreResDTO>> responseBase = baseInfoClient.queryStoreByCondition(queryStoreDTO);
            if (!responseBase.checkSuccess()) {
                throw WarnException.builder().code(responseBase.getCode()).
                        tipMessage(responseBase.getMsg()).build();
            }
            if(responseBase.getData() != null && !CollectionUtils.isEmpty(responseBase.getData().getData())){
                List<StoreResDTO> storeList = responseBase.getData().getData();
                Map<String, String> storeNameMap = storeList.stream().collect(Collectors.toMap(StoreResDTO::getStCode, StoreResDTO::getStName, (e1, e2) -> e1));
                statisticList.forEach(e -> e.setStoreName(storeNameMap.get(e.getBusinessId())));
            }

        }
        ExLogger.logger().field("getCouponSaleStatisticDataPage").info("end");

        return respDTOPageDTO;
    }

    public MarketReportBasicDataRespDTO getBasicData(List<RespDTO> respDTOList, List<RespDTO> respDTOListMoM, List<RespDTO> respDTOListYoY) {
        //本期
        RespDTO respDTOByUse = respDTOList.stream().filter(item -> item.getHxOrderNum().equals(MarketReportHxOrderNum.USE.getCode())).collect(Collectors.toList()).get(0);
        RespDTO respDTOByNotUse = respDTOList.stream().filter(item -> item.getHxOrderNum().equals(MarketReportHxOrderNum.NOT_USE.getCode())).collect(Collectors.toList()).get(0);


        //环比
        RespDTO respDTOMoMUse = respDTOListMoM.stream().filter(item -> item.getHxOrderNum().equals(MarketReportHxOrderNum.USE.getCode())).collect(Collectors.toList()).get(0);


        //同比
        RespDTO respDTOYoYUse = respDTOListYoY.stream().filter(item -> item.getHxOrderNum().equals(MarketReportHxOrderNum.USE.getCode())).collect(Collectors.toList()).get(0);


        MarketReportBasicDataRespDTO basicDataRespDTO = new MarketReportBasicDataRespDTO();
        AllDataDTO commonUse = buildData(respDTOByUse);
        basicDataRespDTO.setPaymentExtUse(commonUse.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProfitUse(commonUse.getProfit().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProfitUseRate(commonUse.getProfitRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusTraUse(commonUse.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProUnitPriceUse(commonUse.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusNumUse(commonUse.getPerCusNum().setScale(2, RoundingMode.HALF_UP));

        AllDataDTO commonNotUse = buildData(respDTOByNotUse);
        basicDataRespDTO.setProfitNotUseRate(commonNotUse.getProfitRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusTraNotUse(commonNotUse.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProUnitPriceNotUse(commonNotUse.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusNumNotUse(commonNotUse.getPerCusNum().setScale(2, RoundingMode.HALF_UP));

        AllDataDTO allDataDTOMoM = buildData(respDTOMoMUse);
        AllDataDTO allDataDTOYoY = buildData(respDTOYoYUse);
        AllRateDataDTO rateMoM = rate(allDataDTOMoM, commonUse);
        AllRateDataDTO rateYoY = rate(allDataDTOYoY, commonUse);
        basicDataRespDTO.setPaymentExtMoMUseRate(rateMoM.getPaymentExtRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPaymentExtYoYUseRate(rateYoY.getPaymentExtRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProfitMoMUseRate(rateMoM.getProfitRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProfitYoYUseRate(rateYoY.getProfitRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProfitUseMoMRateRate(rateMoM.getProfitRateRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProfitUseYoYRateRate(rateYoY.getProfitRateRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusTraMoMUseRate(rateMoM.getPerCusTraRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusTraYoYUseRate(rateYoY.getPerCusTraRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProUnitPriceMoMUseRate(rateMoM.getProUnitPriceRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setProUnitPriceYoYUseRate(rateYoY.getProUnitPriceRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusNumMoMUseRate(rateMoM.getPerCusNumRate().setScale(2, RoundingMode.HALF_UP));
        basicDataRespDTO.setPerCusNumYoYUseRate(rateYoY.getPerCusNumRate().setScale(2, RoundingMode.HALF_UP));

        RespDTO respDTO = respDTOList.stream().filter(item -> item.getHxOrderNum().equals(MarketReportHxOrderNum.ALL.getCode())).collect(Collectors.toList()).get(0);
        //本期总销售额占比
        BigDecimal paymentExtPer = new BigDecimal("0");
        if (respDTO.getTotalSaleAmount().compareTo(BigDecimal.ZERO) != 0) {
            paymentExtPer = basicDataRespDTO.getPaymentExtUse().divide(respDTO.getTotalSaleAmount(), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        basicDataRespDTO.setPaymentExtPer(paymentExtPer.setScale(2, RoundingMode.HALF_UP));

        //本期总毛利额占比
        BigDecimal profitUsePer = new BigDecimal("0");
        if (respDTO.getTotalProfit().compareTo(BigDecimal.ZERO) != 0) {
            profitUsePer = basicDataRespDTO.getProfitUse().divide(respDTO.getTotalProfit(), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        basicDataRespDTO.setProfitUsePer(profitUsePer.setScale(2, RoundingMode.HALF_UP));

        return basicDataRespDTO;
    }

    public List<MarketReportSaleDataRespDTO> getSaleData(List<RespDTO> respDTOList) {
        List<MarketReportSaleDataRespDTO> resultList = Lists.newArrayList();
        respDTOList.forEach(item -> {
            MarketReportSaleDataRespDTO saleDataRespDTO = new MarketReportSaleDataRespDTO();
            AllDataDTO allDataDTO = buildData(item);
            saleDataRespDTO.setHxOrderNum(item.getHxOrderNum());
            saleDataRespDTO.setSaleAmount(allDataDTO.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
            saleDataRespDTO.setProfit(allDataDTO.getProfit().setScale(2, RoundingMode.HALF_UP));
            saleDataRespDTO.setProfitRate(allDataDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
            saleDataRespDTO.setSaleCut(allDataDTO.getSaleCut().setScale(2, RoundingMode.HALF_UP));
            saleDataRespDTO.setSaleCutRate(allDataDTO.getSaleCutRate().setScale(2, RoundingMode.HALF_UP));
            saleDataRespDTO.setPerCusTra(allDataDTO.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
            saleDataRespDTO.setProUnitPrice(allDataDTO.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
            saleDataRespDTO.setPerCusNum(allDataDTO.getPerCusNum().setScale(2, RoundingMode.HALF_UP));
            resultList.add(saleDataRespDTO);
        });
        return resultList;
    }

    public List<MarketReportCompareDataRespDTO> getCompareData(List<RespDTO> respDTOList, List<RespDTO> respDTOListMoM, List<RespDTO> respDTOListYoY) {
        List<MarketReportCompareDataRespDTO> resultList = Lists.newArrayList();

        //本期

        //环比
        Map<Integer, RespDTO> mapMoM = respDTOListMoM.stream().collect(Collectors.toMap(RespDTO::getHxOrderNum, Function.identity(), (k1, k2) -> k1));
        //同比
        Map<Integer, RespDTO> mapYoY = respDTOListYoY.stream().collect(Collectors.toMap(RespDTO::getHxOrderNum, Function.identity(), (k1, k2) -> k1));

        respDTOList.forEach(item -> {
            MarketReportCompareDataRespDTO compareDataRespDTO = new MarketReportCompareDataRespDTO();
            AllDataDTO allDataDTO = buildData(item);
            compareDataRespDTO.setSaleAmount(allDataDTO.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProfit(allDataDTO.getProfit().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProfitRate(allDataDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setPerCusTra(allDataDTO.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProUnitPrice(allDataDTO.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setPerCusNum(allDataDTO.getPerCusNum().setScale(2, RoundingMode.HALF_UP));

            RespDTO itemMoM = mapMoM.get(item.getHxOrderNum());
            RespDTO itemYoY = mapYoY.get(item.getHxOrderNum());
            AllDataDTO allDataDTOMoM = buildData(itemMoM);
            AllDataDTO allDataDTOYoY = buildData(itemYoY);
            AllRateDataDTO rateMoM = rate(allDataDTOMoM, allDataDTO);
            AllRateDataDTO rateYoY = rate(allDataDTOYoY, allDataDTO);
            compareDataRespDTO.setSaleAmountMoMRate(rateMoM.getSaleAmountRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setSaleAmountYoYRate(rateYoY.getSaleAmountRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProfitMoMRate(rateMoM.getProfitRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProfitYoYRate(rateYoY.getProfitRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProfitMoMRateRate(rateMoM.getProfitRateRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProfitYoYRateRate(rateYoY.getProfitRateRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setPerCusTraMoMRate(rateMoM.getPerCusTraRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setPerCusTraYoYRate(rateYoY.getPerCusTraRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProUnitPriceMoMRate(rateMoM.getProUnitPriceRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setProUnitPriceYoYRate(rateYoY.getProUnitPriceRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setPerCusNumMoMRate(rateMoM.getPerCusNumRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setPerCusNumYoYRate(rateYoY.getPerCusNumRate().setScale(2, RoundingMode.HALF_UP));
            compareDataRespDTO.setHxOrderNum(item.getHxOrderNum());
            resultList.add(compareDataRespDTO);
        });
        return resultList;
    }

    public List<MarketReportRepurchaseDataRespDTO> getRepurchase(List<RespDTO> repurchaseRespDTOList) {
        List<MarketReportRepurchaseDataRespDTO> resultList = Lists.newArrayList();

        repurchaseRespDTOList.forEach(item -> {
            MarketReportRepurchaseDataRespDTO repurchaseDataRespDTO = new MarketReportRepurchaseDataRespDTO();
            AllDataDTO allDataDTO = buildData(item);
            repurchaseDataRespDTO.setOrdersRepTotal(allDataDTO.getOrders());
            repurchaseDataRespDTO.setSaleAmountRepTotal(allDataDTO.getSaleAmount().setScale(2, RoundingMode.HALF_UP));
            repurchaseDataRespDTO.setProfitRepTotal(allDataDTO.getProfit().setScale(2, RoundingMode.HALF_UP));
            repurchaseDataRespDTO.setProfitRepTotalRate(allDataDTO.getProfitRate().setScale(2, RoundingMode.HALF_UP));
            repurchaseDataRespDTO.setPerCusTraTotalRep(allDataDTO.getPerCusTra().setScale(2, RoundingMode.HALF_UP));
            repurchaseDataRespDTO.setProUnitPriceRepTotal(allDataDTO.getProUnitPrice().setScale(2, RoundingMode.HALF_UP));
            repurchaseDataRespDTO.setPerCusNumRepTotal(allDataDTO.getPerCusNum().setScale(2, RoundingMode.HALF_UP));
            repurchaseDataRespDTO.setHxOrderNum(item.getHxOrderNum());
            repurchaseDataRespDTO.setRepurchaseNumTotal(item.getNum());
            resultList.add(repurchaseDataRespDTO);
        });
        return resultList;
    }

    @NotNull
    private MarketMemberReqDTO buildParamDTO(MarketReportReqDTO marketReportReqDTO) {
        String startTime = marketReportReqDTO.getStartTime();
        String endTime = marketReportReqDTO.getEndTime();
        Integer merCode = marketReportReqDTO.getMerCode();
        String userName = marketReportReqDTO.getUserName();

        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw WarnException.builder().code("99999").
                    tipMessage("开始时间结束时间不能为空!").build();
        }

        if (Objects.isNull(merCode)) {
            throw WarnException.builder().code("99999").
                    tipMessage("商户编码不能为空!").build();
        }

        MarketMemberReqDTO marketMemberReqDTO = new MarketMemberReqDTO();
        marketMemberReqDTO.setMerCode(merCode);
        marketMemberReqDTO.setStartTime(startTime);
        marketMemberReqDTO.setEndTime(endTime);
        marketMemberReqDTO.setSceneRuleFlag(marketReportReqDTO.getSceneRuleFlag());
//        marketMemberReqDTO.setBusinessIdList(empAuthHandler.getMyStoreList(marketReportReqDTO.getBusinessIdList(), userName, merCode.toString()));
        return marketMemberReqDTO;
    }

    @NotNull
    private MarketMemberPageReqDTO buildParamDTOPage(MarketReportPageReqDTO marketReportPageReqDTO) {
        String startTime = marketReportPageReqDTO.getStartTime();
        String endTime = marketReportPageReqDTO.getEndTime();
        Integer merCode = marketReportPageReqDTO.getMerCode();
        String userName = marketReportPageReqDTO.getUserName();
        int currentPage = marketReportPageReqDTO.getCurrentPage();
        int pageSize = marketReportPageReqDTO.getPageSize();

        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw WarnException.builder().code("99999").
                    tipMessage("开始时间结束时间不能为空!").build();
        }

        if (Objects.isNull(merCode)) {
            throw WarnException.builder().code("99999").
                    tipMessage("商户编码不能为空!").build();
        }

        MarketMemberPageReqDTO marketMemberPageReqDTO = new MarketMemberPageReqDTO();
        marketMemberPageReqDTO.setMerCode(merCode);
        marketMemberPageReqDTO.setStartTime(startTime);
        marketMemberPageReqDTO.setEndTime(endTime);
        marketMemberPageReqDTO.setCurrentPage(currentPage);
        marketMemberPageReqDTO.setPageSize(pageSize);
        marketMemberPageReqDTO.setSceneRuleFlag(marketReportPageReqDTO.getSceneRuleFlag());
        marketMemberPageReqDTO.setBusinessIdList(empAuthHandler.getMyStoreList(marketReportPageReqDTO.getBusinessIdList(), userName, merCode.toString()));
        return marketMemberPageReqDTO;
    }

    private List<AllDataPageDTO> initDefaultData(String businessId) {
        List<AllDataPageDTO> defaultList = Lists.newArrayList();

        AllDataPageDTO allDataPageDTO = new AllDataPageDTO();
        allDataPageDTO.setBusinessId(businessId);
        allDataPageDTO.setMemberOrderNum(MarketReportMemberOrderNum.MEMBER.getCode());
        allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.USE.getCode());
        initBuildDate(allDataPageDTO);
        defaultList.add(allDataPageDTO);

        allDataPageDTO = new AllDataPageDTO();
        allDataPageDTO.setBusinessId(businessId);
        allDataPageDTO.setMemberOrderNum(MarketReportMemberOrderNum.MEMBER.getCode());
        allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.NOT_USE.getCode());
        initBuildDate(allDataPageDTO);
        defaultList.add(allDataPageDTO);

        allDataPageDTO = new AllDataPageDTO();
        allDataPageDTO.setBusinessId(businessId);
        allDataPageDTO.setMemberOrderNum(MarketReportMemberOrderNum.MEMBER.getCode());
        allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.ALL.getCode());
        initBuildDate(allDataPageDTO);
        defaultList.add(allDataPageDTO);

        allDataPageDTO = new AllDataPageDTO();
        allDataPageDTO.setBusinessId(businessId);
        allDataPageDTO.setMemberOrderNum(MarketReportMemberOrderNum.NOT_MEMBER.getCode());
        allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.USE.getCode());
        initBuildDate(allDataPageDTO);
        defaultList.add(allDataPageDTO);

        allDataPageDTO = new AllDataPageDTO();
        allDataPageDTO.setBusinessId(businessId);
        allDataPageDTO.setMemberOrderNum(MarketReportMemberOrderNum.NOT_MEMBER.getCode());
        allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.NOT_USE.getCode());
        initBuildDate(allDataPageDTO);
        defaultList.add(allDataPageDTO);

        allDataPageDTO = new AllDataPageDTO();
        allDataPageDTO.setBusinessId(businessId);
        allDataPageDTO.setMemberOrderNum(MarketReportMemberOrderNum.NOT_MEMBER.getCode());
        allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.ALL.getCode());
        initBuildDate(allDataPageDTO);
        defaultList.add(allDataPageDTO);

        allDataPageDTO = new AllDataPageDTO();
        allDataPageDTO.setBusinessId(businessId);
        allDataPageDTO.setMemberOrderNum(MarketReportMemberOrderNum.ALL.getCode());
        allDataPageDTO.setHxOrderNum(MarketReportHxOrderNum.ALL.getCode());
        initBuildDate(allDataPageDTO);
        defaultList.add(allDataPageDTO);

        return defaultList;
    }

    private void initBuildDate(AllDataPageDTO allDataPageDTO) {
        allDataPageDTO.setPaymentExt(new BigDecimal("0"));
        allDataPageDTO.setSaleAmount(new BigDecimal("0"));
        allDataPageDTO.setProfit(new BigDecimal("0"));
        allDataPageDTO.setProfitRate(new BigDecimal("0"));
        allDataPageDTO.setSaleCut(new BigDecimal("0"));
        allDataPageDTO.setSaleCutRate(new BigDecimal("0"));
        allDataPageDTO.setPerCusTra(new BigDecimal("0"));
        allDataPageDTO.setProUnitPrice(new BigDecimal("0"));
        allDataPageDTO.setPerCusNum(new BigDecimal("0"));
        allDataPageDTO.setRepurchaseNumTotal(0);
        allDataPageDTO.setOrdersRepTotal(0);
    }

    private void splitList(String business, List<PageRespDTO.RespDTO> respDTOList) {
        List<PageRespDTO.RespDTO> dtoList = respDTOList.stream().filter(item -> item.getHxOrderNum().equals(MarketReportHxOrderNum.ALL.getCode())).collect(Collectors.toList());

        //整体合计
        List<RespDTO> respDTOListAll = Lists.newArrayList();
        TBean.copyListProperties(dtoList, respDTOListAll, RespDTO.class);
        RespDTO respDTO = setItemSum(respDTOListAll);
        PageRespDTO.RespDTO all = new PageRespDTO.RespDTO();
        BeanUtils.copyProperties(respDTO, all);
        all.setBusinessId(business);
        all.setMemberOrderNum(MarketReportMemberOrderNum.ALL.getCode());
        all.setHxOrderNum(MarketReportHxOrderNum.ALL.getCode());
        respDTOList.add(all);
    }

    private AllDataDTO buildData(RespDTO item) {
        AllDataDTO allDataDTO = new AllDataDTO();
        //带动销售额
        BigDecimal paymentExt = item.getTotalPaymentExt();
        //销售额
        BigDecimal saleAmount = item.getTotalSaleAmount();
        //毛利额
        BigDecimal profit = item.getTotalProfit();
        //毛利率
        BigDecimal profitRate = new BigDecimal("0");
        if (saleAmount.compareTo(BigDecimal.ZERO) != 0) {
            profitRate = profit.divide(saleAmount, 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        //优惠金额
        BigDecimal saleCut = item.getTotalSaleCut();
        //优惠率
        BigDecimal saleCutRate = new BigDecimal("0");
        if (saleAmount.compareTo(BigDecimal.ZERO) != 0) {
            saleCutRate = saleCut.divide(saleAmount, 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        //订单数
        Integer orders = item.getTotalOrders();
        //客单价
        BigDecimal perCusTra = new BigDecimal("0");
        if (orders != 0) {
            perCusTra = saleAmount.divide(new BigDecimal(String.valueOf(orders)), 6, RoundingMode.HALF_UP);
        }
        //SKU数
        Integer sku = item.getTotalSku();
        //品单价
        BigDecimal proUnitPrice = new BigDecimal("0");
        if (sku != 0) {
            proUnitPrice = saleAmount.divide(new BigDecimal(String.valueOf(sku)), 6, RoundingMode.HALF_UP);
        }
        //客品数
        BigDecimal perCusNum = new BigDecimal("0");
        if (orders != 0) {
            perCusNum = new BigDecimal(String.valueOf(sku)).divide(new BigDecimal(String.valueOf(orders)), 6, RoundingMode.HALF_UP);
        }
        allDataDTO.setPaymentExt(paymentExt);
        allDataDTO.setSaleAmount(saleAmount);
        allDataDTO.setProfit(profit);
        allDataDTO.setProfitRate(profitRate);
        allDataDTO.setSaleCut(saleCut);
        allDataDTO.setSaleCutRate(saleCutRate);
        allDataDTO.setOrders(orders);
        allDataDTO.setPerCusTra(perCusTra);
        allDataDTO.setSku(sku);
        allDataDTO.setProUnitPrice(proUnitPrice);
        allDataDTO.setPerCusNum(perCusNum);
        return allDataDTO;
    }

    private AllDataDTO init() {
        AllDataDTO allDataDTO = new AllDataDTO();
        allDataDTO.setPaymentExt(new BigDecimal("0"));
        allDataDTO.setSaleAmount(new BigDecimal("0"));
        allDataDTO.setProfit(new BigDecimal("0"));
        allDataDTO.setProfitRate(new BigDecimal("0"));
        allDataDTO.setSaleCut(new BigDecimal("0"));
        allDataDTO.setSaleCutRate(new BigDecimal("0"));
        allDataDTO.setOrders(0);
        allDataDTO.setPerCusTra(new BigDecimal("0"));
        allDataDTO.setSku(0);
        allDataDTO.setProUnitPrice(new BigDecimal("0"));
        allDataDTO.setPerCusNum(new BigDecimal("0"));
        return allDataDTO;
    }

    private AllRateDataDTO rate(AllDataDTO allDataDTO, AllDataDTO common) {
        AllRateDataDTO rateDataDTO = new AllRateDataDTO();

        //带动销售额增长率
        BigDecimal paymentExtRate = new BigDecimal("0");
        if (allDataDTO.getPaymentExt().compareTo(BigDecimal.ZERO) != 0) {
            paymentExtRate = common.getPaymentExt().subtract(allDataDTO.getPaymentExt()).divide(allDataDTO.getPaymentExt(), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        rateDataDTO.setPaymentExtRate(paymentExtRate);

        //销售额增长率
        BigDecimal saleAmountRate = new BigDecimal("0");
        if (allDataDTO.getSaleAmount().compareTo(BigDecimal.ZERO) != 0) {
            saleAmountRate = common.getSaleAmount().subtract(allDataDTO.getSaleAmount()).divide(allDataDTO.getSaleAmount(), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        rateDataDTO.setSaleAmountRate(saleAmountRate);

        //毛利额增长率
        BigDecimal profitRate = new BigDecimal("0");
        if (allDataDTO.getProfit().compareTo(BigDecimal.ZERO) != 0) {
            profitRate = common.getProfit().subtract(allDataDTO.getProfit()).divide(allDataDTO.getProfit(), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        rateDataDTO.setProfitRate(profitRate);

        //毛利率增长率
        BigDecimal profitRateRate = new BigDecimal("0");
        if (allDataDTO.getProfitRate().compareTo(BigDecimal.ZERO) != 0) {
            profitRateRate = common.getProfitRate().subtract(allDataDTO.getProfitRate()).divide(allDataDTO.getProfitRate(), 6, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        rateDataDTO.setProfitRateRate(profitRateRate);

        //客单价增长
        BigDecimal perCusTraRate = common.getPerCusTra().subtract(allDataDTO.getPerCusTra()).setScale(6, RoundingMode.HALF_UP);
        rateDataDTO.setPerCusTraRate(perCusTraRate);

        //品单价增长
        BigDecimal proUnitPriceRate = common.getProUnitPrice().subtract(allDataDTO.getProUnitPrice()).setScale(6, RoundingMode.HALF_UP);
        rateDataDTO.setProUnitPriceRate(proUnitPriceRate);

        //客品数增长
        BigDecimal perCusNumRate = common.getPerCusNum().subtract(allDataDTO.getPerCusNum()).setScale(6, RoundingMode.HALF_UP);
        rateDataDTO.setPerCusNumRate(perCusNumRate);
        return rateDataDTO;
    }

    private RespDTO setItemSum(List<RespDTO> respDTOList) {
        RespDTO item = new RespDTO();
        //总带动销售额
        BigDecimal paymentExtTotal = respDTOList.stream().collect(CollectorsUtil.summingBigDecimal(RespDTO::getTotalPaymentExt));
        //总销售额
        BigDecimal saleAmountTotal = respDTOList.stream().collect(CollectorsUtil.summingBigDecimal(RespDTO::getTotalSaleAmount));
        //总毛利额
        BigDecimal profitTotal = respDTOList.stream().collect(CollectorsUtil.summingBigDecimal(RespDTO::getTotalProfit));
        //总优惠金额
        BigDecimal saleCutTotal = respDTOList.stream().collect(CollectorsUtil.summingBigDecimal(RespDTO::getTotalSaleCut));
        //总订单数
        Integer ordersTotal = respDTOList.stream().collect(Collectors.summingInt(RespDTO::getTotalOrders));
        //总SKU数
        Integer skuTotal = respDTOList.stream().collect(Collectors.summingInt(RespDTO::getTotalSku));
        item.setTotalPaymentExt(paymentExtTotal);
        item.setTotalSaleAmount(saleAmountTotal);
        item.setTotalProfit(profitTotal);
        item.setTotalSaleCut(saleCutTotal);
        item.setTotalOrders(ordersTotal);
        item.setTotalSku(skuTotal);
        return item;
    }
}
