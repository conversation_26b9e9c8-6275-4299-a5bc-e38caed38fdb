package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.export.MemberReportConsumptionExportDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.marketreport.MarketReportExportReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.memberconsumption.MemberConsumptionStoreRankingReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.memberconsumptionreport.StoreSaleRankingPageRespDTO;
import cn.hydee.ydjia.merchantmanager.service.MemberConsumptionReportService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.MEMBER_REPORT_CONSUMPTION_EXPORT;

@Slf4j
@Service(MEMBER_REPORT_CONSUMPTION_EXPORT)
public class MemberReportConsumptionExportServiceImpl extends AbstractExportTaskProcessor<MemberReportConsumptionExportDTO> {

    @Autowired
    private MemberConsumptionReportService reportService;

    @Override
    public Class<MemberReportConsumptionExportDTO> getExportClass() {
        return MemberReportConsumptionExportDTO.class;
    }

    @Override
    public List<MemberReportConsumptionExportDTO> getExportDateList(YdjTask task) {
        MarketReportExportReqDTO exportReqDTO = JSONObject.parseObject(task.getCommand(), MarketReportExportReqDTO.class);
        log.info("[MemberReportConsumption_Export] req={}", exportReqDTO);
        int currentPage = 1, pageSize = 1000, currentSize;
        List<MemberReportConsumptionExportDTO> exportList = new ArrayList<>();
        do {
            MemberConsumptionStoreRankingReqDTO reqDTO = BeanUtil.copyProperties(exportReqDTO, MemberConsumptionStoreRankingReqDTO.class);
            reqDTO.setPageSize(pageSize);
            reqDTO.setCurrentPage(currentPage);
            PageDTO<StoreSaleRankingPageRespDTO> respDTO = reportService.getStoreRankingPage(reqDTO);
            if (CollectionUtils.isEmpty(respDTO.getData())) {
                break;
            }
            exportList.addAll(respDTO.getData().stream().
                    map(dto -> BeanUtil.copyProperties(dto, MemberReportConsumptionExportDTO.class))
                    .collect(Collectors.toList()));
            currentSize = respDTO.getData().size();
            currentPage++;
        } while (currentSize >= pageSize);
        return exportList;
    }

    @Override
    public String getFileNamePrefix() {
        return "会员消费报表";
    }
}