package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.req.marketdata.MemberAttendanceStatisticsQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.marketdata.MarketDataAttendanceExportDto;
import cn.hydee.ydjia.merchantmanager.dto.resp.marketdata.MemberAttendanceStatisticsResDTO;
import cn.hydee.ydjia.merchantmanager.feign.MemberAttendanceClient;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.ATTENDANCE_EXPORT;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.EXPORT_COUNT_ERROR;

/**
 * 会员签到数据导出
 */
@Service(ATTENDANCE_EXPORT)
@Slf4j
public class MarketDataAttendanceExportServiceImpl extends AbstractExportTaskProcessor<MarketDataAttendanceExportDto> {

    @Autowired
    private MemberAttendanceClient memberAttendanceClient;
    @Value(("${memberExportLimit}"))
    private Integer memberExportLimit;

    @Override
    public Class<MarketDataAttendanceExportDto> getExportClass() {
        return MarketDataAttendanceExportDto.class;
    }

    @Override
    public List<MarketDataAttendanceExportDto> getExportDateList(YdjTask task) {
        validate(task);

        MemberAttendanceStatisticsQueryDTO reqDTO = JSONObject.parseObject(task.getCommand(), MemberAttendanceStatisticsQueryDTO.class);
        log.info("[ATTENDANCE_EXPORT] req={}", reqDTO);

        int initSize = 10000, currentPage = 1, totalPage = 0;
        List<MarketDataAttendanceExportDto> exportList = null;

        do {
            reqDTO.setCurrentPage(currentPage);
            reqDTO.setPageSize(initSize);

            ResponseBase<PageDTO<MemberAttendanceStatisticsResDTO>> responseBase = memberAttendanceClient.pageAttendanceStatistics(reqDTO);
            if (!responseBase.checkSuccess()) {
                log.error("[ATTENDANCE_EXPORT] query error {} response={} {}", reqDTO, responseBase.getCode(), responseBase.getMsg());
                throw WarnException.builder().code(LocalError.EXCEL_EXPORT_ERROR.getCode())
                        .tipMessage(LocalError.EXCEL_EXPORT_ERROR.getMsg())
                        .build();
            }

            List<MemberAttendanceStatisticsResDTO> queryList;
            if (Objects.nonNull(responseBase.getData())
                    && !CollectionUtils.isEmpty(queryList = responseBase.getData().getData())) {
                if (Objects.isNull(exportList)) {
                    exportList = Lists.newArrayListWithExpectedSize(responseBase.getData().getTotalCount());
                }
                totalPage = responseBase.getData().getTotalPage();
                log.info("[ATTENDANCE_EXPORT] merCode={} total export count is {}", task.getMerCode(), totalPage);

                for (MemberAttendanceStatisticsResDTO attendanceDto : queryList) {
                    exportList.add(transfer(attendanceDto));
                }
            }
            currentPage++;
        } while (currentPage <= totalPage);

        if (CollectionUtils.isEmpty(exportList)) {
            throw WarnException.builder().code(LocalError.EXCEL_ROW_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_ROW_NULL.getMsg()).build();
        }

        return exportList;
    }


    @Override
    public String getFileNamePrefix() {
        return "会员签到";
    }

    /**
     * 数据转化为excel内显示数据格式
     *
     * @param dto 营销数据
     * @return excel内显示数据格式
     */
    private MarketDataAttendanceExportDto transfer(MemberAttendanceStatisticsResDTO dto) {
        return MarketDataAttendanceExportDto.builder()
                .memberName(dto.getMemberInfoResDTO() == null ? "" : dto.getMemberInfoResDTO().getMemberName())
                .memberCard(dto.getMemberInfoResDTO() == null ? "" : dto.getMemberInfoResDTO().getMemberCard())
                .memberPhone(dto.getMemberInfoResDTO() == null ? "" : dto.getMemberInfoResDTO().getMemberPhone())
                .totalCumulativeDays(dto.getTotalCumulativeDays())
                .continuityDays(dto.getContinuityDays())
                .cumulativeIntegral(dto.getCumulativeIntegral())
                .build();
    }

    private void validate(YdjTask task) {
        if (foundExportCountToday(task) >= exportNumLimitToday) {
            throw WarnException.builder().code(EXPORT_COUNT_ERROR.getCode())
                    .message(String.format(EXPORT_COUNT_ERROR.getMsg(), exportNumLimitToday)).build();
        }
    }
}
