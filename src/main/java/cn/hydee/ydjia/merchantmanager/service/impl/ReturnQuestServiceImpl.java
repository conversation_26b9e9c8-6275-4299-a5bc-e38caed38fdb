package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.ReturnQuest;
import cn.hydee.ydjia.merchantmanager.dto.req.AccountLoginDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.RefundReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.RejectReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.UserInfoDTO;
import cn.hydee.ydjia.merchantmanager.enums.ReturnQuestStatus;
import cn.hydee.ydjia.merchantmanager.enums.UserTypes;
import cn.hydee.ydjia.merchantmanager.enums.YesOrNoType;
import cn.hydee.ydjia.merchantmanager.feign.AccountClient;
import cn.hydee.ydjia.merchantmanager.feign.OrderDetailClient;
import cn.hydee.ydjia.merchantmanager.feign.ReturnQuestClient;
import cn.hydee.ydjia.merchantmanager.service.RedisService;
import cn.hydee.ydjia.merchantmanager.service.ReturnQuestService;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/19 16:34
 */
@Service
public class ReturnQuestServiceImpl implements ReturnQuestService {

    @Autowired
    private ReturnQuestClient returnQuestClient;

    @Autowired
    private OrderDetailClient orderDetailClient;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AccountClient accountClient;


    @Override
    public int agree(RefundReqDTO refundReqDTO,String userName,String authorization) {
        String pwd = redisService.get(authorization+"-pwd");

        if(StringUtils.isEmpty(pwd)){
            AccountLoginDTO accountLoginDTO = builderAccountDTO(userName,refundReqDTO.getPwd());

            //验证输入密码是否正确
            UserInfoDTO userInfoDTO = accountClient.loginUser(accountLoginDTO).getData();
            if(userInfoDTO == null){
                throw WarnException.builder().code(LocalError.PASSWORD_ERROR.getCode()).
                        tipMessage(LocalError.PASSWORD_ERROR.getMsg()).build();
            }else{
                redisService.setValueExpireMinute(authorization+"-pwd",YesOrNoType.YES.getCode(),10);
            }
        }
        ResponseBase<Integer> base = returnQuestClient.agree(refundReqDTO,userName);
        if (base != null) {
            if(base.checkSuccess()){
                return 1;
            }else{
                throw WarnException.builder().code(base.getCode()).
                        tipMessage(base.getMsg()).build();
            }

        }
        return 0;

    }

    private AccountLoginDTO builderAccountDTO(String userName,String pwd){
        AccountLoginDTO accountLoginDTO = new AccountLoginDTO();
        accountLoginDTO.setAccount(userName);
        accountLoginDTO.setPwd(pwd);
        List<Integer> list = new ArrayList<>();
        list.add(UserTypes.MERCHANT.getType());
        list.add(UserTypes.PARTNER.getType());
        list.add(UserTypes.EMPLOYEE.getType());
        accountLoginDTO.setUserType(list);
        return accountLoginDTO;
    }

    @Override
    public int reject(RejectReqDTO rejectReqDTO,String userName) {
        String orderDetailId = rejectReqDTO.getOrderDetailId();

        //获取退款申请单
        ReturnQuest returnQuest = returnQuestClient.selectByDetailId(orderDetailId).getData();
        if(returnQuest == null){
            throw WarnException.builder().code(LocalError.REFUND_NOT_EXIST.getCode()).
                    tipMessage(LocalError.REFUND_NOT_EXIST.getMsg()).build();
        }
        if(returnQuest.getStatus().equals(ReturnQuestStatus.REJECT.getCode())
                                            || returnQuest.getStatus().equals(ReturnQuestStatus.REFUND_COMPLETED.getCode())){
            throw WarnException.builder().code(LocalError.REFUN_STATUS_ERROR.getCode()).
                    tipMessage(LocalError.REFUN_STATUS_ERROR.getMsg()).build();
        }
        ResponseBase<Integer> base = returnQuestClient.reject(rejectReqDTO,userName);
        if (base != null) {
            if(base.checkSuccess()){
                return base.getData();
            }else{
                throw WarnException.builder().code(base.getCode()).
                        tipMessage(base.getMsg()).build();
            }
        }
        return 0;
    }

    @Override
    public int checkPwd(String authorization) {
        String pwd = redisService.get(authorization+"-pwd");
        if(StringUtils.isEmpty(pwd)){
            return 0;
        }
        return 1;
    }

    @Override
    public void query() {

    }
}
