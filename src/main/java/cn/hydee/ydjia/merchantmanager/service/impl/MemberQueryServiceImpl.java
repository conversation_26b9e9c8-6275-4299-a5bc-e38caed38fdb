package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryMemberReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.AccountInfoRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.OrgRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.member.QueryEnableAndWxEmpByOrgCodeDTO;
import cn.hydee.ydjia.merchantmanager.feign.MemberManageClient;
import cn.hydee.ydjia.merchantmanager.feign.OrganizationClient;
import cn.hydee.ydjia.merchantmanager.handler.EmpAuthHandler;
import cn.hydee.ydjia.merchantmanager.service.ChannelManagerService;
import cn.hydee.ydjia.merchantmanager.service.MemberQueryService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.util.ValidationUtils.checkResult;

/**
 * @version 1.0
 * @Author: pengyayun
 * @Description:
 * @Date: 2022/1/19
 */
@Slf4j
@Service
public class MemberQueryServiceImpl implements MemberQueryService {

    @Autowired
    private ChannelManagerService channelManagerService;

    @Autowired
    private EmpAuthHandler empAuthHandler;

    @Autowired
    private MemberManageClient memberManageClient;

    @Autowired
    private OrganizationClient organizationClient;

    @Override
    public ResponseBase<PageDTO<MemberDTO>> listEsMember(QueryMemberReqDTO reqDTO) {

        if(!CollectionUtils.isEmpty(reqDTO.getLastConsumeStores())){
            List<String> list = empAuthHandler.getMyStoreList(reqDTO.getLastConsumeStores(), reqDTO.getUserName(), reqDTO.getMerCode());
            if(CollectionUtils.isEmpty(list)){
                reqDTO.setLastConsumeStores(reqDTO.getLastConsumeStores());
            }else{
                reqDTO.setLastConsumeStores(list);
            }

        }
        //如果用户已经查询过有权限的门店，则不再进行门店权限查询
        if(BooleanUtils.isNotTrue(reqDTO.getHasCheckedStoreAuth()) && !CollectionUtils.isEmpty(reqDTO.getOrganizations())){
            List<String> list = empAuthHandler.getMyStoreList(reqDTO.getOrganizations(), reqDTO.getUserName(), reqDTO.getMerCode());
            if(CollectionUtils.isEmpty(list)){
                reqDTO.setOrganizations(reqDTO.getOrganizations());
            }else{
                reqDTO.setOrganizations(list);
            }
        }

        ResponseBase<PageDTO<MemberDTO>> responseBase = memberManageClient.listEsMember(reqDTO);
        checkResult(responseBase);
        if (responseBase.getData() == null || CollectionUtils.isEmpty(responseBase.getData().getData())){
            return ResponseBase.success();
        }
        responseBase.getData().getData().forEach(item->{
            QueryEnableAndWxEmpByOrgCodeDTO dto = new QueryEnableAndWxEmpByOrgCodeDTO();
            List<String> orList = new ArrayList<>();
            orList.add(item.getOrganization());
            orList.add(item.getCommStore());
            dto.setMerCode(reqDTO.getMerCode());
            dto.setOrgCodeList(orList);
            ResponseBase<List<OrgRespDTO>> response = organizationClient.queryOrgNameByCodeList(dto);
            if(response != null && response.checkSuccess() && !CollectionUtils.isEmpty(response.getData())){
                List<OrgRespDTO> orCodeList = response.getData();
                Map<String,String> orCodeMap = orCodeList.stream().collect(Collectors.toMap(OrgRespDTO::getOrCode, OrgRespDTO::getOrName));
                if(orCodeMap.containsKey(item.getOrganization())){
                    item.setOrganizationName(orCodeMap.get(item.getOrganization()));
                }
                if(orCodeMap.containsKey(item.getCommStore())){
                    item.setCommStoreName(orCodeMap.get(item.getCommStore()));
                }

            }
            item.setFromFlagName("1".equals(item.getFromFlag()) ? "新卡开通" :"老卡绑定");
        });

        return responseBase;
    }

    /**
     * 数据权限校验，只能查询当前员工有权看到的门店下的会员
     *
     * @param reqDTO
     */
    @Override
    public void checkStoreAuth(QueryMemberReqDTO reqDTO, String userName) {

        reqDTO.setHasCheckedStoreAuth(true);

        if (!CollectionUtils.isEmpty(reqDTO.getOrganizations())) {
            // 查询条件已经包含了发卡机构，不做处理，因为这里必定是自己有权限的门店
            return;
        }
        String merCode = reqDTO.getMerCode();
        // 查询员工账户信息
        AccountInfoRespDTO accountInfoResp = empAuthHandler.queryInfoByAccount(userName, merCode);
        // 员工账号是超管或者属于总部，不做处理
        if (accountInfoResp.getAdmin() == 1 || empAuthHandler.isHeadOrg(accountInfoResp.getSubOrgCode(), merCode)) {
            return;
        }
        // 员工不属于总部，默认带上员工有权限的组织或门店作为发卡机构条件
        reqDTO.setOrganizations(empAuthHandler.queryOrgCodeListByUserId(accountInfoResp.getId(), merCode));
    }
}
