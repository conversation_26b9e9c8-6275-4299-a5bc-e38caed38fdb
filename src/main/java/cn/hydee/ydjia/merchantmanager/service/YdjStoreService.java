package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.YdjStore;
import cn.hydee.ydjia.merchantmanager.dto.AreaRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.OnOffStoreReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreNumResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;

import java.util.List;
import java.util.Set;

/**
 * 药店加门店服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/21 11:20
 */
public interface YdjStoreService {
    /**
     * 查找中心店（旗舰店）
     * @param merCode
     * @return
     */
    StoreResDTO queryCenterStore(String merCode);

    /**
     * 新增中心店（旗舰店）
     * @param ydjStore 对象
     * @return String
     */
    String addCenterStore(YdjStore ydjStore,String userId);

    /**
     * 门店上下线
     * @param onOffStoreReqDTO 对象
     * @return  int
     */
    ResponseBase onOffStore(OnOffStoreReqDTO onOffStoreReqDTO, String userId);
    /**
     * 查总上线及已上线门店数量
     * @param merCode 商家编码
     * @return  int
     */
    StoreNumResDTO queryStoreNum(String merCode,String sys);

    /**
     * 验证用户门店权限
     * */
    Set<String> buildStore(String merCode, String userId);

    /**
     * 删除旗舰店
     *
     * @param merCode
     * @param userName
     * @return
     */
    Boolean deleteFlagshipStore(String merCode, String userName);

    List<AreaRespDTO> queryAreaByMercode(String merCode);
}
