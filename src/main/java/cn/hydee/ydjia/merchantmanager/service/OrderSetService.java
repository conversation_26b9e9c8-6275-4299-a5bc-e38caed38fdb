package cn.hydee.ydjia.merchantmanager.service;


import cn.hydee.ydjia.merchantmanager.domain.OrderSetup;
import cn.hydee.ydjia.merchantmanager.dto.req.OrderSetupUpdateDTO;

/**
 * <AUTHOR>
 * @Package : cn.hydee.ydjia.merchantmanager.service
 * @Description :
 * @Create on : 2021/1/13 15:12
 **/
public interface OrderSetService {
    /**
     * 获取订单设置
     *
     * @param merCode c
     * @return c
     */
    OrderSetup getOrderSet(String merCode);

    /**
     * 保存订单设置
     *
     * @param orderSetupUpdateDTO c
     * @return c
     */
    Integer saveOrUpdateOrderSet(OrderSetupUpdateDTO orderSetupUpdateDTO);
}
