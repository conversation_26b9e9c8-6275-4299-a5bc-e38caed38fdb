package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.StoreRule;
import cn.hydee.ydjia.merchantmanager.dto.req.StoreRuleReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreRuleResDTO;
import cn.hydee.ydjia.merchantmanager.enums.EnterStoreTypeEnum;
import cn.hydee.ydjia.merchantmanager.enums.ErrorType;
import cn.hydee.ydjia.merchantmanager.enums.NonStoreRuleEnum;
import cn.hydee.ydjia.merchantmanager.enums.StatusEnums;
import cn.hydee.ydjia.merchantmanager.repository.StoreRuleRepo;
import cn.hydee.ydjia.merchantmanager.service.StoreRuleService;
import cn.hydee.ydjia.merchantmanager.service.YdjStoreService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 门店规则业务处理实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/2
 */
@Slf4j
@Service
public class StoreRuleServiceImpl extends ServiceImpl<StoreRuleRepo, StoreRule> implements StoreRuleService {

    @Autowired
    @Lazy
    private YdjStoreService ydjStoreService;

    @Override
    public boolean saveOrUpdate(StoreRuleReqDTO reqDTO, String userName) {
        if (reqDTO.getNonStoreRule() != null
                && NonStoreRuleEnum.ENTER_CENTER_STORE.getCode().equals(reqDTO.getNonStoreRule())) {
            StoreResDTO storeResDTO = ydjStoreService.queryCenterStore(reqDTO.getMerCode());
            if (storeResDTO == null) {
                throw WarnException.builder().code(ErrorType.CENTER_STORE_NON_EXISTENT_ERROR.getCode())
                        .tipMessage(ErrorType.CENTER_STORE_NON_EXISTENT_ERROR.getMsg()).build();
            }
        }
        if (StatusEnums.ENABLING.getCode().equals(reqDTO.getBeyondSelfDistanceStatus())) {
            if (reqDTO.getSelfDistanceLimit() == null) {
                throw WarnException.builder().code(ErrorType.STORE_RULE_SELF_DISTANCE_LIMIT_ERROR.getCode())
                        .tipMessage(ErrorType.STORE_RULE_SELF_DISTANCE_LIMIT_ERROR.getMsg()).build();
            }
        }
        if (Objects.isNull(reqDTO.getBeyondSelfDistanceStatus())) {
            reqDTO.setBeyondSelfDistanceStatus(StatusEnums.STOP_USE.getCode());
        }

        StoreRule saveStoreRule = new StoreRule();
        BeanUtils.copyProperties(reqDTO, saveStoreRule);

        StoreRule storeRule = this.getOne(new QueryWrapper<StoreRule>().lambda()
                .eq(StoreRule::getMerCode, reqDTO.getMerCode()));
        if (storeRule != null) {
            saveStoreRule.setId(storeRule.getId());
        } else {
            saveStoreRule.setCreateName(userName);
        }
        saveStoreRule.setModifyName(userName);

        for (int i = 0; i < reqDTO.getEnterStoreRuleList().size(); i++) {
            StoreRuleReqDTO.EnterStoreRuleReqDTO enterStoreRule = reqDTO.getEnterStoreRuleList().get(i);
            if (EnterStoreTypeEnum.PARMA_STORE.getCode().equals(enterStoreRule.getEnterStoreType())) {
                saveStoreRule.setParamStoreStatus(enterStoreRule.getStatus());
                saveStoreRule.setParamStoreSort(i + 1);
            } else if (EnterStoreTypeEnum.USUAL_STORE.getCode().equals(enterStoreRule.getEnterStoreType())) {
                saveStoreRule.setUsualStoreStatus(enterStoreRule.getStatus());
                saveStoreRule.setUsualStoreSort(i + 1);
            } else if (EnterStoreTypeEnum.NEARBY_STORE.getCode().equals(enterStoreRule.getEnterStoreType())) {
                saveStoreRule.setNearbyStoreStatus(enterStoreRule.getStatus());
                saveStoreRule.setNearbyStoreSort(i + 1);
            }
        }

        return this.saveOrUpdate(saveStoreRule);
    }

    @Override
    public StoreRuleResDTO getDetail(String merCode) {
        if (StringUtils.isEmpty(merCode)) {
            return null;
        }

        StoreRuleResDTO result = new StoreRuleResDTO();
        StoreRule storeRule = this.getOne(new QueryWrapper<StoreRule>().lambda()
                .eq(StoreRule::getMerCode, merCode));
        if (storeRule == null) {
            return result;
        }
        if (storeRule.getSelfDistanceLimit() != null) {
            storeRule.setSelfDistanceLimit(storeRule.getSelfDistanceLimit());
        }
        BeanUtils.copyProperties(storeRule, result);

        List<StoreRuleResDTO.EnterStoreRuleResDTO> enterStoreRuleList = new ArrayList<>();
        for (EnterStoreTypeEnum enterStoreType : EnterStoreTypeEnum.values()) {
            StoreRuleResDTO.EnterStoreRuleResDTO enterStoreRuleRes = new StoreRuleResDTO.EnterStoreRuleResDTO();
            enterStoreRuleRes.setEnterStoreType(enterStoreType.getCode());
            if (EnterStoreTypeEnum.PARMA_STORE.equals(enterStoreType)) {
                enterStoreRuleRes.setStatus(storeRule.getParamStoreStatus());
                enterStoreRuleRes.setSort(storeRule.getParamStoreSort());
            } else if (EnterStoreTypeEnum.USUAL_STORE.equals(enterStoreType)) {
                enterStoreRuleRes.setStatus(storeRule.getUsualStoreStatus());
                enterStoreRuleRes.setSort(storeRule.getUsualStoreSort());
            } else if (EnterStoreTypeEnum.NEARBY_STORE.equals(enterStoreType)) {
                enterStoreRuleRes.setStatus(storeRule.getNearbyStoreStatus());
                enterStoreRuleRes.setSort(storeRule.getNearbyStoreSort());
            }
            enterStoreRuleList.add(enterStoreRuleRes);
        }
        enterStoreRuleList = enterStoreRuleList.stream()
                .sorted(Comparator.comparing(StoreRuleResDTO.EnterStoreRuleResDTO::getSort))
                .collect(Collectors.toList());

        result.setEnterStoreRuleList(enterStoreRuleList);

        return result;
    }

}
