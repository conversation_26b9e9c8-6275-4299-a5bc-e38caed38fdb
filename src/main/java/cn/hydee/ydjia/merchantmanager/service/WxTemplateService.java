package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.MessageTemplate;
import cn.hydee.ydjia.merchantmanager.dto.req.SetTempReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.wx.WxSetTemplateReqDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/14 15:43
 */
public interface WxTemplateService {


    /**
     * 查询微信模板列表，modelType不传为所有
     * @param modelType
     * @return
     */
    ResponseBase<List<MessageTemplate>> queryWxTemplateByType(Integer modelType, String merCode, String messageType);

    /**
     * 公众号模板和小程序消息模板提供一键重置
     * @param modelType
     * @return
     */
    ResponseBase<Boolean> templateReset(Integer modelType, String merCode, String messageType);

    /**
     * 商户修改模板ModelHead和ModelNote
     * @param request
     * @return
     */
    ResponseBase setMerchantHN(SetTempReqDTO request);

    /**
     * 给商户设置微信模板,type:1启用,0禁用
     * @param setTempReqDTO
     * @return
     */
    ResponseBase setTemplate(SetTempReqDTO  setTempReqDTO);


    /**
     * 给商户设置微信模板,type:1启用,0禁用
     * @param setTempReqDTO
     * @return
     */
    ResponseBase wxSetTemplate(WxSetTemplateReqDTO setTempReqDTO);

}
