package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.req.voice.SceneListReq;
import cn.hydee.ydjia.merchantmanager.dto.req.voice.SceneOpreatorReq;
import cn.hydee.ydjia.merchantmanager.dto.resp.AISpeechBackOrderDetailRspDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.AISpeechConvertEffectDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.AISpeechDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.ActivitySendSmsFeeRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.voice.TuoSceneListRsp;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2022/1/13 17:01
 */
public interface MemberBusinessService {

    Boolean speechTaskCreate(AISpeechDTO speechDTO);

    Boolean speechTaskCreateByDataCenter(AISpeechDTO speechDTO);

    AISpeechConvertEffectDTO queryAISpeechReport(String merCode, Long id, String activityBeginTime, String activityEndTime);

    Boolean exportAISpeechDetailList(TaskDetailReq req);


    Boolean smsTaskCreate(MemberBatchSmsReqDTO memberBatchSmsReqDTO, String userName);

    Boolean smsTaskCreateByDataCenter(MemberBatchSmsReqDTO memberBatchSmsReqDTO, String userName);

    PageDTO<AISpeechBackOrderDetailRspDTO> queryAISpeechBackOrder(AISpeechBackOrderDetailReqDTO reqDTO);

    Boolean exportAISpeechBackOrder(AISpeechBackOrderDetailReqDTO req);

    TuoSceneListRsp sceneList(SceneListReq reqDTO);

    String voiceToken(SceneListReq reqDTO);

    boolean sceneCopy(SceneOpreatorReq reqDTO);

    boolean sceneEnableAndDisEnable(SceneOpreatorReq reqDTO);

    ActivitySendSmsFeeRespDTO checkSmsRemainByMemberNum(QueryMemberByConditionOrCrowdDTO reqDTO, Integer memberNum, String smsMerCode);

    Boolean sceneDel(SceneOpreatorReq reqDTO);

    TuoSceneListRsp sceneDetail(String merCode, String id);

    Map<String, Boolean> queryMessageVariableMap(String merCode);

    void replaceMessageVariable(MessageVariableReplaceReqDTO replaceReqDTO, MsgRecipientDTO msgRecipientDTO);

}
