package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.config.SpringContextHolder;
import cn.hydee.ydjia.merchantmanager.controller.MemberRechargeController;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.req.MemberRechargePageReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberRechargeDetailRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberRechargeReportExportDTO;
import cn.hydee.ydjia.merchantmanager.feign.MemberRechargeClient;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import com.google.common.collect.Lists;;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.MEMBER_RECHARGE_REPORT_EXPORT;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.EXPORT_COUNT_ERROR;

/**
 * @description:
 * @author: HuangYiBo
 * @time: 2021/9/10 17:07
 */

@Service(MEMBER_RECHARGE_REPORT_EXPORT)
@Slf4j
public class MemberRechargeReportExportServiceImpl extends AbstractExportTaskProcessor<MemberRechargeReportExportDTO> {
    @Autowired
    private MemberRechargeClient rechargeClient;

    @Override
    public Class<MemberRechargeReportExportDTO> getExportClass() {
        return MemberRechargeReportExportDTO.class;
    }

    @Override
    public List<MemberRechargeReportExportDTO> getExportDateList(YdjTask task) {
//        validateExportLimitCount(task);
        MemberRechargePageReqDTO memberRechargePageReqDTO = JSONObject.parseObject(task.getCommand(), MemberRechargePageReqDTO.class);
        log.info("[Member_Recharge_Report_Export] req={}", memberRechargePageReqDTO);
        memberRechargePageReqDTO.setType(null);
        int initSize = 10000, currentPage = 1, totalPage = 0;
        List<MemberRechargeReportExportDTO> exportList = null;

        do {
            memberRechargePageReqDTO.setCurrentPage(currentPage);
            memberRechargePageReqDTO.setPageSize(initSize);
            ResponseBase<PageDTO<MemberRechargeDetailRespDTO>> responseBase =  SpringContextHolder.getBean(MemberRechargeController.class).list(memberRechargePageReqDTO, task.getMerCode());
            if (!responseBase.checkSuccess()) {
                log.error("[Member_Recharge_Report_Export] query error {}  response={} {}", memberRechargePageReqDTO, responseBase.getCode(), responseBase.getMsg());
                throw WarnException.builder().code(LocalError.EXCEL_EXPORT_ERROR.getCode()).
                        tipMessage(LocalError.EXCEL_EXPORT_ERROR.getMsg()).build();
            }

            if (Objects.nonNull(responseBase.getData()) && !CollectionUtils.isEmpty(responseBase.getData().getData())) {

                List<MemberRechargeDetailRespDTO> data = responseBase.getData().getData();

                if (Objects.isNull(exportList)) {
                    exportList = Lists.newArrayListWithExpectedSize(responseBase.getData().getTotalCount());
                }

                totalPage = responseBase.getData().getTotalPage();
                log.info("[Member_Recharge_Report_Export] merCode={} total member export count is {}", task.getMerCode(), totalPage);

                for (MemberRechargeDetailRespDTO item : data) {

                        MemberRechargeReportExportDTO memberRechargeReportExportDTO = new MemberRechargeReportExportDTO();
                        BeanUtils.copyProperties(item, memberRechargeReportExportDTO);
                        memberRechargeReportExportDTO.setCreateTime(DateFormatUtils.format(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                        memberRechargeReportExportDTO.setReceivedAmount(item.getCapital().add(item.getHandselAmount()));
                        memberRechargeReportExportDTO.setPayAmount(item.getCapital());
                        exportList.add(memberRechargeReportExportDTO);

                }
            }
            currentPage++;
        } while (currentPage <= totalPage);

        if (CollectionUtils.isEmpty(exportList)) {
            throw WarnException.builder().code(LocalError.EXCEL_ROW_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_ROW_NULL.getMsg()).build();
        }

        return exportList;
    }

    private void validateExportLimitCount(YdjTask task) {
        if (foundExportCountToday(task) >= exportNumLimitToday) {
            throw WarnException.builder().code(EXPORT_COUNT_ERROR.getCode())
                    .message(String.format(EXPORT_COUNT_ERROR.getMsg(), exportNumLimitToday)).build();
        }
    }

    @Override
    public String getFileNamePrefix() {
        log.info("[Member_Recharge_Report_Export] FileNamePrefix={}", "余额充值报表");
        return "余额充值报表";
    }
}
