package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.CommodityTypeRelate;
import cn.hydee.ydjia.merchantmanager.dto.req.AssembleCommodityDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.TypeRelateQueryDTO;
import cn.hydee.ydjia.merchantmanager.feign.AssembleCommClient;
import cn.hydee.ydjia.merchantmanager.feign.CommodityTypeRelateClient;
import cn.hydee.ydjia.merchantmanager.service.AssembleCommService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/12
 */
@Service
public class AssembleCommServiceImpl implements AssembleCommService {


    @Resource
    private AssembleCommClient assembleCommClient;

    @Resource
    private CommodityTypeRelateClient commodityTypeRelateClient;

    @Override
    public ResponseBase<AssembleCommodityDTO> getAssembleCommodity(String merCode, String id) {
        ResponseBase<AssembleCommodityDTO> assembleBase = assembleCommClient.getAssembleCommodity(merCode, id);

        //查询商品分组关联关系
        TypeRelateQueryDTO typeRelateQueryDTO = new TypeRelateQueryDTO();
        List<String> ids = new ArrayList<>();
        typeRelateQueryDTO.setMerCode(merCode);
        ids.add(id);
        typeRelateQueryDTO.setProductIds(ids);
        ResponseBase<List<CommodityTypeRelate>> relateBase = commodityTypeRelateClient.selectResponseBaseByProductId(typeRelateQueryDTO);
        List<CommodityTypeRelate> relateList = relateBase.getData();

        if (assembleBase.getData() != null && relateList != null && relateList.size() > 0) {
            List<String> groupIds = new ArrayList<>();
            relateList.forEach(relate-> {
                groupIds.add(relate.getTypeId());
            });
            assembleBase.getData().setGroupIds(groupIds);
        }
        return assembleBase;
    }
}
