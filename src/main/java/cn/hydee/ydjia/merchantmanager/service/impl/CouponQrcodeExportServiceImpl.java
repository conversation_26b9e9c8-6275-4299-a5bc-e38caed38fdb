package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.service.OssFileService;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.UUIDUtil;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.req.MarketQrcodeReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MarketQrcodeRespDTO;
import cn.hydee.ydjia.merchantmanager.enums.ErrorType;
import cn.hydee.ydjia.merchantmanager.feign.MarketingActivitiesClient;
import cn.hydee.ydjia.merchantmanager.util.FileUtil;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.COUPON_CENTER_QRCODE_EXPORT;

@Slf4j
@RequiredArgsConstructor
@Service(COUPON_CENTER_QRCODE_EXPORT)
public class CouponQrcodeExportServiceImpl extends AbstractExportTaskProcessor<Object> {

    private final MarketingActivitiesClient client;

    private final OssFileService ossFileService;

    @Value("${spring.application.name}")
    private String appName;

    private static final String zipFileNamePrefix = "推广二维码";

    private static final Gson gson = new Gson();

    /**
     * 券礼包和领券中心 导出逻辑一致，所以往容器注入名字不通，类型相同的两个bean
     * @return exportService
     */
    @Bean(COUPON_GIFT_QRCODE_EXPORT)
    public AbstractExportTaskProcessor<Object> couponQrcodeExportServiceImpl(){
        CouponQrcodeExportServiceImpl exportService = new CouponQrcodeExportServiceImpl(client, ossFileService);
        exportService.appName = this.appName;
        return exportService;
    }

    @Override
    public String diyExport(YdjTask task) {
        MarketQrcodeReqDTO request = gson.fromJson(task.getCommand(), MarketQrcodeReqDTO.class);
        List<String> storeCodeList = request.getStoreCodeList();
        List<String> pathList = new ArrayList<>();
        String zipFileName = zipFileNamePrefix + "-" + task.getMerCode();
        String zipFilePath = FileUtil.createTempFile(zipFileName, FileUtil.ZIP);
        // 分批次生成二维码 防止接口超时
        for (int i = 0; i < storeCodeList.size(); i += 10) {
            int lastIndex = Math.min(storeCodeList.size(), i + 10);
            request.setStoreCodeList(storeCodeList.subList(i, lastIndex));
            ResponseBase<MarketQrcodeRespDTO> responseBase = client.buildQrcodeList(request);
            MarketQrcodeRespDTO data = responseBase.getData();
            if (!responseBase.checkSuccess() || data == null || CollectionUtils.isEmpty(data.getQrcodeList())) {
                log.warn("create qrcode failure, request:{}", request);
                continue;
            }
            for (MarketQrcodeRespDTO.Qrcode qrcodeDTO : data.getQrcodeList()) {
                // 通过oss url 下载png文件
                String miniAppFilePath = getFileByOssUrl(qrcodeDTO.getMiniAppQrcodeUrl());
                String h5FilePath = getFileByOssUrl(qrcodeDTO.getH5QrcodeUrl());
                pathList.add(miniAppFilePath);
                pathList.add(h5FilePath);
            }
        }
        // 合并所有图片打包成zip
        compress(pathList, zipFilePath);
        zipFileName = zipFileName + "-" + DateUtil.getTodayString() + "-" + UUIDUtil.generateUuid();
        String result = uploadOss(task.getMerCode(), zipFileName, zipFilePath);
        new File(zipFilePath).delete();
        pathList.forEach(path -> new File(path).delete());
        return result;
    }

    private String getFileByOssUrl(String url) {
        InputStream inputStream = ossFileService.download(url).getObjectContent();
        File dir = new File(EXPORT_TEMP_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String[] split = url.split("/");
        String fileName = split[split.length - 1];
        String filePath = dir.getAbsolutePath() + File.separator + fileName;
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
        }

        try {
            FileUtils.copyInputStreamToFile(inputStream, file);
        } catch (IOException e) {
            e.printStackTrace();
            log.warn("file copy failure!", e);
        }
        return filePath;
    }

    private String uploadOss(String merCode, String fileName, String filePath) {
        String ossFileName = appName + FileUtil.SLASH +
                merCode + "/data/" +
                DateUtil.getTodayString() +
                FileUtil.SLASH +
                fileName +
                FileUtil.ZIP;
        File file = new File(filePath);
        try {
            ossFileService.upload(file, ossFileName);
        } catch (Exception e) {
            log.warn("uploadOss error", e);
            throw WarnException.builder().code(ErrorType.FILE_UPLOAD_ERROR.getCode())
                    .tipMessage(ErrorType.FILE_UPLOAD_ERROR.getMsg()).build();
        }
        return ossFileName;
    }

    @Override
    public Class<Object> getExportClass() {
        return null;
    }

    @Override
    public List<Object> getExportDateList(YdjTask task) {
        return null;
    }
}
