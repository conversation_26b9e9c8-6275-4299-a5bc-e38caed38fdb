package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.util.BeanUtil;
import cn.hydee.ydjia.merchantmanager.domain.CommodityStoreSpec;
import cn.hydee.ydjia.merchantmanager.dto.AreaStoreQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.AreaStoreResDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.CommStoreOnlineReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryStoreDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.StoreAndSpecStatusReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.CommStoreOnlineRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreSpecStatusReqDTO;
import cn.hydee.ydjia.merchantmanager.feign.CommodityStoreSpecClient;
import cn.hydee.ydjia.merchantmanager.feign.MallGoodsClient;
import cn.hydee.ydjia.merchantmanager.feign.StoreClient;
import cn.hydee.ydjia.merchantmanager.service.CommodityStoreSpecService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/22
 */
@Service
@Slf4j
public class CommodityStoreSpecServiceImpl implements CommodityStoreSpecService {

    @Resource
    private StoreClient storeClient;

    @Resource
    private CommodityStoreSpecClient commodityStoreSpecClient;
    @Autowired
    private MallGoodsClient mallGoodsClient;


    @Override
    public PageDTO<CommStoreOnlineRespDTO> singleCommAllStoreOnlineStatusPageQuery(CommStoreOnlineReqDTO dto) {
        PageDTO<CommStoreOnlineRespDTO> pageDTO = new PageDTO<>();
        pageDTO.setCurrentPage(dto.getCurrentPage());
        pageDTO.setPageSize(dto.getPageSize());
        List<CommStoreOnlineRespDTO> respDTOS = new ArrayList<>();

        if (Boolean.TRUE.equals(dto.getHasAreaSearch())) {
            AreaStoreQueryDTO areaQuery = new AreaStoreQueryDTO();
            areaQuery.setMerCode(dto.getMerCode());
            areaQuery.setSearchKey(dto.getSearchKey());
            areaQuery.setStCodeList(dto.getStCodeList());
            areaQuery.setAreaId(dto.getAreaId());
            areaQuery.setAreaIds(dto.getAreaIds());
            areaQuery.setIsPage(1);
            areaQuery.setCurrentPage(dto.getCurrentPage());
            areaQuery.setPageSize(dto.getPageSize());
            ResponseBase<PageDTO<AreaStoreResDTO>> storeBase = storeClient.queryMerStoreWithAreas(areaQuery);
            if (storeBase != null && storeBase.getData() != null) {
                List<AreaStoreResDTO> areaDatas = storeBase.getData().getData();
                if (!CollectionUtils.isEmpty(areaDatas)) {
                    try {
                        pageDTO.setTotalPage(storeBase.getData().getTotalPage());
                        pageDTO.setTotalCount(storeBase.getData().getTotalCount());
                        respDTOS = BeanUtil.copyList(areaDatas, CommStoreOnlineRespDTO.class);
                    } catch (Exception e) {
                        log.info("不走区域查询门店数据转化异常",e);
                        return new PageDTO<>();
                    }
                }
            }
        } else {
            //分页查询门店信息
            QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
            queryStoreDTO.setMerCode(dto.getMerCode());
            queryStoreDTO.setOnlineStatus(dto.getStoreOnlineStatus());
            queryStoreDTO.setStatus(dto.getStoreStatus());
            queryStoreDTO.setSearchKey(dto.getSearchKey());
            //门店编码集合
            queryStoreDTO.setStCodeList(dto.getStCodeList());
            queryStoreDTO.setCurrentPage(dto.getCurrentPage());
            queryStoreDTO.setPageSize(dto.getPageSize());
            ResponseBase<PageDTO<StoreResDTO>> base = storeClient.queryStoreByCondition(queryStoreDTO);
            if (base != null && base.getData() != null) {
                //门店相关数据转化
                List<StoreResDTO> data = base.getData().getData();
                pageDTO.setTotalPage(base.getData().getTotalPage());
                pageDTO.setTotalCount(base.getData().getTotalCount());
                if (!CollectionUtils.isEmpty(data)) {
                    try {
                        respDTOS = BeanUtil.copyList(data, CommStoreOnlineRespDTO.class);
                    } catch (Exception e) {
                        log.info("不走区域查询门店数据转化异常",e);
                        return new PageDTO<>();
                    }
                }
            }
        }


        //根据门店ID集合和商品编码查询商品上架状态
        if (!CollectionUtils.isEmpty(respDTOS) && !StringUtils.isEmpty(dto.getSpecId())) {
            List<String> storeIds = respDTOS.stream().map(CommStoreOnlineRespDTO::getId).collect(Collectors.toList());
            StoreSpecStatusReqDTO statusReqDTO = new StoreSpecStatusReqDTO();
            statusReqDTO.setMerCode(dto.getMerCode());
            statusReqDTO.setSpecIds(Collections.singletonList(dto.getSpecId()));
            statusReqDTO.setStoreIds(storeIds);
            if (dto.getCommOnlineStatus() != null) {
                statusReqDTO.setStatus(dto.getCommOnlineStatus());
            }
            ResponseBase<List<CommodityStoreSpec>> storeSpecBase = commodityStoreSpecClient.selectStoreSpecStatus(statusReqDTO);
            List<CommodityStoreSpec> storeSpecs = storeSpecBase.getData();
            if (!CollectionUtils.isEmpty(storeSpecs)) {
                Map<String, CommodityStoreSpec> map = storeSpecs.stream().collect(Collectors.toMap(CommodityStoreSpec::getStoreId, spec -> spec));
                respDTOS.forEach(respDTO -> {
                    CommodityStoreSpec spec = map.get(respDTO.getId());
                    if (spec != null) {
                        respDTO.setSpecId(spec.getSpecId());
                        respDTO.setCommOnlineStatus(spec.getStatus());
                    }else {
                        respDTO.setCommOnlineStatus(0);
                    }
                });
            }else {
                respDTOS.forEach(respDTO -> respDTO.setCommOnlineStatus(0));
            }
        }
        pageDTO.setData(respDTOS);
        return pageDTO;
    }

    @Override
    public ResponseBase updateStoreAndSpecStatus(StoreAndSpecStatusReqDTO dto) {
        log.info("微商城商品-批量上下架（已勾选）入参：{}", JSON.toJSONString(dto));
        return mallGoodsClient.updateStoreAndSpecStatus(dto);
    }

    @Override
    public ResponseBase<PageDTO<CommStoreOnlineRespDTO>> singleCommOnlineStoresPage(CommStoreOnlineReqDTO dto) {
        return commodityStoreSpecClient.singleCommOnlineStoresPage(dto);
    }

    private List<String> getConflictList(Map<String, List<String>> storeSpecIdsMap, Map<String, List<String>> updateMap) {
        List<String> conflictList = new ArrayList<>();
        Set<String> storeIds = updateMap.keySet();
        storeIds.forEach(storeId -> {
            List<String> updateSpecIds = updateMap.get(storeId);
            List<String> actSpecIds = storeSpecIdsMap.get(storeId);
            if (!CollectionUtils.isEmpty(actSpecIds)) {
                updateSpecIds.forEach(specId -> {
                    if (actSpecIds.contains(specId)) {
                        conflictList.add(specId);
                    }
                });
            }
        });
        return conflictList;
    }

}
