package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.ydjia.merchantmanager.domain.ScreenConfig;
import cn.hydee.ydjia.merchantmanager.dto.screen.ScreenConfigDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.ScreenConfigReqDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @create 2021-04-08
 */
public interface ScreenConfigService extends IService<ScreenConfig> {

    /**
     * 列表
     *
     * @param reqDTO
     * @return
     */
    PageDTO<ScreenConfigDTO> list(ScreenConfigReqDTO reqDTO);

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    boolean add(ScreenConfigDTO dto);

    /**
     * 更新
     *
     * @param dto
     * @return
     */
    boolean update(ScreenConfigDTO dto);

    /**
     * 删除
     *
     * @param id
     * @param userName
     * @return
     */
    boolean delete(String id, String userName);

    /**
     * 启用或暂停
     *
     * @param reqDTO
     * @return
     */
    boolean publish(ScreenConfigReqDTO reqDTO);

    /**
     * 列表，供运营后台展示
     * @param reqDTO
     * @return
     */
    PageDTO<ScreenConfigDTO> listForOperate(ScreenConfigReqDTO reqDTO);
}
