package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.dto.req.ActiGiftConditionDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.ActiGiftResqDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.ActivitySpecConditionDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantmanager.enums.CommodityTypeEnum;
import cn.hydee.ydjia.merchantmanager.feign.CommoditySpecClient;
import cn.hydee.ydjia.merchantmanager.feign.PromoteClient;
import cn.hydee.ydjia.merchantmanager.service.ActivityGiftService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/4/22 20:40
 */
@Service
@Slf4j
public class ActivityGiftServiceImpl implements ActivityGiftService {
    @Autowired
    private PromoteClient promoteClient;
    @Autowired
    private CommoditySpecClient commoditySpecClient;

    @Override
    public ActiGiftResqDTO queryGiftById(Long id) {
        ActiGiftResqDTO data = promoteClient.queryGiftById(id).getData();
        if (!StringUtils.isEmpty(data.getSpecId())) {
            ActivitySpecConditionDTO specConditionDTO = new ActivitySpecConditionDTO();
            List<ActivitySpecDTO> specDTOList;
            specConditionDTO.setMerCode(data.getMerCode());
            specConditionDTO.setDistinct(true);
            List<Long> list = new ArrayList<>();
            list.add(Long.parseLong(data.getSpecId()));
            specConditionDTO.setSkuIds(list);
            specConditionDTO.setGroupType(true);
            specConditionDTO.setHasStatus(true);
            List<Integer> commodityTypeList = Lists.newArrayList();
            commodityTypeList.add(CommodityTypeEnum.GENERAL_COMMODITY.getCode());
            commodityTypeList.add(CommodityTypeEnum.GIFT_COMMODITY.getCode());
            specConditionDTO.setCommodityTypeList(commodityTypeList);
            ResponseBase<PageDTO<ActivitySpecDTO>> base = commoditySpecClient.queryActivityComm(specConditionDTO);
            if (base == null || base.getData() == null || CollectionUtils.isEmpty(base.getData().getData())) {
                specDTOList = null;
            } else {
                specDTOList = base.getData().getData();
            }
            data.setSpecDTOList(specDTOList);
        }
        return data;
    }

    @Override
    public PageDTO<ActiGiftResqDTO> queryGiftByCondition(ActiGiftConditionDTO giftReqDTO) {

        ActivitySpecConditionDTO specConditionDTO = new ActivitySpecConditionDTO();
        specConditionDTO.setMerCode(giftReqDTO.getMerCode());
        List<Integer> commodityTypeList = Lists.newArrayList();
        commodityTypeList.add(CommodityTypeEnum.GENERAL_COMMODITY.getCode());
        commodityTypeList.add(CommodityTypeEnum.GIFT_COMMODITY.getCode());
        specConditionDTO.setCommodityTypeList(commodityTypeList);
        giftReqDTO.setOriginType(LocalConst.STATUS_ZERO);

        PageDTO<ActiGiftResqDTO> pagePageResp = new PageDTO<>();
        List<ActiGiftResqDTO> giftList = Lists.newArrayList();

        // 商品编码查询
        if (!CollectionUtils.isEmpty(giftReqDTO.getErpCodes())) {
            specConditionDTO.setErpCodes(giftReqDTO.getErpCodes());
        } else {
            List<Long> list = new ArrayList<>();
            ResponseBase<PageDTO<ActiGiftResqDTO>> responseBase = promoteClient.queryGiftByCondition(giftReqDTO);
            if (!responseBase.checkSuccess()) {
                throw WarnException.builder().code(responseBase.getCode()).message(responseBase.getMsg()).build();
            }
            pagePageResp = responseBase.getData();
            giftList = pagePageResp.getData();
            if (!CollectionUtils.isEmpty(giftList)) {
                pagePageResp.getData().forEach(item -> {
                    list.add(Long.parseLong(item.getSpecId()));
                });
                specConditionDTO.setSkuIds(list);
            }
        }

        ResponseBase<PageDTO<ActivitySpecDTO>> base = commoditySpecClient.queryActivityComm(specConditionDTO);
        List<ActivitySpecDTO> specDTOList = base.getData().getData();
        if (!CollectionUtils.isEmpty(giftReqDTO.getErpCodes()) && !CollectionUtils.isEmpty(specDTOList)) {
            giftReqDTO.setSpecIds(specDTOList.stream().map(o -> Long.valueOf(o.getSpecId())).collect(Collectors.toList()));
            ResponseBase<PageDTO<ActiGiftResqDTO>> responseBase = promoteClient.queryGiftByCondition(giftReqDTO);
            if (!responseBase.checkSuccess()) {
                throw WarnException.builder().code(responseBase.getCode()).message(responseBase.getMsg()).build();
            }
            pagePageResp = responseBase.getData();
            giftList = pagePageResp.getData();
        }

        for (ActiGiftResqDTO actiGiftResqDTO : giftList) {
            actiGiftResqDTO.setProvideNum(actiGiftResqDTO.getStock() - actiGiftResqDTO.getLeaveStock());
            specDTOList.forEach(item -> {
                if (actiGiftResqDTO.getSpecId().equals(item.getSpecId())) {
                    actiGiftResqDTO.setErpCode(item.getErpCode());
                    actiGiftResqDTO.setMainPic(item.getMainPic());
                    actiGiftResqDTO.setMPrice(item.getMPrice());
                }
            });
        }
        return pagePageResp; 
    }
}
