package cn.hydee.ydjia.merchantmanager.service;

import java.util.List;

import javax.validation.Valid;

import cn.hydee.ydjia.merchantmanager.dto.req.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.web.bind.annotation.RequestBody;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.ActivityGroupProductAddStockDTO;
import cn.hydee.ydjia.merchantmanager.dto.ProductDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.ActivityGroupProductDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.ActivityInfoGroupDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.GroupActivityInfoQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.GroupProductInfoQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.PmtGroupActivityInfo;
import cn.hydee.ydjia.merchantmanager.dto.group.PmtGroupProduct;
import cn.hydee.ydjia.merchantmanager.dto.group.QueryGroupIngPageRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.group.QueryGroupIngReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.GroupActivityEditDTO;

public interface GroupAdminService {

    ResponseBase<String> addGroupActivity(@Valid @RequestBody ActivityInfoGroupDTO dto);


    ResponseBase<String> modifyGroupActivity(@Valid @RequestBody ActivityInfoGroupDTO dto);


    ResponseBase<String>  deleteByIds(@RequestBody List<Long> ids);


    ResponseBase modifyProduct(@RequestBody ActivityGroupProductDTO dto) throws JsonProcessingException;


    ResponseBase<PageDTO<PmtGroupProduct>> getProductsByActivityId(@RequestBody GroupProductInfoQueryDTO dto);

    ResponseBase<PmtGroupProduct> getProductInfo(Long specId);

    ResponseBase<String> addProduct(@RequestBody List<ActivityGroupProductDTO> dtos);


    ResponseBase<PageDTO<PmtGroupActivityInfo>> getGroupActivityInfo(@RequestBody GroupActivityInfoQueryDTO condition);


    ResponseBase<String> updateStock(@RequestBody List<ActivityGroupProductDTO> dtos);

    ResponseBase<List<String>> getAllStores(String merCode);

    GroupActivityEditDTO getActivityInfo(Long activityId);

    ResponseBase<Boolean> deleteProductInfo(List<Long> activityspecIds);

    ResponseBase<PageDTO<QueryGroupIngPageRespDTO>> getGroupIngPage(QueryGroupIngReqDTO dto);

    ResponseBase<Boolean> oneTimeGroup(String groupCode);

    ResponseBase<Integer> clearProductStock(ClearStockReqDTO reqDTO);

    /**
     * 根据拼团规则获取商品列表
     * **/
    ResponseBase<PageDTO<ProductDTO>> getProductsByCondition(String merCode);


    ResponseBase  addStock(GroupAddStockDTO dto);

    ResponseBase createGroupActivity(ActivityInfoGroupDTO createDTO);

    ResponseBase updateActivity(ActivityInfoGroupDTO createDTO);

    ActiGroupRespDTO queryGroupActivity(Long id);

    List<PmtGroupProduct> searchDetailRule(Long activityId);

    ResponseBase updateSortGroupRule(List<SortReqDTO> reqDTO);
}
