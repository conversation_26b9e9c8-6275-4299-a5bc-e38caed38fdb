package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.PrescriptionDrugPictureSettingDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.CheckConfigRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.merchant.InitConfigValidateResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.merchant.InitConfigValidateV2ResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.merchant.InitConfigValidateV3ResDTO;

/**
 * <AUTHOR>
 * @date 2020/4/24 11:38
 */
public interface MerchantService {

    /**
     * 根据商家编码查询商家信息
     *
     * @param merCode
     * @return
     */
    ResponseBase queryMerchantByCode(String merCode);

    CheckConfigRespDTO checkConfig(String merCode);

    /**
     * 初始化检测商户配置信息
     *
     * @param merCode 商户编码
     * @return 检测结果
     */
    InitConfigValidateResDTO initConfigValidate(String merCode);

    /**
     * 初始化检测商户配置信息 3.0
     *
     * @param merCode 商户编码
     * @return 检测结果
     */
    InitConfigValidateV3ResDTO initConfigValidateV3(String merCode);

    /**
     * 查询商户处方药合规图片设置
     *
     * @param merCode
     * @return
     */
    PrescriptionDrugPictureSettingDTO getPictureSetting(String merCode);

    /**
     * 处方药合规-保存图片设置
     *
     * @param reqDTO
     * @return
     */
    Boolean savePictureSetting(PrescriptionDrugPictureSettingDTO reqDTO);


}
