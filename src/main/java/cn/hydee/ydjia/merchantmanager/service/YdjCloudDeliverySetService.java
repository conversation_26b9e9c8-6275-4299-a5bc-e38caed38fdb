package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.ydjia.merchantmanager.dto.req.YdjCloudDeliverySetBatchDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjCloudDeliverySetBatchRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjCloudDeliverySetResp;
import cn.hydee.ydjia.merchantmanager.dto.resp.YdjDeliverySetResDTO;

/**
 * 快递配置服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/29 15:15
 */
public interface YdjCloudDeliverySetService {
    /**
     * 新增内部链接
     *
     * @param ydjCloudDeliverySetBatchDTO 对象
     * @param userId                    操作人
     * @param spCode
     * @return String
     */
    void addUpdateBatch(YdjCloudDeliverySetBatchDTO ydjCloudDeliverySetBatchDTO, String userId, String spCode);

    /**
     * 查询快递配置
     */
    YdjCloudDeliverySetBatchRespDTO queryCloudDeliverySet(String merCode, String spCode);

    /**
     * 通过区域ID查快递配置信息
     *
     * @param merCode 商家编码
     * @param rangeId 区域ID
     * @param spCode
     * @return YdjDeliverySetResDTO
     */
    YdjCloudDeliverySetResp queryByRangeId(String merCode, String rangeId, String spCode);


}
