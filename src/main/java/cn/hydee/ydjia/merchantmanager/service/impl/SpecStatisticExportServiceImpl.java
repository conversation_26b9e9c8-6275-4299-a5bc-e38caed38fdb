package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.SpecStatisticsDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.SpecStatisticsQueryDTO;
import cn.hydee.ydjia.merchantmanager.feign.CommodityStoreSpecClient;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品统计数据导出
 *
 * <AUTHOR>
 * @date 2020/6/10
 */
@Slf4j
@Service(value = "SPEC_STATISTIC")
public class SpecStatisticExportServiceImpl extends AbstractExportTaskProcessor<SpecStatisticsDTO> {

    @Autowired
    private CommodityStoreSpecClient client;

    @Override
    public Class<SpecStatisticsDTO> getExportClass() {
        return SpecStatisticsDTO.class;
    }

    @Override
    public List<SpecStatisticsDTO> getExportDateList(YdjTask task) {
        SpecStatisticsQueryDTO queryDTO = JSONObject.parseObject(task.getCommand(), SpecStatisticsQueryDTO.class);
        //导出统计数据的查询分页参数默认为500
        int currentPage = 1;
        int pageSize = 500;
        int totalPage = 0;
        List<SpecStatisticsDTO> statisticsDTOList = new ArrayList<>();
        do {
            queryDTO.setCurrentPage(currentPage);
            queryDTO.setPageSize(pageSize);
            ResponseBase<PageDTO<SpecStatisticsDTO>> base = client.specStatistics(queryDTO);
            if (base != null) {
                PageDTO<SpecStatisticsDTO> pageDTO = base.getData();
                if (pageDTO != null && !CollectionUtils.isEmpty(pageDTO.getData())) {
                    statisticsDTOList.addAll(pageDTO.getData());
                    totalPage = pageDTO.getTotalPage();
                    log.info("第" + currentPage + "次查询导出的商品统计记录数量为：" + pageDTO.getData().size());
                }
                currentPage++;
            }
        } while (currentPage <= totalPage);
        if (!CollectionUtils.isEmpty(statisticsDTOList)) {
            statisticsDTOList.forEach(spec -> spec.setKeyValue(spec.getKeyValue()));
            log.info("导出的商品统计记录总数量为：" + statisticsDTOList.size());
        }
        return statisticsDTOList;
    }
}
