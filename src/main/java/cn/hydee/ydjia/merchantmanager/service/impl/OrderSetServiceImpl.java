package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.OrderSetup;
import cn.hydee.ydjia.merchantmanager.dto.req.OrderOtherSetDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.OrderSetupUpdateDTO;
import cn.hydee.ydjia.merchantmanager.enums.YesOrNoType;
import cn.hydee.ydjia.merchantmanager.feign.OrderSetupClient;
import cn.hydee.ydjia.merchantmanager.service.OrderSetService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Package : cn.hydee.ydjia.merchantmanager.service.impl
 * @Description :
 * @Create on : 2021/1/13 15:15
 **/
@Service
@Slf4j
public class OrderSetServiceImpl implements OrderSetService {
    private final OrderSetupClient orderSetupClient;

    public OrderSetServiceImpl(OrderSetupClient orderSetupClient) {
        this.orderSetupClient = orderSetupClient;
    }

    @Override
    public OrderSetup getOrderSet(String merCode) {
        if (StringUtils.isEmpty(merCode)) {
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        ResponseBase<OrderSetup> responseBase = orderSetupClient.getOrderSetup(merCode);
        
        if (!responseBase.checkSuccess() || responseBase.getData() == null) {
            log.warn("/order-setup/{}，{}", merCode, JSONObject.toJSONString(responseBase));
            return new OrderSetup();
        }
        return responseBase.getData();
    }

    @Override
    public Integer saveOrUpdateOrderSet(OrderSetupUpdateDTO req) {
        if (req.getDeductionStatus() != null && req.getDeductionStatus() == 1) {
            if (req.getDeductionType() == null || req.getDeductionValue() == null
                    || req.getDeductionValue() == 0) {
                throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode())
                        .tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
            }
        }
        if(req.getOrderOtherSetDto() != null ){
            req.setOrderOtherSet(JSONObject.toJSONString(req.getOrderOtherSetDto()));
        }
        
        ResponseBase<Integer> resp = orderSetupClient.saveOrUpdateOrderSetup(req);
        if (!resp.checkSuccess() || resp.getData() == null
                || YesOrNoType.NO.getCode().equals(resp.getData())) {
            throw WarnException.builder().code(ErrorType.OPERATOR_ERROR.getCode())
                    .tipMessage(ErrorType.OPERATOR_ERROR.getMsg()).build();
        }
        return resp.getData();
    }
}
