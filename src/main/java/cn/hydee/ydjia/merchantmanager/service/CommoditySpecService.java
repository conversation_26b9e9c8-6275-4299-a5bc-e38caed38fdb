package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.req.DownloadQrCodeReq;
import cn.hydee.ydjia.merchantmanager.dto.req.SpecSearchReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreSpec;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/12/1 16:16
 */
public interface CommoditySpecService {
    ResponseBase<PageDTO<StoreSpec>> querySpecByCode(SpecSearchReqDTO dto, String userId);

    void downloadQrCode(DownloadQrCodeReq req);
}
