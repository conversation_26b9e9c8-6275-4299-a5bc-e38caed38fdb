package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.*;
import cn.hydee.ydjia.merchantmanager.feign.LiveClient;
import cn.hydee.ydjia.merchantmanager.service.LiveStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class LiveStatisticsServiceImpl implements LiveStatisticsService {

    @Autowired
    private LiveClient liveClient;

    @Override
    public ResponseBase<LiveTotalDataRspDTO> totalData(String merCode) {
        return liveClient.totalData(merCode);
    }

    @Override
    public ResponseBase<LiveTotalDataRspDTO> factoryTotalData(String merCode) {
        return liveClient.factoryTotalData(merCode);
    }

    @Override
    public ResponseBase<PageDTO<LiveSimpleRspDTO>> historyList(LiveHistoryReqDTO reqDTO) {
        return liveClient.historyList(reqDTO);
    }

    @Override
    public ResponseBase<PageDTO<LiveSimpleRspDTO>> factoryHistoryList(LiveHistoryReqDTO reqDTO) {
        return liveClient.factoryList(reqDTO);
    }

    @Override
    public ResponseBase<LiveDataRspDTO> getLiveStatisticsByLiveId(String merCode, Long liveId) {
        return liveClient.get(merCode,liveId);
    }

    @Override
    public ResponseBase<LiveDataRspDTO> getFactory(String merCode, Long liveId) {
        return liveClient.getFactory(merCode,liveId);
    }

    @Override
        public ResponseBase<PageDTO<LiveSubscrPageRspDTO>> getFactorySubsList(LiveSubscrPageReqDTO reqDTO){
        return liveClient.getFactorySubsList(reqDTO);
    }

    @Override
    public ResponseBase<LiveTotalDataRspDTO> actFactoryTotalData(String merCode) {
        return liveClient.actFactoryTotalData(merCode);
    }

    @Override
    public ResponseBase<PageDTO<LiveSimpleRspDTO>> activityFactoryList(LiveHistoryReqDTO reqDTO) {
        return liveClient.activityFactoryList(reqDTO);
    }

    @Override
    public ResponseBase<List<String>> getOrderIdsInLive(LivePayOrderIdReqDTO reqDTO) {
        return liveClient.getOrderIdsInLive(reqDTO);
    }
}
