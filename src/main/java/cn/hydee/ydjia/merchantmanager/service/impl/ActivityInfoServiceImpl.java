package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.service.OssFileService;
import cn.hydee.ydjia.merchantmanager.domain.CommoditySpecSku;
import cn.hydee.ydjia.merchantmanager.dto.*;
import cn.hydee.ydjia.merchantmanager.dto.commission.OssFileRspDto;
import cn.hydee.ydjia.merchantmanager.dto.req.ActiSearchRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.req.isp.ISPMerchantQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.aggregate.ActAggPreferenceRes;
import cn.hydee.ydjia.merchantmanager.enums.*;
import cn.hydee.ydjia.merchantmanager.feign.*;
import cn.hydee.ydjia.merchantmanager.service.ActivityInfoService;
import cn.hydee.ydjia.merchantmanager.util.*;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.micrometer.core.lang.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 公共活动实现类
 * @Date 2020/4/17 9:52
 */
@Service
@Slf4j
public class ActivityInfoServiceImpl implements ActivityInfoService {

    private final ActivityInfoClient activityInfoClient;
    private final StoreClient storeClient;
    private final CommoditySpecClient commoditySpecClient;
    private final CommodityClient commodityClient;
    private final PromoteClient promoteClient;
    @Autowired
    private MallGoodsClient mallGoodsClient;

    @Resource
    private OssFileService ossFileService;
    @Autowired
    private SPMerchantQueryClient spMerchantQueryClient;

    @Autowired
    public ActivityInfoServiceImpl(ActivityInfoClient activityInfoClient, StoreClient storeClient, CommoditySpecClient commoditySpecClient,
                                   CommodityClient commodityClient, PromoteClient promoteClient) {
        this.activityInfoClient = activityInfoClient;
        this.storeClient = storeClient;
        this.commoditySpecClient = commoditySpecClient;
        this.commodityClient = commodityClient;
        this.promoteClient = promoteClient;
    }

    @Override
    public ResponseBase<PageDTO<ActiInfoRespDTO>> queryActivityCommByCondition(ActiSearchReqDTO conditionDTO) {
        ResponseBase<PageDTO<ActiInfoRespDTO>> pageDTOResponseBase = ResponseBase.success();
        if (StringUtils.hasText(conditionDTO.getSearchKeyWord())) {
            // 先匹配商品库规格
            List<Long> specIds = this.getActivityListSpecDTOS(conditionDTO.getMerCode(), conditionDTO.getSearchKeyWord());
            if (CollectionUtils.isEmpty(specIds)) {
                PageDTO<ActiInfoRespDTO> pageDTO = new PageDTO<>();
                pageDTO.setTotalCount(0);
                pageDTO.setTotalPage(0);
                pageDTO.setData(Lists.newArrayList());
                pageDTOResponseBase.setData(pageDTO);
                return pageDTOResponseBase;
            }
            conditionDTO.setSearchSpecIds(specIds);
        }
        pageDTOResponseBase = activityInfoClient.queryActivityCommByCondition(conditionDTO);
        if (pageDTOResponseBase == null || !pageDTOResponseBase.checkSuccess() || pageDTOResponseBase.getData() == null) {
            log.warn("{}", JSON.toJSONString(pageDTOResponseBase));
            return ResponseBase.success();
        }
        List<ActiInfoRespDTO> list = pageDTOResponseBase.getData().getData();
        if (CollectionUtils.isEmpty(list)) {
            return pageDTOResponseBase;
        }
        // 统计门店数量
        if (conditionDTO.getIsStoreCount() != null && conditionDTO.getIsStoreCount()) {
            List<ActiInfoRespDTO> allStoreActList = list.stream()
                    .filter(o -> LocalConst.STATUS_ZERO.equals(o.getStoreSelection())).collect(Collectors.toList());
            List<ActiInfoRespDTO> nonStoreActList = list.stream()
                    .filter(o -> LocalConst.STATUS_TWO.equals(o.getStoreSelection())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(allStoreActList) || !CollectionUtils.isEmpty(nonStoreActList)) {
                ResponseBase<Integer> countOnlineStore = storeClient.countOnlineStore(conditionDTO.getMerCode());
                if (countOnlineStore == null || !countOnlineStore.checkSuccess() || countOnlineStore.getData() == null) {
                    log.warn("countOnlineStore res {}", JSON.toJSONString(countOnlineStore));
                    return pageDTOResponseBase;
                }
                int allStoreCount = countOnlineStore.getData();
                list.stream().forEach(o -> {
                    if (LocalConst.STATUS_ZERO.equals(o.getStoreSelection())) {
                        o.setCountStore(allStoreCount);
                    }
                    if (LocalConst.STATUS_TWO.equals(o.getStoreSelection())) {
                        int nonCount = o.getCountStore() == null ? 0 : o.getCountStore();
                        o.setCountStore(allStoreCount - nonCount);
                    }
                });
            }
        }
        // 是否需要查询门店名称，目前就B端分销活动列表使用，改为查门店ID，调用方再查询门店名称
        if (conditionDTO.getHasStoreName() != null && conditionDTO.getHasStoreName()) {
            Set<String> storeIds = new HashSet<>();
            list.stream().forEach(o -> {
                if (CollectionUtils.isEmpty(o.getStoreIds())) {
                    return;
                }
                String storeId = o.getStoreIds().get(0);
                if (!storeIds.contains(storeId)) {
                    storeIds.add(storeId);
                }
            });
            if (CollectionUtils.isEmpty(storeIds)) {
                return pageDTOResponseBase;
            }
            ResponseBase<List<StoreResDTO>> listResponseBase = storeClient.queryStoreByIds(storeIds);
            // 批量查询服务商
            List<StoreResDTO> spList = this.getSpInfo(conditionDTO.getMerCode(), storeIds.stream().collect(Collectors.toList()), false);
            // list转map
            Map<String, StoreResDTO> spInfoMap = spList.stream().collect(Collectors.toMap(StoreResDTO::getStCode, v -> v));
            if (listResponseBase != null && listResponseBase.checkSuccess() && !CollectionUtils.isEmpty(listResponseBase.getData())) {
                Map<String, StoreResDTO> storeResDTOMap = listResponseBase.getData().parallelStream().distinct()
                        .collect(Collectors.toMap(StoreResDTO::getId, Function.identity(), (key1, key2) -> key1));
                list.stream().forEach(o -> {
                    if (CollectionUtils.isEmpty(o.getStoreIds())) {
                        return;
                    }
                    List<String> storeNames = new ArrayList<>();
                    // 参与机构为连锁门店时
                    if (o.getOrgType().intValue() == 1) {
                        o.getStoreIds().stream().forEach(storeId -> {
                            if (storeResDTOMap.get(storeId) == null) {
                                return;
                            }
                            if (storeNames.contains(storeResDTOMap.get(storeId).getStName())) {
                                return;
                            }
                            storeNames.add(storeResDTOMap.get(storeId).getStName());
                            o.setStoreNames(storeNames);
                        });
                    } else {
                        // 参与机构为服务商时
                        o.getStoreIds().stream().forEach(storeId -> {
                            if (spInfoMap.get(storeId) == null) {
                                return;
                            }
                            if (storeNames.contains(spInfoMap.get(storeId).getStName())) {
                                return;
                            }
                            storeNames.add(spInfoMap.get(storeId).getStName());
                            o.setStoreNames(storeNames);
                        });
                    }
                });
            } else {
                // 服务商的情况
                list.stream().forEach(o -> {
                    if(CollUtil.isNotEmpty(o.getStoreIds())) {
                        StoreResDTO resDTO = spInfoMap.get(o.getStoreIds().get(0));
                        if(!ObjectUtils.isEmpty(resDTO)) {
                            List<String> storeNames = Lists.newArrayList();
                            storeNames.add(resDTO.getStName());
                            o.setStoreNames(storeNames);
                        }
                    }
                });
            }
        }
        return pageDTOResponseBase;
    }

    @Override
    public ResponseBase deleteActivity(Long id, String userName) {
        return activityInfoClient.deleteActivity(id, userName);
    }

    @Override
    public ResponseBase inValidActivity(Long id, String userName) {
        return activityInfoClient.inValidActivity(id, userName);
    }

    @Override
    public ResponseBase setEffectiveActivity(Long id) {
        return activityInfoClient.setEffectiveActivity(id);
    }

    /**
     * 活动复制
     *
     * @param actCopyReqDTO
     * @return
     */
    @Override
    public ResponseBase copyActivity(ActCopyReqDTO actCopyReqDTO) {
        ActiCreateDTO actiCreateDTO = new ActiCreateDTO();
        //1.先查询活动信息
        ResponseBase<ActiInfoRespDTO> actInfoPage = activityInfoClient.queryActivityInfo(actCopyReqDTO.getId() + "");
        if (!actInfoPage.checkSuccess() || actInfoPage.getData() == null) {
            throw WarnException.builder().code(ErrorType.SEARCH_ACT_ERROR.getCode()).
                    tipMessage(ErrorType.SEARCH_ACT_ERROR.getMsg()).build();
        }
        ActiInfoRespDTO actInfo = actInfoPage.getData();
        //活动旧数据复制到新活动
        BeanUtils.copyProperties(actInfo, actiCreateDTO);
        //活动新数据复制到新活动（出现字段被覆盖的情况）-改为部分字段赋值（20210817调整）
        actiCreateDTO.setCopyParam(actInfo, actCopyReqDTO);
        //复制门店和商品数据
        ActivityStoreDTO actStoreDTO = this.getStoreAndSpecData(actInfo, actCopyReqDTO, actiCreateDTO);
        //默认不投放状态
        actiCreateDTO.setStatus(StatusEnums.STOP_USE.getCode());
        actiCreateDTO.setStoreSpeList(actStoreDTO);
        log.info("request promote param: {}", JSON.toJSONString(actiCreateDTO));
        return activityInfoClient.createActivity(actiCreateDTO);
    }

    /**
     * 投放活动
     *
     * @param actLaunchReqDTO
     * @return
     */
    @Override
    public ResponseBase launchActivity(ActLaunchReqDTO actLaunchReqDTO) {
        ResponseBase<List<ActiSearchRespDTO>> searchActListResult = activityInfoClient.getConflictActivity(actLaunchReqDTO);
        if (!searchActListResult.checkSuccess()) {
            throw WarnException.builder().code(ErrorType.SEARCH_ACT_ERROR.getCode()).
                    tipMessage(ErrorType.SEARCH_ACT_ERROR.getMsg()).build();
        }
        List<ActiSearchRespDTO> actList = searchActListResult.getData();
        // 把冲突的活动商品信息生成excel
        if (!CollectionUtils.isEmpty(actList)) {
            return this.generateConflictFile(actList, actLaunchReqDTO);
        }
        // 分销活动时，需要判断门店或服务商状态是否正常
        if(actLaunchReqDTO.getType().equals(PromotionType.DISTRIBUTION.getCode())) {
            checkStoreStatus(actLaunchReqDTO.getId());
        }
        // 没有冲突 更新活动状态
        return activityInfoClient.setEffectiveActivity(actLaunchReqDTO.getId());
    }

    /**
     * @Description: 判断门店（服务商）状态，状态异常（如门店下线等）不能发布
     * @Author: Kaven
     * @Date: 2023/9/11 14:43
     * @param [id]
     * @return void
     * @Exception
     */
    private void checkStoreStatus(Long id) {
        // 查询活动信息
        ActiInfoRespDTO actInfo = this.activityInfoClient.queryActivityInfo(id.toString()).getData();
        if(ObjectUtil.isNull(actInfo)) {
            throw WarnException.builder().code(ErrorType.RECORD_NOT_EXIST.getCode()).tipMessage(ErrorType.RECORD_NOT_EXIST.getMsg()).build();
        }
        // 如果参与活动的是门店
        if(actInfo.getOrgType().intValue() == 1) {
            // 查询门店
            List<StoreResDTO> storeResDTOS = this.getStoreResDTOList(actInfo.getMerCode(), actInfo.getStoreIds());
            // 判断是否已下线或停用
            StoreResDTO storeResDTO = storeResDTOS.get(0);
            if(!storeResDTO.getOnlineStatus().equals(StoreOnLineStatus.ON_LINE.getCode())
                    || !storeResDTO.getStStatus().equals(StatusEnums.ENABLING.getCode())) {
                throw WarnException.builder().code(ErrorType.STORE_SP_STATE_ERROR.getCode()).tipMessage(ErrorType.STORE_SP_STATE_ERROR.getMsg()).build();
            }
            return;
        }
        // 如果参与活动的是服务商
        List<StoreResDTO> resDTOS = getSpInfo(actInfo.getMerCode(), actInfo.getStoreIds(), true);
        if(CollectionUtils.isEmpty(resDTOS)) {
            throw WarnException.builder().code(ErrorType.STORE_SP_STATE_ERROR.getCode()).tipMessage(ErrorType.STORE_SP_STATE_ERROR.getMsg()).build();
        }
    }

    @Override
    public ActivitySpecImportResp importCommodityExcel(String merCode, Integer isHasDrug, MultipartFile excelFile) {
        // 返回结果
        ActivitySpecImportResp activitySpecImportResp = new ActivitySpecImportResp();
        List<ExcelActivitySpecDTO> importExcels = this.importExcel(excelFile, ExcelActivitySpecDTO.class, "普通商品");
        if (CollectionUtils.isEmpty(importExcels) || null == importExcels.get(0) || StringUtils.isEmpty(importExcels.get(0).getErpCode())) {
            throw WarnException.builder().code(LocalError.EXCEL_DATA_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_DATA_NULL.getMsg()).build();
        }
        // 导入失败的规格
        List<ExcelActivitySpecDTO> importFailActivitySpec = Lists.newArrayList();
        // 第一部分失败数据：基础校验不通过的
        Map<String, ExcelActivitySpecDTO> dataMap = validateBase(importExcels, importFailActivitySpec);
        // 商品编码集合
        List<String> erpCodes = importExcels.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
        // 找出重复的商品编码集
        List<String> repeatCodes = erpCodes.stream().collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                .entrySet().stream().filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        // 去除上面校验失败部分，查询并处理商品信息
        if(!CollectionUtils.isEmpty(importFailActivitySpec)) {
            List<String> failErpCodes = importFailActivitySpec.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
            erpCodes.removeAll(failErpCodes);
        }
        // 若存在重复的商品，从上面的map中取出需要正确导入的重复数据（因为map里面的数据才是全部校验通过的，产品要求重复的数据必须成功导入1条）
        if(!CollectionUtils.isEmpty(repeatCodes)) {
            repeatCodes.stream().filter(dataMap::containsKey).forEach(erpCodes::add);
        }
        // 若全部失败，生成导入失败文件并返回
        if(CollectionUtils.isEmpty(erpCodes)) {
            handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());
            return activitySpecImportResp;
        }
        // 基础校验通过，则查询商品中心商品数据
        activitySpecImportResp = this.handleCommodityData(merCode, isHasDrug, erpCodes, null);
        // 如果有未查询到的商品
        if (activitySpecImportResp.getFailCount() > 0) {
            // 第二部分失败数据：商品中台未查到的，即总的减去查询到的
            List<String> wareCodes = activitySpecImportResp.getActivitySpecs().stream().map(ActivitySpecImportResp.ActivitySpec::getErpCode).collect(Collectors.toList());
            erpCodes.removeAll(wareCodes);
            List<ExcelActivitySpecDTO> queryFailList = Lists.newArrayList();
            erpCodes.forEach(code -> queryFailList.add(dataMap.get(code)));
            // 批量设置失败信息
            queryFailList.stream().forEach(item -> item.setErrorMsg(LocalError.COMMODITY_NOT_EXIST_ERROR.getMsg()));
            importFailActivitySpec.addAll(queryFailList);
        }
        // 导入失败的商品规格处理，生成Excel文件供前端下载
        handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());
        return activitySpecImportResp;
    }

    /**
     * @Description: 必填校验、重复性校验
     * @Author: Kaven
     * @Date: 2023/3/14 14:29
     * @param [importExcels, importFailActivitySpec]
     * @return dataMap
     * @Exception
     */
    private Map<String, ExcelActivitySpecDTO> validateBase(List<ExcelActivitySpecDTO> importExcels, List<ExcelActivitySpecDTO> importFailActivitySpec) {
        // 临时变量，用于存放导入商品数据
        Map<String,ExcelActivitySpecDTO> dataMap = Maps.newHashMap();
        importExcels.stream().forEach(limitedSpecDTO -> {
            String errMsg = ValidationUtils.getValidatorReason(limitedSpecDTO);
            if(ObjectUtil.isNotNull(errMsg)) {
                // 加入失败集合
                limitedSpecDTO.setErrorMsg(LocalError.EXCEL_IMPORT_ERROR.getMsg() + "：" + errMsg);
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
            // 商品重复校验
            ExcelActivitySpecDTO repeatVo = dataMap.get(limitedSpecDTO.getErpCode());
            if(ObjectUtils.isEmpty(repeatVo)) {
                dataMap.put(limitedSpecDTO.getErpCode(), limitedSpecDTO);
            } else {
                limitedSpecDTO.setErrorMsg(String.format(LocalError.ERP_CODE_DUPLICATE_ERROR.getMsg(), limitedSpecDTO.getErpCode()));
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
        });
        return dataMap;
    }

    /**
     * 组装活动冲突数据
     *
     * @param actList         冲突数据
     * @param actLaunchReqDTO 要投放的活动
     * @return 文件名称
     */
    private ResponseBase generateConflictFile(List<ActiSearchRespDTO> actList, ActLaunchReqDTO actLaunchReqDTO) {
        // 获取当前活动商品，不符合条件商品存入记录表中
        List<String> conflictStoreIdList = new ArrayList<>();
        List<String> conflictSpecIdsList = new ArrayList<>();
        Integer pmtType = actList.get(0).getPmtType();
        List<ActiSearchRespDTO> allStoreActList = actList.stream().filter(act -> act.getAllStoreConflict() != null
                && act.getAllStoreConflict()).collect(Collectors.toList());
        List<StoreResDTO> commonAllStore;
        List<String> commonAllStoreIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allStoreActList)) {
            commonAllStore = this.getStoreResDTOList(actLaunchReqDTO.getMerCode(), null);
            commonAllStoreIds = commonAllStore.stream().map(StoreResDTO::getId).collect(Collectors.toList());
        }
        List<ActiSearchRespDTO> allSpecActList = actList.stream().filter(act -> act.getAllSpecConflict() != null
                && act.getAllSpecConflict()).collect(Collectors.toList());
        List<ActivitySpecDTO> commonAllSpec;
        List<String> commonAllSpecIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allSpecActList)) {
            commonAllSpec = this.getActivitySpecDTOS(pmtType, actLaunchReqDTO.getMerCode(), null);
            commonAllSpecIds = commonAllSpec.stream().map(ActivitySpecDTO::getSpecId).collect(Collectors.toList());
        }
        List<String> finalCommonAllStoreIds = commonAllStoreIds;
        List<String> finalCommonAllSpecIds = commonAllSpecIds;
        actList.forEach(act -> {
            List<String> storeIds = act.getStoreIds();
            if (act.getAllStoreConflict() != null && act.getAllStoreConflict()) {
                // 表示当前活动与要发布的活动都是全部门店
                storeIds = finalCommonAllStoreIds;
            }
            List<String> specIds = act.getSpecIds();
            if (act.getAllSpecConflict() != null && act.getAllSpecConflict()) {
                // 表示当前活动与要发布的活动都是全部商品
                specIds = finalCommonAllSpecIds;
            }
            if (CollectionUtils.isEmpty(storeIds)) {
                // 判断非冲突门店ID
                List<String> nonConflictStoreIds = act.getNonConflictStoreIds();
                if (!CollectionUtils.isEmpty(nonConflictStoreIds)) {
                    storeIds = this.getConflictStoreIds(actLaunchReqDTO.getMerCode(), nonConflictStoreIds);
                    if (CollectionUtils.isEmpty(storeIds)) {
                        log.warn("nonConflictStoreIds = {}", JSON.toJSONString(nonConflictStoreIds));
                        return;
                    }
                }
            }
            if (CollectionUtils.isEmpty(specIds)) {
                // 判读非冲突规格ID
                List<String> nonConflictSpecIds = act.getNonConflictSpecIds();
                if (!CollectionUtils.isEmpty(nonConflictSpecIds)) {
                    specIds = this.getConflictSpecIds(pmtType, actLaunchReqDTO.getMerCode(), nonConflictSpecIds);
                    if (CollectionUtils.isEmpty(specIds)) {
                        log.warn("nonConflictSpecIds = {}", JSON.toJSONString(nonConflictSpecIds));
                        return;
                    }
                }
            }
            if (!CollectionUtils.isEmpty(storeIds)) {
                act.setStoreIds(storeIds);
                conflictStoreIdList.addAll(storeIds);
            }
            if (!CollectionUtils.isEmpty(specIds)) {
                act.setSpecIds(specIds);
                conflictSpecIdsList.addAll(specIds);
            }
        });
        if (CollectionUtils.isEmpty(conflictStoreIdList)) {
            throw WarnException.builder().code(ErrorType.ACTIVITY_STORE_NULL.getCode()).
                    tipMessage(ErrorType.ACTIVITY_STORE_NULL.getMsg()).build();
        }
        if (CollectionUtils.isEmpty(conflictSpecIdsList)) {
            throw WarnException.builder().code(ErrorType.ACTIVITY_SPEC_NULL.getCode()).
                    tipMessage(ErrorType.ACTIVITY_SPEC_NULL.getMsg()).build();
        }
        //去重
        List<String> storeIdList = conflictStoreIdList.stream().distinct().collect(Collectors.toList());
        List<Long> specIdsList = conflictSpecIdsList.stream().distinct().map(Long::parseLong).collect(Collectors.toList());
        //查询门店详情
        List<StoreResDTO> storeResDTOList = this.getStoreResDTOList(actLaunchReqDTO.getMerCode(), storeIdList);
        if (CollectionUtils.isEmpty(storeResDTOList)) {
            throw WarnException.builder().code(ErrorType.ACTIVITY_STORE_NULL.getCode()).
                    tipMessage(ErrorType.ACTIVITY_STORE_NULL.getMsg()).build();
        }
        //查询商品详情
        List<ActivitySpecDTO> activitySpecList = this.getActivitySpecDTOS(pmtType, actLaunchReqDTO.getMerCode(), specIdsList);
        if (CollectionUtils.isEmpty(activitySpecList)) {
            throw WarnException.builder().code(ErrorType.ACTIVITY_SPEC_NULL.getCode()).
                    tipMessage(ErrorType.ACTIVITY_SPEC_NULL.getMsg()).build();
        }
        //获取活动冲突数据
        List<PmtErrorRecordDTO> pmtRecordList = this.getPmtErrorRecordList(actList, storeResDTOList, activitySpecList,
                actLaunchReqDTO.getUserName());
        if (CollectionUtils.isEmpty(pmtRecordList)) {
            throw WarnException.builder().code(ErrorType.ACTIVITY_LAUNCH_ERROR.getCode()).
                    tipMessage(ErrorType.ACTIVITY_LAUNCH_ERROR.getMsg()).build();
        }
        ActiInfoFileRespDTO file = new ActiInfoFileRespDTO();
        file.setId(actLaunchReqDTO.getId() + "");
        file.setMerCode(actLaunchReqDTO.getMerCode());
        file.setCreateName(actLaunchReqDTO.getUserName());
        file.setPmtRecordList(pmtRecordList);
        //生成冲突file
        return activityInfoClient.saveLanuchFile(file);
    }

    private List<String> getConflictSpecIds(Integer pmtType, String merCode, List<String> nonConflictSpecIds) {
        if (CollectionUtils.isEmpty(nonConflictSpecIds)) {
            return Lists.newArrayList();
        }
        ActivitySpecConditionDTO dto = new ActivitySpecConditionDTO();
        dto.setMerCode(merCode);
        dto.setPageSize(LocalConst.COMMON_SPEC_COUNT_SIZE + nonConflictSpecIds.size());
        if (PromotionType.getDrugPmtTypeList().contains(pmtType)) {
            dto.setHasDrug(true);
        }
        ResponseBase<PageDTO<ActivitySpecDTO>> base = commoditySpecClient.queryActivityComm(dto);
        if (base == null || base.getData() == null || CollectionUtils.isEmpty(base.getData().getData())) {
            log.warn("queryActivityComm request = {}，res = {}", JSON.toJSONString(dto), JSON.toJSONString(base));
            return Lists.newArrayList();
        }
        List<ActivitySpecDTO> activitySpecDTOS = base.getData().getData();
        return activitySpecDTOS.stream().filter(o -> !nonConflictSpecIds.contains(o.getSpecId()))
                .map(ActivitySpecDTO::getSpecId).collect(Collectors.toList());
    }

    private List<String> getConflictStoreIds(String merCode, List<String> nonConflictStoreIds) {
        if (CollectionUtils.isEmpty(nonConflictStoreIds)) {
            return Lists.newArrayList();
        }
        QueryStoreDTO storeDTO = new QueryStoreDTO();
        storeDTO.setMerCode(merCode);
        storeDTO.setOnlineStatus(StoreOnLineStatus.ON_LINE.getCode());
        storeDTO.setStatus(IsvalidStatus.EFFECTIVE.getCode());
        storeDTO.setPageSize(LocalConst.COMMON_STORE_COUNT_SIZE + nonConflictStoreIds.size());
        ResponseBase<PageDTO<StoreResDTO>> responseBase = storeClient.queryStoreByCondition(storeDTO);
        if (responseBase == null || !responseBase.checkSuccess() || responseBase.getData() == null
                || CollectionUtils.isEmpty(responseBase.getData().getData())) {
            log.warn("queryStoreByCondition request = {}, res = {}", JSON.toJSONString(storeDTO),
                    JSON.toJSONString(responseBase));
            throw WarnException.builder().code(ErrorType.ACTIVITY_STORE_NULL.getCode()).
                    tipMessage(ErrorType.ACTIVITY_STORE_NULL.getMsg()).build();
        }
        List<StoreResDTO> storeResDTOS = responseBase.getData().getData();
        return storeResDTOS.stream().filter(o -> !nonConflictStoreIds.contains(o.getId())).map(StoreResDTO::getId)
                .collect(Collectors.toList());
    }

    /**
     * 获取文件数据参数
     *
     * @param actList          冲突活动集合
     * @param storeResDTOList  门店详情集合
     * @param activitySpecList 商品详情集合
     * @param userName         操作人
     * @return 文件数据集合
     */
    private List<PmtErrorRecordDTO> getPmtErrorRecordList(List<ActiSearchRespDTO> actList,
                                                          List<StoreResDTO> storeResDTOList,
                                                          List<ActivitySpecDTO> activitySpecList, String userName) {
        List<PmtErrorRecordDTO> pmtRecordList = new ArrayList<>();
        //按照门店id分组
        Map<String, StoreResDTO> storeMap = storeResDTOList.stream().collect(Collectors.toMap(StoreResDTO::getId,
                Function.identity()));
        //按照商品规格id分组
        Map<String, ActivitySpecDTO> specMap = activitySpecList.stream()
                .collect(Collectors.toMap(ActivitySpecDTO::getSpecId, Function.identity()));
        actList.forEach(act -> {
            String pmtName = PromotionType.getItem(act.getPmtType()).getName();
            String reason = String.format(LocalConst.ACTI_COMM_ERROR,
                    pmtName, act.getPmtName());
            //获取门店信息
            StringBuilder stCodeBuilder = new StringBuilder();
//            StringBuilder storeNameBuilder = new StringBuilder();
            int existsStoreCount = 0;
            if (CollectionUtils.isEmpty(act.getStoreIds())) {
                return;
            }
            for (String storeId : act.getStoreIds()) {
//                if (existsStoreCount >= LocalConst.EXCEL_FILE_CELL_STORE_MAX_COUNT) {
//                    break;
//                }
                StoreResDTO store = storeMap.get(storeId);
                if (store == null) {
                    continue;
                }
                stCodeBuilder.append(store.getStCode()).append(",");
//                storeNameBuilder.append(store.getStName()).append(",");
                existsStoreCount++;
            }
            //获取商品信息
            StringBuilder erpCodeBuilder = new StringBuilder();
//            StringBuilder commNameBuilder = new StringBuilder();
//            StringBuilder specNameBuilder = new StringBuilder();
            int existsSpecCount = 0;
            if (CollectionUtils.isEmpty(act.getSpecIds())) {
                return;
            }
            for (String specId : act.getSpecIds()) {
//                if (existsStoreCount >= LocalConst.EXCEL_FILE_CELL_SPEC_MAX_COUNT) {
//                    break;
//                }
                ActivitySpecDTO spec = specMap.get(specId);
                if (spec == null) {
                    continue;
                }
                erpCodeBuilder.append(spec.getErpCode()).append(",");
//                commNameBuilder.append(spec.getName()).append(",");
//                if (!CollectionUtils.isEmpty(spec.getSpecSkus())) {
//                    specNameBuilder.append(spec.getSpecSkus().toString()).append(",");
//                }
                existsSpecCount++;
            }
            if (existsStoreCount == 0 || existsSpecCount == 0) {
                return;
            }
            PmtErrorRecordDTO record = new PmtErrorRecordDTO();
            record.setError(LocalConst.ACT_CONFLICT);
            record.setReason(reason);
            record.setStCode(stCodeBuilder.substring(0, stCodeBuilder.toString().length() - 1));
//            record.setStoreName(storeNameBuilder.substring(0, storeNameBuilder.toString().length() - 1));
            record.setErpCode(erpCodeBuilder.substring(0, erpCodeBuilder.toString().length() - 1));
//            record.setCommName(commNameBuilder.substring(0, commNameBuilder.toString().length() - 1));
//            if (specNameBuilder.length() > 0) {
//                record.setSpecName(specNameBuilder.substring(0, specNameBuilder.toString().length() - 1));
//            }
            record.setStartTime(LocalDateUtil.stringNormal(act.getStartTime()));
            record.setEndTime(LocalDateUtil.stringNormal(act.getEndTime()));
            record.setCreateName(userName);
            pmtRecordList.add(record);
        });
        return pmtRecordList;
    }

    /**
     * @Description: 查询分销活动服务商信息
     * @Author: Kaven
     * @Date: 2023/8/28 09:31
     * @param [merCode, spCodes]
     * @return java.util.List<cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO>
     * @Exception
     */
    private List<StoreResDTO> getSpInfo(String merCode, List<String> spCodes, boolean enable) {
        List<StoreResDTO> storeResDTOS = Lists.newArrayList();
        // 查询服务商信息
        ISPMerchantQueryDTO queryDTO = new ISPMerchantQueryDTO();
        queryDTO.setMerCode(merCode);
        queryDTO.setSpCodes(spCodes);
        List<SPMerchantDetailInfoResDTO> spInfoList = spMerchantQueryClient.queryListBySpCodes(queryDTO).getData();
        log.info("根据编码查询服务商信息返回：{}", JSON.toJSONString(spInfoList));

        if(CollectionUtils.isEmpty(spInfoList)) {
            return storeResDTOS;
        }
        for(SPMerchantDetailInfoResDTO sp : spInfoList) {
            StoreResDTO storeResDTO = new StoreResDTO();
            storeResDTO.setMerCode(merCode);
            storeResDTO.setId(sp.getMerCode());
            storeResDTO.setStCode(sp.getMerCode());
            storeResDTO.setStName(sp.getMerName());
            storeResDTO.setStStatus(sp.getAuthStatus());
            storeResDTOS.add(storeResDTO);
        }
        // 是否需要过滤停用的服务商
        if(enable) {
            storeResDTOS = storeResDTOS.stream().filter(d -> d.getStStatus().equals(YesOrNoType.YES.getCode())).collect(Collectors.toList());
        }
        return storeResDTOS;
    }

    /**
     * 复制门店和商品
     *
     * @param dto           原活动信息
     * @param actCopyReqDTO 复制内容
     * @param actiCreateDTO 新活动
     * @return 门店商品集合
     */
    private ActivityStoreDTO getStoreAndSpecData(ActiInfoRespDTO dto, ActCopyReqDTO actCopyReqDTO, ActiCreateDTO actiCreateDTO) {
        ActivityStoreDTO storeSpeList = new ActivityStoreDTO();

        // 复制门店数据
        List<StoreResDTO> storeResDTOList = new ArrayList<>();
        if (actCopyReqDTO.getStoreFlag() != null && actCopyReqDTO.getStoreFlag()) {
            if (!LocalConst.STATUS_ZERO.equals(dto.getStoreSelection())
                    && !CollectionUtils.isEmpty(dto.getStoreIds())) {
                storeResDTOList = this.getCopyStoreResDTOList(dto.getOrgType(), dto.getMerCode(), dto.getStoreIds());
            }
        } else {
            actiCreateDTO.setStoreSelection(LocalConst.STATUS_ONE);
        }

        // 复制商品数据
        List<ActivitySpecDTO> activitySpecList = new ArrayList<>();
        if (actCopyReqDTO.getSpecFlag() != null && actCopyReqDTO.getSpecFlag()) {
            if (!CollectionUtils.isEmpty(dto.getSpecIds())) {
                List<Long> spectLongIds = dto.getSpecIds().stream().map(Long::parseLong).collect(Collectors.toList());
                activitySpecList = this.getCopyActivitySpecDTOS(dto.getOrgType(), dto.getStoreIds(), dto.getMerCode(), spectLongIds);
            }
        } else {
            actiCreateDTO.setSpecSelection(LocalConst.STATUS_ONE);
        }

        storeSpeList.setSpecList(activitySpecList);
        storeSpeList.setStoreResDTOList(storeResDTOList);
        return storeSpeList;
    }

    /**
     * @Description: 复制商品
     * @Author: Kaven
     * @Date: 2023/9/5 16:04
     * @param [orgType, storeIds, merCode, collect]
     * @return java.util.List<cn.hydee.ydjia.merchantmanager.dto.req.ActivitySpecDTO>
     * @Exception
     */
    private List<ActivitySpecDTO> getCopyActivitySpecDTOS(Integer orgType, List<String> storeIds, String merCode, List<Long> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return Lists.newArrayList();
        }
        ActivitySpecConditionDTO dto = new ActivitySpecConditionDTO();
        dto.setMerCode(merCode);
        dto.setSkuIds(collect);
        // orgType=2时查云仓商品
        if(!ObjectUtils.isEmpty(orgType) && orgType.intValue() == 2) {
            dto.setIsSp(true);
            // 查云仓商品时服务商编码必传
            dto.setSpCodes(storeIds);
            // 只查询上架商品
            dto.setStatus(YesOrNoType.YES.getCode());
        } else {
            // 查普通商品
            dto.setPageSize(0);
            dto.setPageTag(false);
            dto.setHasDrug(true);
            dto.setFilterSrm(true);
        }
        log.info("查询商品信息入参：{}", JSON.toJSONString(dto));
        ResponseBase<PageDTO<ActivitySpecDTO>> base = commoditySpecClient.queryActivityComm(dto);
        if (base == null || base.getData() == null || CollectionUtils.isEmpty(base.getData().getData())) {
            log.warn("queryActivityComm request = {}，res = {}", JSON.toJSONString(dto), JSON.toJSONString(base));
            return Lists.newArrayList();
        }
        return base.getData().getData();
    }

    /**
     * 获取商品详情（生成冲突文件）
     *
     * @param pmtType
     * @param merCode 商户编码
     * @param collect 商品规格id集合
     * @return 商品集合
     */
    private List<ActivitySpecDTO> getActivitySpecDTOS(Integer pmtType, String merCode, List<Long> collect) {
        ActivitySpecConditionDTO dto = new ActivitySpecConditionDTO();
        dto.setMerCode(merCode);
        if (!CollectionUtils.isEmpty(collect)) {
            dto.setSkuIds(collect);
        }
        if (PromotionType.getDrugPmtTypeList().contains(pmtType)) {
            dto.setHasDrug(true);
        }

        // 封板后临时增加
        List<ActivitySpecDTO> result = new ArrayList<>(collect.size());
        int pageSize = 5000;
        dto.setPageSize(pageSize);
        int page = 1;
        int currentPageSize;
        do {
            dto.setCurrentPage(page++);
            ResponseBase<PageDTO<ActivitySpecDTO>> base = commoditySpecClient.queryActivityComm(dto);
            if (base == null || base.getData() == null || null == base.getData().getData()) {
                log.warn("queryActivityComm request = {}，res = {}", JSON.toJSONString(dto), JSON.toJSONString(base));
                break;
            }
            List<ActivitySpecDTO> data = base.getData().getData();
            if (CollectionUtils.isEmpty(data)) {
                break;
            }
            currentPageSize = data.size();
            result.addAll(data);
        } while (currentPageSize == pageSize);

        return result;
    }

    /**
     * @Description: 复制参与机构
     * @Author: Kaven
     * @Date: 2023/9/5 15:56
     * @param [orgType, merCode, storeIds]
     * @return java.util.List<cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO>
     * @Exception
     */
    private List<StoreResDTO> getCopyStoreResDTOList(Integer orgType, String merCode, List<String> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Lists.newArrayList();
        }
        // 如果参与机构是服务商
        if(!ObjectUtils.isEmpty(orgType) && orgType.intValue() == 2) {
            return this.getSpInfo(merCode, storeIds.stream().collect(Collectors.toList()), true);
        }
        QueryStoreDTO storeDTO = new QueryStoreDTO();
        storeDTO.setMerCode(merCode);
        storeDTO.setList(storeIds);
        storeDTO.setPageSize(LocalConst.MAX_STORE_NUM);
        ResponseBase<PageDTO<StoreResDTO>> responseBase = storeClient.queryStoreByCondition(storeDTO);
        if (responseBase == null || !responseBase.checkSuccess() || responseBase.getData() == null
                || CollectionUtils.isEmpty(responseBase.getData().getData())) {
            log.warn("queryStoreByCondition request = {}, res = {}", JSON.toJSONString(storeDTO),
                    JSON.toJSONString(responseBase));
            return Lists.newArrayList();
        }
        return responseBase.getData().getData();
    }

    /**
     * 获取门店详情（生成冲突文件）
     *
     * @param merCode  商户编码
     * @param storeIds 门店id集合
     * @return 门店集合
     */
    private List<StoreResDTO> getStoreResDTOList(String merCode, List<String> storeIds) {
        QueryStoreDTO storeDTO = new QueryStoreDTO();
        storeDTO.setMerCode(merCode);
        if (!CollectionUtils.isEmpty(storeIds)) {
            storeDTO.setList(storeIds);
        } else {
            // 全部门店时，随机取上线且启用的门店
            storeDTO.setOnlineStatus(StoreOnLineStatus.ON_LINE.getCode());
            storeDTO.setStatus(IsvalidStatus.EFFECTIVE.getCode());
        }
        // 封板后临时增加
        List<StoreResDTO> result = new ArrayList<>(null == storeIds ? 512 : storeIds.size());
        int pageSize = 5000;
        storeDTO.setPageSize(pageSize);
        int page = 1;
        int currentPageSize;
        do {
            storeDTO.setCurrentPage(page++);
            ResponseBase<PageDTO<StoreResDTO>> responseBase = storeClient.queryStoreByCondition(storeDTO);
            if (responseBase == null || !responseBase.checkSuccess() || responseBase.getData() == null) {
                log.warn("queryStoreByCondition request = {}, res = {}", JSON.toJSONString(storeDTO),
                        JSON.toJSONString(responseBase));
                throw WarnException.builder().code(ErrorType.ACTIVITY_STORE_NULL.getCode()).
                        tipMessage(ErrorType.ACTIVITY_STORE_NULL.getMsg()).build();
            }
            List<StoreResDTO> data = responseBase.getData().getData();
            if (CollectionUtils.isEmpty(data)) {
                break;
            }
            currentPageSize = data.size();
            result.addAll(data);
        } while (currentPageSize == pageSize);

        return result;
    }

    public <T> List<T> importExcel(MultipartFile file, Class<T> pojoClass, String importDesc) {
        List<T> list;
        try {
            list = ExcelUtil.read(file, pojoClass);
            // 导入数据行数不得超过指定上限
            if(!CollectionUtils.isEmpty(list) && list.size() > LocalConst.PMT_SPEC_MAX_COUNT) {
                throw WarnException.builder().code(ErrorType.PMT_IMPORT_DATA_LIMIT.getCode()).
                        tipMessage(String.format(ErrorType.PMT_IMPORT_DATA_LIMIT.getMsg(), LocalConst.PMT_SPEC_MAX_COUNT)).build();
            }
            log.info("促销活动导入类型：{}, 导入数据条数：{}", importDesc, list.size());
        } catch (NoSuchElementException e) {
            log.error(e.getMessage(), e);
            throw WarnException.builder().code(LocalError.EXCEL_FILE_ERROR.getCode()).message(LocalError.EXCEL_FILE_ERROR.getMsg()).build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw WarnException.builder().message(e.getMessage()).build();
        }
        return list;
    }

    @Override
    public List<ActAggPreferenceRes> querySecKillData(ActivityMainReqDTO req) {
        //1、查询活动聚合数据
        ActiStoreSearchReqDTO newReq = new ActiStoreSearchReqDTO();
        newReq.setRegion(LocalConst.STATUS_ONE);
        newReq.setGroupFlag(false);
        newReq.setMerCode(req.getMerCode());
        newReq.setType(req.getPmtType());
        newReq.setIsAggPage(true);
        newReq.setLimitCountFlag(true);
        newReq.setNow(LocalDateTime.now());
        //查询所有活动的信息，包含规则信息
        if (req.getReqTime() == null) {
            LocalDateTime start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            newReq.setReqTime(start);
        }
        ResponseBase<ActAggregateResDTO> res = this.promoteClient.queryStoreActList(newReq);
        if (res == null || !res.checkSuccess() || res.getData() == null
                || CollectionUtils.isEmpty(res.getData().getPriceList())
                || CollectionUtils.isEmpty(res.getData().getTimePointList())) {
            log.warn("/act-store/queryAll param = {}，res = {}", JSON.toJSONString(newReq), JSON.toJSONString(res));
            return Lists.newArrayList();
        }
        //2、设置活动规格信息
        List<ActiPriceSearchRespDTO> secKillList = res.getData().getPriceList();
        //过滤掉没有活动商品的活动对象
        List<ActiPriceSearchRespDTO> filterSecKills = secKillList.stream()
                .filter(o -> !CollectionUtils.isEmpty(o.getInSpecIds())).collect(Collectors.toList());
        //过滤掉结束的场次
        List<ActiPriceSearchRespDTO> collect = filterSecKills.stream()
                .filter(s -> {
                    if (s.getTimePoint() == null) {
                        return true;
                    }
                    return (LocalTime.now().isAfter(s.getTimePoint()) && !s.getFlag());
                }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            filterSecKills.removeAll(collect);
        }
        if (CollectionUtils.isEmpty(filterSecKills)) {
            return Lists.newArrayList();
        }
        //按照时间节点对应排序
        filterSecKills.sort(Comparator.comparing(ActiPriceSearchRespDTO::getTimePoint));
        //如果活动场次的数量大于最大显示数量需要截取固定数量
        if (filterSecKills.size() >= LocalConst.SEC_KILL_LIST_MAX_SIZE) {
            //判断第一场是否是正在进行中的场次，是则截取前四场，否则截取前三场未开始的场次
            if (filterSecKills.get(0).getFlag()) {
                filterSecKills = filterSecKills.subList(0, 4);
            } else {
                filterSecKills = filterSecKills.subList(0, 3);
            }
        }
        List<LocalTime> timePointList = res.getData().getTimePointList();
        timePointList.sort(LocalTime::compareTo);
        filterSecKills.forEach(o -> {
            List<Long> skuIds = new ArrayList<>();
            o.getInSpecIds().forEach(x -> skuIds.add(Long.valueOf(x)));
            o.setCurrentTime(LocalDateTime.now());
            if (CollectionUtils.isEmpty(skuIds)) {
                return;
            }
            this.setPriceAggSpecInfo(o, skuIds, newReq);
            o.calculateValidStatus();
        });
        List<ActAggPreferenceRes> preferenceResList = new ArrayList<>();
        this.loopSetPointData(filterSecKills, timePointList, preferenceResList);
        return preferenceResList;
    }

    @Override
    public ActivitySpecImportResp importLimitedExcel(String merCode, Integer isHasDrug, MultipartFile excelFile) {
        // 返回结果
        ActivitySpecImportResp activitySpecImportResp = new ActivitySpecImportResp();
        // 导入限时优惠商品Excel数据
        List<ExcelLimitedSpecDTO> importExcels = this.importExcel(excelFile, ExcelLimitedSpecDTO.class, LocalConst.PMT_IMPORT_EXCEL_FILE_NAME);
        if (CollectionUtils.isEmpty(importExcels) || null == importExcels.get(0) || StringUtils.isEmpty(importExcels.get(0).getErpCode())) {
            throw WarnException.builder().code(LocalError.EXCEL_DATA_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_DATA_NULL.getMsg()).build();
        }
        // 导入失败的规格
        List<ExcelLimitedSpecDTO> importFailActivitySpec = Lists.newArrayList();
        // 数据合法性校验，校验未通过的数据归并到失败集合
        Map<String, ExcelLimitedSpecDTO> dataMap = validateLimitBaseAndRule(importFailActivitySpec, importExcels);

        // 商品编码集合
        List<String> erpCodes = importExcels.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
        // 找出重复的商品编码集
        List<String> repeatCodes = erpCodes.stream().collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                .entrySet().stream().filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        // 去除上面校验失败部分，查询并处理商品信息
        if(!CollectionUtils.isEmpty(importFailActivitySpec)) {
            List<String> failErpCodes = importFailActivitySpec.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
            erpCodes.removeAll(failErpCodes);
        }
        // 若存在重复的商品，从上面的map中取出需要正确导入的重复数据（因为map里面的数据才是全部校验通过的，产品要求重复的数据必须成功导入1条）
        if(!CollectionUtils.isEmpty(repeatCodes)) {
            repeatCodes.stream().filter(dataMap::containsKey).forEach(erpCodes::add);
        }
        // 若全部失败，生成导入失败文件并返回
        if(CollectionUtils.isEmpty(erpCodes)) {
            handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());
            return activitySpecImportResp;
        }
        // 基础校验通过，则查询商品中心商品数据，此处传入的erpCodes已去重
        activitySpecImportResp = this.handleCommodityData(merCode, isHasDrug, erpCodes, null);
        // 如果有未查询到的商品
        if (activitySpecImportResp.getFailCount() > 0) {
            // 第三部分失败数据：商品中台未查到的，即总的减去查询到的
            List<String> wareCodes = activitySpecImportResp.getActivitySpecs().stream().map(ActivitySpecImportResp.ActivitySpec::getErpCode).collect(Collectors.toList());
            erpCodes.removeAll(wareCodes);
            List<ExcelLimitedSpecDTO> queryFailList = Lists.newArrayList();
            erpCodes.forEach(code -> queryFailList.add(dataMap.get(code)));
            // 批量设置失败信息
            queryFailList.stream().forEach(item -> item.setErrorMsg(LocalError.COMMODITY_NOT_EXIST_ERROR.getMsg()));
            importFailActivitySpec.addAll(queryFailList);
        }
        // 对成功匹配到的商品进行活动规则数据封装
        if(activitySpecImportResp.getSuccessCount() > 0) {
            this.handleActList(importExcels, activitySpecImportResp);
        }
        // 导入失败的商品规格处理，生成Excel文件供前端下载
        handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());

        if (CollectionUtils.isEmpty(activitySpecImportResp.getActivitySpecs())) {
            return activitySpecImportResp;
        }
        List<ActivitySpecImportResp.ActivitySpec> collect = activitySpecImportResp.getActivitySpecs()
                .stream().sorted(Comparator.comparing(ActivitySpecImportResp.ActivitySpec::getSort)).collect(Collectors.toList());
        activitySpecImportResp.setActivitySpecs(collect);
        return activitySpecImportResp;
    }

    /**
     * @Description: 限时优惠商品导入：数据必填和合法性校验
     * @Author: Kaven
     * @Date: 2023/3/14 11:24
     * @param [importFailActivitySpec, importExcels]
     * @return dataMap
     * @Exception
     */
    private Map<String,ExcelLimitedSpecDTO> validateLimitBaseAndRule(List<ExcelLimitedSpecDTO> importFailActivitySpec, List<ExcelLimitedSpecDTO> importExcels) {
        // 返回值，用于存放导入商品数据，后面校验重复需要用到
        Map<String,ExcelLimitedSpecDTO> dataMap = Maps.newHashMap();
        // 遍历导入数据，进行合规性校验
        importExcels.stream().forEach(limitedSpecDTO -> {
            // 第一部分失败数据：基础校验不通过的
            String errMsg = ValidationUtils.getValidatorReason(limitedSpecDTO);
            if(ObjectUtil.isNotNull(errMsg)) {
                // 加入失败集合
                limitedSpecDTO.setErrorMsg(LocalError.EXCEL_IMPORT_ERROR.getMsg() + "：" + errMsg);
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
            // 第二部分失败数据：商品规则校验不通过的
            // 优惠模式(1-固定折扣,2-固定减价,3-固定价格)
            if(!(limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_DISCOUNT.getName())
                    || limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_REDUCE.getName())
                    || limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_FIXED.getName()))) {
                limitedSpecDTO.setErrorMsg(LocalError.LIMIT_MODE_ERROR.getMsg());
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
            // 优惠力度校验
            BigDecimal discount = MathUtils.setSacle(new BigDecimal(Optional.ofNullable(limitedSpecDTO.getDiscount()).orElse("0")));
            // 优惠模式为固定折扣时，优惠力度值应介于1%-99%之间
            if(limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_DISCOUNT.getName()) && (discount.intValue() < 1 || discount.intValue() > 99)) {
                limitedSpecDTO.setErrorMsg(LocalError.LIMIT_DICOUNT_RATE_ERROR.getMsg());
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
            // 优惠模式为固定价格时，金额必须大于等于0.01且不能超过上限值
            if(limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_FIXED.getName())
                    && (discount.compareTo(new BigDecimal("0.01")) < 0 || discount.compareTo(new BigDecimal(LocalConst.PMT_ACTIVITY_LIMIT_NUM)) > 0)) {
                limitedSpecDTO.setErrorMsg(String.format(LocalError.LIMIT_PRICE_ERROR.getMsg(), LocalConst.PMT_ACTIVITY_LIMIT_NUM));
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
            // 活动库存、限购数上限校验
            if((!ObjectUtils.isEmpty(limitedSpecDTO.getStock()) && Integer.valueOf(limitedSpecDTO.getStock()) > LocalConst.PMT_ACTIVITY_LIMIT_NUM)
                    || Integer.valueOf(limitedSpecDTO.getConfineNum()) > LocalConst.PMT_ACTIVITY_LIMIT_NUM) {
                limitedSpecDTO.setErrorMsg(String.format(LocalError.PRESALE_STOCK_NUM_LIMIT_ERROR.getMsg(), LocalConst.PMT_ACTIVITY_LIMIT_NUM));
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
            // 以上全部校验通过，最后进行重复性校验
            ExcelLimitedSpecDTO repeatVo = dataMap.get(limitedSpecDTO.getErpCode());
            if(ObjectUtils.isEmpty(repeatVo)) {
                dataMap.put(limitedSpecDTO.getErpCode(), limitedSpecDTO);
            } else {
                limitedSpecDTO.setErrorMsg(String.format(LocalError.ERP_CODE_DUPLICATE_ERROR.getMsg(), limitedSpecDTO.getErpCode()));
                importFailActivitySpec.add(limitedSpecDTO);
                return;
            }
        });
        return dataMap;
    }

    /**
     * @Description: 导入失败商品集合生成文件并返回下载链接
     * @Author: Kaven
     * @Date: 2023/3/13 17:07
     * @param [importFailActivitySpec, activitySpecImportResp, totalSize]
     * @return void
     * @Exception
     */
    private <T> void handleActUpdFailSpecs(List<T> importFailActivitySpec, ActivitySpecImportResp activitySpecImportResp, int totalSize) {
        // 设置导入成功和失败条数
        activitySpecImportResp.setFailCount(importFailActivitySpec.size());
        activitySpecImportResp.setSuccessCount(totalSize - importFailActivitySpec.size());
        if (CollectionUtils.isEmpty(importFailActivitySpec)) {
            return;
        }
        List<ActivityInfoImportTempDTO> list = importFailActivitySpec.stream().map(activitySpec -> {
            ActivityInfoImportTempDTO tempDTO = ModelConvertUtils.convert(activitySpec, ActivityInfoImportTempDTO.class);
            return tempDTO;
        }).collect(Collectors.toList());
        try {
            activitySpecImportResp.setOssFileRspDto(this.getOssFileRspDTO(list, LocalConst.STATUS_ZERO));
        } catch (Exception e) {
            log.error("活动商品导入：生成错误文件失败", e);
        }
    }

    @Override
    public ActivityStoreImportResp importStoreExcel(String merCode, MultipartFile excelFile) {
        List<ExcelActivityStoreDTO> importExcels = this.importExcel(excelFile, ExcelActivityStoreDTO.class, "导入活动门店");
        if (CollectionUtils.isEmpty(importExcels) || null == importExcels.get(0) || StringUtils.isEmpty(importExcels.get(0).getStCode())) {
            throw WarnException.builder().code(LocalError.EXCEL_DATA_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_DATA_NULL.getMsg()).build();
        }

        ActivityStoreImportResp activityStoreImportResp;
        // 获取模板中的门店编码
        List<String> stCodes = importExcels.stream().filter(o -> StringUtils.hasLength(o.getStCode())).map(o -> o.getStCode().trim()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stCodes)) {
            log.warn("all stCodes is null");
            activityStoreImportResp = new ActivityStoreImportResp();
            activityStoreImportResp.setSuccessCount(0);
            activityStoreImportResp.setFailCount(importExcels.size());
            List<ActivityInfoStoreImportTempDTO> list = importExcels.stream().map(excelActivityStoreDTO -> {
                ActivityInfoStoreImportTempDTO tempDTO = ModelConvertUtils.convert(excelActivityStoreDTO, ActivityInfoStoreImportTempDTO.class);
                tempDTO.setErrorMsg(LocalError.EXCEL_IMPORT_STORE_NOT_EXIST_ERROR.getMsg());
                return tempDTO;
            }).collect(Collectors.toList());
            try {
                activityStoreImportResp.setOssFileRspDto(this.getOssFileRspDTO(list, LocalConst.STATUS_ONE));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            return activityStoreImportResp;
        }

        // 初始化顺序
        for (int i = 0, length = importExcels.size(); i < length; i++) {
            importExcels.get(i).setSerialNumber(i);
        }

        // 处理门店数据
        activityStoreImportResp = this.handleCommonActivityStore(merCode, stCodes, importExcels.size());
        if (activityStoreImportResp == null) {
            activityStoreImportResp = new ActivityStoreImportResp();
            activityStoreImportResp.setSuccessCount(0);
            activityStoreImportResp.setFailCount(importExcels.size());
            return activityStoreImportResp;
        }

        // 上线门店（导入成功门店）、重复门店编码、下线门店、无效门店（编码不存在）、空编码
        List<ActivityStoreImportResp.ActivityStore> redundantActivityStores = activityStoreImportResp.getRedundantActivityStores();
        List<ActivityStoreImportResp.ActivityStore> offlineActivityStores = activityStoreImportResp.getOfflineActivityStores();
        List<ActivityStoreImportResp.ActivityStore> invalidActivityStores = activityStoreImportResp.getInvalidActivityStores();
        Integer failCount = activityStoreImportResp.getFailCount();
        if (failCount != null && failCount > 0) {
            List<ActivityInfoStoreImportTempDTO> failTempList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(redundantActivityStores)) {
                redundantActivityStores.stream().forEach(o -> {
                    ActivityInfoStoreImportTempDTO activityInfoStoreImportTempDTO = new ActivityInfoStoreImportTempDTO();
                    activityInfoStoreImportTempDTO.setStCode(o.getStoreCode());
                    activityInfoStoreImportTempDTO.setStName(o.getStoreName());
                    activityInfoStoreImportTempDTO.setErrorMsg(LocalConst.EXCEL_STORE_IMPORT_REDUNDANT_CODE_ERROR);
                    failTempList.add(activityInfoStoreImportTempDTO);
                });
            }
            if (!CollectionUtils.isEmpty(offlineActivityStores)) {
                offlineActivityStores.stream().forEach(o -> {
                    ActivityInfoStoreImportTempDTO activityInfoStoreImportTempDTO = new ActivityInfoStoreImportTempDTO();
                    activityInfoStoreImportTempDTO.setStCode(o.getStoreCode());
                    activityInfoStoreImportTempDTO.setStName(o.getStoreName());
                    activityInfoStoreImportTempDTO.setErrorMsg(LocalConst.EXCEL_STORE_IMPORT_OFFLINE_ERROR);
                    failTempList.add(activityInfoStoreImportTempDTO);
                });
            }
            if (!CollectionUtils.isEmpty(invalidActivityStores)) {
                invalidActivityStores.stream().forEach(o -> {
                    ActivityInfoStoreImportTempDTO activityInfoStoreImportTempDTO = new ActivityInfoStoreImportTempDTO();
                    activityInfoStoreImportTempDTO.setStCode(o.getStoreCode());
                    activityInfoStoreImportTempDTO.setErrorMsg(LocalConst.EXCEL_STORE_IMPORT_CODE_INVALID_ERROR);
                    failTempList.add(activityInfoStoreImportTempDTO);
                });
            }
            if (importExcels.size() > stCodes.size()) {
                // 编码为空数据放失败文件最后
                for (int i = 0, length = importExcels.size() - stCodes.size(); i < length; i++) {
                    ActivityInfoStoreImportTempDTO activityInfoStoreImportTempDTO = new ActivityInfoStoreImportTempDTO();
                    activityInfoStoreImportTempDTO.setErrorMsg(LocalConst.EXCEL_STORE_IMPORT_CODE_EMPTY_ERROR);
                    failTempList.add(activityInfoStoreImportTempDTO);
                }
            }
            try {
                activityStoreImportResp.setOssFileRspDto(this.getOssFileRspDTO(failTempList, LocalConst.STATUS_ONE));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        activityStoreImportResp.setRedundantActivityStores(null);
        activityStoreImportResp.setOfflineActivityStores(null);
        activityStoreImportResp.setInvalidActivityStores(null);
        return activityStoreImportResp;
    }

    @Override
    public ActivitySpecImportResp importDepositPresaleExcel(String merCode, Integer isHasDrug, MultipartFile excelFile, String presaleType) {
        // 返回结果
        ActivitySpecImportResp activitySpecImportResp = new ActivitySpecImportResp();
        // 校验活动类型与模板是否匹配
        checkTemplate(presaleType, excelFile.getOriginalFilename());
        // 导入预售商品Excel数据
        List<ExcelDepositPresaleSpecDTO> importExcels = this.importExcel(excelFile, ExcelDepositPresaleSpecDTO.class, LocalConst.PMT_DEPOSIT_PRESALE_IMPORT);
        if (CollectionUtils.isEmpty(importExcels) || null == importExcels.get(0) || StringUtils.isEmpty(importExcels.get(0).getErpCode())) {
            throw WarnException.builder().code(LocalError.EXCEL_DATA_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_DATA_NULL.getMsg()).build();
        }
        // 导入失败的规格
        List<ExcelDepositPresaleSpecDTO> importFailActivitySpec = Lists.newArrayList();
        // 数据合法性校验，校验未通过的数据归并到失败集合
        Map<String, ExcelDepositPresaleSpecDTO> dataMap = validateDepositPresaleBaseAndRule(importFailActivitySpec, importExcels);

        // 商品编码集合
        List<String> erpCodes = importExcels.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
        // 找出重复的商品编码集
        List<String> repeatCodes = erpCodes.stream().collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                .entrySet().stream().filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        // 去除上面校验失败部分，查询并处理商品信息
        if(!CollectionUtils.isEmpty(importFailActivitySpec)) {
            List<String> failErpCodes = importFailActivitySpec.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
            erpCodes.removeAll(failErpCodes);
        }
        // 若存在重复的商品，从上面的map中取出需要正确导入的重复数据（因为map里面的数据才是全部校验通过的，产品要求重复的数据必须成功导入1条）
        if(!CollectionUtils.isEmpty(repeatCodes)) {
            repeatCodes.stream().filter(dataMap::containsKey).forEach(erpCodes::add);
        }
        // 若全部失败，生成导入失败文件并返回
        if(CollectionUtils.isEmpty(erpCodes)) {
            handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());
            return activitySpecImportResp;
        }
        // 基础校验通过，则查询商品中心商品数据
        activitySpecImportResp = this.handleCommodityData(merCode, isHasDrug, erpCodes, null);
        // 如果有未查询到的商品
        if (activitySpecImportResp.getFailCount() > 0) {
            // 第三部分失败数据：商品中台未查到的，即总的减去查询到的
            List<String> wareCodes = activitySpecImportResp.getActivitySpecs().stream().map(ActivitySpecImportResp.ActivitySpec::getErpCode).collect(Collectors.toList());
            erpCodes.removeAll(wareCodes);
            List<ExcelDepositPresaleSpecDTO> queryFailList = Lists.newArrayList();
            erpCodes.forEach(code -> queryFailList.add(dataMap.get(code)));
            // 批量设置失败信息
            queryFailList.stream().forEach(item -> item.setErrorMsg(LocalError.COMMODITY_NOT_EXIST_ERROR.getMsg()));
            importFailActivitySpec.addAll(queryFailList);
        }
        // 对成功匹配到的商品进行活动规则数据封装
        if(activitySpecImportResp.getSuccessCount() > 0) {
            this.handleDepositPresaleSpecList(importExcels, activitySpecImportResp);
        }
        // 上传失败的商品规格处理，生成Excel文件供前端下载
        handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());

        if (CollectionUtils.isEmpty(activitySpecImportResp.getActivitySpecs())) {
            return activitySpecImportResp;
        }
        List<ActivitySpecImportResp.ActivitySpec> collect = activitySpecImportResp.getActivitySpecs()
                .stream().sorted(Comparator.comparing(ActivitySpecImportResp.ActivitySpec::getSort)).collect(Collectors.toList());
        activitySpecImportResp.setActivitySpecs(collect);
        return activitySpecImportResp;
    }

    /**
     * @Description: 定金预售商品导入：数据必填和合法性校验
     * @Author: Kaven
     * @Date: 2023/3/13 14:02
     * @param [importFailActivitySpec, importExcels]
     * @return void
     * @Exception
     */
    private Map<String,ExcelDepositPresaleSpecDTO> validateDepositPresaleBaseAndRule(List<ExcelDepositPresaleSpecDTO> importFailActivitySpec,
                                                                                     List<ExcelDepositPresaleSpecDTO> importExcels) {
        // 临时变量，用于存放导入商品数据
        Map<String,ExcelDepositPresaleSpecDTO> dataMap = Maps.newHashMap();
        // 遍历导入数据，进行合规性校验
        importExcels.stream().forEach(presaleSpecDTO -> {
            // 第一部分失败数据：基础校验不通过的
            String errMsg = ValidationUtils.getValidatorReason(presaleSpecDTO);
            if(ObjectUtil.isNotNull(errMsg)) {
                // 加入失败集合
                presaleSpecDTO.setErrorMsg(LocalError.EXCEL_IMPORT_ERROR.getMsg() + "：" + errMsg);
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 第二部分失败数据：商品规则校验不通过的
            if (!(presaleSpecDTO.getPmtMode().contains(PmtModeEnum.PRICE_DISCOUNT.getName())
                    || presaleSpecDTO.getPmtMode().contains(PmtModeEnum.PRICE_FIXED.getName()))) {
                presaleSpecDTO.setErrorMsg(LocalError.PRESALE_PMT_MODE_ERROR.getMsg());
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 限购规则：不限购、每人限购、每单限购
            if(!(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_NONE.getName())
                    || presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EACH_PERSON.getName())
                    || presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EVERY_ORDER.getName()))) {
                presaleSpecDTO.setErrorMsg(LocalError.PRESALE_LIMIT_MODE_ERROR.getMsg());
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 活动价不能小于0.01，不能超过上限值99999999
            BigDecimal actPrice = MathUtils.setSacle(new BigDecimal(presaleSpecDTO.getActPrice()));
            if(actPrice.compareTo(new BigDecimal("0.01")) < 0 || actPrice.compareTo(new BigDecimal(LocalConst.PMT_ACTIVITY_LIMIT_NUM)) > 0 ) {
                presaleSpecDTO.setErrorMsg(String.format(LocalError.PRESALE_PRICE_INVALID_ERROR.getMsg(), LocalConst.PMT_ACTIVITY_LIMIT_NUM));
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 固定比例校验
            BigDecimal discount = MathUtils.setSacle(new BigDecimal(presaleSpecDTO.getDiscount()));
            if(presaleSpecDTO.getPmtMode().contains(PmtModeEnum.PRICE_DISCOUNT.getName()) && (discount.intValue() < 1 || discount.intValue() > 99)) {
                presaleSpecDTO.setErrorMsg(LocalError.PRESALE_DEPOSIT_RATE_ERROR.getMsg());
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 固定金额校验：不能大于等于活动价，必须大于0
            if(presaleSpecDTO.getPmtMode().contains(PmtModeEnum.PRICE_FIXED.getName())
                    && (discount.compareTo(new BigDecimal("0.00")) <= 0 || discount.compareTo(actPrice) > -1)) {
                presaleSpecDTO.setErrorMsg(LocalError.PRESALE_DEPOSIT_PRICE_ERROR.getMsg());
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 活动库存、限购数上限校验
            if(Integer.valueOf(presaleSpecDTO.getStock()) > LocalConst.PMT_ACTIVITY_LIMIT_NUM
                    || Integer.valueOf(presaleSpecDTO.getConfineNum()) > LocalConst.PMT_ACTIVITY_LIMIT_NUM) {
                presaleSpecDTO.setErrorMsg(String.format(LocalError.PRESALE_STOCK_NUM_LIMIT_ERROR.getMsg(), LocalConst.PMT_ACTIVITY_LIMIT_NUM));
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 商品重复校验放到最后
            ExcelDepositPresaleSpecDTO repeatVo = dataMap.get(presaleSpecDTO.getErpCode());
            if(ObjectUtils.isEmpty(repeatVo)) {
                dataMap.put(presaleSpecDTO.getErpCode(), presaleSpecDTO);
            } else {
                presaleSpecDTO.setErrorMsg(String.format(LocalError.ERP_CODE_DUPLICATE_ERROR.getMsg(), presaleSpecDTO.getErpCode()));
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
        });
        return dataMap;
    }

    /**
     * @Description: 定金预售商品导入：数据必填和合法性校验
     * @Author: Kaven
     * @Date: 2023/3/14 09:22
     * @param [importFailActivitySpec, importExcels]
     * @return void
     * @Exception
     */
    private Map<String, ExcelFullPayPresaleSpecDTO> validateFullPresaleBaseAndRule(List<ExcelFullPayPresaleSpecDTO> importFailActivitySpec,
                                                                                   List<ExcelFullPayPresaleSpecDTO> importExcels) {
        // 临时变量，用于存放导入商品数据
        Map<String,ExcelFullPayPresaleSpecDTO> dataMap = Maps.newHashMap();
        // 遍历导入数据，进行合规性校验
        importExcels.stream().forEach(presaleSpecDTO -> {
            // 第一部分失败数据：基础校验不通过的
            String errMsg = ValidationUtils.getValidatorReason(presaleSpecDTO);
            if(ObjectUtil.isNotNull(errMsg)) {
                // 加入失败集合
                presaleSpecDTO.setErrorMsg(LocalError.EXCEL_IMPORT_ERROR.getMsg() + "：" + errMsg);
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 第二部分失败数据：商品规则校验不通过的
            // 限购规则：不限购、每人限购、每单限购
            if(!(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_NONE.getName())
                    || presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EACH_PERSON.getName())
                    || presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EVERY_ORDER.getName()))) {
                presaleSpecDTO.setErrorMsg(LocalError.PRESALE_LIMIT_MODE_ERROR.getMsg());
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 活动价不能小于0.01，不能超过上限值99999999
            BigDecimal actPrice = MathUtils.setSacle(new BigDecimal(presaleSpecDTO.getActPrice()));
            if(actPrice.compareTo(new BigDecimal("0.01")) < 0 || actPrice.compareTo(new BigDecimal(LocalConst.PMT_ACTIVITY_LIMIT_NUM)) > 0 ) {
                presaleSpecDTO.setErrorMsg(String.format(LocalError.PRESALE_PRICE_INVALID_ERROR.getMsg(), LocalConst.PMT_ACTIVITY_LIMIT_NUM));
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 活动库存、限购数上限校验
            if(Integer.valueOf(presaleSpecDTO.getStock()) > LocalConst.PMT_ACTIVITY_LIMIT_NUM
                    || Integer.valueOf(presaleSpecDTO.getConfineNum()) > LocalConst.PMT_ACTIVITY_LIMIT_NUM) {
                presaleSpecDTO.setErrorMsg(String.format(LocalError.PRESALE_STOCK_NUM_LIMIT_ERROR.getMsg(), LocalConst.PMT_ACTIVITY_LIMIT_NUM));
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
            // 商品重复校验放到最后
            ExcelFullPayPresaleSpecDTO repeatVo = dataMap.get(presaleSpecDTO.getErpCode());
            if(ObjectUtils.isEmpty(repeatVo)) {
                dataMap.put(presaleSpecDTO.getErpCode(), presaleSpecDTO);
            } else {
                presaleSpecDTO.setErrorMsg(String.format(LocalError.ERP_CODE_DUPLICATE_ERROR.getMsg(), presaleSpecDTO.getErpCode()));
                importFailActivitySpec.add(presaleSpecDTO);
                return;
            }
        });
        return dataMap;
    }

    /**
     * @Description: 分销商品导入：数据必填和合法性校验
     * @Author: Kaven
     * @Date: 2023/8/24 14:39
     * @param [commType, importFailActivitySpec, importExcels]
     * @return java.util.Map<java.lang.String,cn.hydee.ydjia.merchantmanager.dto.ExcelDistributionSpecDTO>
     * @Exception
     */
    private Map<String, ExcelDistributionSpecDTO> validateDistributeBaseAndRule(String commType, List<ExcelDistributionSpecDTO> importFailActivitySpec,
                                                                                  List<ExcelDistributionSpecDTO> importExcels) {
        // 临时变量，用于存放导入商品数据
        Map<String, ExcelDistributionSpecDTO> dataMap = Maps.newHashMap();
        // 遍历导入数据，进行合规性校验
        importExcels.stream().forEach(specDTO -> {
            // 第一部分失败数据：基础校验不通过的
            String errMsg = ValidationUtils.getValidatorReason(specDTO);
            if(ObjectUtil.isNotNull(errMsg)) {
                // 加入失败集合
                specDTO.setErrorMsg(LocalError.EXCEL_IMPORT_ERROR.getMsg() + "：" + errMsg);
                importFailActivitySpec.add(specDTO);
                return;
            }
            // 第二部分失败数据：商品规则校验不通过的
            // 佣金规则：1-按比例 2-按固定金额
            if(!(specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_DISCOUNT.getName())
                    || specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_FIXED.getName()))) {
                specDTO.setErrorMsg(LocalError.COMMISSION_MODE_ERROR.getMsg());
                importFailActivitySpec.add(specDTO);
                return;
            }
            // 分销价不能小于0.01，不能超过上限值99999999
            BigDecimal price = MathUtils.setSacle(new BigDecimal(specDTO.getDistributionPrice()));
            if(price.compareTo(new BigDecimal("0.01")) < 0 || price.compareTo(new BigDecimal(LocalConst.PMT_ACTIVITY_LIMIT_NUM)) > 0 ) {
                specDTO.setErrorMsg(String.format(LocalError.DISTRIBUTION_PRICE_INVALID_ERROR.getMsg(), LocalConst.PMT_ACTIVITY_LIMIT_NUM));
                importFailActivitySpec.add(specDTO);
                return;
            }
            // 若选择的是普通商品且按比例分佣，返佣比例不能大于30%
            if("1".equals(commType) && specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_DISCOUNT.getName())) {
                // 计算总的分佣比例
                Integer onePercentage = Integer.parseInt(specDTO.getOnePercentage());
                Integer totalPercent = onePercentage + (ObjectUtils.isEmpty(specDTO.getTwoPercentage()) ? 0 : Integer.parseInt(specDTO.getTwoPercentage()));
                if(totalPercent > 30) {
                    specDTO.setErrorMsg(LocalError.COMMISSION_PERCENT_LIMIT_ERROR.getMsg());
                    importFailActivitySpec.add(specDTO);
                    return;
                }
            }
            // 若选择的是普通商品且设置为按固定价格时，换算后的返佣比例不能大于30%
            if("1".equals(commType) && specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_FIXED.getName())) {
                // 计算分佣比例
                BigDecimal onePercentage = new BigDecimal(specDTO.getOnePercentage()).setScale(4, RoundingMode.UP);
                BigDecimal twoPercentage = ObjectUtils.isEmpty(specDTO.getTwoPercentage()) ? BigDecimal.valueOf(0.00):
                        new BigDecimal(specDTO.getTwoPercentage()).setScale(4, RoundingMode.UP);
                BigDecimal percentage = onePercentage.add(twoPercentage).divide(new BigDecimal(specDTO.getDistributionPrice()), BigDecimal.ROUND_HALF_UP);
                if(percentage.multiply(new BigDecimal(100)).compareTo(new BigDecimal(30)) > 0) {
                    specDTO.setErrorMsg(LocalError.COMMISSION_PERCENT_LIMIT_ERROR.getMsg());
                    importFailActivitySpec.add(specDTO);
                    return;
                }
            }
            // 若选择的是服务商商品且为按固定价格时，需要将一级返佣金+二级返佣金换算成返佣比例，该比例不可大于30%
            if("2".equals(commType) && specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_FIXED.getName())) {
                // 处理一二级分佣金额
                BigDecimal onePercentage = new BigDecimal(specDTO.getOnePercentage()).setScale(4, RoundingMode.UP);
                BigDecimal twoPercentage = ObjectUtils.isEmpty(specDTO.getTwoPercentage()) ? BigDecimal.valueOf(0.00) :
                        new BigDecimal(specDTO.getTwoPercentage()).setScale(4, RoundingMode.UP);
                // 计算出分佣比例
                BigDecimal percentage = onePercentage.add(twoPercentage).divide(new BigDecimal(specDTO.getDistributionPrice()), BigDecimal.ROUND_HALF_UP);
                if(percentage.multiply(new BigDecimal(100)).compareTo(new BigDecimal(30)) > 0) {
                    specDTO.setErrorMsg(LocalError.COMMISSION_PERCENT_LIMIT_ERROR.getMsg());
                    importFailActivitySpec.add(specDTO);
                    return;
                }
            }
            // 商品重复校验放到最后
            ExcelDistributionSpecDTO repeatVo = dataMap.get(specDTO.getErpCode());
            if(ObjectUtils.isEmpty(repeatVo)) {
                dataMap.put(specDTO.getErpCode(), specDTO);
            } else {
                specDTO.setErrorMsg(String.format(LocalError.ERP_CODE_DUPLICATE_ERROR.getMsg(), specDTO.getErpCode()));
                importFailActivitySpec.add(specDTO);
                return;
            }
        });
        return dataMap;
    }

    /**
     * @Description: 校验预售商品导入模板是否跟活动类型匹配
     * @Author: Kaven
     * @Date: 2023/1/10 15:10
     * @param [presaleType, originalFilename]
     * @return void
     * @Exception
     */
    private void checkTemplate(String presaleType, String originalFilename) {
        if ((originalFilename.contains(LocalConst.PMT_DEPOSIT_PRESALE_IMPORT) && !"0".equalsIgnoreCase(presaleType))
                || (originalFilename.contains(LocalConst.PMT_FULL_PAYMENT_PRESALE_IMPORT) && !"1".equalsIgnoreCase(presaleType))) {
            throw WarnException.builder().code(LocalError.PRESALE_IMPORT_TEMPLATE_ERROR.getCode()).
                    tipMessage(LocalError.PRESALE_IMPORT_TEMPLATE_ERROR.getMsg()).build();
        }
    }

    @Override
    public ActivitySpecImportResp importFullPayPresaleExcel(String merCode, Integer isHasDrug, MultipartFile excelFile, String presaleType) {
        // 返回结果
        ActivitySpecImportResp activitySpecImportResp = new ActivitySpecImportResp();
        // 校验活动类型与模板是否匹配
        checkTemplate(presaleType, excelFile.getOriginalFilename());
        // 导入预售商品Excel数据
        List<ExcelFullPayPresaleSpecDTO> importExcels = this.importExcel(excelFile, ExcelFullPayPresaleSpecDTO.class, LocalConst.PMT_FULL_PAYMENT_PRESALE_IMPORT);
        if (CollectionUtils.isEmpty(importExcels) || null == importExcels.get(0) || StringUtils.isEmpty(importExcels.get(0).getErpCode())) {
            throw WarnException.builder().code(LocalError.EXCEL_DATA_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_DATA_NULL.getMsg()).build();
        }
        // 导入失败的规格
        List<ExcelFullPayPresaleSpecDTO> importFailActivitySpec = Lists.newArrayList();
        // 数据合法性校验，校验未通过的数据归并到失败集合
        Map<String, ExcelFullPayPresaleSpecDTO> dataMap = validateFullPresaleBaseAndRule(importFailActivitySpec, importExcels);

        // 商品编码集合
        List<String> erpCodes = importExcels.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
        // 找出重复的商品编码集
        List<String> repeatCodes = erpCodes.stream().collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                .entrySet().stream().filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        // 去除上面校验失败部分，查询并处理商品信息
        if(!CollectionUtils.isEmpty(importFailActivitySpec)) {
            List<String> failErpCodes = importFailActivitySpec.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
            erpCodes.removeAll(failErpCodes);
        }
        // 若存在重复的商品，从上面的map中取出需要正确导入的重复数据（因为map里面的数据才是全部校验通过的，产品要求重复的数据必须成功导入1条）
        if(!CollectionUtils.isEmpty(repeatCodes)) {
            repeatCodes.stream().filter(dataMap::containsKey).forEach(erpCodes::add);
        }
        // 若全部失败，生成导入失败文件并返回
        if(CollectionUtils.isEmpty(erpCodes)) {
            handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());
            return activitySpecImportResp;
        }
        // 基础校验通过，则查询商品中心商品数据
        activitySpecImportResp = this.handleCommodityData(merCode, isHasDrug, erpCodes, null);
        // 如果有未查询到的商品
        if (activitySpecImportResp.getFailCount() > 0) {
            // 第三部分失败数据：商品中台未查到的，即总的减去查询到的
            List<String> wareCodes = activitySpecImportResp.getActivitySpecs().stream().map(ActivitySpecImportResp.ActivitySpec::getErpCode).collect(Collectors.toList());
            erpCodes.removeAll(wareCodes);
            List<ExcelFullPayPresaleSpecDTO> queryFailList = Lists.newArrayList();
            erpCodes.forEach(code -> queryFailList.add(dataMap.get(code)));
            // 批量设置失败信息
            queryFailList.stream().forEach(item -> item.setErrorMsg(LocalError.COMMODITY_NOT_EXIST_ERROR.getMsg()));
            importFailActivitySpec.addAll(queryFailList);
        }
        // 对成功匹配到的商品进行活动规则数据封装
        if(activitySpecImportResp.getSuccessCount() > 0) {
            this.handleFullPayPresaleSpecList(importExcels, activitySpecImportResp);
        }
        // 导入失败的商品规格处理，生成Excel文件供前端下载
        handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());

        if (CollectionUtils.isEmpty(activitySpecImportResp.getActivitySpecs())) {
            return activitySpecImportResp;
        }
        List<ActivitySpecImportResp.ActivitySpec> collect = activitySpecImportResp.getActivitySpecs()
                .stream().sorted(Comparator.comparing(ActivitySpecImportResp.ActivitySpec::getSort)).collect(Collectors.toList());
        activitySpecImportResp.setActivitySpecs(collect);
        return activitySpecImportResp;
    }

    @Override
    public ActivitySpecImportResp importDistributionExcel(String merCode, String commType, Integer isHasDrug, MultipartFile excelFile, String storeId) {
        // 返回结果
        ActivitySpecImportResp activitySpecImportResp = new ActivitySpecImportResp();
        // 导入分销活动商品Excel数据
        List<ExcelDistributionSpecDTO> importExcels = this.importExcel(excelFile, ExcelDistributionSpecDTO.class, LocalConst.FX_LABEL_NAME);
        if (CollectionUtils.isEmpty(importExcels) || null == importExcels.get(0) || StringUtils.isEmpty(importExcels.get(0).getErpCode())) {
            throw WarnException.builder().code(LocalError.EXCEL_DATA_NULL.getCode()).tipMessage(LocalError.EXCEL_DATA_NULL.getMsg()).build();
        }
        // 导入失败的规格
        List<ExcelDistributionSpecDTO> importFailActivitySpec = Lists.newArrayList();
        // 数据合法性校验，校验未通过的数据归并到失败集合
        Map<String, ExcelDistributionSpecDTO> dataMap = validateDistributeBaseAndRule(commType, importFailActivitySpec, importExcels);

        // 商品编码集合
        List<String> erpCodes = importExcels.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
        // 找出重复的商品编码集
        List<String> repeatCodes = erpCodes.stream().collect(Collectors.toMap(e -> e, e -> 1, Integer::sum))
                .entrySet().stream().filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        // 去除上面校验失败部分，查询并处理商品信息
        if(!CollectionUtils.isEmpty(importFailActivitySpec)) {
            List<String> failErpCodes = importFailActivitySpec.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(o -> o.getErpCode().trim()).collect(Collectors.toList());
            erpCodes.removeAll(failErpCodes);
        }
        // 若存在重复的商品，从上面的map中取出需要正确导入的重复数据（因为map里面的数据才是全部校验通过的，产品要求重复的数据必须成功导入1条）
        if(!CollectionUtils.isEmpty(repeatCodes)) {
            repeatCodes.stream().filter(dataMap::containsKey).forEach(erpCodes::add);
        }
        // 若全部失败，生成导入失败文件并返回
        if(CollectionUtils.isEmpty(erpCodes)) {
            handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());
            return activitySpecImportResp;
        }
        // 基础校验通过，则查询商品中心商品数据
        Map<String, Object> extraParam = Maps.newHashMap();
        extraParam.put(LocalConst.COMMODITY_TYPE, commType);
        List<String> storeIds = Lists.newArrayList();
        storeIds.add(storeId);
        extraParam.put(LocalConst.STOREIDS, storeIds);
        activitySpecImportResp = this.handleCommodityData(merCode, isHasDrug, erpCodes, extraParam);
        // 如果有未查询到的商品
        if (activitySpecImportResp.getFailCount() > 0) {
            // 第三部分失败数据：商品中台未查到的，即总的减去查询到的
            List<String> wareCodes = activitySpecImportResp.getActivitySpecs().stream().map(ActivitySpecImportResp.ActivitySpec::getErpCode).collect(Collectors.toList());
            erpCodes.removeAll(wareCodes);
            List<ExcelDistributionSpecDTO> queryFailList = Lists.newArrayList();
            erpCodes.forEach(code -> queryFailList.add(dataMap.get(code)));
            // 批量设置失败信息
            queryFailList.stream().forEach(item -> item.setErrorMsg(LocalError.COMMODITY_NOT_EXIST_ERROR.getMsg()));
            importFailActivitySpec.addAll(queryFailList);
        }
        // 对成功匹配到的商品进行活动规则数据封装
        if(activitySpecImportResp.getSuccessCount() > 0) {
            this.handleDistributionSpecList(commType, importFailActivitySpec, importExcels, activitySpecImportResp);
        }
        // 导入失败的商品规格处理，生成Excel文件供前端下载
        handleActUpdFailSpecs(importFailActivitySpec, activitySpecImportResp, importExcels.size());

        if (CollectionUtils.isEmpty(activitySpecImportResp.getActivitySpecs())) {
            return activitySpecImportResp;
        }
        List<ActivitySpecImportResp.ActivitySpec> collect = activitySpecImportResp.getActivitySpecs()
                .stream().sorted(Comparator.comparing(ActivitySpecImportResp.ActivitySpec::getSort)).collect(Collectors.toList());
        activitySpecImportResp.setActivitySpecs(collect);
        return activitySpecImportResp;
    }

    /**
     * @Description: 处理分销商品规则数据
     * @Author: Kaven
     * @Date: 2023/8/24 18:41
     * @param [commType, importFailActivitySpec, importExcels, activitySpecImportResp]
     * @return void
     * @Exception
     */
    private void handleDistributionSpecList(String commType, List<ExcelDistributionSpecDTO> importFailActivitySpec,
                                            List<ExcelDistributionSpecDTO> importExcels, ActivitySpecImportResp activitySpecImportResp) {
        // 遍历商品集合，校验云仓商品返佣比例并封装商品规则
        Iterator<ActivitySpecImportResp.ActivitySpec> it = activitySpecImportResp.getActivitySpecs().iterator();
        while (it.hasNext()) {
            ActivitySpecImportResp.ActivitySpec o = it.next();
            // 校验云仓商品返佣比例
            ExcelDistributionSpecDTO specDTO = importExcels.stream().filter(o1 -> o1.getErpCode().trim().equals(o.getErpCode())).distinct().collect(Collectors.toList()).get(0);
            // 若选择的是服务商商品且按比例分佣
            if("2".equals(commType) && specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_DISCOUNT.getName())) {
                // 计算总的分佣比例
                Integer onePercentage = Integer.parseInt(specDTO.getOnePercentage());
                Integer totalPercent = onePercentage + (ObjectUtils.isEmpty(specDTO.getTwoPercentage()) ? 0 : Integer.parseInt(specDTO.getTwoPercentage()));
                // 分销价
                BigDecimal distributionPrice = new BigDecimal(specDTO.getDistributionPrice()).setScale(4, RoundingMode.UP);
                // 供应商成本价
                BigDecimal billVaue = ObjectUtils.isEmpty(o.getBillValue()) ? new BigDecimal(0) : new BigDecimal(o.getBillValue());
                // 分销价不能小于服务商成本价
                if(distributionPrice.compareTo(billVaue) < 0) {
                    specDTO.setErrorMsg(LocalError.COMMISSION_VALUE_CANNOT_LESS_THAN_BILLVALUE.getMsg());
                    importFailActivitySpec.add(specDTO);
                    // 从集合中剔除
                    it.remove();
                    continue;
                }
                // 当分佣金额＞分销活动价-供应商成本，则弹框提醒：分佣金额不能大于连锁获得部分
                if(distributionPrice.multiply(new BigDecimal(totalPercent)).compareTo(distributionPrice.subtract(billVaue)) > 0) {
                    specDTO.setErrorMsg(LocalError.COMMISSION_VALUE_OVERFLOW_ERROR.getMsg());
                    importFailActivitySpec.add(specDTO);
                    // 从集合中剔除
                    it.remove();
                    continue;
                }
            }
            // 若选择的是服务商商品且按固定价格分佣，则必须满足：分佣价<=分销活动价-供应商成本
            if("2".equals(commType) && specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_FIXED.getName())) {
                // 处理一二级分佣价格
                BigDecimal oneAmount = new BigDecimal(specDTO.getOnePercentage()).setScale(4, RoundingMode.UP);
                BigDecimal twoAmount = ObjectUtils.isEmpty(specDTO.getTwoPercentage()) ? BigDecimal.valueOf(0.00) :
                        new BigDecimal(specDTO.getTwoPercentage()).setScale(4, RoundingMode.UP);
                // 分销价
                BigDecimal distributionPrice = new BigDecimal(specDTO.getDistributionPrice()).setScale(4, RoundingMode.UP);
                // 供应商成本价
                BigDecimal billVaue = ObjectUtils.isEmpty(o.getBillValue()) ? new BigDecimal(0) : new BigDecimal(o.getBillValue());
                // 分销价不能小于服务商成本价
                if(distributionPrice.compareTo(billVaue) < 0) {
                    specDTO.setErrorMsg(LocalError.COMMISSION_VALUE_CANNOT_LESS_THAN_BILLVALUE.getMsg());
                    importFailActivitySpec.add(specDTO);
                    // 从集合中剔除
                    it.remove();
                    continue;
                }
                if(oneAmount.add(twoAmount).compareTo(distributionPrice.subtract(billVaue)) > 0) {
                    specDTO.setErrorMsg(LocalError.COMMISSION_VALUE_OVERFLOW_ERROR.getMsg());
                    importFailActivitySpec.add(specDTO);
                    it.remove();
                    continue;
                }
            }
            // 处理佣金比例和佣金设置类型
            o.setCommissionType(CommissionModeEnum.PRICE_FIXED.getCode());
            if (specDTO.getCommissionMode().contains(CommissionModeEnum.PRICE_DISCOUNT.getName())) {
                o.setCommissionType(CommissionModeEnum.PRICE_DISCOUNT.getCode());
            }
            // 佣金比例设置为1-自定义
            o.setCommissionMode(LocalConst.STATUS_ONE);
            o.setDistributionPrice(new BigDecimal(specDTO.getDistributionPrice()).setScale(2, RoundingMode.UP));
            o.setOnePercentage(new BigDecimal(specDTO.getOnePercentage()).setScale(2, RoundingMode.UP));
            if(!StringUtils.isEmpty(specDTO.getTwoPercentage())) {
                o.setTwoPercentage(new BigDecimal(specDTO.getTwoPercentage()).setScale(2, RoundingMode.UP));
            }
            o.setSort(Integer.valueOf(specDTO.getSort()));
        }
    }

    /**
     * 处理导入门店数据
     *
     * @param merCode
     * @param stCodes
     * @param rowSize
     * @return
     */
    private ActivityStoreImportResp handleCommonActivityStore(String merCode, List<String> stCodes, int rowSize) {
        if (StringUtils.isEmpty(merCode) || CollectionUtils.isEmpty(stCodes)) {
            return null;
        }
        ActivityStoreImportResp activityStoreImportResp = new ActivityStoreImportResp();
        List<StoreResDTO> storeResDTOS = this.getStoreResDTOListByCodes(merCode, stCodes);
        // 无效的门店
        List<ActivityStoreImportResp.ActivityStore> invalidActivityStores = new ArrayList<>();
        if (CollectionUtils.isEmpty(storeResDTOS)) {
            activityStoreImportResp = new ActivityStoreImportResp();
            activityStoreImportResp.setSuccessCount(0);
            activityStoreImportResp.setFailCount(rowSize);
            // 全部无效
            stCodes.stream().forEach(stCode -> {
                ActivityStoreImportResp.ActivityStore activityStore = new ActivityStoreImportResp.ActivityStore();
                activityStore.setStoreCode(stCode);
                invalidActivityStores.add(activityStore);
            });
            activityStoreImportResp.setInvalidActivityStores(invalidActivityStores);
            return activityStoreImportResp;
        }

        // 上线门店
        List<StoreResDTO> onlineStoreResDTOS = new ArrayList<>();
        // 下线门店
        List<StoreResDTO> offlineStoreResDTOS = new ArrayList<>();
        // 存在的门店编码
        List<String> existStCodes = new ArrayList<>();
        storeResDTOS.stream().forEach(storeResDTO -> {
            if (existStCodes.contains(storeResDTO.getStCode())) {
                return;
            }
            existStCodes.add(storeResDTO.getStCode());
            if (StoreOnLineStatus.ON_LINE.getCode().equals(storeResDTO.getOnlineStatus())) {
                onlineStoreResDTOS.add(storeResDTO);
            } else {
                offlineStoreResDTOS.add(storeResDTO);
            }
        });
        // 按门店编码数量分组
        Map<String, Long> stCodeCountMap = stCodes.stream().collect(Collectors.groupingBy(s -> s, Collectors.counting()));
        // 1、导入成功的门店
        List<ActivityStoreImportResp.ActivityStore> activityStores = new ArrayList<>(onlineStoreResDTOS.size());
        // 2、重复门店编码数据
        List<ActivityStoreImportResp.ActivityStore> redundantActivityStores = new ArrayList<>();
        // 遍历上线门店，组装成功门店和重复门店编码数据
        onlineStoreResDTOS.stream().forEach(o -> {
            ActivityStoreImportResp.ActivityStore activityStore = new ActivityStoreImportResp.ActivityStore();
            activityStore.setStoreId(o.getId());
            activityStore.setStoreCode(o.getStCode());
            activityStore.setStoreName(o.getStName());
            activityStore.setAddress(o.getAddress());
            activityStore.setMobile(o.getMobile());
            activityStores.add(activityStore);
            // 判断导入成功门店数据编码是否有重复
            Long stCodeCount = stCodeCountMap.get(o.getStCode());
            if (stCodeCount != null && stCodeCount.intValue() > LocalConst.THRESHOLD_AMOUNT_MIN) {
                for (int i = 1; i < stCodeCount; i++) {
                    redundantActivityStores.add(activityStore);
                }
            }
        });
        activityStoreImportResp.setActivityStores(activityStores);
        activityStoreImportResp.setRedundantActivityStores(redundantActivityStores);

        // 3、下线的门店
        List<ActivityStoreImportResp.ActivityStore> offlineActivityStores = new ArrayList<>(offlineStoreResDTOS.size());
        offlineStoreResDTOS.stream().forEach(o -> {
            ActivityStoreImportResp.ActivityStore activityStore = new ActivityStoreImportResp.ActivityStore();
            activityStore.setStoreId(o.getId());
            activityStore.setStoreCode(o.getStCode());
            activityStore.setStoreName(o.getStName());
            offlineActivityStores.add(activityStore);
        });
        activityStoreImportResp.setOfflineActivityStores(offlineActivityStores);

        // 4、无效门店（编码不存在等）
        List<String> invalidStCodes = stCodes.stream().filter(stCode -> !existStCodes.contains(stCode)).collect(Collectors.toList());
        invalidStCodes.stream().forEach(stCode -> {
            ActivityStoreImportResp.ActivityStore activityStore = new ActivityStoreImportResp.ActivityStore();
            activityStore.setStoreCode(stCode);
            invalidActivityStores.add(activityStore);
        });
        activityStoreImportResp.setInvalidActivityStores(invalidActivityStores);
        // 设置最终导入成功和失败数量
        activityStoreImportResp.setSuccessCount(activityStores.size());
        activityStoreImportResp.setFailCount(rowSize - activityStoreImportResp.getSuccessCount());
        return activityStoreImportResp;
    }

    /**
     * 通过门店编码查询
     *
     * @param merCode
     * @param stCodes
     * @return
     */
    private List<StoreResDTO> getStoreResDTOListByCodes(String merCode, List<String> stCodes) {
        QueryStoreDTO storeDTO = new QueryStoreDTO();
        storeDTO.setMerCode(merCode);
        storeDTO.setStCodeList(stCodes);
        // storeDTO.setOnlineStatus(StoreOnLineStatus.ON_LINE.getCode());
        storeDTO.setPageSize(LocalConst.COMMON_QUERY_MAX_COUNT);
        ResponseBase<PageDTO<StoreResDTO>> responseBase = storeClient.queryStoreByCondition(storeDTO);
        if (responseBase == null || !responseBase.checkSuccess() || responseBase.getData() == null
                || CollectionUtils.isEmpty(responseBase.getData().getData())) {
            log.error("queryStoreByCondition request = {}, res = {}", JSON.toJSONString(storeDTO),
                    JSON.toJSONString(responseBase));
            return Lists.newArrayList();
        }
        return responseBase.getData().getData();
    }

    public void handleActList(List<ExcelLimitedSpecDTO> importExcels, ActivitySpecImportResp activitySpecImportResp) {
        activitySpecImportResp.getActivitySpecs().stream().forEach(o -> {
            ExcelLimitedSpecDTO limitedSpecDTO = importExcels.stream().filter(o1 -> o1.getErpCode().trim().equals(o.getErpCode())).distinct().collect(Collectors.toList()).get(0);
            if (limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_DISCOUNT.getName())) {
                o.setPmtMode(PromotionMode.PRICE_DISCOUNT.getCode());
            } else if (limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_REDUCE.getName())) {
                o.setPmtMode(PromotionMode.PRICE_REDUCE.getCode());
            } else if (limitedSpecDTO.getPmtMode().contains(PromotionMode.PRICE_FIXED.getName())) {
                o.setPmtMode(PromotionMode.PRICE_FIXED.getCode());
            }
            o.setDiscount(MathUtils.setSacle(new BigDecimal(limitedSpecDTO.getDiscount())));
            o.setConfineNum(Integer.valueOf(limitedSpecDTO.getConfineNum()));
            o.setLimitMode(PmtLimitModeEnum.LIMIT_EACH_PERSON.getCode());
            if (o.getConfineNum() == 0) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_NONE.getCode());
            }
            if (limitedSpecDTO.getStockFlag().contains(YesOrNoType.YES.getMsg())) {
                o.setStock(StringUtils.isEmpty(limitedSpecDTO.getStock()) ? 0 : Integer.valueOf(limitedSpecDTO.getStock()));
            } else {
                o.setStock(0);
            }
            o.setLeftStock(o.getStock());
            o.setSort(Integer.valueOf(limitedSpecDTO.getSort()));
        });
    }

    /**
     * @Description: 处理定金预售导入商品数据
     * @Author: Kaven
     * @Date: 2023/1/5 13:58
     * @param [importExcels, activitySpecImportResp]
     * @return void
     * @Exception
     */
    public void handleDepositPresaleSpecList(List<ExcelDepositPresaleSpecDTO> importExcels, ActivitySpecImportResp activitySpecImportResp) {
        // 商品规则数据封装
        activitySpecImportResp.getActivitySpecs().stream().forEach(o -> {
            ExcelDepositPresaleSpecDTO presaleSpecDTO = importExcels.stream().filter(o1 -> o1.getErpCode().trim().equals(o.getErpCode())).distinct().collect(Collectors.toList()).get(0);
            // 处理定金方式
            o.setPmtMode(PmtModeEnum.PRICE_FIXED.getCode());
            if (presaleSpecDTO.getPmtMode().contains(PmtModeEnum.PRICE_DISCOUNT.getName())) {
                o.setPmtMode(PmtModeEnum.PRICE_DISCOUNT.getCode());
            }
            o.setActPrice(MathUtils.setSacle(new BigDecimal(presaleSpecDTO.getActPrice())));
            o.setDiscount(MathUtils.setSacle(new BigDecimal(presaleSpecDTO.getDiscount())));
            // 限购规则：不限购、每人限购、每单限购
            if(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_NONE.getName())) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_NONE.getCode());
            } else if(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EACH_PERSON.getName())) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_EACH_PERSON.getCode());
            } else if(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EVERY_ORDER.getName())) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_EVERY_ORDER.getCode());
            }
            // 限购数
            o.setConfineNum(Integer.valueOf(presaleSpecDTO.getConfineNum()));
            // 活动库存
            o.setStock(Integer.valueOf(presaleSpecDTO.getStock()));
            // 限购数为0则表示不限购
            if (o.getConfineNum() == 0) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_NONE.getCode());
            }
            // 活动库存为0表示不限库存
            if (o.getStock() == 0) {
                o.setLimitStockFlag(YesOrNoType.NO.getCode());
            } else {
                o.setLimitStockFlag(YesOrNoType.YES.getCode());
            }
            o.setLeftStock(o.getStock());
            o.setSort(Integer.valueOf(presaleSpecDTO.getSort()));
        });
    }

    /**
     * @Description: 处理全款预售导入商品数据
     * @Author: Kaven
     * @Date: 2023/1/5 14:57
     * @param [importExcels, activitySpecImportResp]
     * @return void
     * @Exception
     */
    public void handleFullPayPresaleSpecList(List<ExcelFullPayPresaleSpecDTO> importExcels, ActivitySpecImportResp activitySpecImportResp) {
        activitySpecImportResp.getActivitySpecs().stream().forEach(o -> {
            ExcelFullPayPresaleSpecDTO presaleSpecDTO = importExcels.stream().filter(o1 -> o1.getErpCode().trim().equals(o.getErpCode())).distinct().collect(Collectors.toList()).get(0);
            o.setActPrice(MathUtils.setSacle(new BigDecimal(presaleSpecDTO.getActPrice())));
            // 限购规则：不限购、每人限购、每单限购
            if(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_NONE.getName())) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_NONE.getCode());
            } else if(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EACH_PERSON.getName())) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_EACH_PERSON.getCode());
            } else if(presaleSpecDTO.getLimitMode().contains(PmtLimitModeEnum.LIMIT_EVERY_ORDER.getName())) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_EVERY_ORDER.getCode());
            }
            // 限购数
            o.setConfineNum(Integer.valueOf(presaleSpecDTO.getConfineNum()));
            // 活动库存
            o.setStock(Integer.valueOf(presaleSpecDTO.getStock()));
            // 限购数为0则表示不限购
            if (o.getConfineNum() == 0) {
                o.setLimitMode(PmtLimitModeEnum.LIMIT_NONE.getCode());
            }
            // 活动库存为0表示不限库存
            if (o.getStock() == 0) {
                o.setLimitStockFlag(YesOrNoType.NO.getCode());
            } else {
                o.setLimitStockFlag(YesOrNoType.YES.getCode());
            }
            o.setLeftStock(o.getStock());
            o.setSort(Integer.valueOf(presaleSpecDTO.getSort()));
        });
    }

    public ActivitySpecImportResp handleCommodityData(String merCode, Integer isHasDrug, List<String> erpCodes, Map<String, Object> extraParam) {
        ActivitySpecConditionDTO conditionDTO = new ActivitySpecConditionDTO();
        // 根据商品类型查询不同的服务，commType=2时表示服务商商品
        conditionDTO.setErpCodes(erpCodes);
        conditionDTO.setMerCode(merCode);
        // 商品类型
        final String commType = CollectionUtils.isEmpty(extraParam) ? null : (String) extraParam.get(LocalConst.COMMODITY_TYPE);
        if("2".equals(commType)) {
            conditionDTO.setSpCodes((List<String>) extraParam.get(LocalConst.STOREIDS));
            conditionDTO.setIsSp(true);
        } else {
            // 其他情况均查普通商品
            conditionDTO.setPageTag(false);
            conditionDTO.setHasLabelSearch(true);
            conditionDTO.setHasDrug(YesOrNoType.YES.getCode().equals(isHasDrug));
            List<Integer> commodityTypeList = Lists.newArrayList();
            commodityTypeList.add(CommodityTypeEnum.GENERAL_COMMODITY.getCode());
            conditionDTO.setCommodityTypeList(commodityTypeList);
            conditionDTO.setFilterSrm(true);
        }
        ResponseBase<PageDTO<ActivitySpecDTO>> pageDTO = commoditySpecClient.queryActivityComm(conditionDTO);
        // 商品服务不通直接抛异常
        if (!pageDTO.checkSuccess() || pageDTO.getData() == null) {
            throw WarnException.builder().code(LocalError.COMMODITY_NOT_EXIST_ERROR.getCode()). tipMessage(LocalError.COMMODITY_NOT_EXIST_ERROR.getMsg()).build();
        }
        ActivitySpecImportResp activitySpecImportResp = new ActivitySpecImportResp();
        List<ActivitySpecImportResp.ActivitySpec> asList = new ArrayList<>();
        List<ActivitySpecDTO> specList = pageDTO.getData().getData();
        if (CollectionUtils.isEmpty(specList)) {
            activitySpecImportResp.setSuccessCount(0);
            activitySpecImportResp.setFailCount(erpCodes.size());
        } else {
            activitySpecImportResp.setSuccessCount(specList.size());
            activitySpecImportResp.setFailCount(erpCodes.size() - specList.size());
            // 门店商品map集合
            Map<String, StoreSpec> storeSpecMap = Maps.newHashMap();
            // 如果commType不为空且为1，说明是分销连锁商品的导入，需要查询门店价
            if(ObjectUtil.isNotNull(commType) && "1".equals(commType)) {
                // 提取specId集合，查询商品门店价
                List<Long> collect = specList.stream().map(ActivitySpecDTO::getSpecId).map(Long::parseLong).collect(Collectors.toList());
                // 查询门店商品信息获取门店价
                List<String> storeIds = (List<String>) extraParam.get(LocalConst.STOREIDS);
                List<StoreSpec> storeSpecs = this.queryStoreSpecs(merCode, storeIds, collect);
                storeSpecMap = storeSpecs.stream().collect(Collectors.toMap(StoreSpec::getSpecId, v -> v));
            }
            Map<String, StoreSpec> finalStoreSpecMap = storeSpecMap;
            specList.forEach(act -> {
                ActivitySpecImportResp.ActivitySpec ac = new ActivitySpecImportResp.ActivitySpec();
                ac.setCommodityId(act.getId());
                ac.setSpecId(act.getSpecId());
                ac.setName(act.getName());
                ac.setErpCode(act.getErpCode());
                ac.setMPrice(act.getMPrice());
                ac.setPicUrl(act.getPicUrl());
                // 如果图片空，赋主图
                if (StringUtils.isEmpty(ac.getPicUrl())) {
                    ac.setPicUrl(act.getMainPic());
                }
                if (!CollectionUtils.isEmpty(act.getSpecSkus())) {
                    ac.setSpecSkus(act.getSpecSkus());
                }
                // 如果是云仓商品，返回的specSku为规格信息
                if("2".equals(commType)) {
                    List<CommoditySpecSku> specSkus = Lists.newArrayList();
                    CommoditySpecSku specSku = new CommoditySpecSku();
                    specSku.setSpecId(act.getSpecId());
                    specSku.setSkuValue(act.getSpecSku());
                    specSku.setSkuKeyName(act.getSpecSku());
                    specSkus.add(specSku);
                    ac.setSpecSkus(specSkus);
                    // 新增字段：服务商成本价
                    ac.setBillValue(act.getBillValue());
                } else {
                    // 连锁门店商品需要返回门店价
                    StoreSpec storeSpec = finalStoreSpecMap.get(act.getSpecId());
                    ac.setPrice(ObjectUtils.isEmpty(storeSpec) ? null : storeSpec.getPrice());
                }
                asList.add(ac);
            });
        }
        activitySpecImportResp.setActivitySpecs(asList);
        return activitySpecImportResp;
    }

    /**
     * @Description: 根据规格ID集合查询门店商品
     * @Author: Kaven
     * @Date: 2023/9/7 18:12
     * @param [merCode, specIds]
     * @return java.util.List<cn.hydee.ydjia.merchantmanager.dto.resp.StoreSpec>
     * @Exception
     */
    private List<StoreSpec> queryStoreSpecs(String merCode, List<String> storeIds, List<Long> specIds) {
        // 获取门店价
        SpecStoreQueryDTO specDTO = new SpecStoreQueryDTO();
        specDTO.setMerCode(merCode);
        specDTO.setSpecIds(specIds);
        specDTO.setStoreIds(storeIds);
        ResponseBase<List<StoreSpec>> responseBase = this.mallGoodsClient.queryOnlineSpecList(specDTO);
        if (responseBase == null || !responseBase.checkSuccess() || CollectionUtils.isEmpty(responseBase.getData())) {
            log.warn("批量查询门店商品信息未返回数据：{} => {}", JSON.toJSONString(specDTO), JSON.toJSONString(responseBase));
            return Lists.newArrayList();
        }
        return responseBase.getData();
    }

    /**
     * 生成导入失败数据的excel
     *
     * @param errorList
     * @param importDataType（0-商品，1-门店）
     * @param <T>
     * @return
     * @throws IOException
     */
    private <T> OssFileRspDto getOssFileRspDTO(List<T> errorList, Integer importDataType) throws IOException {
        OssFileRspDto ossCommissionFileRspDto = new OssFileRspDto().setFileKey("").setFileUrl("");
        File temp = null;
        FileOutputStream fileOutputStream = null;
        FileInputStream inputStream = null;
        Integer sheetCount = LocalConst.INTEGER_ZERO;
        ExcelWriter excelWriter = null;
        WriteSheet writeSheet;
        String fileName = "导入失败商品列表";
        try {
            temp = File.createTempFile("temp", "xlsx");
            fileOutputStream = new FileOutputStream(temp, false);
            if (!CollectionUtils.isEmpty(errorList)) {
                excelWriter = EasyExcel.write(fileOutputStream).build();
                if (LocalConst.STATUS_ONE.equals(importDataType)) {
                    fileName = "导入失败门店列表";
                    writeSheet = EasyExcel.writerSheet(sheetCount++, fileName + sheetCount).head(ActivityInfoStoreImportTempDTO.class).build();
                } else {
                    writeSheet = EasyExcel.writerSheet(sheetCount++, fileName + sheetCount).head(ActivityInfoImportTempDTO.class).build();
                }
                excelWriter.write(errorList, writeSheet);
            }
            if (null != excelWriter) {
                excelWriter.finish();
            }
            inputStream = new FileInputStream(temp);
            ossCommissionFileRspDto = store(inputStream);
            return ossCommissionFileRspDto;
        } catch (Exception e) {
            log.error("生成错误文件失败", e);
        } finally {
            if (null != fileOutputStream) {
                fileOutputStream.close();
            }
            if (null != inputStream) {
                inputStream.close();
            }
            if (null != temp && temp.exists()) {
                temp.delete();
            }
        }
        return ossCommissionFileRspDto;
    }

    /**
     * 上传至os服务器
     *
     * @param inputStream
     * @return
     */
    private OssFileRspDto store(@Nullable FileInputStream inputStream) {
        //上传至oss文件服务
        String fileKey = UUID.randomUUID().toString().replaceAll("-", "");
        ossFileService.upload(inputStream, fileKey);
        //返回访问地址
        return new OssFileRspDto().setFileKey(fileKey).setFileUrl(ossFileService.downloadURL(fileKey));
    }

    /**
     * 循环设置节点场次的数据
     *
     * @param filterSecKills    带有时间节点的活动对象
     * @param timePointList     时间节点集合
     * @param preferenceResList 返回的场次集合对象
     */
    private void loopSetPointData(List<ActiPriceSearchRespDTO> filterSecKills
            , List<LocalTime> timePointList, List<ActAggPreferenceRes> preferenceResList) {
        for (LocalTime localTime : timePointList) {
            ActAggPreferenceRes actAggPreferenceRes = new ActAggPreferenceRes();
            List<ActiPriceSearchRespDTO> all = new ArrayList<>();
            List<ActiPriceSearchRespDTO> list0 = new ArrayList<>();
            List<ActiPriceSearchRespDTO> list1 = new ArrayList<>();
            for (ActiPriceSearchRespDTO dto : filterSecKills) {
                if (!localTime.equals(dto.getTimePoint())) {
                    continue;
                }
                if (LocalConst.STATUS_ZERO.equals(dto.getSort())) {
                    list0.add(dto);
                }
                if (LocalConst.STATUS_ONE.equals(dto.getSort())) {
                    list1.add(dto);
                }
            }
            if (!CollectionUtils.isEmpty(list0)) {
                all.addAll(list0);
            }
            if (!CollectionUtils.isEmpty(list1)) {
                all.addAll(list1);
            }
            if (CollectionUtils.isEmpty(all)) {
                continue;
            } else {
                all = all.stream().filter(o -> !CollectionUtils.isEmpty(o.getRuleList()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(all)) {
                    continue;
                }
            }
            actAggPreferenceRes.setTimePoint(localTime);
            int count = LocalConst.SEC_KILL_SET_GOOD_MAX_COUNT;
            ArrayList<StoreSpecNewDTO> list = new ArrayList<>();
            for (ActiPriceSearchRespDTO resp : all) {
                if (list.size() >= count) {
                    break;
                }
                if (CollectionUtils.isEmpty(resp.getSecKillGoods())) {
                    continue;
                }
                List<StoreSpecNewDTO> secKillGoods = resp.getSecKillGoods();
                for (StoreSpecNewDTO one : secKillGoods) {
                    if (list.size() >= count) {
                        break;
                    }
                    if (one == null) {
                        continue;
                    }
                    list.add(one);
                }
            }
            actAggPreferenceRes.setSecKillGoods(list);
            actAggPreferenceRes.setFlag(all.get(0).getFlag());
            preferenceResList.add(actAggPreferenceRes);
        }
    }

    /**
     * 设置活动商品规格信息
     *
     * @param preference c
     * @param skuIds     c
     * @param reqDTO     c
     */
    private void setPriceAggSpecInfo(ActiPriceSearchRespDTO preference
            , List<Long> skuIds, ActiStoreSearchReqDTO reqDTO) {
        List<StoreSpecNewDTO> data = this.queryAggCommodityInfo(skuIds, reqDTO);
        if (CollectionUtils.isEmpty(data) || preference == null
                || CollectionUtils.isEmpty(preference.getRuleList())) {
            return;
        }
        RuleKillRespDTO ruleKillRespDTO = preference.getRuleList().get(0);
        if (CollectionUtils.isEmpty(ruleKillRespDTO.getRuleList())) {
            return;
        }
        this.setAggPriceData(data, preference, reqDTO);
    }

    /**
     * 查询场次中的活动商品信息
     *
     * @param skuIds c
     * @param reqDTO c
     * @return c
     */
    private List<StoreSpecNewDTO> queryAggCommodityInfo(List<Long> skuIds, ActiStoreSearchReqDTO reqDTO) {
        SpecSearchNewDTO searchDTO = new SpecSearchNewDTO();
        searchDTO.setMerCode(reqDTO.getMerCode());
        searchDTO.setIds(skuIds);
        searchDTO.setHasSpec(true);
        searchDTO.setPageSize(skuIds.size());
        searchDTO.setFilterSrm(true);
        ResponseBase<PageDTO<StoreSpecNewDTO>> spec = commodityClient.querySpecByCode(searchDTO);
        if (spec == null || !spec.checkSuccess() || spec.getData() == null) {
            return Lists.newArrayList();
        }
        List<StoreSpecNewDTO> data = spec.getData().getData();
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(o -> {
                if (StringUtils.isEmpty(o.getPicUrl())) {
                    o.setPicUrl(o.getMainPic());
                }
                o.setPrice(o.getMPrice());
            });
        }
        return data;
    }

    /**
     * 设置场次中的活动商品价格
     *
     * @param data       c
     * @param preference c
     * @param reqDTO     c
     */
    private void setAggPriceData(List<StoreSpecNewDTO> data, ActiPriceSearchRespDTO preference
            , ActiStoreSearchReqDTO reqDTO) {
        List<RuleKillRespDTO> ruleList = preference.getRuleList();
        for (PmtSeckillResDTO pmtSeckillResDTO : preference.getRuleList().get(0).getRuleList()) {
            if (CollectionUtils.isEmpty(ruleList)) {
                ruleList = new ArrayList<>();
            } else {
                if (ruleList.size() >= reqDTO.getPageSize()) {
                    break;
                }
            }
            String specId = String.valueOf(pmtSeckillResDTO.getSpecId());
            for (StoreSpecNewDTO storeSpec : data) {
                if (storeSpec.getId() != null && specId.equals(String.valueOf(storeSpec.getId()))) {
                    storeSpec.setSpecId(specId);
                    storeSpec.setSpecValue(this.setSpecValue(storeSpec.getSpecSkuList()));
                    BigDecimal storePrice = storeSpec.getPrice();
                    BigDecimal defaultStorePrice = storeSpec.getPrice();
                    BigDecimal mPrice = storeSpec.getMPrice();
                    BigDecimal defaultMPrice = storeSpec.getMPrice();
                    Integer pmtMode = pmtSeckillResDTO.getPmtMode();
                    BigDecimal discount = pmtSeckillResDTO.getDiscount();
                    if (storePrice != null) {
                        if (PromotionMode.PRICE_DISCOUNT.getCode().equals(pmtMode)) {
                            BigDecimal multiply = storePrice.multiply(discount);
                            if (!StringUtils.isEmpty(multiply)) {
                                storePrice = multiply.multiply(new BigDecimal(LocalConst.PMT_SPIKE_RATE));
                                mPrice = defaultStorePrice;
                            }
                        }
                        if (PromotionMode.PRICE_REDUCE.getCode().equals(pmtMode)) {
                            storePrice = storePrice.subtract(discount);
                            mPrice = defaultStorePrice;
                        }
                        if (PromotionMode.PRICE_FIXED.getCode().equals(pmtMode)) {
                            storePrice = discount;
                            mPrice = defaultStorePrice;
                        }
                        storePrice = MathUtils.setSacle(storePrice);
                        if (mPrice.compareTo(new BigDecimal(LocalConst.DECIMAL_ZERO_STR)) > 0) {
                            mPrice = MathUtils.setSacle(mPrice);
                            storeSpec.setMPrice(mPrice);
                        } else {
                            mPrice = MathUtils.setSacle(defaultMPrice);
                            storeSpec.setMPrice(mPrice);
                        }
                        if (storePrice.compareTo(new BigDecimal(LocalConst.DECIMAL_ZERO_STR)) > 0) {
                            storeSpec.setPrice(storePrice);
                        } else {
                            storeSpec.setPrice(LocalConst.DECIMAL_ZERO);
                        }
                    } else {
                        if (mPrice != null) {
                            if (mPrice.compareTo(new BigDecimal(LocalConst.DECIMAL_ZERO_STR)) > 0) {
                                mPrice = MathUtils.setSacle(mPrice);
                                storeSpec.setMPrice(mPrice);
                            } else {
                                mPrice = MathUtils.setSacle(defaultMPrice);
                                storeSpec.setMPrice(mPrice);
                            }
                            storeSpec.setPrice(LocalConst.DECIMAL_ZERO);
                        }
                    }
                    break;
                }
            }
        }
        preference.setSecKillGoods(data);
    }

    /**
     * 设置规格信息
     *
     * @param specSkuList c
     * @return c
     */
    private String setSpecValue(List<CommoditySpecSku> specSkuList) {
        if (CollectionUtils.isEmpty(specSkuList)) {
            return null;
        }
        String specValue = "";
        for (CommoditySpecSku commoditySpecSku : specSkuList) {
            if (StringUtils.isEmpty(commoditySpecSku.getSkuValue())) {
                continue;
            }
            specValue = specValue.concat(commoditySpecSku.getSkuValue()).concat(LocalConst.COMMA_SPLIT);
        }
        if (!StringUtils.isEmpty(specValue)) {
            specValue = specValue.substring(0, specValue.lastIndexOf(LocalConst.COMMA_SPLIT));
        }
        return specValue;
    }

    private List<Long> getActivityListSpecDTOS(String merCode, String searchKeyWord) {
        if (StringUtils.isEmpty(merCode) || StringUtils.isEmpty(searchKeyWord)
                || StringUtils.isEmpty(searchKeyWord.trim())) {
            return Lists.newArrayList();
        }
        ActivitySpecConditionDTO dto = new ActivitySpecConditionDTO();
        dto.setMerCode(merCode);
        dto.setPageSize(0);
        dto.setPageTag(false);
        dto.setHasDrug(true);
        dto.setSearchKeyWord(searchKeyWord);
        dto.setCommodityTypeList(Collections.singletonList(CommodityTypeEnum.GENERAL_COMMODITY.getCode()));
        dto.setFilterSrm(true);
        ResponseBase<PageDTO<ActivitySpecDTO>> base = commoditySpecClient.queryActivityComm(dto);
        if (base == null || base.getData() == null || CollectionUtils.isEmpty(base.getData().getData())) {
            log.warn("queryActivityComm request = {}，res = {}", JSON.toJSONString(dto), JSON.toJSONString(base));
            return Lists.newArrayList();
        }
        return base.getData().getData().stream().map(o -> Long.valueOf(o.getSpecId())).distinct()
                .collect(Collectors.toList());
    }

}
