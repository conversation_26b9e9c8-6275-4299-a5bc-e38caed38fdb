package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.ActivityCostFreeRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantmanager.enums.CommodityTypeEnum;
import cn.hydee.ydjia.merchantmanager.enums.ErrorType;
import cn.hydee.ydjia.merchantmanager.enums.PromotionType;
import cn.hydee.ydjia.merchantmanager.enums.StatusEnums;
import cn.hydee.ydjia.merchantmanager.feign.ActivityInfoClient;
import cn.hydee.ydjia.merchantmanager.feign.CommoditySpecClient;
import cn.hydee.ydjia.merchantmanager.feign.StoreClient;
import cn.hydee.ydjia.merchantmanager.service.ActivityCostFreeService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-29
 */
@Service
@Slf4j
public class ActivityCostFreeServiceImpl implements ActivityCostFreeService {

    @Autowired
    private ActivityInfoClient activityInfoClient;
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private CommoditySpecClient commoditySpecClient;

    @Override
    public ResponseBase<String> createActivity(ActivityCostFreeReqDTO createDTO) {
        ActiCreateDTO activityStoreDTOS = this.getActivityStoreDTOS(createDTO);
        return activityInfoClient.createActivity(activityStoreDTOS);
    }

    @Override
    public ResponseBase<String> updateActivity(ActivityCostFreeReqDTO updateDTO) {
        ActiCreateDTO activityStoreDTOS = this.getActivityStoreDTOS(updateDTO);
        return activityInfoClient.updateActivity(activityStoreDTOS);
    }

    @Override
    public ActivityCostFreeRespDTO queryActivityById(Long id) {
        ResponseBase<ActivityCostFreeRespDTO> res = activityInfoClient.queryCostFreeActivity(id);
        if (res != null && res.getData() != null) {
            ActivityCostFreeRespDTO data = res.getData();
            if (LocalConst.STATUS_ZERO.equals(data.getStoreSelection()) || CollectionUtils.isEmpty(data.getStoreIds())) {
                data.setStoreResDTOList(new ArrayList<>());
            } else {
                data.setStoreResDTOList(this.getStoreResDTOList(data.getMerCode(), data.getStoreIds()));
            }

            List<String> skuIds = data.getSpecIds();
            if (LocalConst.STATUS_ZERO.equals(data.getSpecSelection()) || CollectionUtils.isEmpty(skuIds)) {
                data.setCommList(new ArrayList<>());
            } else {
                List<Long> collect = skuIds.stream().map(Long::parseLong).collect(Collectors.toList());
                List<ActivitySpecDTO> specData = this.getActivitySpecDTOS(data.getMerCode(), collect);
                data.setCommList(specData);
            }
            return data;
        }
        return null;
    }

    private ActivityStoreDTO getStoreAndSpecData(ActivityCostFreeReqDTO dto) {
        ActivityStoreDTO storeSpecList = new ActivityStoreDTO();
        List<StoreResDTO> storeResDTOList;
        if (LocalConst.STATUS_ZERO.equals(dto.getStoreSelection())) {
            storeResDTOList = new ArrayList<>();
        } else {
            storeResDTOList = this.getStoreResDTOList(dto.getMerCode(), dto.getStoreIds());
            if (CollectionUtils.isEmpty(storeResDTOList)) {
                throw WarnException.builder().code(ErrorType.ACTIVITY_STORE_NULL.getCode()).tipMessage(ErrorType.ACTIVITY_STORE_NULL.getMsg()).build();
            }
        }
        List<ActivitySpecDTO> activitySpecList;
        if (LocalConst.STATUS_ZERO.equals(dto.getSpecSelection())) {
            activitySpecList = new ArrayList<>();
        } else {
            activitySpecList = this.getActivitySpecDTOS(dto.getMerCode(), dto.getSpecIds());
            if (CollectionUtils.isEmpty(activitySpecList)) {
                throw WarnException.builder().code(ErrorType.ACTIVITY_SPEC_NULL.getCode()).tipMessage(ErrorType.ACTIVITY_SPEC_NULL.getMsg()).build();
            }
        }
        storeSpecList.setSpecList(activitySpecList);
        storeSpecList.setStoreResDTOList(storeResDTOList);
        return storeSpecList;
    }

    private ActiCreateDTO getActivityStoreDTOS(ActivityCostFreeReqDTO dto) {
        // 获取活动门店和商品数据
        ActivityStoreDTO storeSpeList = this.getStoreAndSpecData(dto);
        ActiCreateDTO actiCreateDTO = new ActiCreateDTO();
        BeanUtils.copyProperties(dto, actiCreateDTO);
        // 默认不投放状态
        actiCreateDTO.setStatus(StatusEnums.STOP_USE.getCode());
        // 不限参与次数
        actiCreateDTO.setLimitTimes(0);
        // 标签名
        actiCreateDTO.setLabelName(PromotionType.COST_FREE.getName());
        actiCreateDTO.setStoreSpeList(storeSpeList);
        return actiCreateDTO;
    }

    private List<StoreResDTO> getStoreResDTOList(String merCode, List<String> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Lists.newArrayList();
        }
        QueryStoreDTO storeDTO = new QueryStoreDTO();
        storeDTO.setMerCode(merCode);
        storeDTO.setList(storeIds);
        storeDTO.setPageSize(LocalConst.COMMON_QUERY_MAX_COUNT);
        ResponseBase<PageDTO<StoreResDTO>> responseBase = storeClient.queryStoreByCondition(storeDTO);
        if (responseBase == null || !responseBase.checkSuccess() || responseBase.getData() == null
                || CollectionUtils.isEmpty(responseBase.getData().getData())) {
            log.warn("queryStoreByCondition request = {}, res = {}", JSON.toJSONString(storeDTO), JSON.toJSONString(responseBase));
            return Lists.newArrayList();
        }
        return responseBase.getData().getData();
    }

    private List<ActivitySpecDTO> getActivitySpecDTOS(String merCode, List<Long> spedIds) {
        if (CollectionUtils.isEmpty(spedIds)) {
            return Lists.newArrayList();
        }
        ActivitySpecConditionDTO dto = new ActivitySpecConditionDTO();
        dto.setMerCode(merCode);
        dto.setSkuIds(spedIds);
        dto.setPageSize(0);
        dto.setPageTag(false);
        dto.setHasDrug(true);
        dto.setCommodityTypeList(Collections.singletonList(CommodityTypeEnum.GENERAL_COMMODITY.getCode()));
        ResponseBase<PageDTO<ActivitySpecDTO>> base = commoditySpecClient.queryActivityComm(dto);
        if (base == null || base.getData() == null || CollectionUtils.isEmpty(base.getData().getData())) {
            log.warn("queryActivityComm request = {}，res = {}", JSON.toJSONString(dto), JSON.toJSONString(base));
            return Lists.newArrayList();
        }
        return base.getData().getData();
    }

}
