package cn.hydee.ydjia.merchantmanager.service.impl.flowdataexp;

import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.resp.memberflow.RankingListQueryAreaResp;
import cn.hydee.ydjia.merchantmanager.dto.resp.memberflow.RankingListQueryResp;
import cn.hydee.ydjia.merchantmanager.service.MemberFlowDataService;
import cn.hydee.ydjia.merchantmanager.service.impl.AbstractExportTaskProcessor;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import cn.hydee.ydjia.merchantmanager.util.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.FLOW_RANKING_AREA_DATA_EXPORT;

/**
 * @Name: AreaRankingDataExportServiceImpl
 * @Description: 片区流量排行榜数据导出service实现类
 * @Author: Kaven
 * @Date: 2023/3/29 15:12
 */
@Service(FLOW_RANKING_AREA_DATA_EXPORT)
@Slf4j
public class AreaRankingDataExportServiceImpl extends AbstractExportTaskProcessor<RankingListQueryAreaResp> {

    @Autowired
    private MemberFlowDataService memberFlowDataService;


    @Override
    public List<RankingListQueryAreaResp> getExportDateList(YdjTask task) {
        List<RankingListQueryResp> list = memberFlowDataService.getExportDataList(task);
        return list.stream().map(e -> {
            RankingListQueryAreaResp respDTO = new RankingListQueryAreaResp();
            BeanUtils.copyProperties(e, respDTO);
            // 处理uv价值精度，四舍五入保留2位小数
            respDTO.setUvValue(MathUtils.setSacle(respDTO.getUvValue(), LocalConst.SCALE));
            return respDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Class<RankingListQueryAreaResp> getExportClass() {
        return RankingListQueryAreaResp.class;
    }

    @Override
    public String getFileNamePrefix() {
        return "微商城流量数据分析-片区流量排行榜明细";
    }

}
