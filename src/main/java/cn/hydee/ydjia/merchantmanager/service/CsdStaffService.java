package cn.hydee.ydjia.merchantmanager.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantmanager.dto.csd.CsdStaffAddReq;
import cn.hydee.ydjia.merchantmanager.dto.csd.CsdStaffReq;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryEmpDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.EmployeeResDTO;

public interface CsdStaffService {

    /**
     * 修改客服状态（新）
     *
     * @param req 入参
     * @return 是否成功
     */
    ResponseBase<Boolean> modifyStaffStatus(CsdStaffReq req);

    /**
     * 搜索员工信息（分页）
     *
     * @param req 入参
     * @return 数据
     */
    ResponseBase<PageDTO<EmployeeResDTO>> searchEmployee(QueryEmpDTO req);

    /**
     * 添加客服
     *
     * @param req 入参
     * @return 是否成功
     */
    ResponseBase<Boolean> addStaff(CsdStaffAddReq req);
}
