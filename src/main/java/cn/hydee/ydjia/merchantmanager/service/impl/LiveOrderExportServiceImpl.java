package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.resp.OrderExcelResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.LIVE_ORDER_EXPORT;

/**
 * 直播订单导出
 */
@Service(LIVE_ORDER_EXPORT)
public class LiveOrderExportServiceImpl extends AbstractExportTaskProcessor<OrderExcelResDTO> {
    @Autowired
    private OrderExportServiceImpl orderExportService;

    @Override
    public Class<OrderExcelResDTO> getExportClass() {
        return OrderExcelResDTO.class;
    }

    @Override
    public List<OrderExcelResDTO> getExportDateList(YdjTask task) {
        return orderExportService.getExportDateList(task);
    }

    @Override
    public String getFileNamePrefix() {
        return "直播订单列表";
    }
}
