package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.config.SpringContextHolder;
import cn.hydee.ydjia.merchantmanager.controller.ActivityPrizeManageController;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.LotteryPrizeDTO;
import cn.hydee.ydjia.merchantmanager.dto.LotteryPrizeExportDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.LotteryEntityPrizeReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberDTO;
import cn.hydee.ydjia.merchantmanager.enums.ActivityTemplateEnum;
import cn.hydee.ydjia.merchantmanager.feign.ActivityPrizeManageClient;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import cn.hydee.ydjia.merchantmanager.util.TBean;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.PRIZE_LOG_EXPORT;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/07/08 17:14
 */
@Service(PRIZE_LOG_EXPORT)
@Slf4j
public class PrizeLogExportService extends AbstractExportTaskProcessor<LotteryPrizeExportDTO> {
    @Autowired
    private ActivityPrizeManageClient activityPrizeManageClient;

    @Override
    public Class<LotteryPrizeExportDTO> getExportClass() {
        return LotteryPrizeExportDTO.class;
    }

    @Override
    public List<LotteryPrizeExportDTO> getExportDateList(YdjTask task) {
        LotteryEntityPrizeReqDTO exportDTO = JSONObject.parseObject(task.getCommand(), LotteryEntityPrizeReqDTO.class);
        log.debug("[ExportPrizeLog] begin query export list request={}", exportDTO);

        int initSize = 5000, currentPage = 1, totalPage = 0;

        List<LotteryPrizeExportDTO> exportList = null;

        do {
            exportDTO.setCurrentPage(currentPage);
            exportDTO.setPageSize(initSize);
            ResponseBase<PageDTO<LotteryPrizeDTO>> responseBase = activityPrizeManageClient.queryLotteryEntityPrizeList(exportDTO);
            if (!responseBase.checkSuccess()) {
                log.error("[ExportPrizeLog] query prize log error {} response={} {} {}", exportDTO, responseBase.getCode(),
                        responseBase.getData(), responseBase.getData());
                throw WarnException.builder().code(LocalError.EXCEL_EXPORT_ERROR.getCode()).
                        tipMessage(LocalError.EXCEL_EXPORT_ERROR.getMsg()).build();
            }

            List<LotteryPrizeDTO> prizeList;
            if (Objects.nonNull(responseBase.getData())
                    && !CollectionUtils.isEmpty(prizeList = responseBase.getData().getData())) {
                totalPage = responseBase.getData().getTotalPage();

                if (CollectionUtils.isEmpty(exportList)) {
                    exportList = Lists.newArrayListWithCapacity(responseBase.getData().getTotalCount());
                }

                Map<Long, MemberDTO> memberMapping =
                        SpringContextHolder.getBean(ActivityPrizeManageController.class).batchMappingUserInfo(task.getMerCode(), prizeList);

                exportList.addAll(prizeList.parallelStream().map(lotteryPrizeDTO -> {
                    LotteryPrizeExportDTO prizeExportDTO = TBean.copy(LotteryPrizeExportDTO.class, lotteryPrizeDTO);
                    prizeExportDTO.setActivityType(ActivityTemplateEnum.getDescriptionByCode(prizeExportDTO.getActivityType()));
                    prizeExportDTO.setHx(lotteryPrizeDTO.getIsHx());
                    prizeExportDTO.withHxTime(lotteryPrizeDTO.getHxTime());
                    prizeExportDTO.withPrizeTime(lotteryPrizeDTO.getPrizeTime());

                    prizeExportDTO.withActivityBeginTime(lotteryPrizeDTO.getActivityBeginTime());
                    prizeExportDTO.withActivityEndTime(lotteryPrizeDTO.getActivityEndTime());
                    prizeExportDTO.setMemberCardId(memberMapping.getOrDefault(lotteryPrizeDTO.getUserId(), new MemberDTO()).getMemberCard());
                    prizeExportDTO.setMemberName(memberMapping.getOrDefault(lotteryPrizeDTO.getUserId(), new MemberDTO()).getMemberName());
                    prizeExportDTO.setMemberPhone(memberMapping.getOrDefault(lotteryPrizeDTO.getUserId(), new MemberDTO()).getMemberPhone());
                    return prizeExportDTO;
                }).collect(Collectors.toList()));

            }
            currentPage++;

        } while (currentPage <= totalPage);

        if (CollectionUtils.isEmpty(exportList)) {
            throw WarnException.builder().code(LocalError.EXCEL_ROW_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_ROW_NULL.getMsg()).build();
        }

        //Flip
        List<LotteryPrizeExportDTO> export = Lists.newArrayListWithExpectedSize(exportList.size());
        for (int i = exportList.size() - 1; i >= 0; i--) {
            export.add(exportList.get(i));
        }


        return export;
    }

    @Override
    public String getFileNamePrefix() {
        return "中奖记录列表";
    }
}
