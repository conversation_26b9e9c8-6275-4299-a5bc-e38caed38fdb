package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.req.ActivityReceiveListReqDto;
import cn.hydee.ydjia.merchantmanager.dto.resp.ActivityReceiveDetailResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MarketReceiveRecordReportExportDTO;
import cn.hydee.ydjia.merchantmanager.feign.MarketingActivitiesClient;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.MARKET_SCRAP_RECEIVE_RECORD_EXPORT;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.EXPORT_COUNT_ERROR;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.EXPORT_NUM_EXCEEDS_LIMIT;

/**
 * @description:
 * @author: lisp
 * @time: 2022/11/01 17:07
 */

@Service(MARKET_SCRAP_RECEIVE_RECORD_EXPORT)
@Slf4j
public class MarketScrapReceiveRecordExportServiceImpl extends AbstractExportTaskProcessor<MarketReceiveRecordReportExportDTO> {
    @Autowired
    private MarketingActivitiesClient marketingActivitiesClient;

    @Value(("${memberExportLimit}"))
    private Integer memberExportLimit;

    @Override
    public Class<MarketReceiveRecordReportExportDTO> getExportClass() {
        return MarketReceiveRecordReportExportDTO.class;
    }

    @Override
    public List<MarketReceiveRecordReportExportDTO> getExportDateList(YdjTask task) {
//        validateExportLimitCount(task);
        ActivityReceiveListReqDto receiveListReqDto = JSONObject.parseObject(task.getCommand(), ActivityReceiveListReqDto.class);
        log.info("[MARKET_SCRAP_RECEIVE_RECORD_EXPORT] req={}", receiveListReqDto);
        int initSize = 2000, currentPage = 1, totalPage = 0;
        List<MarketReceiveRecordReportExportDTO> exportList = null;

        do {
            receiveListReqDto.setCurrentPage(currentPage);
            receiveListReqDto.setPageSize(initSize);
            ResponseBase<PageDTO<ActivityReceiveDetailResDTO>>  responseBase =  marketingActivitiesClient.activityReceiveList(receiveListReqDto);
            if (!responseBase.checkSuccess()) {
                log.error("[MARKET_SCRAP_RECEIVE_RECORD_EXPORT] query error {}  response={} {}", receiveListReqDto, responseBase.getCode(), responseBase.getMsg());
                throw WarnException.builder().code(LocalError.EXCEL_EXPORT_ERROR.getCode()).
                        tipMessage(LocalError.EXCEL_EXPORT_ERROR.getMsg()).build();
            }

            if (Objects.nonNull(responseBase.getData()) && !CollectionUtils.isEmpty(responseBase.getData().getData())) {

                PageDTO<ActivityReceiveDetailResDTO> pageDTO = responseBase.getData();

                if (pageDTO.getTotalCount() > memberExportLimit) {
                    throw WarnException.builder().code(EXPORT_NUM_EXCEEDS_LIMIT.getCode())
                            .tipMessage(String.format(EXPORT_NUM_EXCEEDS_LIMIT.getMsg(), memberExportLimit)).build();
                }
                List<ActivityReceiveDetailResDTO> data = pageDTO.getData();
                if (Objects.isNull(exportList)) {
                    exportList = Lists.newArrayListWithExpectedSize(responseBase.getData().getTotalCount());
                }
                totalPage = pageDTO.getTotalPage();
                log.info("[MARKET_SCRAP_RECEIVE_RECORD_EXPORT] merCode={} total record export count is {}", task.getMerCode(), totalPage);

                for (ActivityReceiveDetailResDTO item : data) {
                        MarketReceiveRecordReportExportDTO marketReceiveRecordReportExportDTO = new MarketReceiveRecordReportExportDTO();
                        BeanUtils.copyProperties(item, marketReceiveRecordReportExportDTO);
                        exportList.add(marketReceiveRecordReportExportDTO);
                }
            }
            currentPage++;
        } while (currentPage <= totalPage);

        if (CollectionUtils.isEmpty(exportList)) {
            throw WarnException.builder().code(LocalError.EXCEL_ROW_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_ROW_NULL.getMsg()).build();
        }

        return exportList;
    }

    private void validateExportLimitCount(YdjTask task) {
        if (foundExportCountToday(task) >= exportNumLimitToday) {
            throw WarnException.builder().code(EXPORT_COUNT_ERROR.getCode())
                    .message(String.format(EXPORT_COUNT_ERROR.getMsg(), exportNumLimitToday)).build();
        }
    }

    @Override
    public String getFileNamePrefix() {
        log.info("[MARKET_SCRAP_RECEIVE_RECORD_EXPORT] FileNamePrefix={}", "参与记录报表");
        return "刮刮乐活动参与记录";
    }
}
