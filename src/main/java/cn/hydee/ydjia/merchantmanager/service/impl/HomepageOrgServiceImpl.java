package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.ydjia.merchantmanager.domain.HomepageOrg;
import cn.hydee.ydjia.merchantmanager.repository.HomepageOrgRepo;
import cn.hydee.ydjia.merchantmanager.service.HomepageOrgService;
import cn.hydee.ydjia.merchantmanager.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @create 2021/6/10
 * @desc
 */
@Slf4j
@Service
public class HomepageOrgServiceImpl extends ServiceImpl<HomepageOrgRepo, HomepageOrg> implements HomepageOrgService {

    @DS(LocalConst.DB_MANAGER_MASTER)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateStatus(HomepageOrg homepageOrg) {
        return this.baseMapper.updateStatus(homepageOrg);
    }
}
