package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.req.BatchSmsTaskDetailListQueryDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.QueryMembersReqDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.BatchSmsTaskDetailListResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.export.SmsIngTaskDetailExport;
import cn.hydee.ydjia.merchantmanager.enums.ErrorType;
import cn.hydee.ydjia.merchantmanager.feign.HcSmsClient;
import cn.hydee.ydjia.merchantmanager.feign.MemberInfoClient;
import cn.hydee.ydjia.merchantmanager.util.AssertUtils;
import cn.hydee.ydjia.merchantmanager.util.LocalError;
import cn.hydee.ydjia.merchantmanager.util.ModelConvertUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.SMS_ING_TASK_DETAIL_EXPORT;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.CONNECTED_ERROR;
import static cn.hydee.ydjia.merchantmanager.util.LocalError.EXPORT_COUNT_ERROR;

/**
 * 短信任务发送中详情导出
 *
 * <AUTHOR>
 * @date 2022/8/8 14:32
 */
@Service(SMS_ING_TASK_DETAIL_EXPORT)
@Slf4j
public class SmsIngTaskDetailExportServiceImpl extends AbstractExportTaskProcessor<SmsIngTaskDetailExport> {


    @Autowired
    private HcSmsClient hcSmsClient;

    @Autowired
    private MemberInfoClient memberInfoClient;


    @Override
    protected Class<SmsIngTaskDetailExport> getExportClass() {
        return SmsIngTaskDetailExport.class;
    }

    @Override
    public List<SmsIngTaskDetailExport> getExportDateList(YdjTask task) {
        validateExportLimitCount(task);
        BatchSmsTaskDetailListQueryDTO reqDTO = JSONObject.parseObject(task.getCommand(), BatchSmsTaskDetailListQueryDTO.class);
        log.info("[SmsTaskDetailExport ] req={}", reqDTO);

        int initSize = 2000, currentPage = 1, totalPage = 0;
        List<SmsIngTaskDetailExport> exportList = null;

        do {
            reqDTO.setCurrentPage(currentPage);
            reqDTO.setPageSize(initSize);
            ResponseBase<PageDTO<BatchSmsTaskDetailListResDTO>> responseBase = buildSmsTaskDetail(reqDTO);
            if (!responseBase.checkSuccess()) {
                log.error("[SmsTaskDetailExport] query error {}  response={} {}", reqDTO, responseBase.getCode(), responseBase.getMsg());
                throw WarnException.builder().code(LocalError.EXCEL_EXPORT_ERROR.getCode()).
                        tipMessage(LocalError.EXCEL_EXPORT_ERROR.getMsg()).build();
            }
            List<BatchSmsTaskDetailListResDTO> queryList;

            if (Objects.nonNull(responseBase.getData())
                    && !CollectionUtils.isEmpty(queryList = responseBase.getData().getData())) {

                if (Objects.isNull(exportList)) {
                    exportList = Lists.newArrayListWithExpectedSize(responseBase.getData().getTotalCount());
                }
                totalPage = responseBase.getData().getTotalPage();
                log.info("[getExportDateList] merCode={} total export count is {}", task.getMerCode(), totalPage);

                for (BatchSmsTaskDetailListResDTO smsTaskDetailListResDTO : queryList) {
                    SmsIngTaskDetailExport exportDTO = ModelConvertUtils.convert(smsTaskDetailListResDTO, SmsIngTaskDetailExport.class);
                    AssertUtils.notNull(exportDTO, ErrorType.EXPORT_RECORD_NULL_ERROR);

                    exportList.add(exportDTO);
                }
            }
            currentPage++;
        } while (currentPage <= totalPage);

        if (CollectionUtils.isEmpty(exportList)) {
            throw WarnException.builder().code(LocalError.EXCEL_ROW_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_ROW_NULL.getMsg()).build();
        }

        return exportList;
    }


    private void validateExportLimitCount(YdjTask task) {
        if (foundExportCountToday(task) >= exportNumLimitToday) {
            throw WarnException.builder().code(EXPORT_COUNT_ERROR.getCode())
                    .message(String.format(EXPORT_COUNT_ERROR.getMsg(), exportNumLimitToday)).build();
        }
    }


    private ResponseBase<PageDTO<BatchSmsTaskDetailListResDTO>> buildSmsTaskDetail(BatchSmsTaskDetailListQueryDTO reqDTO) {

        ResponseBase<PageDTO<BatchSmsTaskDetailListResDTO>> dtoResponseBase = ResponseBase.success();
        PageDTO<BatchSmsTaskDetailListResDTO> dtoPageDTO = new PageDTO<>();
        ResponseBase responseBase = hcSmsClient.batchSendSmsDetailList(reqDTO);
        if (responseBase.checkSuccess()
                && responseBase.getData() != null) {
            JSONObject pageDTO = JSON.parseObject(responseBase.getData().toString());
            List<BatchSmsTaskDetailListResDTO> jsonArray = JSON.parseArray(pageDTO.getString("data"), BatchSmsTaskDetailListResDTO.class);
            List<String> memberPhones = Lists.newArrayList();
            jsonArray.forEach(item -> {
                if (!StringUtils.isEmpty(item.getMobile())) {
                    memberPhones.add(item.getMobile());
                }
            });
            QueryMembersReqDTO queryMembersReqDTO = new QueryMembersReqDTO();
            queryMembersReqDTO.setMerCode(reqDTO.getMerCode());
            queryMembersReqDTO.setMemberPhones(memberPhones);
            Map<String, MemberDTO> memberInfoMap = Maps.newHashMap();
            ResponseBase<List<MemberDTO>> memberInfoRes = memberInfoClient.queryMemberInfos(queryMembersReqDTO);
            if (!memberInfoRes.checkSuccess()) {
                log.info("短信发送详情发送中导出任务查询会员信息失败, param={}, result={}", JSON.toJSONString(queryMembersReqDTO), JSON.toJSONString(memberInfoRes));
                throw WarnException.builder().code(CONNECTED_ERROR.getCode()).message(CONNECTED_ERROR.getMsg()).build();
            }
            if (!CollectionUtils.isEmpty(memberInfoRes.getData())) {
                for (MemberDTO memberDTO : memberInfoRes.getData()) {
                    memberInfoMap.put(memberDTO.getMemberPhone(), memberDTO);
                }
            }

            for (BatchSmsTaskDetailListResDTO batchSmsTaskDetailListResDTO : jsonArray) {
                if (!StringUtils.isEmpty(batchSmsTaskDetailListResDTO.getMobile())) {
                    MemberDTO memberDTO = memberInfoMap.get(batchSmsTaskDetailListResDTO.getMobile());
                    if (Objects.nonNull(memberDTO)) {
                        batchSmsTaskDetailListResDTO.setMemberName(StringUtils.isEmpty(memberDTO.getMemberName()) ? "" : memberDTO.getMemberName());
                        batchSmsTaskDetailListResDTO.setMemberCard(StringUtils.isEmpty(memberDTO.getMemberCard()) ? "" : memberDTO.getMemberCard());
                    }
                }
            }

            dtoPageDTO.setData(jsonArray);
            dtoPageDTO.setTotalPage(pageDTO.getInteger("totalPage"));
            dtoPageDTO.setTotalCount(pageDTO.getInteger("totalCount"));
            dtoResponseBase.setData(dtoPageDTO);
        }
        return dtoResponseBase;
    }


    @Override
    public String getFileNamePrefix() {
        return "短信发送中任务详情";
    }

}
