package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.service.OssFileService;
import cn.hydee.ydjia.merchantmanager.dto.ExcelGrossProfitItemDTO;
import cn.hydee.ydjia.merchantmanager.dto.ExcelMemberLabelDTO;
import cn.hydee.ydjia.merchantmanager.dto.MemberBaseInfoDTO;
import cn.hydee.ydjia.merchantmanager.dto.MemberFileImportTempDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.MemberInfoItemImportResp;
import cn.hydee.ydjia.merchantmanager.dto.resp.market.payGift.MemberInfoImportResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.market.payGift.MemberInfoImportSuccessResDTO;
import cn.hydee.ydjia.merchantmanager.excel.BaseExcelData;
import cn.hydee.ydjia.merchantmanager.excel.ExcelInfo;
import cn.hydee.ydjia.merchantmanager.feign.MemberFileImportClient;
import cn.hydee.ydjia.merchantmanager.feign.MemberInfoClient;
import cn.hydee.ydjia.merchantmanager.feign.MemberLabelClient;
import cn.hydee.ydjia.merchantmanager.service.ExcelCommonService;
import cn.hydee.ydjia.merchantmanager.service.MemberFileImportService;
import cn.hydee.ydjia.merchantmanager.util.*;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import io.micrometer.core.lang.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantmanager.enums.ErrorType.COUPON_ERROR_11;
import static com.baomidou.mybatisplus.core.toolkit.StringPool.SLASH;

;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 会员标签导入实现类
 * @Date 2022/1/07 9:52
 */
@Service
@Slf4j
public class MemberFileImportServiceImpl implements MemberFileImportService {

    @Resource
    private OssFileService ossFileService;

    @Autowired
    private MemberFileImportClient memberFileImportClient;

    @Autowired
    private MemberLabelClient memberLabelClient;

    @Autowired
    private MemberInfoClient memberInfoClient;

    @Autowired
    private ExcelCommonService excelCommonService;

    @Value("${spring.application.name}")
    private String appName;

    @Override
    public void importMemberLabelExcel(String merCode, MemberFileImportSaveReqDTO reqDTO, MultipartFile excelFile) {
        List<ExcelMemberLabelDTO> importExcels = this.importExcel(excelFile, 0, 1, ExcelMemberLabelDTO.class);
        if (CollectionUtils.isEmpty(importExcels) || null == importExcels.get(0)) {
            throw WarnException.builder().code(LocalError.EXCEL_DATA_NULL.getCode()).
                    tipMessage(LocalError.EXCEL_DATA_NULL.getMsg()).build();
        }
        if (importExcels.size() > 1000) {
            throw WarnException.builder().code(LocalError.LABEL_EXCEL_IMPORT_SIZE_ERROR.getCode()).
                    tipMessage(LocalError.LABEL_EXCEL_IMPORT_SIZE_ERROR.getMsg()).build();
        }
        ResponseBase<Integer> responseBase = memberFileImportClient.saveMemberFileImport(reqDTO);

        handleMemberLabelData(merCode, responseBase.getData(), excelFile.getOriginalFilename(), importExcels);
    }

    @Override
    public MemberInfoItemImportResp importMemberInfoItemExcel(MultipartFile excelFile, String merCode) throws Exception {
        ExcelInfo importList = excelCommonService.getImportList(merCode, excelFile, new HashSet<>(), MemberInfoImportResDTO.class);

        List<BaseExcelData> list = importList.getExcelList();
        if (CollectionUtils.isEmpty(list)) {
            throw WarnException.builder().code(LocalError.EXCEL_CONTENT_EMPTY.getCode()).tipMessage(LocalError.EXCEL_CONTENT_EMPTY.getMsg()).build();
        }

        Map<Boolean, List<MemberInfoImportResDTO>> result2list = list.stream()
                .filter(MemberInfoImportResDTO.class::isInstance)
                .map(item -> (MemberInfoImportResDTO) item)
                .collect(Collectors.groupingBy(
                        BaseExcelData::isVerificationPassed));

        List<MemberInfoImportResDTO> success = result2list.getOrDefault(Boolean.TRUE,new ArrayList<>());
        List<MemberInfoImportResDTO> fail = result2list.getOrDefault(Boolean.FALSE,new ArrayList<>());
        MemberInfoBatchQueryDTO memberInfoBatchQueryDTO = new MemberInfoBatchQueryDTO();
        memberInfoBatchQueryDTO.setMerCode(merCode);
        memberInfoBatchQueryDTO.setUserIds( success.stream().map(e -> Long.parseLong(e.getMemberId())).distinct().collect(Collectors.toList()));
        ResponseBase<List<MemberBaseInfoDTO>> responseBase = memberInfoClient.queryUserInfo(memberInfoBatchQueryDTO);
        if(!responseBase.checkSuccess()){
            throw ExceptionUtil.getWarnException(COUPON_ERROR_11);
        }

        List<Long> existUserIds = responseBase.getData().stream().map(MemberBaseInfoDTO::getUserId).collect(Collectors.toList());

        success = success.stream().peek(item -> {
            if (!existUserIds.contains(Long.parseLong(item.getMemberId()))) {
                String error = StringUtils.isEmpty(item.getErrorMsg()) ? StringUtils.EMPTY : item.getErrorMsg();
                item.setErrorMsg(error + "会员" + item.getMemberId() + "不存在，" );
            }

            if (!StringUtils.isEmpty(item.getErrorMsg())) {
                item.setErrorMsg("第" + item.getRowNum() + "行：" + item.getErrorMsg());
                fail.add(item);
            }
        }).filter(e -> StringUtils.isEmpty(e.getErrorMsg()))
                .collect(Collectors.toList());

        MemberInfoItemImportResp memberInfoItemImportResp = new MemberInfoItemImportResp();
        memberInfoItemImportResp.setMemberList(success.stream().map(e->(MemberInfoImportSuccessResDTO)e).collect(Collectors.toList()));
        memberInfoItemImportResp.setFailNum(fail.size());
        memberInfoItemImportResp.setSuccessNum(success.size());
        if (!CollectionUtils.isEmpty(success)){
            memberInfoItemImportResp.setSuccessFileDownloadPath(getOssFileUrl(merCode, "会员信息导入成功数据.xlsx", success,MemberInfoImportSuccessResDTO.class));
        }
        if (!CollectionUtils.isEmpty(fail)){
            memberInfoItemImportResp.setFailedFileDownloadPath(getOssFileUrl(merCode, "会员信息导入失败数据.xlsx", fail,MemberInfoImportResDTO.class));
        }
        return memberInfoItemImportResp;
    }

    @Async
    public void handleMemberLabelData(String merCode, Integer id, String fileName, List<ExcelMemberLabelDTO> importExcels) {

        //导入的数据
        List<ExcelMemberLabelDTO> insertList = new ArrayList<>();
        //导入失败的数据
        List<MemberFileImportTempDTO> errorLabelList = new ArrayList<>();
        //校验数据是否符合规则
        this.validateList(importExcels, insertList, errorLabelList);

        MemberFileImportUpdateReqDTO updateReqDTO = new MemberFileImportUpdateReqDTO();
        updateReqDTO.setId(id);
        if (!CollectionUtils.isEmpty(insertList)) {
            List<MemberFileImportTempDTO> list = insertList.stream().map(label -> {
                MemberFileImportTempDTO tempDTO = ModelConvertUtils.convert(label, MemberFileImportTempDTO.class);
                tempDTO.setErrorMsg("数据校验正确");
                return tempDTO;
            }).collect(Collectors.toList());
            errorLabelList.addAll(list);
            MemberLabelImportReqDTO reqDTO = new MemberLabelImportReqDTO();
            reqDTO.setMerCode(merCode);
            reqDTO.setImportList(insertList);
            memberLabelClient.importLabel(reqDTO);
            updateReqDTO.setImportStatus(1);
        } else {
            updateReqDTO.setImportStatus(2);
        }
        try {
            String url = getOssFileUrl(merCode, fileName, errorLabelList,MemberFileImportTempDTO.class);
            updateReqDTO.setDownloadUrl(url);
        } catch (Exception e) {
            log.error("生成下载文件失败", e);
        }

        memberFileImportClient.updateMemberFileImport(updateReqDTO);
    }

    private void validateList(List<ExcelMemberLabelDTO> list, List<ExcelMemberLabelDTO> insertList,
                              List<MemberFileImportTempDTO> errorLabelList) {


        for (ExcelMemberLabelDTO labelDTO : list) {
            MemberFileImportTempDTO tempDTO = ModelConvertUtils.convert(labelDTO, MemberFileImportTempDTO.class);
            if (StringUtils.isEmpty(tempDTO.getGroupName())
                    || tempDTO.getGroupName().length() > 50) {
                tempDTO.setErrorMsg("标签组名称格式不正确");
                errorLabelList.add(tempDTO);
            }
            //通用名为空
            else if (StringUtils.isEmpty(tempDTO.getLabelName())
                    || tempDTO.getLabelName().length() > 50) {
                tempDTO.setErrorMsg("标签名称格式不正确");
                errorLabelList.add(tempDTO);
            } else {
                labelDTO.setGroupName(tempDTO.getGroupName().trim());
                labelDTO.setLabelName(tempDTO.getLabelName().trim());
                insertList.add(labelDTO);
            }
        }
    }

    public <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, Class<T> pojoClass) {
        ImportParams params = new ImportParams();
        params.setNeedCheckOrder(false);
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setKeyIndex(null);
        List<T> list;
        try {
            list = ExcelUtil.read(file, pojoClass);
        } catch (NoSuchElementException e) {
            throw WarnException.builder().code(LocalError.EXCEL_FILE_ERROR.getCode()).
                    message(LocalError.EXCEL_FILE_ERROR.getMsg()).build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw WarnException.builder().code(LocalError.EXCEL_FILE_ERROR.getCode()).
                    message(LocalError.EXCEL_FILE_ERROR.getMsg()).build();
        }
        return list;
    }


    private String getOssFileUrl(String merCode, String fileName, List<?> resultList,Class<?> cls) throws IOException {
        File temp = null;
        FileOutputStream fileOutputStream = null;
        FileInputStream inputStream = null;
        Integer sheetcount = LocalConst.INTEGER_ZERO;
        ExcelWriter excelWriter = null;
        WriteSheet writeSheet = null;
        try {
            temp = File.createTempFile("temp", "xlsx");
            fileOutputStream = new FileOutputStream(temp, false);
            if (null != resultList) {
                excelWriter = EasyExcel.write(fileOutputStream).build();
                writeSheet = EasyExcel.writerSheet(sheetcount++, fileName + sheetcount).head(cls).build();
                excelWriter.write(resultList, writeSheet);
            }
            excelWriter.finish();
            inputStream = new FileInputStream(temp);
            return store(merCode, fileName, inputStream);
        } catch (Exception e) {
            log.error("生成错误文件失败", e);
        } finally {
            if (null != fileOutputStream) {
                fileOutputStream.close();
            }
            if (null != inputStream) {
                inputStream.close();
            }
            if (temp.exists()) {
                temp.delete();
            }
        }
        return "";
    }

    /**
     * 上传至os服务器
     *
     * @param inputStream
     * @return
     */
    private String store(String merCode, String fileName, @Nullable FileInputStream inputStream) {
        //上传至oss文件服务
        String key = appName + SLASH + merCode + SLASH + System.currentTimeMillis() + SLASH + fileName;
        ossFileService.upload(inputStream, key);
        //返回访问地址
        return key;
    }

}