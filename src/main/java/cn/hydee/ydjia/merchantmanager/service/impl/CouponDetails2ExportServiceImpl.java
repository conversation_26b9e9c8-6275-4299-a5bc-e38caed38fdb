package cn.hydee.ydjia.merchantmanager.service.impl;

import cn.hydee.ydjia.merchantmanager.domain.YdjTask;
import cn.hydee.ydjia.merchantmanager.dto.resp.CouponExportDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static cn.hydee.ydjia.merchantmanager.service.ExportTaskService.ALL_COUPON_EXPORT;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/10/13 11:43
 */
@Service(ALL_COUPON_EXPORT)
@Slf4j
@Deprecated
public class CouponDetails2ExportServiceImpl extends AbstractExportTaskProcessor<CouponExportDTO> {
    @Autowired
    private CouponDetailsExportServiceImpl couponDetailsExportService;

    @Override
    public Class<CouponExportDTO> getExportClass() {
        return CouponExportDTO.class;
    }

    @Override
    public String getFileNamePrefix() {
        return "优惠券领取列表";
    }

    @Override
    public List<CouponExportDTO> getExportDateList(YdjTask task) {
        return couponDetailsExportService.getExportDateList(task);
    }


}
