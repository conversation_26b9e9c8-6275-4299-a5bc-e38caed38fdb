package cn.hydee.ydjia.merchantmanager.domain;

import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商品评论设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-12
 */
@Data
@Accessors(chain = true)
@ApiModel(value="CommodityCommentSet对象", description="商品评论设置表")
public class CommodityCommentSet  {

    private Long id;

    @ApiModelProperty(value = "商家编码")
    private String merCode;

    @ApiModelProperty(value = "每个用户对同一个商品评论条数限制")
    private Integer countLimit;

    @ApiModelProperty(value = "是否可以上传图片 0-不能 1-能")
    private Integer imtStatus;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


}
