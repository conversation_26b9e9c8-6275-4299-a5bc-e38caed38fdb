package cn.hydee.ydjia.merchantmanager.domain;

import cn.hydee.ydjia.merchantmanager.dto.resp.ActivityCouponDetailResDTO;
import cn.hydee.ydjia.merchantmanager.enums.PromotionType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 首页组件下的条目
 *
 * <AUTHOR>
 * @create 2020-06-17
 */
@Data
@TableName("homepage_item")
public class HomepageItem {

    @ApiModelProperty("条目ID,创建时不传")
    private String id;
    @ApiModelProperty("组件ID,创建组件时不传")
    private String setId;
    @ApiModelProperty("名称：商品名称或优惠券名称或公告组件-公告内容或直播名称")
    private String name;
    @ApiModelProperty("图片地址")
    private String img;
    @ApiModelProperty("链接路径：普通链接或H5链接")
    private String url;
    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("条目ID：商品规格id，分类id，优惠券id、小程序APPID等")
    private String itemId;
    @ApiModelProperty("商品ID")
    private String commodityId;
    @ApiModelProperty("设置值信息：优惠券组件-写优惠券领取总数；小程序链接url")
    private String value;
    @ApiModelProperty(value = "链接选择：0-商城内链，1-自定义链接（H5和小程序链接）")
    private Integer chooseFlag;
    @ApiModelProperty(value = "商品来源：0-非云仓（默认）、1-云仓商品")
    private Integer commodityOrigin;
    @ApiModelProperty(value = "云仓商品所属供应商编码")
    private String spCode;
    @ApiModelProperty(value = "商品标签")
    private String  goodsTag;
    @ApiModelProperty(value = "价格标签")
    private String  priceTags;

    @TableField(exist = false)
    private CardGoodsDTO cardGoodsDTO;

    @TableField(exist = false)
    @ApiModelProperty("商品药品类型")
    private Integer drugType;
    @TableField(exist = false)
    @ApiModelProperty(value = "商品名称,如果是商品配置，返回该字段")
    private String productName;
    @TableField(exist = false)
    @ApiModelProperty("分类ID")
    private String typeId;
    @TableField(exist = false)
    @ApiModelProperty(value = "分类名称,如果是分类配置，返回该字段")
    private String className;
    @TableField(exist = false)
    @ApiModelProperty(value = "市场价,如果是商品配置，返回该字段")
    private BigDecimal mPrice;
    @TableField(exist = false)
    @ApiModelProperty(value = "售价,如果是商品配置，返回该字段")
    private BigDecimal price;
    @TableField(exist = false)
    @ApiModelProperty("库存")
    private Integer stock;
    @TableField(exist = false)
    @ApiModelProperty("商品参加的活动类型")
    private Integer promoteType;
    @TableField(exist = false)
    @ApiModelProperty(value = "功能主治")
    private String keyFeature;
    @TableField(exist = false)
    @ApiModelProperty(value = "药品适应症")
    private String indications;
    @TableField(exist = false)
    @ApiModelProperty(value = "优惠券名称")
    private String cname;

    @TableField(exist = false)
    @ApiModelProperty("优惠券的设置数量")
    private Integer couponCount;
    @TableField(exist = false)
    @ApiModelProperty(value = "优惠券组件中当前优惠券明细")
    private ActivityCouponDetailResDTO couponDetail;

    @TableField(exist = false)
    @ApiModelProperty(value = "活动类型名称集合")
    private List<String> actTypeNames;

    public void setActType(PromotionType promotionType) {
        String typeName = promotionType.getName();
        if (CollectionUtils.isEmpty(actTypeNames)) {
            actTypeNames = Lists.newArrayList(typeName);
        } else {
            if (!actTypeNames.contains(typeName)) {
                actTypeNames.add(typeName);
            }
        }
    }
}
