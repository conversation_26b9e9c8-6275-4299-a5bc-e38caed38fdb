package cn.hydee.ydjia.merchantmanager.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 首页模板维度
 * <AUTHOR>
 * @date 17:47 2020/4/9
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_dimension")
public class CmsDimension extends DomainBase {
    @ApiModelProperty("维度id")
    private String id;
    @ApiModelProperty("维度名称,未填写时'未命名'")
    private String name;
    @ApiModelProperty("维度对应页面标题")
    private String title;
    @ApiModelProperty("商户编码")
    private String merCode;
    @ApiModelProperty("C端搜索提示")
    private String searchHint;
    @ApiModelProperty("设置首页，0-否，1-是，同时只有一个是")
    @TableField(fill=FieldFill.UPDATE)
    private Integer isUse;
}