package cn.hydee.ydjia.merchantmanager.domain;

import cn.hydee.ydjia.merchantmanager.dto.resp.MessageVariableDTO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/** 心币清零规则表
 * <AUTHOR>
 * @date 2022/11/28 10:53
 */
@Data
@TableName("integral_clean_rule")
public class IntegralCleanRule implements Serializable {

    private static final long serialVersionUID = 9202323673495616405L;


    private Integer id;

    @ApiModelProperty("商户号")
    @NotBlank(message = "商户号不能为空")
    private String merCode;

    @ApiModelProperty("心币清零规则  0、不清零 1、清零")
    @NotNull(message = "心币清零规则不能为空")
    private Integer clearRule;

    @ApiModelProperty("心币清零类型  1.按年度清零")
    private Integer clearType;

    @ApiModelProperty("心币清零时间节点 mmdd格式")
    private String clearTimeNode;

    @ApiModelProperty("心币失效时间 mmdd格式")
    private String invalidTime;

    @ApiModelProperty("1.站内信 2.短信")
    private String clearNotice;

    @ApiModelProperty("清零前通知时间")
    private Integer noticeTime;

    @ApiModelProperty("短信内容")
    private String smsContent;

    @ApiModelProperty(value = "是否有效（0失效，1有效）")
    private Integer isValid;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateName;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty("短信变量")
    private List<MessageVariableDTO> messageVariables;

    @ApiModelProperty(value = "短信变量json字符串", hidden = true)
    private String messageVariable;

    public List<MessageVariableDTO> getMessageVariables(){
        if(StringUtils.isNotBlank(this.messageVariable)){
            return JSON.parseArray(this.messageVariable, MessageVariableDTO.class);
        }
        return null;
    }

    public String getMessageVariable(){
        if(!CollectionUtils.isEmpty(this.messageVariables)){
            return JSON.toJSONString(this.messageVariables);
        }
        return this.messageVariable;
    }
}
