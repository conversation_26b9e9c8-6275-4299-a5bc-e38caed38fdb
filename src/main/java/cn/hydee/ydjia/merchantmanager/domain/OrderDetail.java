package cn.hydee.ydjia.merchantmanager.domain;

import cn.hydee.ydjia.merchantmanager.dto.resp.ActiSearchRespDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.ActivityDiscountDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.DiscountItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderDetail extends DomainBase{
    private String id;
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "订单id")
    private String orderId;
    @ApiModelProperty(value = "药品类型：0：甲类OTC，1：处方药，2：乙类OTC，3：非处方药")
    private Integer drugType;
    @ApiModelProperty(value = "商品id")
    private String commodityId;
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "商品类型")
    private Integer commodityType;
    @ApiModelProperty(value = "商品数量")
    private Integer commodityNumber;
    @ApiModelProperty(value = "商品成本价,采购价")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "商品原单价")
    private BigDecimal mprice;
    @ApiModelProperty(value = "商品售价")
    private BigDecimal commodityPrice;
    @ApiModelProperty(value = "商品心币价格")
    private Integer exchangeHb;
    @ApiModelProperty(value = "小计金额售价*数量")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "心币金额心币数量*商品数量")
    private Integer totalHb;
    @ApiModelProperty(value = "促销优惠金额")
    private BigDecimal couponAmount;
    @ApiModelProperty(value = "成交总额小计-优惠")
    private BigDecimal totalActualAmount;
    @ApiModelProperty(value = "明细状态")
    private Integer status;
    @ApiModelProperty(value = "促销活动ID")
    private String promotionId;
    @ApiModelProperty(value = "包裹ID")
    private String packageId;
    @ApiModelProperty(value = "退款申请表id")
    private String returnRequestId;
    @ApiModelProperty(value = "SKU主图")
    private  String  mPic;
    @ApiModelProperty(value = "是否组合商品0否1是")
    private Integer isCombinedCommodity;
    @ApiModelProperty(value = "是否主商品0否1是")
    private Integer isMainCommodity;
    @ApiModelProperty(value = "主商品编码")
    private String mainCommodityCode;
    @ApiModelProperty(value = "是否促销活动0否1是")
    private Integer isPromotion;
    @ApiModelProperty(value = "商品规格属性描述")
    private String skuValue;
    @ApiModelProperty(value = "规格id")
    private String specId;
    @ApiModelProperty(value = "活动优惠金额")
    private BigDecimal activityDiscountAmont;
    @ApiModelProperty(value = "促销商品类型N.正品G.赠品R.换购商品")
    private String pmtProductType;
    @ApiModelProperty(value = "商品来源类型:  1-云货架来源，2-DC仓来源")
    private Integer originType;
    @ApiModelProperty(value = ":组织机构编码（云货架商家编码或DC仓编码)")
    private String stCode;
    @ApiModelProperty(value = "预计送达时间")
    private Date expectDeliveryTime;
    @ApiModelProperty(value = "订单包裹")
    private OrderPackage orderPackage;
    @ApiModelProperty(value = "活动类型")
    private Integer pmtType;
    @ApiModelProperty(value = "订单明细参与的活动")
    private List<ActiSearchRespDTO> activityList;
    @ApiModelProperty(value = "订单参与的活动信息")
    private List<ActivityDiscountDTO> activityDiscounts;
    @ApiModelProperty(value = "订单明细折价项")
    private List<DiscountItem> discountItems;
    @ApiModelProperty(value = "商品医保编码")
    private String medicalInsuranceCode;
    @ApiModelProperty(value = "渠道类型")
    private Integer sourceChannelType;
    @ApiModelProperty(value = "渠道ID")
    private String sourceChannelId;
    @ApiModelProperty(value = "直播活动name")
    private String liveActivityName;
    @ApiModelProperty(value = "邀请人姓名")
    private String inviteName;
    @ApiModelProperty(value = "邀请人手机号")
    private String invitePhone;
}