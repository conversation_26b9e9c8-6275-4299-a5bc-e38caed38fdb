package cn.hydee.ydjia.merchantmanager.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/8/14 10:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CommodityLabel对象", description="商品标签表")
public class CommodityLabel extends DomainBase {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "标签名称")
    private String name;

    @ApiModelProperty(value = "商家编码")
    private String merCode;

    @ApiModelProperty(value = "标签类型")
    private String type;

    @ApiModelProperty(value = "备注")
    private String remark;


}