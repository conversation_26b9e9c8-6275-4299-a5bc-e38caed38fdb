package cn.hydee.ydjia.merchantmanager.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderPackage extends DomainBase{
    private String id;

    private String merCode;

    private String orderId;

    private String deptId;

    private String packageNo;

    private Integer companyId;

    private String companyName;

    private String companyNo;

    private String logisticNo;

    private BigDecimal logisticFee;

    private Date logisticTime;

    private Integer deliveryStatus;

    private Date deliveryTime;

    private BigDecimal deliveryDist;

    private String deliveryUserId;

    private String deliveryUserName;

    private String deliveryUserPhone;

    private Integer type;


}