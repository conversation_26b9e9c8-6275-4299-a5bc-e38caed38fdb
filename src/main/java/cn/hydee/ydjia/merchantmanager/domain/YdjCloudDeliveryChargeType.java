package cn.hydee.ydjia.merchantmanager.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ydj_cloud_delivery_charge_type")
public class YdjCloudDeliveryChargeType extends DomainBase{

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "商家编码",required=true)
    private String merCode;

    @ApiModelProperty(value = "服务商编码",required=true)
    private String spCode;

    @ApiModelProperty(value = "计费方式，1 默认无邮费 2 按区域/重量计费",required=true)
    private Integer chargeType;

}