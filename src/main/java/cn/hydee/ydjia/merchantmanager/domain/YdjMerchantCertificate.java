package cn.hydee.ydjia.merchantmanager.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ydj_merchant_certificate")
public class YdjMerchantCertificate extends DomainBase{
    private String id;

    private String merCode;

    private String certificateCode;

    private String certificateName;

    private String certificateNumber;

    private String certificatePicture;

    private Integer certificateStatus;

    private Integer certificateType;

    private Integer sortNumber;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "有效开始时间")
    private Date validBeginTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "有效结束时间")
    private Date validEndTime;
}