package cn.hydee.ydjia.merchantmanager.poster.template;

import cn.hydee.ydjia.merchantmanager.dto.req.PosterReqDTO;
import cn.hydee.ydjia.merchantmanager.poster.dto.PosterDTO;
import cn.hydee.ydjia.merchantmanager.poster.dto.PosterFontDTO;
import cn.hydee.ydjia.merchantmanager.poster.dto.PosterImgDTO;
import cn.hydee.ydjia.merchantmanager.poster.dto.PosterQrCodeDTO;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 工业自定义海报
 * <AUTHOR>
 * @date 2022/6/30 16:32
 */
public class IndustryPosterCustomTemplate extends PosterTemplate {


    @Override
    public PosterDTO initData(PosterReqDTO dto) throws Exception {
        PosterDTO poster = new PosterDTO();
        //初始化图片信息
        initImgList(poster, dto);
        //初始化文字信息
        initFontList(poster, dto);
        //初始化二维码信息
        initQrList(poster, dto);
        poster.setWidth(750);
        poster.setHeight(1250);
        return poster;
    }

    /**
     * 初始化图片模板信息
     * @param poster poster
     * @param dto dto
     */
    public static void initImgList(PosterDTO poster, PosterReqDTO dto) {

        List<PosterImgDTO> imgList = new ArrayList<>();

        // 海报图片
        PosterImgDTO userImg = new PosterImgDTO();
        userImg.setUrl(dto.getMainPicUrl());
        userImg.setIsCircle(false);
        userImg.setLeftX(0);
        userImg.setTopY(0);
        userImg.setWidth(750);
        userImg.setHeight(1250);
        userImg.setImgType(2);
        imgList.add(userImg);


        poster.setImgList(imgList);
    }

    /**
     * 初始化文字模板信息
     *
     * @param poster 海报dto
     * @param dto    拼团参数
     */
    public void initFontList(PosterDTO poster, PosterReqDTO dto) {

        //文字集合
        List<PosterFontDTO> fontList = new ArrayList<>();
        poster.setFontList(fontList);

    }


    /**
     * 初始化二维码信息
     *
     * @param poster poster
     * @param dto dto
     */
    public static void initQrList(PosterDTO poster, PosterReqDTO dto) {
        //二维码
        List<PosterQrCodeDTO> qrList = new ArrayList<>();
        PosterQrCodeDTO qrDTO = new PosterQrCodeDTO();
        qrDTO.setUrl(dto.getQrUrl());
        qrDTO.setIsCircle(true);
        qrDTO.setLeftX(dto.getCodePositionX());
        qrDTO.setTopY(dto.getCodePositionY());
        qrDTO.setWidth(dto.getCodeWidth());
        qrDTO.setHeight(dto.getCodeHeight());
        qrDTO.setType(dto.getQrType());
        qrDTO.setScene(dto.getScene());
        qrList.add(qrDTO);
        poster.setQrList(qrList);
    }
}
