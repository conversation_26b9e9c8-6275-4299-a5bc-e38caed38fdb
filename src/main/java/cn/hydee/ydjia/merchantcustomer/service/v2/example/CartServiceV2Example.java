package cn.hydee.ydjia.merchantcustomer.service.v2.example;

import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.enums.PmtProductType;
import cn.hydee.ydjia.merchantcustomer.service.v2.ShoppingCartHandleServiceV2;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 购物车服务V2使用示例
 * 
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Component
public class CartServiceV2Example {
    
    @Autowired
    private ShoppingCartHandleServiceV2 shoppingCartHandleServiceV2;
    
    /**
     * 基础使用示例
     */
    public void basicUsageExample() {
        try {
            // 1. 构建请求参数
            ShoppingCartCommodityReqDTO request = buildBasicRequest();
            
            // 2. 调用V2服务
            CartCommodityRespCommonDTO response = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
            
            // 3. 处理响应结果
            handleResponse(response);
            
        } catch (Exception e) {
            log.error("购物车V2服务调用失败", e);
        }
    }
    
    /**
     * 高级使用示例 - 包含更多配置
     */
    public void advancedUsageExample() {
        try {
            // 1. 构建高级请求参数
            ShoppingCartCommodityReqDTO request = buildAdvancedRequest();
            
            // 2. 调用V2服务
            CartCommodityRespCommonDTO response = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
            
            // 3. 详细处理响应结果
            handleAdvancedResponse(response);
            
        } catch (Exception e) {
            log.error("购物车V2高级服务调用失败", e);
        }
    }
    
    /**
     * 构建基础请求参数
     */
    private ShoppingCartCommodityReqDTO buildBasicRequest() {
        ShoppingCartCommodityReqDTO request = new ShoppingCartCommodityReqDTO();
        
        // 基础信息
        request.setMerCode("TEST_MERCHANT_001");
        request.setUserId("123456789");
        request.setIfCartOperate(true);
        
        // 登录用户信息
        LoginUserDTO loginUser = LoginUserDTO.builder()
                .merCode("TEST_MERCHANT_001")
                .userId("123456789")
                .build();
        request.setLoginUser(loginUser);
        
        // 购物车商品列表
        List<CartCommodityDTO> cartSpecs = Lists.newArrayList();
        
        // 添加第一个商品
        CartCommodityDTO spec1 = new CartCommodityDTO();
        spec1.setSpecId("100001");
        spec1.setStoreId("STORE_001");
        spec1.setCount(2);
        spec1.setPmtProductType(PmtProductType.NORMAL.getCode());
        spec1.setChoseFlag(1); // 选中状态
        cartSpecs.add(spec1);
        
        // 添加第二个商品
        CartCommodityDTO spec2 = new CartCommodityDTO();
        spec2.setSpecId("100002");
        spec2.setStoreId("STORE_001");
        spec2.setCount(1);
        spec2.setPmtProductType(PmtProductType.NORMAL.getCode());
        spec2.setChoseFlag(1); // 选中状态
        cartSpecs.add(spec2);
        
        request.setCartRedisSpecs(cartSpecs);
        
        return request;
    }
    
    /**
     * 构建高级请求参数
     */
    private ShoppingCartCommodityReqDTO buildAdvancedRequest() {
        ShoppingCartCommodityReqDTO request = buildBasicRequest();
        
        // 高级配置
        request.setIfCartPageAdd(true);
        request.setReplaceCommodityNameFlag(true);
        request.setPrescriptionDrugComplianceVersion("2.0.0");
        request.setCollectivizationFlag(false);
        request.setLongitude("116.397128");
        request.setLatitude("39.916527");
        
        // 添加更多商品类型
        List<CartCommodityDTO> cartSpecs = request.getCartRedisSpecs();
        
        // 添加B2C商品
        CartCommodityDTO b2cSpec = new CartCommodityDTO();
        b2cSpec.setSpecId("200001");
        b2cSpec.setStoreId("B2C_STORE");
        b2cSpec.setCount(1);
        b2cSpec.setPmtProductType(PmtProductType.NORMAL.getCode());
        b2cSpec.setIsB2c(1);
        b2cSpec.setChoseFlag(1);
        cartSpecs.add(b2cSpec);
        
        // 添加云仓商品
        CartCommodityDTO cloudSpec = new CartCommodityDTO();
        cloudSpec.setSpecId("300001");
        cloudSpec.setStoreId("CLOUD_WSC001");
        cloudSpec.setSpCode("WSC001");
        cloudSpec.setCount(3);
        cloudSpec.setPmtProductType(PmtProductType.NORMAL.getCode());
        cloudSpec.setChoseFlag(1);
        cartSpecs.add(cloudSpec);
        
        return request;
    }
    
    /**
     * 处理基础响应结果
     */
    private void handleResponse(CartCommodityRespCommonDTO response) {
        if (response == null) {
            log.warn("购物车响应为空");
            return;
        }
        
        log.info("购物车处理成功:");
        log.info("- 门店数量: {}", response.getStoreCommodityList() != null ? response.getStoreCommodityList().size() : 0);
        log.info("- 选中商品数量: {}", response.getChooseCommodityCount());
        log.info("- 商品总价: {}", response.getTotalPrice());
        log.info("- 优惠前总价: {}", response.getBeforePrice());
        log.info("- 预估优惠: {}", response.getReducePrice());
        
        // 遍历门店商品
        if (response.getStoreCommodityList() != null) {
            response.getStoreCommodityList().forEach(store -> {
                log.info("门店: {} ({}), 商品数量: {}", 
                        store.getStoreName(), store.getStoreId(), 
                        store.getCommodities() != null ? store.getCommodities().size() : 0);
            });
        }
    }
    
    /**
     * 处理高级响应结果
     */
    private void handleAdvancedResponse(CartCommodityRespCommonDTO response) {
        handleResponse(response); // 先处理基础信息
        
        if (response == null || response.getStoreCommodityList() == null) {
            return;
        }
        
        // 详细分析每个门店的商品
        response.getStoreCommodityList().forEach(store -> {
            log.info("=== 门店详情: {} ===", store.getStoreName());
            log.info("门店总价: {}", store.getTotalPrice());
            log.info("门店优惠前总价: {}", store.getBeforePrice());
            log.info("门店优惠金额: {}", store.getReducePrice());
            
            if (store.getCommodities() != null) {
                store.getCommodities().forEach(commodity -> {
                    log.info("  商品: {} ({})", commodity.getCommodityName(), commodity.getSpecId());
                    log.info("    价格: {} -> {}", commodity.getBeforePrice(), commodity.getPrice());
                    log.info("    数量: {}", commodity.getCount());
                    log.info("    是否VIP: {}", commodity.getIsVip());
                    
                    // 活动信息
                    if (commodity.getCommodityLevelActivities() != null && !commodity.getCommodityLevelActivities().isEmpty()) {
                        log.info("    商品级活动: {}", commodity.getCommodityLevelActivities().size());
                    }
                    if (commodity.getOrderLevelActivities() != null && !commodity.getOrderLevelActivities().isEmpty()) {
                        log.info("    订单级活动: {}", commodity.getOrderLevelActivities().size());
                    }
                });
            }
        });
        
        // Plus会员优惠信息
        if (response.getPredThriftRespDTO() != null) {
            log.info("Plus会员预计优惠: {}", response.getPredThriftRespDTO().getTotalThrift());
        }
    }
    
    /**
     * 错误处理示例
     */
    public void errorHandlingExample() {
        try {
            // 构建无效请求
            ShoppingCartCommodityReqDTO invalidRequest = new ShoppingCartCommodityReqDTO();
            // 故意不设置必要参数
            
            CartCommodityRespCommonDTO response = shoppingCartHandleServiceV2.handleCartCommodityV2(invalidRequest);
            
        } catch (IllegalArgumentException e) {
            log.warn("参数验证失败: {}", e.getMessage());
            // 处理参数错误
            
        } catch (RuntimeException e) {
            log.error("系统运行时错误: {}", e.getMessage(), e);
            // 处理系统错误
            
        } catch (Exception e) {
            log.error("未知错误: {}", e.getMessage(), e);
            // 处理其他错误
        }
    }
    
    /**
     * 性能测试示例
     */
    public void performanceTestExample() {
        int testCount = 100;
        long totalTime = 0;
        
        for (int i = 0; i < testCount; i++) {
            long startTime = System.currentTimeMillis();
            
            try {
                ShoppingCartCommodityReqDTO request = buildBasicRequest();
                request.setUserId("test_user_" + i);
                
                CartCommodityRespCommonDTO response = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
                
                long endTime = System.currentTimeMillis();
                totalTime += (endTime - startTime);
                
            } catch (Exception e) {
                log.error("性能测试第{}次调用失败", i + 1, e);
            }
        }
        
        double avgTime = (double) totalTime / testCount;
        log.info("性能测试完成: 总次数={}, 平均耗时={}ms", testCount, avgTime);
    }
}
