package cn.hydee.ydjia.merchantcustomer.service.v2.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 购物车服务V2配置类
 * 
 * <AUTHOR>
 * @version 2.0
 */
@Configuration
@ComponentScan(basePackages = {
    "cn.hydee.ydjia.merchantcustomer.service.v2.impl",
    "cn.hydee.ydjia.merchantcustomer.service.v2.processor"
})
public class CartServiceV2Configuration {
    
    // 可以在这里配置一些Bean或者其他配置
}
