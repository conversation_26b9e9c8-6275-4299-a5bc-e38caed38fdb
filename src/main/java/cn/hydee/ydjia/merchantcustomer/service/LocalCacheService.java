package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.SysMerchantProduct;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MerchantDetailResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.MerchantInfoClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.MedicarePaymentClient;
import cn.hydee.ydjia.merchantcustomer.feign.domain.PrescriptionDrugPictureSet;
import cn.hydee.ydjia.merchantcustomer.feign.dto.medical.MedicalInsuranceBatchReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.medical.MedicalInsurancePaymentConfig;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.PrescriptionDrugPictureSettingDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.feign.repository.PrescriptionDrugPictureSetRepo;
import cn.hydee.ydjia.merchantcustomer.feign.service.MallRedisService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType.MER_CODE_IS_NULL;
import static cn.hydee.ydjia.merchantcustomer.feign.util.LocalConst.PRESCRIPTION_DRUG_PIC_TRANSPARENCY;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * 缓存业务层
 *
 * <AUTHOR>
 * @create 2021-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocalCacheService {

    private final MerchantInfoClient merchantInfoClient;

    private final MallRedisService redisService;

    private final MedicarePaymentClient medicarePaymentClient;

    @Lazy
    @Autowired
    private PrescriptionDrugPictureSetRepo prescriptionDrugPictureSetRepo;

    @Cacheable(value = "merchantConfig:detail", key = "'mall:merCode:'.concat(#merCode)")
    public MerchantDetailResDTO queryMerchantPlatformInfoByCode(String merCode) {
        if (!StringUtils.hasText(merCode)) {
            throw WarnException.builder().code(MER_CODE_IS_NULL.getCode()).message(MER_CODE_IS_NULL.getMsg()).build();
        }
        ResponseBase<MerchantDetailResDTO> responseBase = merchantInfoClient.queryMerchantPlatformInfoByCode(merCode);
        checkResult(responseBase);
        return responseBase.getData();
    }

    //@Cacheable(value = "merchantProduct", key = "'mall:merCode:'.concat(#merCode)")
    public List<SysMerchantProduct> queryUserHasSystem(String merCode) {
        ResponseBase<List<SysMerchantProduct>> responseBase = merchantInfoClient.queryUserHasSystem(merCode);
        checkResult(responseBase);
        return responseBase.getData();
    }

    /**
     * 商户门店医保支付配置
     *
     * @param merCode
     * @param storeId
     * @return
     */
//    @Cacheable(value = "medicalPaymentConfig", key = "'")
    public Boolean queryMedicalPaymentConfig(String merCode, String storeId) {
        String cacheKey = "mall:localCacheService.queryMedicalPaymentConfig:merCode:" + merCode + ":storeId." + storeId;
        Object object = redisService.getObject(cacheKey);
        if (object != null) {
            return (Boolean) object;
        }

        MedicalInsuranceBatchReqDTO medicalInsuranceBatchReqDTO = MedicalInsuranceBatchReqDTO.builder().merCode(merCode)
                .type(LocalConst.STATUS_ONE).storeIds(Collections.singletonList(storeId)).build();
        ResponseBase<List<MedicalInsurancePaymentConfig>> responseBase = medicarePaymentClient.storeMedicareConfigList(medicalInsuranceBatchReqDTO);
        if (responseBase == null || !responseBase.checkSuccess() || CollectionUtils.isEmpty(responseBase.getData())) {
            log.warn("{} => {}", JSON.toJSONString(medicalInsuranceBatchReqDTO), JSON.toJSONString(responseBase));
            return false;
        }
        boolean equals = YesOrNoType.YES.getCode().equals(responseBase.getData().get(0).getStatus());
        redisService.setValueExpire(cacheKey, equals, 5, TimeUnit.MINUTES);
        return equals;
    }

    /**
     * 失效门店医保配置状态缓存
     *
     * @param merCode
     * @param storeId
     */
    @CacheEvict(value = "medicalPaymentConfig", key = "'mall:merCode:'.concat(#merCode).concat(#storeId)")
    public void evictMedicalInsuranceConfig(String merCode, String storeId) {
    }

    /**
     * @param [merCode]
     * @return cn.hydee.ydjia.merchantcustomer.feign.dto.resp.PrescriptionDrugPictureSettingDTO
     * <AUTHOR>
     * @description //TODO 查询合规处方药图片设置
     * @date 2022/12/19
     **/
    @Cacheable(value = "merchantPrescriptionDrugPictureSetting", key = "'mall:merCode:'.concat(#merCode)")
    public PrescriptionDrugPictureSettingDTO getPrescriptionDrugPictureSetting(String merCode) {
        List<PrescriptionDrugPictureSet> prescriptionDrugPictureSets = this.prescriptionDrugPictureSetRepo.selectList(
                new QueryWrapper<PrescriptionDrugPictureSet>().lambda()
                        .eq(PrescriptionDrugPictureSet::getMerCode, merCode)
                        .orderByDesc(PrescriptionDrugPictureSet::getModifyTime));
        PrescriptionDrugPictureSettingDTO prescriptionDrugPictureSettingDTO = new PrescriptionDrugPictureSettingDTO();
        if (!CollectionUtils.isEmpty(prescriptionDrugPictureSets)) {
            PrescriptionDrugPictureSet prescriptionDrugPictureSet = prescriptionDrugPictureSets.get(0);
            prescriptionDrugPictureSettingDTO.setType(prescriptionDrugPictureSet.getType());
            prescriptionDrugPictureSettingDTO.setTransparency(prescriptionDrugPictureSet.getTransparency());
            prescriptionDrugPictureSettingDTO.setPicUrl(prescriptionDrugPictureSet.getPicUrl());
        }
        if (Objects.isNull(prescriptionDrugPictureSettingDTO.getType())) {
            // 默认：自定义蒙层，透明度为90
            prescriptionDrugPictureSettingDTO.setType(LocalConst.STATUS_ZERO);
            prescriptionDrugPictureSettingDTO.setTransparency(PRESCRIPTION_DRUG_PIC_TRANSPARENCY);
        }
        return prescriptionDrugPictureSettingDTO;
    }

    public Map<String, Integer> queryStoresMedicalPaymentConfigStatus(String merCode, List<String> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return null;
        }
        MedicalInsuranceBatchReqDTO medicalInsuranceBatchReqDTO = MedicalInsuranceBatchReqDTO.builder().merCode(merCode)
                .type(LocalConst.STATUS_ONE).storeIds(storeIds).build();
        ResponseBase<List<MedicalInsurancePaymentConfig>> responseBase = medicarePaymentClient.storeMedicareConfigList(medicalInsuranceBatchReqDTO);
        if (responseBase == null || !responseBase.checkSuccess() || CollectionUtils.isEmpty(responseBase.getData())) {
            log.warn("{} => {}", JSON.toJSONString(medicalInsuranceBatchReqDTO), JSON.toJSONString(responseBase));
            return null;
        }
        Map<String, Integer> storeMedicalConfigStatusMap = new HashMap<>();
        responseBase.getData().stream().forEach(medicalInsurancePaymentConfig -> {
            if (storeMedicalConfigStatusMap.keySet().contains(medicalInsurancePaymentConfig.getStoreId())) {
                return;
            }
            storeMedicalConfigStatusMap.put(medicalInsurancePaymentConfig.getStoreId(), medicalInsurancePaymentConfig.getStatus());
        });
        return storeMedicalConfigStatusMap;
    }
}
