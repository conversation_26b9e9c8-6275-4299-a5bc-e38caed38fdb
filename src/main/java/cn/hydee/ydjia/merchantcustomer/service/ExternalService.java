package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.req.LinkSearchReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.SpecPriceQueryReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.LinkSearchResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SpecPriceInfoDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.ActAggregate;
import cn.hydee.ydjia.merchantcustomer.feign.pmt.AggregateQueryReqDTO;

import java.util.List;

/**
 * 外部业务接口
 *
 * <AUTHOR>
 * @create 2020-11-15
 */
public interface ExternalService {

    /**
     * 链接查询
     *
     * @param dto
     * @return
     */
    LinkSearchResDTO linkList(LinkSearchReqDTO dto);

    /**
     * 查询微商城规格价格信息
     *
     * @param reqDTO
     * @return
     */
    List<SpecPriceInfoDTO> querySpecPrice(SpecPriceQueryReqDTO reqDTO);

    /**
     * @param reqDTO
     * @return cn.hydee.ydjia.merchantcustomer.feign.dto.resp.ActAggregate
     * @description TODO 活动聚合组件查询
     * <AUTHOR>
     * @date 2023/5/15
     */
    ActAggregate queryActAggregateComponent(AggregateQueryReqDTO reqDTO);
}
