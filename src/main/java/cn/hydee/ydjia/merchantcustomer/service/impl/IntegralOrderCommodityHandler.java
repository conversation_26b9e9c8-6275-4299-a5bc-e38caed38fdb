package cn.hydee.ydjia.merchantcustomer.service.impl;
import java.math.BigDecimal;


import cn.hutool.core.util.ObjectUtil;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.ActivityIntegralSpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.PurchaseCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.feign.PromoteClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.CommodityQueryAssembleParamDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreSpec;
import cn.hydee.ydjia.merchantcustomer.feign.enums.DrugType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.service.AbstractHandler;
import cn.hydee.ydjia.merchantcustomer.service.ActivityIntegralCartService;
import cn.hydee.ydjia.merchantcustomer.service.InPurchaseCartService;
import cn.hydee.ydjia.merchantcustomer.service.ShoppingCartService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * 提交订单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/28 14:38
 */
@Slf4j
@Service
public class IntegralOrderCommodityHandler extends AbstractHandler<IntegralOrderCommodityReqDTO, List<IntegralCommodityRespDTO>> {

    @Autowired
    private ShoppingCartService shoppingCartService;
    @Autowired
    private PromoteClient promoteClient;

    @Autowired
    private ActivityIntegralCartService activityIntegralCartService;

    @Autowired
    private InPurchaseCartService inPurchaseCartService;

    @Override
    public List<IntegralCommodityRespDTO> handleRequest(IntegralOrderCommodityReqDTO obj) {
        //特殊处理外部传参
        List<MultiCartCommodityDTO> multiCartCommodityDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(obj.getMultiCartCommodity())) {
            if (!StringUtils.isEmpty(obj.getSpecId()) && obj.getCount() > 0) {
                MultiCartCommodityDTO multiCartCommodityDTO = MultiCartCommodityDTO.builder().specId(obj.getSpecId()).count(obj.getCount()).build();
                multiCartCommodityDTOList.add(multiCartCommodityDTO);
                obj.setMultiCartCommodity(multiCartCommodityDTOList);
            } else {
                //购物车直接下单去redis查
                ActivityIntegralCartCommonRespDTO activityIntegralCartCommonRespDTO = activityIntegralCartService.get(obj.getMerCode(), obj.getMemberId(), null);
                if (activityIntegralCartCommonRespDTO != null && !CollectionUtils.isEmpty(activityIntegralCartCommonRespDTO.getStoreCommodityList())) {
                    for (ActivityIntegralCartStoreDTO activityIntegralCartStoreDTO : activityIntegralCartCommonRespDTO.getStoreCommodityList()) {
                        if (!obj.isIntegralCloudGoods() && !obj.getStoreId().equals(activityIntegralCartStoreDTO.getStoreId())) {
                            continue;
                        }
                        for (ActivityIntegralCartCommodityRespDTO commodity : activityIntegralCartStoreDTO.getCommodities()) {
                            if (YesOrNoType.NO.getCode().equals(commodity.getChoseFlag()) || YesOrNoType.NO.getCode().equals(commodity.getStatus())) {
                                continue;
                            }
                            MultiCartCommodityDTO multiCartCommodityDTO = MultiCartCommodityDTO.builder().specId(commodity.getSpecId()).count(commodity.getCount()).build();
                            multiCartCommodityDTOList.add(multiCartCommodityDTO);
                        }
                        obj.setMultiCartCommodity(multiCartCommodityDTOList);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(obj.getMultiCartCommodity())) {
            throw WarnException.builder().code(ErrorType.PROD_IS_NULL.getCode()).
                    tipMessage(ErrorType.PROD_IS_NULL.getMsg()).build();
        }

        List<IntegralCommodityRespDTO> integralCommodityList = getIntegralCommodityInfo(obj);

        if (CollectionUtils.isEmpty(integralCommodityList)) {
            throw WarnException.builder().code(ErrorType.PROD_IS_NULL.getCode()).
                    tipMessage(ErrorType.PROD_IS_NULL.getMsg()).build();
        }
        log.debug("查询积分商城兑换商品数据结束");
        return integralCommodityList;
    }


    public List<IntegralCommodityRespDTO> handleRequestV2(IntegralOrderCommodityReqDTO obj) {
        //特殊处理外部传参
        List<MultiCartCommodityDTO> multiCartCommodityDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(obj.getMultiCartCommodity())) {
            if (!StringUtils.isEmpty(obj.getSpecId()) && obj.getCount() > 0) {
                MultiCartCommodityDTO multiCartCommodityDTO = MultiCartCommodityDTO.builder().specId(obj.getSpecId()).count(obj.getCount()).build();
                multiCartCommodityDTOList.add(multiCartCommodityDTO);
                obj.setMultiCartCommodity(multiCartCommodityDTOList);
            } else {
                //购物车直接下单去redis查
                ActivityIntegralCartCommonRespDTO activityIntegralCartCommonRespDTO = inPurchaseCartService.get(obj.getMerCode(), obj.getMemberId(), null);
                if (activityIntegralCartCommonRespDTO != null && !CollectionUtils.isEmpty(activityIntegralCartCommonRespDTO.getStoreCommodityList())) {
                    for (ActivityIntegralCartStoreDTO activityIntegralCartStoreDTO : activityIntegralCartCommonRespDTO.getStoreCommodityList()) {
                        if (!obj.isIntegralCloudGoods() && !obj.getStoreId().equals(activityIntegralCartStoreDTO.getStoreId())) {
                            continue;
                        }
                        for (ActivityIntegralCartCommodityRespDTO commodity : activityIntegralCartStoreDTO.getCommodities()) {
                            if (YesOrNoType.NO.getCode().equals(commodity.getChoseFlag()) || YesOrNoType.NO.getCode().equals(commodity.getStatus())) {
                                continue;
                            }
                            MultiCartCommodityDTO multiCartCommodityDTO = MultiCartCommodityDTO.builder().specId(commodity.getSpecId()).count(commodity.getCount()).build();
                            multiCartCommodityDTOList.add(multiCartCommodityDTO);
                        }
                        obj.setMultiCartCommodity(multiCartCommodityDTOList);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(obj.getMultiCartCommodity())) {
            throw WarnException.builder().code(ErrorType.PROD_IS_NULL.getCode()).
                    tipMessage(ErrorType.PROD_IS_NULL.getMsg()).build();
        }
        List<IntegralCommodityRespDTO> integralCommodityList = getPurchaseCommodityInfo(obj);

        if (CollectionUtils.isEmpty(integralCommodityList)) {
            throw WarnException.builder().code(ErrorType.PROD_IS_NULL.getCode()).
                    tipMessage(ErrorType.PROD_IS_NULL.getMsg()).build();
        }
        return integralCommodityList;
    }

    /**
     * 调用购货车公共方法获取商品详细及活动相关的信息
     *
     * @return List<IntegralCommodityRespDTO> 返回对象
     */
    private List<IntegralCommodityRespDTO> getIntegralCommodityInfo(IntegralOrderCommodityReqDTO obj) {
        List<String> storeIds = Collections.singletonList(obj.getStoreId());
        List<Long> specIds = Lists.newArrayList();
        Map<Long, Integer> specCountMap = Maps.newHashMap();
        for (MultiCartCommodityDTO commodityDTO : obj.getMultiCartCommodity()) {
            specIds.add(Long.valueOf(commodityDTO.getSpecId()));
            specCountMap.put(Long.valueOf(commodityDTO.getSpecId()), commodityDTO.getCount());
        }

        // 查询商品信息 区分自营商品和云仓商品
        PageDTO<StoreSpec> storeSpecPageResp;
        Map<String, CartCommodityRespDTO> cartCommodityRespDTOMap = Maps.newHashMap();
        if (obj.isIntegralCloudGoods()) {
            storeSpecPageResp = shoppingCartService.querySpStoreSpecByClient(specIds, storeIds, obj.getMerCode(), obj.getSpCode());
            cartCommodityRespDTOMap = getCartCommodityInfo(obj).getCommodities().stream().collect(Collectors.toMap(CartCommodityRespDTO::getSpecId, Function.identity(), (k1, k2) -> k2));
            log.info("查询cartCommodityRespDTOMap:{}", JSON.toJSONString(cartCommodityRespDTOMap));
        } else {
            // 积分订单修改为单独的查询商品库存接口
            storeSpecPageResp = shoppingCartService.queryIntegralStore(specIds, storeIds, obj.getMerCode(),
                    CommodityQueryAssembleParamDTO.builder().hasShare(true).replaceCommodityNameFlag(false)
                            .replacePrescriptionDrugPicFlag(false).notSetRefPriceZeroFlag(obj.getIsAddOrder()).build());
        }
        if (storeSpecPageResp == null || CollectionUtils.isEmpty(storeSpecPageResp.getData())) {
            throw WarnException.builder().code(ErrorType.PROD_IS_NULL.getCode()).
                    tipMessage(ErrorType.PROD_IS_NULL.getMsg()).build();
        }

        List<IntegralCommodityRespDTO> integralCommodityRespList = Lists.newArrayList();
        for (StoreSpec storeSpec : storeSpecPageResp.getData()) {
            if (DrugType.PRESCRIPTION.getCode().equals(storeSpec.getDrugType())) {
                throw WarnException.builder().code(ErrorType.ORDER_PRODUCT_IS_ERROR.getCode()).
                        tipMessage(ErrorType.ORDER_PRODUCT_IS_ERROR.getMsg()).build();
            }
            // 积分商城兑换订单 商品上下架校验 规则调整
            // 1.不在单独校验云仓商品上下架
            // 2.以兑换商品上下架为准
//            if (obj.isIntegralCloudGoods()) {
//                // 云仓商品下架校验
//                if (LocalConst.STATUS_ZERO.equals(storeSpec.getShopStatus()) || LocalConst.STATUS_ZERO.equals(storeSpec.getStatus())) {
//                    throw WarnException.builder().code(ErrorType.SP_GOODS_DISABLE.getCode()).tipMessage(ErrorType.SP_GOODS_DISABLE.getMsg()).build();
//                }
//            }

            //查活动规则信息
            IntegralCommodityReqDTO integralCommodityReqDTO = new IntegralCommodityReqDTO();
            integralCommodityReqDTO.setMerCode(obj.getMerCode());
            integralCommodityReqDTO.setStoreId(obj.getStoreId());
            integralCommodityReqDTO.setSpecId(storeSpec.getSpecId());
            integralCommodityReqDTO.setUserId(obj.getMemberId());
            ResponseBase<ActivityIntegralSpecDTO> exchangeSpecInfo = promoteClient.getExchangeSpecInfo(integralCommodityReqDTO);
            // 活动商品删除校验: 商品如果已下架 接口返回的是错误码
            checkResult(exchangeSpecInfo);
            // 如果该商品有起售限制时,最好先判断库存是否充足
            ActivityIntegralSpecDTO activityIntegralSpecDTO = exchangeSpecInfo.getData();
            // 起售字段校验
            boolean b = ObjectUtil.isNotEmpty(activityIntegralSpecDTO.getMinSellNum());
            // 限购与起售校验
            boolean b1 = activityIntegralSpecDTO.getPurchaseLimit() != 0 &&  activityIntegralSpecDTO.getLimitAmount() < activityIntegralSpecDTO.getMinSellNum();
            // 库存与起售校验
            boolean b2 = activityIntegralSpecDTO.getTotalAmount() < activityIntegralSpecDTO.getMinSellNum() || activityIntegralSpecDTO.getLeftAmount() < activityIntegralSpecDTO.getMinSellNum();
            if ( b && (b1 || b2)) {
                throw WarnException.builder().code(ErrorType.STOCK_CHANGED.getCode()).
                        tipMessage(ErrorType.STOCK_CHANGED.getMsg()).build();
            }
            //组装商品数据
            IntegralCommodityRespDTO integralCommodityRespDTO = new IntegralCommodityRespDTO();
            BeanUtils.copyProperties(activityIntegralSpecDTO, integralCommodityRespDTO);
            integralCommodityRespDTO.setCount(specCountMap.get(Long.valueOf(storeSpec.getSpecId())));
            // 当门店库存为0时,并且有共享库存,则取共享库存,否则取门店库存
            if ((Objects.isNull( storeSpec.getStock()) ||  storeSpec.getStock() == 0 )&&   (Objects.nonNull( storeSpec.getSharedStock()) &&  storeSpec.getSharedStock() !=0 )) {
                storeSpec.setStock(storeSpec.getSharedStock());
            }
            integralCommodityRespDTO.packCommodity(storeSpec);
            if (!StringUtils.isEmpty(storeSpec.getOwner()) && storeSpec.getOwner() == 2) {
                // 如果货主是第三方供应商
                integralCommodityRespDTO.setSupplierCode(storeSpec.getSupplierCode());
            }
            integralCommodityRespDTO.setEpidemicRegistration(storeSpec.getHasOutbreak());
            integralCommodityRespDTO.setNeedHealthCode(Optional.ofNullable(storeSpec.getHasHealthCode()).orElse(YesOrNoType.NO.getCode()));
            integralCommodityRespDTO.setMedicalInsuranceCode(storeSpec.getMedicalInsuranceCode());
            if (obj.isIntegralCloudGoods()) {
                integralCommodityRespDTO.setMerCode(obj.getMerCode());
                integralCommodityRespDTO.setRealPrice(storeSpec.getRealPrice());
                integralCommodityRespDTO.setSpecName(storeSpec.getSpecSku());
                CartCommodityRespDTO cartCommodityRespDTO = cartCommodityRespDTOMap.get(storeSpec.getSpecId());
                if (Objects.nonNull(cartCommodityRespDTO)) {
                    integralCommodityRespDTO.setCommodityLevelActivities(cartCommodityRespDTO.getCommodityLevelActivities());
                    integralCommodityRespDTO.setOrderLevelActivities(cartCommodityRespDTO.getOrderLevelActivities());
                    integralCommodityRespDTO.setRealPrice(cartCommodityRespDTO.getGoodsSalesPrice());
                    integralCommodityRespDTO.setCostPrice(cartCommodityRespDTO.getCostPrice());
                }
            }
            integralCommodityRespList.add(integralCommodityRespDTO);
        }
        return integralCommodityRespList;
    }

    /**
     * 查询内购商品单个商品信息
     * @param obj
     * @return
     */
    private List<IntegralCommodityRespDTO> getPurchaseCommodityInfo(IntegralOrderCommodityReqDTO obj) {
        List<String> storeIds = Collections.singletonList(obj.getStoreId());
        List<Long> specIds = Lists.newArrayList();
        Map<Long, Integer> specCountMap = Maps.newHashMap();
        for (MultiCartCommodityDTO commodityDTO : obj.getMultiCartCommodity()) {
            specIds.add(Long.valueOf(commodityDTO.getSpecId()));
            specCountMap.put(Long.valueOf(commodityDTO.getSpecId()), commodityDTO.getCount());
        }

        // 查询商品信息 区分自营商品和云仓商品
        PageDTO<StoreSpec> storeSpecPageResp;
        Map<String, CartCommodityRespDTO> cartCommodityRespDTOMap = Maps.newHashMap();
        // 云仓
        if (obj.isIntegralCloudGoods()) {
            storeSpecPageResp = shoppingCartService.querySpStoreSpecByClient(specIds, storeIds, obj.getMerCode(), obj.getSpCode());
            cartCommodityRespDTOMap = getCartCommodityInfo(obj).getCommodities().stream().collect(Collectors.toMap(CartCommodityRespDTO::getSpecId, Function.identity(), (k1, k2) -> k2));
            log.info("查询cartCommodityRespDTOMap:{}", JSON.toJSONString(cartCommodityRespDTOMap));
        } else {
            storeSpecPageResp = shoppingCartService.queryStoreSpecByClient(specIds, storeIds, obj.getMerCode(),
                    CommodityQueryAssembleParamDTO.builder().hasShare(true).replaceCommodityNameFlag(false)
                            .replacePrescriptionDrugPicFlag(false).notSetRefPriceZeroFlag(obj.getIsAddOrder()).build());
        }
        if (storeSpecPageResp == null || CollectionUtils.isEmpty(storeSpecPageResp.getData())) {
            throw WarnException.builder().code(ErrorType.PROD_IS_NULL.getCode()).
                    tipMessage(ErrorType.PROD_IS_NULL.getMsg()).build();
        }

        List<IntegralCommodityRespDTO> integralCommodityRespList = Lists.newArrayList();
        for (StoreSpec storeSpec : storeSpecPageResp.getData()) {
            if (DrugType.PRESCRIPTION.getCode().equals(storeSpec.getDrugType())) {
                throw WarnException.builder().code(ErrorType.ORDER_PRODUCT_IS_ERROR.getCode()).
                        tipMessage(ErrorType.ORDER_PRODUCT_IS_ERROR.getMsg()).build();
            }
//            if (obj.isIntegralCloudGoods()) {
//                // 云仓商品下架校验
//                if (LocalConst.STATUS_ZERO.equals(storeSpec.getShopStatus()) || LocalConst.STATUS_ZERO.equals(storeSpec.getStatus())) {
//                    throw WarnException.builder().code(ErrorType.SP_GOODS_DISABLE.getCode()).tipMessage(ErrorType.SP_GOODS_DISABLE.getMsg()).build();
//                }
//            }
            // 获取商品信息
            PurchaseCommodityRespDTO dto = getPurchaseCommodityRespDTO(obj, storeSpec);
            // 内购商城订单 商品上下架校验 规则调整
            // 1.不在单独校验云仓商品上下架
            // 2.以内购商品上下架为准
            if ( LocalConst.STATUS_ZERO.equals(dto.getStatus())) {
                throw WarnException.builder().code(ErrorType.SP_GOODS_DISABLE.getCode()).tipMessage(ErrorType.SP_GOODS_DISABLE.getMsg()).build();
            }

            //组装商品数据
            IntegralCommodityRespDTO integralCommodityRespDTO = new IntegralCommodityRespDTO();
            integralCommodityRespDTO.setActivityId(String.valueOf(dto.getId()));
            integralCommodityRespDTO.setProdSpecId(dto.getSpecId());
            integralCommodityRespDTO.setExpiring(ObjectUtil.isEmpty(dto.getExpiring())? YesOrNoType.NO.getCode():dto.getExpiring());
            // 当门店库存为0时,并且有共享库存,则取共享库存,否则取门店库存
            if ((Objects.isNull( storeSpec.getStock()) ||  storeSpec.getStock() == 0 )&&   (Objects.nonNull( storeSpec.getSharedStock()) &&  storeSpec.getSharedStock() !=0 )) {
                storeSpec.setStock(storeSpec.getSharedStock());
            }

            BigDecimal price;
            if (YesOrNoType.NO.getCode().equals(dto.getUsePrice())) {
                price = dto.getActPrice();
            } else {
                price = ObjectUtil.isNotEmpty(storeSpec.getEmployPrice()) ? storeSpec.getEmployPrice() : storeSpec.getPrice();
            }
            price = ObjectUtil.isNotEmpty(price) ? price : BigDecimal.ZERO;
            // 活动库存
            integralCommodityRespDTO.setTotalAmount(dto.getStock());
            // 活动剩余库存
            integralCommodityRespDTO.setLeftAmount(dto.getSurplusStock());
            integralCommodityRespDTO.setLimitAmount(dto.getLimitAmount());
            integralCommodityRespDTO.setSpecId(dto.getSpecId().toString());
            integralCommodityRespDTO.setStoreId(storeSpec.getStoreId());
            integralCommodityRespDTO.setStoreName(storeSpec.getName());
            integralCommodityRespDTO.setErpCode(dto.getErpCode());
            integralCommodityRespDTO.setCommodityName(dto.getName());
            integralCommodityRespDTO.setMainPic(dto.getCommPic());
            integralCommodityRespDTO.setMPrice(price);
            integralCommodityRespDTO.setExchangePrice(new BigDecimal(0));
            integralCommodityRespDTO.setCount(specCountMap.get(Long.valueOf(storeSpec.getSpecId())));
            integralCommodityRespDTO.packCommodity(storeSpec);
            if (!StringUtils.isEmpty(storeSpec.getOwner()) && storeSpec.getOwner() == 2) {
                // 如果货主是第三方供应商
                integralCommodityRespDTO.setSupplierCode(storeSpec.getSupplierCode());
            }
            integralCommodityRespDTO.setBeforePrice(price);
            integralCommodityRespDTO.setPrice(price);
            integralCommodityRespDTO.setEpidemicRegistration(storeSpec.getHasOutbreak());
            integralCommodityRespDTO.setNeedHealthCode(Optional.ofNullable(storeSpec.getHasHealthCode()).orElse(YesOrNoType.NO.getCode()));
            integralCommodityRespDTO.setMedicalInsuranceCode(storeSpec.getMedicalInsuranceCode());
            if (obj.isIntegralCloudGoods()) {
                integralCommodityRespDTO.setMerCode(obj.getMerCode());
                integralCommodityRespDTO.setRealPrice(storeSpec.getRealPrice());
                integralCommodityRespDTO.setSpecName(storeSpec.getSpecSku());
                CartCommodityRespDTO cartCommodityRespDTO = cartCommodityRespDTOMap.get(storeSpec.getSpecId());
                if (Objects.nonNull(cartCommodityRespDTO)) {
                    integralCommodityRespDTO.setCommodityLevelActivities(cartCommodityRespDTO.getCommodityLevelActivities());
                    integralCommodityRespDTO.setOrderLevelActivities(cartCommodityRespDTO.getOrderLevelActivities());
                    integralCommodityRespDTO.setRealPrice(cartCommodityRespDTO.getGoodsSalesPrice());
                    integralCommodityRespDTO.setCostPrice(cartCommodityRespDTO.getCostPrice());
                }
            }
            integralCommodityRespDTO.setMinSellNum(dto.getMinSellNum());
            integralCommodityRespDTO.setIsDiscount(dto.getIsDiscount());
            integralCommodityRespList.add(integralCommodityRespDTO);
        }
        return integralCommodityRespList;
    }

    private PurchaseCommodityRespDTO getPurchaseCommodityRespDTO(IntegralOrderCommodityReqDTO obj, StoreSpec storeSpec) {
        // 查内购商品规则信息
        List<PurchasePmtReqDTO> purchasePmtReqDTOList = Lists.newArrayList();
        PurchasePmtReqDTO purchasePmtReqDTO = new PurchasePmtReqDTO();
        purchasePmtReqDTO.setMerCode(obj.getMerCode());
        purchasePmtReqDTO.setStoreId(obj.getStoreId());
        purchasePmtReqDTO.setSpecId(Long.valueOf(storeSpec.getSpecId()));
        purchasePmtReqDTOList.add(purchasePmtReqDTO);
        ResponseBase<List<PurchaseCommodityRespDTO>> responseBase = promoteClient.searchPurchaseCommodityBySpecIds(purchasePmtReqDTOList);
        log.info("查询内购商品规则信息,返回结果:{}", JSON.toJSONString(responseBase));
        // 内购商品删除校验: 商品如果已下架,则抛出异常
        checkResult(responseBase);
        checkPurchasesResult(responseBase.getData());
        PurchaseCommodityRespDTO dto = responseBase.getData().get(0);
        return dto;
    }

    /**
     * 查询商品信息
     *
     * @param obj
     * @return
     */
    private CloudStoreRespDTO getCartCommodityInfo(IntegralOrderCommodityReqDTO obj) {
        OrderConfirmCommodityReqDTO orderConfirmCommodityReqDTO = new OrderConfirmCommodityReqDTO();
        orderConfirmCommodityReqDTO.setMerCode(obj.getMerCode());
        orderConfirmCommodityReqDTO.setMultiCartCommodity(obj.getMultiCartCommodity());
        orderConfirmCommodityReqDTO.setUserId(obj.getMemberId());
        orderConfirmCommodityReqDTO.setStoreId(LocalConst.CLOUD_FIXED_STORE + obj.getSpCode());
        orderConfirmCommodityReqDTO.setSpCode(obj.getSpCode());
        orderConfirmCommodityReqDTO.setOrderType(obj.getOrderType());
        orderConfirmCommodityReqDTO.setIsAddOrder(true);
        List<CartCommodityStoreDTO> cartStoreList = shoppingCartService.orderConfirmCommodity(orderConfirmCommodityReqDTO);
        if (CollectionUtils.isEmpty(cartStoreList)) {
            log.info("查询云仓商品结果为空，req:{}", JSON.toJSONString(obj));
            throw WarnException.builder().code(ErrorType.ORDER_PROD_LIST_IS_EMPTY.getCode()).
                    tipMessage(ErrorType.ORDER_PROD_LIST_IS_EMPTY.getMsg()).build();
        }
        CartCommodityStoreDTO cartCommodityStoreDTO = cartStoreList.get(0);
        return getCloudStoreRespDTO(cartCommodityStoreDTO);
    }

    /**
     * 组装返回格式
     *
     * @param cartCommodityStoreDTO
     * @return
     */
    private CloudStoreRespDTO getCloudStoreRespDTO(CartCommodityStoreDTO cartCommodityStoreDTO) {
        if (Objects.isNull(cartCommodityStoreDTO)) {
            throw WarnException.builder().code(ErrorType.ORDER_PROD_LIST_IS_EMPTY.getCode()).
                    tipMessage(ErrorType.ORDER_PROD_LIST_IS_EMPTY.getMsg()).build();
        }
        CloudStoreRespDTO cloudStoreRespDTO = new CloudStoreRespDTO();
        cloudStoreRespDTO.setSpCode(cartCommodityStoreDTO.getSpCode());
        cloudStoreRespDTO.setSpName(cartCommodityStoreDTO.getSpName());
        cloudStoreRespDTO.setStoreName(LocalConst.CLOUD_FIXED_STORE_NAME);
        cloudStoreRespDTO.setCommodities(Lists.newArrayList());
        cloudStoreRespDTO.setFailCommodities(Lists.newArrayList());
        cloudStoreRespDTO.setTotalOrderAmount(cartCommodityStoreDTO.getBeforePrice());
        for (CartCommodityRespDTO cartCommodityRespDTO : cartCommodityStoreDTO.getCommodities()) {
            // 判断上架、库存
            if (YesOrNoType.YES.getCode().equals(cartCommodityRespDTO.getStatus()) && cartCommodityRespDTO.getCount() > 0) {
                cloudStoreRespDTO.getCommodities().add(cartCommodityRespDTO);
            } else {
                cloudStoreRespDTO.getFailCommodities().add(cartCommodityRespDTO);
            }
        }
        if (CollectionUtils.isEmpty(cloudStoreRespDTO.getCommodities())) {
            throw WarnException.builder().code(ErrorType.PROD_LIST_NULL.getCode()).
                    tipMessage(ErrorType.PROD_LIST_NULL.getMsg()).build();
        }
        return cloudStoreRespDTO;
    }

    private void checkPurchasesResult(List<PurchaseCommodityRespDTO> purchaseCommodities) {
        log.info("checkPurchasesResult purchaseCommodities:{}", JSON.toJSONString(purchaseCommodities));
        if (CollectionUtils.isEmpty(purchaseCommodities)) {
            throw WarnException.builder().code(ErrorType.NOVO_ORDER_NOT_COMMODITY.getCode()).
                    tipMessage(ErrorType.NOVO_ORDER_NOT_COMMODITY.getMsg()).build();
        }

        PurchaseCommodityRespDTO purchaseCommodityRespDTO = purchaseCommodities.get(0);
        if (Objects.isNull(purchaseCommodityRespDTO)
                ||  Objects.isNull(purchaseCommodityRespDTO.getIsPartition())
                || !purchaseCommodityRespDTO.getIsPartition()
                || purchaseCommodityRespDTO.getStatus() == 0) {
            throw WarnException.builder().code(ErrorType.PROD_IS_NULL.getCode()).
                    tipMessage(ErrorType.PROD_IS_NULL.getMsg()).build();
        }

        // 如果该商品有起售限制时,最好先判断库存是否充足
        // 起售字段校验
        boolean b = ObjectUtil.isNotEmpty(purchaseCommodityRespDTO.getMinSellNum());
        // 限购与起售校验
        boolean b1 = purchaseCommodityRespDTO.getLimitType() != 0 && purchaseCommodityRespDTO.getLimitAmount() < purchaseCommodityRespDTO.getMinSellNum();
        // 库存与起售校验
        boolean b2 =  purchaseCommodityRespDTO.getStock() < purchaseCommodityRespDTO.getMinSellNum() || purchaseCommodityRespDTO.getSurplusStock() < purchaseCommodityRespDTO.getMinSellNum();
        if (b && (b1 || b2)) {
            throw WarnException.builder().code(ErrorType.STOCK_CHANGED.getCode()).
                    tipMessage(ErrorType.STOCK_CHANGED.getMsg()).build();
        }

    }

}

