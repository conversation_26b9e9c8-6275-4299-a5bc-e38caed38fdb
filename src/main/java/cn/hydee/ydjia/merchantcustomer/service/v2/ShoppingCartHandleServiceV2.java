package cn.hydee.ydjia.merchantcustomer.service.v2;

import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;

/**
 * 购物车处理服务V2版本
 * 重构后的购物车商品处理服务，提供更清晰的业务逻辑分层
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2024
 */
public interface ShoppingCartHandleServiceV2 {
    
    /**
     * 处理购物车商品数据V2版本
     * 
     * 主要功能：
     * 1. 商品基础信息获取和验证
     * 2. 促销活动信息处理
     * 3. 会员权益和优惠券计算
     * 4. 价格计算和数据组装
     * 5. 处方药合规处理
     * 
     * @param request 购物车商品请求参数
     * @return 购物车商品响应数据
     * @throws IllegalArgumentException 当请求参数无效时
     * @throws RuntimeException 当处理过程中发生错误时
     */
    CartCommodityRespCommonDTO handleCartCommodityV2(ShoppingCartCommodityReqDTO request);
    
    /**
     * 验证请求参数
     * 
     * @param request 请求参数
     * @throws IllegalArgumentException 当参数无效时
     */
    void validateRequest(ShoppingCartCommodityReqDTO request);
}
