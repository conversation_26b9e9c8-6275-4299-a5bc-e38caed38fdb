package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.req.AddOrderReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.OrderCommitReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.OrderConfirmReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.AddOrderResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.OrderConfirmInitResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.OrderConfirmResDTO;

/**
 * <Description>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/04/17 20:29
 */
public abstract class AbstractOrderService {
    /**
     * 订单确认页面
     * @param orderConfirmReqDTO
     * @return OrderConfirmResDTO
     */
    public OrderConfirmResDTO orderConfirm(OrderConfirmReqDTO orderConfirmReqDTO){
        return null;
    }

    /**
     * 订单确认页面初始化进去页面
     * @param OrderConfirmReqDTO
     * @return OrderConfirmResDTO
     */
    public OrderConfirmInitResDTO orderConfirmInit(OrderConfirmReqDTO OrderConfirmReqDTO){
        return null;
    }

    /**
     * 提交订单
     * @param orderCommitReqDTO
     * @return
     */
    public AddOrderResDTO addOrder(OrderCommitReqDTO orderCommitReqDTO) {
        return null;
    }


    public AddOrderResDTO addVipOrder(AddOrderReqDTO addOrderReqDTO) {
        return null;
    }


    public AddOrderResDTO isExistVipOrder(AddOrderReqDTO addOrderReqDTO) {
        return null;
    }


}
