package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.CouponProductDetailDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.OrderConfirmReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.OrderCouponCalcuReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.OrderCouponDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.OrderPresaleResDTO;
import cn.hydee.ydjia.merchantcustomer.thirdinterface.marketplatform.resp.CouponDTO;

import java.math.BigDecimal;
import java.util.List;

public interface CalculationService {
    /**
     * 计算优惠券优惠金额，优先级折扣、抵价、现金
     *
     * @param prodList
     * @param couponNews
     */
    List<OrderCouponDTO> calculate(List<CartCommodityRespDTO> prodList, List<CouponDTO> couponNews, boolean flag);

    /**
     * 初始进入订单确认页面时，获取最优优惠券组合
     * 优惠券初始使用优先级：1抵价券 2折扣券 3现金券
     * 如果存在多张抵价券，则默认选择面额最大的
     * 如果存在多张折扣券，则选择折扣最低的
     * 如果存在多张现金券，则优先计算最大的，直到计算优惠金额<=0
     */
    List<CouponDTO> getBestCouponList(List<CouponDTO> couponNews);
    /**
     * 计算单个总金额
     */
    BigDecimal getTotalAmount(CartCommodityRespDTO respDTO);

    BigDecimal getTotalAmountV3(CartCommodityRespDTO respDTO);

    Integer sumTotalGoodsNumber(List<CartCommodityRespDTO> cartCommodityRespDTOS);

    /**
     * 计算总重量
     */
    Integer sumTotalGoodsWeight(List<CartCommodityRespDTO> cartCommodityRespDTOS);

    /**
     * 计算活动优惠金额
     */
    BigDecimal cacuActivityDiscountAmont(List<CartCommodityRespDTO> cartCommodityRespDTOS);

    /**
     * 计算实付金额
     */
    BigDecimal cacuTotalOrderAmount(BigDecimal totalActualOrderAmount, BigDecimal actualFreightAmount,
                                    BigDecimal activityDiscountAmont, boolean mutualExclusion, boolean hasVipActDiscount);

    /**
     * 计算总金额
     */
    BigDecimal sumTotalAmount(List<CartCommodityRespDTO> cartCommodityRespDTOS);

    /**
     * 计算总金额
     */
    BigDecimal sumTotalAmountV2(List<CartCommodityRespDTO> cartCommodityRespDTOS);

    /**
     * 计算单个商品总金额-活动优惠金额（用于优惠券优惠计算）
     */
    BigDecimal getGoodsPrice(CartCommodityRespDTO respDTO);

    /**
     * 计算单个商品总金额-活动优惠金额（用于优惠券优惠计算）
     */
    BigDecimal sumGoodsPrice(List<CartCommodityRespDTO> cartCommodityRespDTOS);

    /**
     * 计算余额支付金额
     */
    BigDecimal cacuPayBalanceAmount(BigDecimal totalActualOrderAmount, BigDecimal balanceAmount);

    /**
     * 能用券的订单场景，遍历所有商品，判断是否是否能用满减、折扣
     * @param cartCommodityList
     * @param orderType
     * @return
     */
    List<CartCommodityRespDTO> getUseCouponCommodityList(List<CartCommodityRespDTO> cartCommodityList, String orderType);

    /**
     * 计算订单确认页优惠券摊分情况
     * @param orderCouponCalcuReqDTO
     * @param paymentList
     */
    void cacuOrderConfirmDetailCoupon(OrderCouponCalcuReqDTO orderCouponCalcuReqDTO, List<CouponProductDetailDTO> paymentList);

    /**
     * 计算总定金
     */
    void handleDepositPayAmountInit(List<CartCommodityRespDTO> cartCommodityRespDTOS, OrderConfirmReqDTO orderConfirmReqDTO, OrderPresaleResDTO orderPresaleRespDTO);
    
}
