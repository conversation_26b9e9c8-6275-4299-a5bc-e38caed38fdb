package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.MerchantSwitchDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.MerchantDeliveryReq;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.StoreListReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.MerchantSwitchResp;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.MerchantSwitchType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ServiceMode;
import cn.hydee.ydjia.merchantcustomer.feign.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonStoreClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.service.MerchantDeliveryService;
import cn.hydee.ydjia.merchantcustomer.feign.service.MerchantSwitchService;
import cn.hydee.ydjia.merchantcustomer.service.YdjStoreService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Package : cn.hydee.ydjia.merchantcustomer.service.impl
 * @Description :
 * @Create on : 2020/12/8 17:21
 **/
@Service
@Slf4j
@AllArgsConstructor
public class MerchantDeliveryServiceImpl implements MerchantDeliveryService {

    private final StoreClient storeClient;
    private final MerchantSwitchService merchantSwitchService;
    private final YdjStoreService ydjStoreService;
    private final CommonStoreClientWrapperService commonStoreClientWrapperService;

    @Override
    public List<MerchantSwitchDTO> getSwitchLabel(MerchantDeliveryReq req) {
        if (StringUtils.isEmpty(req.getStoreId()) || req.getSwitchResp() == null) {
            return null;
        }
        //如果无定位的情况不匹配配送上门标签
        if (StringUtils.isEmpty(req.getLatitude()) || StringUtils.isEmpty(req.getLongitude())) {
            return this.getLabelWithoutLocation(req);
        }
        //如果有定位则匹配配送上门
        return this.getLabelWithLocation(req);
    }

    /**
     * 有定位查询标签
     *
     * @param req c
     * @return c
     */
    private List<MerchantSwitchDTO> getLabelWithLocation(MerchantDeliveryReq req) {
        if (StringUtils.isEmpty(req.getStoreId())) {
            return Lists.newArrayList();
        }
        MerchantSwitchResp switchResp = req.getSwitchResp();
        //1、查询当前门店数据
        StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
        BeanUtils.copyProperties(req, storeListReqDTO);
        storeListReqDTO.setPageSize(1);
        storeListReqDTO.setOnlineStatus(YesOrNoType.YES.getCode());
        PageDTO<StoreListResDTO> pageDTO = commonStoreClientWrapperService.pageStoreV3(storeListReqDTO);
        if (pageDTO == null || CollectionUtils.isEmpty(pageDTO.getData())) {
            return Lists.newArrayList();
        }
        //2、根据门店数据获取配送的三种方式
        StoreListResDTO store = pageDTO.getData().get(0);
        Integer isDelivery = store.getIsdelivery();
        Integer isDistribution = store.getIsdistribution();
        Integer isSelf = store.getIsself();
        MerchantSwitchDTO deliveryInfo = switchResp.getDeliveryInfo();
        MerchantSwitchDTO expressInfo = switchResp.getExpressInfo();
        MerchantSwitchDTO pickSelfInfo = switchResp.getPickSelfInfo();
        ArrayList<MerchantSwitchDTO> respList = new ArrayList<>();
        //配送上门标签开启并且门店管理配置了配送上门服务
        if (deliveryInfo != null && YesOrNoType.YES.getCode().equals(deliveryInfo.getStatus())
                && YesOrNoType.YES.getCode().equals(isDistribution)
                && !StringUtils.isEmpty(store.getDistance()) && store.getServiceScope() != null) {
            //如果顾客当前距离小于门店配送范围，即添加该标签到返回对象
            double actualDistance = Double.parseDouble(store.getDistance()) * 1000;
            int flag = BigDecimal.valueOf(actualDistance).compareTo(store.getServiceScope());
            if (flag < 0) {
                //如果B端没有配置标签文本信息，赋值为默认值
                if (StringUtils.isEmpty(deliveryInfo.getValue())) {
                    deliveryInfo.setValue(MerchantSwitchType.DELIVERY.getMsg());
                }
                respList.add(deliveryInfo);
            }
        }
        this.addLabelToList(isDelivery, isSelf, expressInfo, pickSelfInfo, respList);
        respList.sort(Comparator.comparing(MerchantSwitchDTO::getType));

        // 处理B2C业务门店快递标签
        //  todo 排除B2C 模式对标签得影响 https://jira.hxyxt.com/browse/YXDJ-1447?filter=10615
//        this.handleB2cExpressLabel(req, store, expressInfo, respList);
        return respList;
    }

    /**
     * 公共方法判断并添加商城配置标签到返回对象中
     *
     * @param isDelivery c
     * @param isSelf c
     * @param expressInfo c
     * @param pickSelfInfo c
     * @param respList c
     */
    private void addLabelToList(Integer isDelivery, Integer isSelf, MerchantSwitchDTO expressInfo
            , MerchantSwitchDTO pickSelfInfo, ArrayList<MerchantSwitchDTO> respList) {
        //普通快递标签开启并且门店管理配置了普通快递服务
        if (expressInfo != null && YesOrNoType.YES.getCode().equals(expressInfo.getStatus())
                && YesOrNoType.YES.getCode().equals(isDelivery)) {
            if (StringUtils.isEmpty(expressInfo.getValue())) {
                expressInfo.setValue(MerchantSwitchType.EXPRESS.getMsg());
            }
            respList.add(expressInfo);
        }
        //门店自提标签开启并且门店管理配置了门店自提服务
        if (pickSelfInfo != null && YesOrNoType.YES.getCode().equals(pickSelfInfo.getStatus())
                && YesOrNoType.YES.getCode().equals(isSelf)) {
            if (StringUtils.isEmpty(pickSelfInfo.getValue())) {
                pickSelfInfo.setValue(MerchantSwitchType.PICK_SELF.getMsg());
            }
            respList.add(pickSelfInfo);
        }
    }

    /**
     * 无定位状态下获取商城配置配送标签状态
     *
     * @param req c
     * @return c
     */
    private List<MerchantSwitchDTO> getLabelWithoutLocation(MerchantDeliveryReq req) {
        if (StringUtils.isEmpty(req.getStoreId())) {
            return Lists.newArrayList();
        }
        MerchantSwitchResp switchResp = req.getSwitchResp();
        ResponseBase<StoreResDTO> storeResp = storeClient.queryStore(req.getStoreId());
        if (storeResp == null || !storeResp.checkSuccess() || storeResp.getData() == null) {
            log.warn("req = {}, res store is {}", JSON.toJSONString(req), JSON.toJSONString(storeResp));
            return Lists.newArrayList();
        }
        //1、无定位根据门店ID查询门店数据
        StoreResDTO store = storeResp.getData();
        Integer isDelivery = store.getIsdelivery();
        Integer isSelf = store.getIsself();
        //2、添加、排序并返回数据
        MerchantSwitchDTO expressInfo = switchResp.getExpressInfo();
        MerchantSwitchDTO pickSelfInfo = switchResp.getPickSelfInfo();
        ArrayList<MerchantSwitchDTO> respList = new ArrayList<>();
        this.addLabelToList(isDelivery, isSelf, expressInfo, pickSelfInfo, respList);
        respList.sort(Comparator.comparing(MerchantSwitchDTO::getType));

        // 处理B2C业务门店快递标签
        //  todo 排除B2C 模式对标签得影响 https://jira.hxyxt.com/browse/YXDJ-1447?filter=10615
//        this.handleB2cExpressLabel(req, store, expressInfo, respList);
        return respList;
    }

    /**
     *
     * @param req
     * @param store
     * @param expressInfo
     * @param respList
     */
    private void handleB2cExpressLabel(MerchantDeliveryReq req, Object store, MerchantSwitchDTO expressInfo, ArrayList<MerchantSwitchDTO> respList) {
        /**
         * 前提：
         * 1、判断B2C业务
         * 2、商户配送标签普通快递开关开启且当前门店（不支持普通快递且支持B2C业务）时，做以下判断
         *
         * 3.1、查询过旗舰店：1、旗舰店为空则结束；2、当前门店不是旗舰店，则判断旗舰店是否支持B2C业务，不支持则结束；3、判断商户是否开启OBC模式，未开启则结束；4、加上快递标签
         * 3.2、未查询过旗舰店：1、判断商户是否开启OBC模式，开启后再查旗舰店；2、无旗舰店则结束；3、当前门店不是旗舰店，则判断旗舰店是否支持B2C业务，不支持则返回；4、加上快递标签
         *
         */

        if (req.getB2cFlag() == null || !req.getB2cFlag()) {
            return;
        }

        if (!(store instanceof StoreResDTO || store instanceof StoreListResDTO)) {
            return;
        }

        if (expressInfo == null || YesOrNoType.NO.getCode().equals(expressInfo.getStatus())) {
            return;
        }

        // 当前门店是否支持快递、是否支持B2C
        Integer isDelivery = YesOrNoType.NO.getCode();
        boolean storeSupportB2c = false;
        if (store instanceof StoreResDTO) {
            StoreResDTO storeResDTO = (StoreResDTO) store;
            isDelivery = storeResDTO.getIsdelivery();
            storeSupportB2c = ServiceMode.B2C.getCode().equals(storeResDTO.getServiceMode()) || ServiceMode.O2OB2C.getCode().equals(storeResDTO.getServiceMode());
        } else if (store instanceof StoreListResDTO) {
            StoreListResDTO storeListResDTO = (StoreListResDTO) store;
            isDelivery = storeListResDTO.getIsdelivery();
            storeSupportB2c = ServiceMode.B2C.getCode().equals(storeListResDTO.getServiceMode()) || ServiceMode.O2OB2C.getCode().equals(storeListResDTO.getServiceMode());
        }
        if (YesOrNoType.YES.getCode().equals(isDelivery) || !storeSupportB2c) {
            return;
        }

        // 查询过旗舰店
        if (req.getQueryFlagshipStore() != null && req.getQueryFlagshipStore()) {
            // 旗舰店为空则返回
            if (StringUtils.isEmpty(req.getFlagshipStoreId())) {
                return;
            }
            // 当前门店不是旗舰店，则判断旗舰店是否支持B2C业务，不支持则返回
            if (!req.getStoreId().equals(req.getFlagshipStoreId())) {
                if (req.getFlagshipStoreSupportB2c() == null || !req.getFlagshipStoreSupportB2c()) {
                    return;
                }
            }

            boolean isOpenObc = merchantSwitchService.queryModelIsOpen(req.getMerCode());
            if (!isOpenObc) {
                // 商户未开启OBC模式
                return;
            }
        } else {
            boolean isOpenObc = merchantSwitchService.queryModelIsOpen(req.getMerCode());
            if (!isOpenObc) {
                // 商户未开启OBC模式
                return;
            }
            StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
            storeListReqDTO.setMerCode(req.getMerCode());
            StoreListResDTO ydjStore = this.ydjStoreService.queryCenterStore(storeListReqDTO, true);
            if (ydjStore == null || !StringUtils.hasLength(ydjStore.getId())) {
                return;
            }
            // 当前门店不是旗舰店，则判断旗舰店是否支持B2C业务，不支持则返回
            if (!req.getStoreId().equals(req.getFlagshipStoreId())) {
                boolean supportB2c = ServiceMode.B2C.getCode().equals(ydjStore.getServiceMode()) || ServiceMode.O2OB2C.getCode().equals(ydjStore.getServiceMode());
                if (!supportB2c) {
                    return;
                }
            }
        }
        if (StringUtils.isEmpty(expressInfo.getValue())) {
            expressInfo.setValue(MerchantSwitchType.EXPRESS.getMsg());
        }
        respList.add(expressInfo);
        respList.sort(Comparator.comparing(MerchantSwitchDTO::getType));
    }

}
