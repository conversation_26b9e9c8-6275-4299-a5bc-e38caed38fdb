package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.UserInfoAddDto;
import cn.hydee.ydjia.merchantcustomer.dto.req.UserFollowDetailReqDto;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SearchFansRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.UserFollowDetailRespDto;
import cn.hydee.ydjia.merchantcustomer.dto.resp.UserSearchRespDto;
import cn.hydee.ydjia.merchantcustomer.dto.sdp.UserSwitchDTO;
import cn.hydee.ydjia.merchantcustomer.dto.sdp.resp.SdpUserInfoRespDto;

/**
 * <AUTHOR>
 * @date 2020/9/9 11:27
 */
public interface SdpUserInfoService {
    /**
     * 新增分销员
     *
     * @param dto
     * @return
     */
    ResponseBase<Boolean> add(UserInfoAddDto dto);

    /**
     * 检测分销员是否存在
     *
     * @param dto
     * @return
     */
    ResponseBase<Integer> checkSdpDistributor(UserFollowDetailReqDto dto);

    /**
     * 分销用户中心
     *
     * @param userId
     * @return
     */
    ResponseBase<UserSearchRespDto> detail(String merCode, String userId);


    /**
     * 查询分销员粉丝信息
     *
     * @param detailDto
     * @return
     */
    @Deprecated
    ResponseBase<UserFollowDetailRespDto> seachFollowDetail(UserFollowDetailReqDto detailDto);

    /**
     * 查询分销员的粉丝列表信息
     *
     * @param detailDto 查询参数
     * @return  分销中心-粉丝列表数据
     */
    ResponseBase<SearchFansRespDTO> searchFansPage(UserFollowDetailReqDto detailDto);

    ResponseBase<SdpUserInfoRespDto> getUserInfo(String merCode, String userId);

    /**
     * 查询用户协议状态
     *
     * @param req    c
     * @param userId c
     * @return c
     */
    Boolean queryUserAgreement(UserSwitchDTO req, String userId);

    /**
     * 新增或更新用户协议状态
     *
     * @param req    c
     * @param userId c
     * @return c
     */
    Integer updateUserAgreement(UserSwitchDTO req, String userId);
}
