package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.domain.LoydCrdCard;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

/**
 * (LoydCrdCard)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-01 14:47:15
 */
public interface LoydCrdCardService {


    /**
     * 分页查询
     *
     * @param loydCrdCard 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<LoydCrdCard> queryByPage(LoydCrdCard loydCrdCard, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param loydCrdCard 实例对象
     * @return 实例对象
     */
    LoydCrdCard insert(LoydCrdCard loydCrdCard);


}
