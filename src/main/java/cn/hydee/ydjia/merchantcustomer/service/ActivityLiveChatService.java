package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.LiveRecommendQueryReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.LiveSimpleRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @Date 2020/5/18 17:52
 */
public interface ActivityLiveChatService {

    LiveInfoRespDTO getLiveInfo(LiveReqDTO dto);

    LiveInfoRespDTO getFactoryLiveInfo(LiveReqDTO dto);

    LiveInfoRespDTO getLiveAnchorInfo(LiveReqDTO dto);

    LiveInfoRespDTO initLiveInfo(String merCode,Long liveId);

    ResponseBase<Boolean> getCoupon(LiveCouponReqDTO req);

    ResponseBase<Boolean> getLottery(String merCode,String userId,Long liveId,Integer activityId);

    ResponseBase<String> getUserSig(String userId);

    List<LiveCouponRespDto> getCouponList(LiveCouponListReqDTO req);

    List<CommoditySpecDTO> getCommodityList(LiveCommodityReqDTO req);

    ResponseBase<PageDTO<LiveSimpleRespDTO>> getRecommendList(LiveRecommendQueryReqDTO reqDTO);

    ResponseBase<Boolean> remindLiveStart(LiveStartRemindReqDTO reqDTO);

    ResponseBase<Boolean> saveShareRecord(LiveSaveShareRecordReqDTO reqDTO);

    ResponseBase<Boolean> updateLikeCount(LiveLikeCountReqDTO reqDTO);

    ResponseBase<Boolean> updateFrequency(LiveFrequencyReqDTO reqDTO);

    ResponseBase<List<LiveSharerRecordRankRespDTO>> shareRecordRank(String merCode,Long liveId);

    ResponseBase<List<LiveViewerRespDTO>> liveLikeRank(Long liveId);

    LiveIdCardCheckRespDTO idCardCheck(LiveIdCardCheckReqDTO reqDTO, String merCode, String userId);

    ResponseBase<List<LiveViewerRespDTO>> getLiveViewerInfo(Long liveId, List<Long> userIds);

    LiveSendMessageDTO getMessage(Long liveId);

    Long getAnchorUserId(Long liveId);

    String createSharePoster(LiveSharePosterReqDTO reqDTO);

    ResponseBase<Boolean> updateInviteName(LiveInviteNameReqDTO reqDTO);
}
