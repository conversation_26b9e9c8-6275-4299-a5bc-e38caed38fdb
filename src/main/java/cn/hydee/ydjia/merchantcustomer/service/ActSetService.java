package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.ActSetDTO;

import cn.hydee.ydjia.merchantcustomer.feign.domain.CommDecorationSet;
import cn.hydee.ydjia.merchantcustomer.feign.domain.DmPageSet;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.MainPageReqDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Author: mengzilei
 * @Date: 2024-05-07  16:24
 */
public interface ActSetService extends IService<CommDecorationSet> {

    /**
     * 查询定制活动单组件信息
     *
     * @param setDTO
     * @return
     */
    List<ActSetDTO> queryActSet(String userId, MainPageReqDTO setDTO);
}
