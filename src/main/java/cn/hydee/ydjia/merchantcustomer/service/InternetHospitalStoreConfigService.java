package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.domain.InternetHospitalStoreConfig;
import cn.hydee.ydjia.merchantcustomer.dto.req.IntHosStoreConfigReq;
import cn.hydee.ydjia.merchantcustomer.dto.resp.IntHosStoreConfigResp;

/**
 * @Author: mengzilei
 * @Date: 2024-10-18  16:57
 * <p>
 * 互联网医院门店配置相关
 */


public interface InternetHospitalStoreConfigService {

    IntHosStoreConfigResp queryStoreConfig(IntHosStoreConfigReq req);

}
