package cn.hydee.ydjia.merchantcustomer.service.v2.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.enums.CommodityOriginType;
import cn.hydee.ydjia.merchantcustomer.enums.PmtProductType;
import cn.hydee.ydjia.merchantcustomer.feign.dto.CommodityQueryAssembleParamDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreSpec;
import cn.hydee.ydjia.merchantcustomer.service.ShoppingCartHandleService;
import cn.hydee.ydjia.merchantcustomer.service.ShoppingCartService;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartCommodityProcessor;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 购物车商品处理器实现
 *
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Component
public class CartCommodityProcessorImpl implements CartCommodityProcessor {

    @Autowired
    private ShoppingCartService shoppingCartService;

    @Autowired
    private ShoppingCartHandleService shoppingCartHandleService;

    @Override
    public ShoppingCartProductHandleDTO processProductInfo(ShoppingCartCommodityReqDTO request) {
        try {
            log.info("开始处理商品基础信息，用户：{}", request.getUserId());

            // 1. 清理无效商品
            ShoppingCartProductHandleDTO cleanedData = cleanInvalidProducts(request);
            if (cleanedData == null) {
                return null;
            }

            // 2. 过滤普通商品（排除赠品、换购商品等）
            List<CartCommodityDTO> normalProducts = filterNormalProducts(request.getCartRedisSpecs());
            if (CollectionUtils.isEmpty(normalProducts)) {
                log.info("无普通商品，用户：{}", request.getUserId());
                return null;
            }

            // 3. 处理集团化数据隔离
            normalProducts = handleCollectivizationIfNeeded(request, normalProducts);
            if (CollectionUtils.isEmpty(normalProducts)) {
                log.info("集团化处理后无有效商品，用户：{}", request.getUserId());
                return null;
            }

            // 4. 更新请求中的商品列表
            request.setCartRedisSpecs(normalProducts);

            // 5. 获取商品详细信息
            ShoppingCartProductHandleDTO productData = getProductDetails(request);

            log.info("商品基础信息处理完成，有效商品数量：{}",
                    productData != null ? productData.getRedisSpecs().size() : 0);

            return productData;

        } catch (Exception e) {
            log.error("处理商品基础信息失败，用户：{}", request.getUserId(), e);
            throw new RuntimeException("商品信息处理失败", e);
        }
    }

    @Override
    public boolean validateProductData(ShoppingCartProductHandleDTO productData) {
        if (productData == null) {
            return false;
        }

        if (CollectionUtils.isEmpty(productData.getRedisSpecs())) {
            log.warn("商品数据验证失败：Redis商品列表为空");
            return false;
        }

        if (CollectionUtils.isEmpty(productData.getStoreSpecs())) {
            log.warn("商品数据验证失败：门店商品规格列表为空");
            return false;
        }

        return true;
    }

    @Override
    public ShoppingCartProductHandleDTO cleanInvalidProducts(ShoppingCartCommodityReqDTO request) {
        try {
            // 这里可以调用原有的清理逻辑
            // 例如清理失效门店商品、下架商品等
            log.info("开始清理无效商品，用户：{}", request.getUserId());

            // 实际实现中应该调用具体的清理方法
            // 这里简化处理，返回基础数据结构
            ShoppingCartProductHandleDTO result = new ShoppingCartProductHandleDTO();
            result.setStoreList(request.getStoreList());

            return result;

        } catch (Exception e) {
            log.error("清理无效商品失败，用户：{}", request.getUserId(), e);
            return null;
        }
    }

    /**
     * 过滤普通商品
     */
    private List<CartCommodityDTO> filterNormalProducts(List<CartCommodityDTO> cartSpecs) {
        if (CollectionUtils.isEmpty(cartSpecs)) {
            return Lists.newArrayList();
        }

        return cartSpecs.stream()
                .filter(spec -> PmtProductType.NORMAL.getCode().equals(spec.getPmtProductType()))
                .collect(Collectors.toList());
    }

    /**
     * 处理集团化数据隔离（如果需要）
     */
    private List<CartCommodityDTO> handleCollectivizationIfNeeded(ShoppingCartCommodityReqDTO request,
                                                                 List<CartCommodityDTO> products) {
        if (!Boolean.TRUE.equals(request.getCollectivizationFlag())) {
            return products;
        }

        try {
            log.info("开始处理集团化数据隔离，用户：{}", request.getUserId());

            // 创建集团化处理参数
            // CartCollectivizationHandlerParamDTO param = createCollectivizationParam(request);

            // 调用集团化处理服务
            // return shoppingCartService.handleCollectivization(param, products);

            // 这里简化处理，直接返回原数据
            return products;

        } catch (Exception e) {
            log.error("集团化数据处理失败，用户：{}", request.getUserId(), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 获取商品详细信息
     */
    private ShoppingCartProductHandleDTO getProductDetails(ShoppingCartCommodityReqDTO request) {
        try {
            log.info("开始获取商品详细信息，用户：{}，商品数量：{}",
                    request.getUserId(), request.getCartRedisSpecs().size());

            ShoppingCartProductHandleDTO result = new ShoppingCartProductHandleDTO();
            result.setStoreList(request.getStoreList());
            result.setRedisSpecs(request.getCartRedisSpecs());

            // 1. 分离不同类型的商品和门店
            List<CartCommodityDTO> redisSpecs = request.getCartRedisSpecs();
            List<Long> o2oSpecIds = Lists.newArrayList();
            List<String> o2oStoreIds = Lists.newArrayList();
            List<Long> spSpecIds = Lists.newArrayList();
            List<String> spCodes = Lists.newArrayList();

            // 分类商品规格ID和门店ID
            for (CartCommodityDTO spec : redisSpecs) {
                if (spec.getStoreId().startsWith(LocalConst.CLOUD_FIXED_STORE)) {
                    // 云仓商品
                    spSpecIds.add(Long.valueOf(spec.getSpecId()));
                    if (!spCodes.contains(spec.getSpCode())) {
                        spCodes.add(spec.getSpCode());
                    }
                } else {
                    // O2O商品
                    o2oSpecIds.add(Long.valueOf(spec.getSpecId()));
                    if (!o2oStoreIds.contains(spec.getStoreId())) {
                        o2oStoreIds.add(spec.getStoreId());
                    }
                }
            }

            List<StoreSpec> allStoreSpecs = Lists.newArrayList();

            // 2. 查询O2O商品信息
            if (!CollectionUtils.isEmpty(o2oSpecIds) && !CollectionUtils.isEmpty(o2oStoreIds)) {
                PageDTO<StoreSpec> o2oSpecs = queryO2OStoreSpecs(o2oSpecIds, o2oStoreIds, request);
                if (o2oSpecs != null && !CollectionUtils.isEmpty(o2oSpecs.getData())) {
                    allStoreSpecs.addAll(o2oSpecs.getData());
                }
            }

            // 3. 查询云仓商品信息
            if (!CollectionUtils.isEmpty(spSpecIds) && !CollectionUtils.isEmpty(spCodes)) {
                PageDTO<StoreSpec> spSpecs = querySpStoreSpecs(spSpecIds, spCodes, request);
                if (spSpecs != null && !CollectionUtils.isEmpty(spSpecs.getData())) {
                    allStoreSpecs.addAll(spSpecs.getData());
                }
            }

            // 4. 构建商品规格Map
            Map<String, StoreSpec> storeSpecMap = buildStoreSpecMap(allStoreSpecs);
            Map<String, CartCommodityDTO> redisSpecMap = buildRedisSpecMap(redisSpecs);

            // 5. 设置结果数据
            result.setStoreSpecs(allStoreSpecs);
            result.setStoreSpecMap(storeSpecMap);
            result.setRedisSpecMap(redisSpecMap);
            result.setStoreIds(extractStoreIds(redisSpecs));

            // 6. 设置OBC和B2C配置（简化处理）
            result.setIsOpenObc(true);
            result.setIsSupportB2c(true);

            log.info("获取商品详细信息完成，用户：{}，有效商品规格数量：{}",
                    request.getUserId(), allStoreSpecs.size());

            return result;

        } catch (Exception e) {
            log.error("获取商品详细信息失败，用户：{}", request.getUserId(), e);
            throw new RuntimeException("获取商品详情失败", e);
        }
    }

    /**
     * 查询O2O商品规格
     */
    private PageDTO<StoreSpec> queryO2OStoreSpecs(List<Long> specIds, List<String> storeIds,
                                                  ShoppingCartCommodityReqDTO request) {
        try {
            CommodityQueryAssembleParamDTO queryParam = CommodityQueryAssembleParamDTO.builder()
                    .hasShare(true)
                    .replaceCommodityNameFlag(request.getReplaceCommodityNameFlag() != null &&
                                            request.getReplaceCommodityNameFlag() &&
                                            request.getIfCartOperate() != null &&
                                            request.getIfCartOperate())
                    .replacePrescriptionDrugPicFlag(false)
                    .notSetRefPriceZeroFlag(false)
                    .build();

            return shoppingCartHandleService.queryStoreSpecByClient(specIds, storeIds,
                    request.getMerCode(), queryParam);

        } catch (Exception e) {
            log.error("查询O2O商品规格失败，用户：{}", request.getUserId(), e);
            return new PageDTO<>();
        }
    }

    /**
     * 查询云仓商品规格
     */
    private PageDTO<StoreSpec> querySpStoreSpecs(List<Long> specIds, List<String> spCodes,
                                                 ShoppingCartCommodityReqDTO request) {
        try {
            // 这里应该调用云仓商品查询接口
            // 简化处理，返回空结果
            log.info("查询云仓商品规格，用户：{}，规格数量：{}，供应商数量：{}",
                    request.getUserId(), specIds.size(), spCodes.size());
            return new PageDTO<>();

        } catch (Exception e) {
            log.error("查询云仓商品规格失败，用户：{}", request.getUserId(), e);
            return new PageDTO<>();
        }
    }

    /**
     * 构建商品规格Map
     */
    private Map<String, StoreSpec> buildStoreSpecMap(List<StoreSpec> storeSpecs) {
        if (CollectionUtils.isEmpty(storeSpecs)) {
            return Maps.newHashMap();
        }

        return storeSpecs.stream().collect(Collectors.toMap(
                spec -> {
                    if (CommodityOriginType.belongToSp(spec.getOrigin())) {
                        // 云仓商品：CLOUD_STORE + spCode + "_" + specId
                        return LocalConst.CLOUD_FIXED_STORE + spec.getSpCode() + "_" + spec.getSpecId();
                    } else {
                        // O2O商品：storeId + "_" + specId
                        return spec.getStoreId() + "_" + spec.getSpecId();
                    }
                },
                Function.identity(),
                (existing, replacement) -> existing
        ));
    }

    /**
     * 构建Redis商品规格Map
     */
    private Map<String, CartCommodityDTO> buildRedisSpecMap(List<CartCommodityDTO> redisSpecs) {
        if (CollectionUtils.isEmpty(redisSpecs)) {
            return Maps.newHashMap();
        }

        return redisSpecs.stream().collect(Collectors.toMap(
                spec -> spec.getSpecId() + "_" + (spec.getBreakType() == null ? 0 : spec.getBreakType()),
                Function.identity(),
                (existing, replacement) -> existing
        ));
    }

    /**
     * 提取门店ID列表
     */
    private List<String> extractStoreIds(List<CartCommodityDTO> redisSpecs) {
        if (CollectionUtils.isEmpty(redisSpecs)) {
            return Lists.newArrayList();
        }

        return redisSpecs.stream()
                .map(CartCommodityDTO::getStoreId)
                .distinct()
                .collect(Collectors.toList());
    }
}
