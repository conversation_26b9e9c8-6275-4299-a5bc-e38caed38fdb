package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.annotation.AuthedDispatch;
import cn.hydee.ydjia.merchantcustomer.dto.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.coupon.ChooseCouponRequest;
import cn.hydee.ydjia.merchantcustomer.dto.req.coupon.SaleCouponPlusRequest;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.enums.ActivityHandleType;
import cn.hydee.ydjia.merchantcustomer.enums.CommodityType;
import cn.hydee.ydjia.merchantcustomer.enums.CouponDeliveryTypeEnum;
import cn.hydee.ydjia.merchantcustomer.enums.CouponSceneRule;
import cn.hydee.ydjia.merchantcustomer.feign.ActivityCouponBusClient;
import cn.hydee.ydjia.merchantcustomer.feign.MemberInfoClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.ActivityGetCouponCenterDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CouponProductDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CouponProductTypeDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.CommodityOriginType;
import cn.hydee.ydjia.merchantcustomer.service.*;
import cn.hydee.ydjia.merchantcustomer.thirdinterface.marketplatform.resp.CouponDTO;
import cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon.IdprefixCheckGoodsInfo;
import cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon.MerCouponLimitGoodsNew;
import cn.hydee.ydjia.merchantcustomer.util.BeanCopyUtil;
import cn.hydee.ydjia.merchantcustomer.util.DateUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.ServletUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.controller.ActivityCouponController.COUPON_TEMPLATE_CODE;
import static cn.hydee.ydjia.merchantcustomer.util.TypeConversionUtil.integerToLong;
import static cn.hydee.ydjia.merchantcustomer.util.TypeConversionUtil.stringToLong;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * 新营销平台优惠券相关的接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/05/14 14:08
 */
@Slf4j
@Service
@AuthedDispatch(value = "coupon")
public class CouponNewServiceImpl implements CouponService {

    private final static String COMBINE_PRO_PREFIX = "ZH";
    @Autowired
    private ActivityCouponBusClient couponBusClient;
    @Autowired
    private MemberInfoClient memberInfoClient;
    @Autowired
    private ActivityReceiveCouponFactory couponFactory;
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private StoreService storeService;
    @Autowired
    private CalculationService calculationService;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private OrderValidatorService orderValidatorService;


    /**
     * @param joinString
     * @param type       1:普通商品/分类 2:组合商品
     * @return
     */
    private static List<String> getCodeList(String joinString, Integer type) {
        if (StringUtils.isBlank(joinString)) {
            return Lists.newLinkedList();
        }

        List<String> proCodeList = Arrays.asList(joinString.split("#"));

        if (type.equals(1)) {
            proCodeList = proCodeList.parallelStream().filter(proCode -> checkProRule(proCode, type))
                    .collect(Collectors.toList());
        }

        if (type.equals(2)) {
            proCodeList = proCodeList.parallelStream().filter(proCode -> checkProRule(proCode, type))
                    .collect(Collectors.toList());
        }
        return proCodeList;
    }

    private static boolean checkProRule(String proCode, Integer type) {
        boolean flag = false;
        if (type.equals(1)) {
            flag = !StringUtils.startsWithIgnoreCase(proCode, COMBINE_PRO_PREFIX);
        }
        if (type.equals(2)) {
            flag = StringUtils.startsWithIgnoreCase(proCode, COMBINE_PRO_PREFIX);
        }
        return flag;
    }

    /**
     * 调营销平台接口通过优惠券券码列表查优惠券信息
     *
     * @param commodityList 商品编码集合
     * @return 返回当前用户对应券码中未使用的优惠券集合
     */
    @Override
    public ConsumeCouponAdapterResponse getCouponListByCode(OrderCouponCalcuReqDTO reqDTO, List<CartCommodityRespDTO> commodityList) {
        //未使用优惠券
        if (CollectionUtils.isEmpty(reqDTO.getCouponCodeList())) {
            if (Objects.isNull(reqDTO.getNoCouponCalculationFlag()) || !reqDTO.getNoCouponCalculationFlag()) {
                return null;
            }
            // 特殊场景：不计算券后价不返回券优惠不返回券码，仅返回可用券
        }
        ChooseCouponRequest request = new ChooseCouponRequest();
        request.setMemberCard(!StringUtils.isEmpty(reqDTO.getMemberCard()) ? reqDTO.getMemberCard() : foundMemberCardId(reqDTO.getMerCode(), reqDTO.getMemberId()));
        request.setUserId(Long.valueOf(reqDTO.getMemberId()));
        request.setMerCode(reqDTO.getMerCode());
        request.setSceneRuleFlag(CouponSceneRule.ONLINE.getCode());

        BigDecimal allAmount = new BigDecimal(0);
        List<CouponProductDetailDTO> paymentList = Lists.newLinkedList();
        for (CartCommodityRespDTO respDTO : commodityList) {
            CouponProductDetailDTO activityPaymentDTO = new CouponProductDetailDTO();
            activityPaymentDTO.setProductCode(respDTO.getErpCode());
            activityPaymentDTO.setTypeCode(respDTO.getTypeId());
            BigDecimal payment = respDTO.getCouponPayment() != null ? respDTO.getCouponPayment() : calculationService.getGoodsPrice(respDTO);
            //定金预售优惠券要除去定金
            if(orderValidatorService.checkPresaleDepositOrder(respDTO.getCommodityLevelActivities(),reqDTO.getOrderPresaleRespDTO())){
                payment = payment.subtract(respDTO.getDepositPayAmount());
            }
            activityPaymentDTO.setPayment(payment);
            activityPaymentDTO.setOriginPayment(calculationService.getTotalAmount(respDTO));
            activityPaymentDTO.setUseCoupon(respDTO.getUseCoupon());
            activityPaymentDTO.setGainType(CommodityType.Combined_commodity.getCode().equals(respDTO.getCommodityType()) ? 3 : 0);
            activityPaymentDTO.setCommodityOrigin(StringUtils.isEmpty(reqDTO.getSpCode()) ? CommodityOriginType.HYDEE.getCollectionType() : CommodityOriginType.CLOUD.getCollectionType());
            activityPaymentDTO.setCommodityCount(respDTO.getCount());
            activityPaymentDTO.setApplyPaidMemberPrice(respDTO.getApplyPaidMemberPrice());
            paymentList.add(activityPaymentDTO);
            allAmount = allAmount.add(payment);
        }

        request.setPaymentList(paymentList);
        request.setAmount(allAmount);
        request.setStoreCode(reqDTO.getStCode());
        request.setIspCode(reqDTO.getSpCode());
        request.setOperateCouponCodes(reqDTO.getCouponCodeList());
        request.setEmpFlag(LocalConst.STATUS_ONE.toString().equals(ServletUtils.getHeaderIgnoreCase(LocalConst.HEAD_EMPLOYEE_FLAG)));

        // 设置当前配送方式
        if (Objects.nonNull(reqDTO.getDeliveryType())) {
            CouponDeliveryTypeEnum itemByDeliveryType = CouponDeliveryTypeEnum.getItemByDeliveryType(reqDTO.getDeliveryType());
            if (Objects.nonNull(itemByDeliveryType) && Objects.nonNull(itemByDeliveryType.getCode())) {
                request.setDeliveryType(Collections.singletonList(itemByDeliveryType.getCode()));
            }
        }
        log.debug("[chooseCoupon] req={}", JSON.toJSONString(request));
        ResponseBase<ConsumeCouponResponse> responseBase = couponBusClient.chooseCoupon(request);
        if (!responseBase.checkSuccess()) {
            log.error("[chooseCoupon] invoke 'consumeCouponList' error  resp={}", responseBase);
            throw WarnException.builder().code(responseBase.getCode()).message(responseBase.getMsg()).build();
        }
        ConsumeCouponResponse response = responseBase.getData();
        if (CollectionUtils.isEmpty(response.getCommonCouponDetails()) && CollectionUtils.isEmpty(response.getAdditionCouponDetails())) {
            return null;
        }
        log.debug("[chooseCoupon] resp={}", JSON.toJSONString(responseBase.getData()));

        ConsumeCouponAdapterResponse couponAdapterResponse = BeanCopyUtil.copy(response, ConsumeCouponAdapterResponse::new, (s, t) -> {
            if (!CollectionUtils.isEmpty(s.getCommonCouponDetails())) {
                t.setCommonCouponDetails(s.getCommonCouponDetails().stream().map(detail -> transfer(detail, commodityList)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(s.getAdditionCouponDetails())) {
                t.setAdditionCouponDetails(s.getAdditionCouponDetails().stream().map(detail -> transfer(detail, commodityList)).collect(Collectors.toList()));
            }
        });

        ConsumerCouponInitReqDTO couponInitReqDTO = ConsumerCouponInitReqDTO.builder()
                .storeCode(request.getStoreCode()).amount(request.getAmount()).paymentList(request.getPaymentList()).build();
        if (StringUtils.isNotBlank(request.getIspCode())) {
            couponInitReqDTO.setSpCode(request.getIspCode());
            couponInitReqDTO.setStoreCode(null);
        }
        couponAdapterResponse.setCouponInitReqDTO(couponInitReqDTO);
        return couponAdapterResponse;
    }

    private boolean checkAvailable(CouponDetailDTO couponDetailDTO, String userId, String merCode, String memberCard) {
        return StringUtils.equals(couponDetailDTO.getMerCode(), merCode)
                && couponDetailDTO.getState().equals(0)
                && checkUserInfo(Long.valueOf(userId), couponDetailDTO, memberCard);
    }

    private boolean checkUserInfo(Long userId, CouponDetailDTO couponDetailDTO, String memberCard) {
        //匹配会员ID和会员卡号有一个成功，即可使用优惠券。兼容测试环境被删用户，userId新生成，卡号保留的情况
        if (Objects.nonNull(couponDetailDTO.getUserId()) && couponDetailDTO.getUserId().equals(userId)) {
            return true;
        }
        if (Objects.nonNull(couponDetailDTO.getUserCard())) {
            memberCard = !StringUtils.isEmpty(memberCard) ? memberCard : foundMemberCardId(couponDetailDTO.getMerCode(), String.valueOf(userId));
            return couponDetailDTO.getUserCard().equals(memberCard);
        }
        Long uid = memberInfoService.foundUserIdByMemberCard(couponDetailDTO.getMerCode(), couponDetailDTO.getUserCard());
        return userId.equals(uid);
    }

    /**
     * 商品对应可领的优惠券(不分页)
     *
     * @param dto
     * @return
     */
    @Override
    public List<CouponDTO> getCouponsRuleInfo(CouponRuleQueryDTO dto) {
        log.info("[GetCouponsRuleInfo] {}", dto);
        ActivityGetCouponProductReqDTO request = new ActivityGetCouponProductReqDTO();
        if (StringUtils.isEmpty(dto.getStoreCode())) {
            if (!StringUtils.isEmpty(dto.getStores().get(0).getSpCode())) {
                request.setIspCode(dto.getStores().get(0).getSpCode());
            } else {
                ResponseBase<StoreResDTO> storeResponse = storeClient.queryStore(dto.getStores().get(0).getStoreId());
                checkResult(storeResponse);
                request.setStoreCode(storeResponse.getData().getStCode());
            }
        } else {
            request.setStoreCode(dto.getStoreCode());
        }
        request.setUserId(stringToLong(dto.getUserId()));
        request.setActivityType("1"); //free
        request.setActivityTemplateCode(COUPON_TEMPLATE_CODE);
        request.setMerCode(dto.getMerCode());
        request.setState(1);
        request.setSceneRuleFlag(1);
        request.setEmpFlag(dto.getEmpFlag());
        if (!CollectionUtils.isEmpty(dto.getStores())) {
            request.setPaymentList(dto.getStores().get(0).getErpCodes().stream().map(var -> {
                ActivityPaymentDTO paymentDTO = new ActivityPaymentDTO();
                paymentDTO.setProductCode(var.getProductCode());
                paymentDTO.setTypeCode(var.getProductTypeCode());
                paymentDTO.setCommodityOrigin(StringUtils.isEmpty(request.getIspCode()) ? CommodityOriginType.HYDEE.getCollectionType() : CommodityOriginType.CLOUD.getCollectionType());
                return paymentDTO;
            }).collect(Collectors.toList()));
        }
        ResponseBase<List<ActivityGetCouponCenterDTO>> responseBase = couponBusClient.listActivityDetailProduct(request);
        if (!responseBase.checkSuccess()) {
            log.error("[GetCouponsRuleInfo] invoke 'listActivityDetailProduct' response error request={} ", request);
            throw WarnException.builder().code(responseBase.getCode()).
                    tipMessage(responseBase.getMsg()).build();
        }

        if (CollectionUtils.isEmpty(responseBase.getData())) {
            log.info("[GetCouponsRuleInfo] not found any available coupon request={}", request);
            return Lists.newLinkedList();
        }

        return responseBase.getData().parallelStream().map(details -> {
            try {
                return transfer(details);
            } catch (Exception ex) {
                log.error("[getCouponsRuleInfo] transfer error {}", details);
                throw ex;
            }
        }).collect(Collectors.toList());
    }

    private CouponDTO transfer(ActivityGetCouponCenterDTO details) {
        log.debug("[getCouponsRuleInfo] market-response=> {}", details);
        CouponDTO couponDTO = new CouponDTO();
        couponDTO.setMerCode(details.getMerCode());
        couponDTO.setTitle(details.getCName());
        couponDTO.setType(details.getCType());
        couponDTO.setId(Long.valueOf(details.getId()));
        couponDTO.setMoney(details.getDenomination().doubleValue());
        couponDTO.setMoneyLimit(details.getUseRule().doubleValue());
        couponDTO.setActivityId(details.getActivityId());
        couponDTO.setId(Long.valueOf(details.getId()));
        couponDTO.setCanTakeFlag(true);
        couponDTO.setLimitUsedNum(details.getLimitUsedNum());
        couponDTO.setTotalUsedNum(details.getTotalUsedNum());
        couponDTO.setGiftLimitNum(details.getGiftLimitNum());
        couponDTO.setDeliveryType(details.getDeliveryType());
        couponDTO.setMaxPrice(details.getMaxPrice());
        if (details.getActivityType() != null) {
            switch (details.getActivityType()) {
                case 1:
                    couponDTO.setSendType(2L);
                    break;
                case 2:  //integral
                    couponDTO.setCostType(1);
                    couponDTO.setCost(details.getIntegral());
                    couponDTO.setSendType(6L);
                    break;
                case 3:  //cash
                    couponDTO.setCostType(2);
                    couponDTO.setCost(details.getAmount());
                    couponDTO.setSendType(7L);
                    break;
            }
        }

        if (details.getTimeRule() != null) {
            String[] rule;
            switch (details.getTimeRule()) {
                case 1:   //领取有效
                    couponDTO.setEffectiveDays(stringToLong(details.getEffectTime()));
                    break;

                case 2: //有失效时间
                    rule = details.splitEffectTime();
                    if (rule != null) {
                        couponDTO.setEffectiveDays(stringToLong(rule[1]));
                        couponDTO.setInvalidDays(stringToLong(rule[0]));
                    }
                    break;
                case 3: //具体时间
                    rule = details.splitEffectTime();
                    if (rule != null) {
                        try {
                            couponDTO.setBecomeEffectiveTime(DateUtil.parse(rule[0], "yyyy-MM-dd HH:mm:ss"));
                            couponDTO.setLoseEffectiveTime(DateUtil.parse(rule[1], "yyyy-MM-dd HH:mm:ss"));
                        } catch (ParseException ignore) {
                        }
                    }
                    break;
            }

        }
        couponDTO.setStatus(Long.valueOf(details.getState()));
        couponDTO.setGetStartTime(DateUtil.parseToDate(details.getBeginTime()));
        couponDTO.setGetEndTime(DateUtil.parseToDate(details.getEndTime()));
        couponDTO.setDescription(details.getNote());
        couponDTO.setResidueCount(integerToLong(details.getTotalCount() - details.getTotalReceiveCountActivity()));
        couponDTO.setCName(details.getCName());
        couponDTO.setDenomination(details.getDenomination());
        couponDTO.setCombineProductRule(details.getCombineProductRule());
        couponDTO.setUseRule(details.getUseRule());
        couponDTO.setListCouponStoreEntity(details.getListCouponStoreEntity());
        couponDTO.setListCouponProductEntity(details.getListCouponProductEntity());
        couponDTO.setListCouponProductTypeEntity(details.getListCouponProductTypeEntity());
        couponDTO.setCType(details.getCType());
        couponDTO.setState(details.getState());
        couponDTO.setActivityState(details.getActivityState());
        couponDTO.setNote(details.getNote());
        couponDTO.setTotalCount(details.getTotalCount());
        couponDTO.setPerCount(details.getPerCount());
        couponDTO.setPerCountGet(details.getPerCountGet());
        couponDTO.setPerCountGetAvailable(details.getPerCountGetAvailable());
        couponDTO.setTotalReceiveCountActivity(details.getTotalReceiveCountActivity());
        couponDTO.setActivityType(details.getActivityType());
        couponDTO.setShopRule(details.getShopRule());
        couponDTO.setProductRule(details.getProductRule());
        couponDTO.setTimeRule(details.getTimeRule());
        couponDTO.setBeginTime(details.getBeginTime());
        couponDTO.setEndTime(details.getEndTime());
        couponDTO.setIntegral(details.getIntegral());
        couponDTO.setAmount(details.getAmount());
        couponDTO.setSceneRule(details.getSceneRule());
        couponDTO.setCreateFrom(details.getCreateFrom());
        couponDTO.setListCouponStoreEntity(details.getListCouponStoreEntity());
        couponDTO.setListCouponProductEntity(details.getListCouponProductEntity());
        couponDTO.setListCouponProductTypeEntity(details.getListCouponProductTypeEntity());
        couponDTO.setUseRuleType(details.getUseRuleType());
        couponDTO.setSceneRuleList(details.getSceneRuleList());
        couponDTO.setDenominationType(details.getDenominationType());
        log.debug("[getCouponsRuleInfo] response=> {}", couponDTO);
        return couponDTO;
    }

    private CouponDTO transfer(CouponDetailSimpleResDTO details) {
        CouponDTO couponDTO = new CouponDTO();
        couponDTO.setMerCode(details.getMerCode());
        couponDTO.setTitle(details.getCName());
        couponDTO.setType(details.getCType());
        couponDTO.setId(Long.valueOf(details.getId()));
        couponDTO.setMoney(details.getDenomination().doubleValue());
        couponDTO.setMoneyLimit(details.getUseRule().doubleValue());
        couponDTO.setActivityId(details.getActivityId());
        couponDTO.setId(Long.valueOf(details.getCouponId()));
        couponDTO.setCanTakeFlag(true);
        couponDTO.setStatus(Long.valueOf(details.getState()));
        couponDTO.setCName(details.getCName());
        couponDTO.setDenomination(details.getDenomination());
        couponDTO.setCombineProductRule(details.getCombineProductRule());
        couponDTO.setUseRule(details.getUseRule());
        couponDTO.setCType(details.getCType());
        couponDTO.setState(details.getState());
        couponDTO.setShopRule(details.getShopRule());
        couponDTO.setProductRule(details.getProductRule());
        couponDTO.setSceneRule(details.getSceneRule());
        couponDTO.setBecomeEffectiveTime(DateUtil.parseToDate(details.getValidBeginTime()));
        couponDTO.setLoseEffectiveTime(DateUtil.parseToDate(details.getValidEndTime()));
        couponDTO.setNote(details.getNote());
        couponDTO.setCreateFrom(details.getCreateFrom());
        couponDTO.setLimitUsedNum(details.getLimitUsedNum());
        couponDTO.setTotalUsedNum(details.getTotalUsedNum());
        couponDTO.setGiftLimitNum(details.getGiftLimitNum());
        couponDTO.setDeliveryType(details.getDeliveryType());
        couponDTO.setListCouponStoreEntity(details.getListCouponStore());
        couponDTO.setListCouponProductEntity(details.getListCouponProduct());
        couponDTO.setListCouponProductTypeEntity(details.getListCouponProductType());
        couponDTO.setMaxPrice(details.getMaxPrice());
        couponDTO.setSceneRuleList(details.getSceneRuleList());
        couponDTO.setUseRuleType(details.getUseRuleType());
        couponDTO.setDenominationType(details.getDenominationType());
        return couponDTO;
    }

    /**
     * 优惠券领取
     *
     * @param couponsTakeDTO
     * @return
     */
    @Override
    public boolean couponsTake(CouponsTakeDTO couponsTakeDTO) {
        ActivityReceiveCouponReqDto request = new ActivityReceiveCouponReqDto();
        request.setUserId(Long.valueOf(couponsTakeDTO.getUserId()));
        request.setUserCard(foundMemberCardId(couponsTakeDTO.getMerCode(), couponsTakeDTO.getUserId()));
        request.setActivityId(couponsTakeDTO.getActivityId());
        request.setCouponId(Integer.valueOf(couponsTakeDTO.getCouponId()));
        request.setMerCode(couponsTakeDTO.getMerCode());
        request.setNum(1);
        request.setProvideType(1);
        try {
            couponFactory.
                    foundReceiveCouponService(ActivityHandleType.FREE.getCode()).receive(request);
        } catch (Exception ex) {
            log.warn("[couponsTake] receive coupon error req={}", request);
            throw ex;
        }
        return true;
    }

    /**
     * 优惠券使用
     *
     * @param merCode
     * @param idprefix
     * @return
     */
    @Override
    public ConsumeCouponAdapterResponse couponsUsed(String merCode, List<String> idprefix, SaleCouponReqDTO request) {
        SaleCouponPlusRequest saleRequest = BeanCopyUtil.copy(request, SaleCouponPlusRequest::new, (s, t) -> {
            t.setSceneRuleFlag(1);
            t.setMemberCard(s.getUserCode());
            t.setCouponCode(s.getCouponCodes());
            t.setEmpFlag(LocalConst.STATUS_ONE.toString().equals(ServletUtils.getHeaderIgnoreCase(LocalConst.HEAD_EMPLOYEE_FLAG)));
        });


        log.info("[couponsUsed] invoke couponBusClient.saleRequest {}", JSON.toJSONString(saleRequest));
        ResponseBase<ConsumeCouponResponse> responseBase = couponBusClient.saleCouponPlus(saleRequest);
        checkResult(responseBase);

        return BeanCopyUtil.copy(responseBase.getData(), ConsumeCouponAdapterResponse::new);
    }

    /**
     * 1、下单失败,优惠券回退
     *
     * @param merCode
     * @param couponCodeList
     * @return
     */
    @Override
    public boolean couponsReturn(String merCode, List<String> couponCodeList, String userId) {
        return true;
//        if (CollectionUtils.isEmpty(couponCodeList)) {
//            return true;
//        }
//        return couponBusClient.returnCoupon(couponCodeList.get(0), merCode, Long.valueOf(userId)).checkSuccess();
    }

    /**
     * 逻辑放在核销优惠券接口处
     *
     * @param merCode
     * @param idprefixs  id list
     * @param goodsPrice
     * @return
     */
    @Override
    public List<IdprefixCheckGoodsInfo> couponsCheck(String merCode, List<String> idprefixs, Map<String, String> goodsPrice) {
        return null;
    }

    /**
     * 查会员已领取且可使用的优惠券码 （备注：返回券码和规则）
     *
     * @return coupon list
     */
    @Override
    public ConsumeCouponAdapterResponse couponsTakeInfo(String merCode, String userId, List<String> storeCodes, List<CartCommodityRespDTO> couponProductList, String memberCard, String spCode) {
        ConsumerCouponListReqDTO reqDTO = new ConsumerCouponListReqDTO();
        reqDTO.setMemberCard(!StringUtils.isEmpty(memberCard) ? memberCard : foundMemberCardId(merCode, userId));
        reqDTO.setUserId(Long.valueOf(userId));
        reqDTO.setMerCode(merCode);
        reqDTO.setSceneRuleFlag(1);
        reqDTO.setReturnCouponRule(1);

        BigDecimal allAmount = new BigDecimal(0);
        List<CouponProductDetailDTO> paymentList = Lists.newLinkedList();
        for (CartCommodityRespDTO respDTO : couponProductList) {
            CouponProductDetailDTO activityPaymentDTO = new CouponProductDetailDTO();
            activityPaymentDTO.setProductCode(respDTO.getErpCode());
            activityPaymentDTO.setTypeCode(respDTO.getTypeId());
            BigDecimal payment = respDTO.getCouponPayment() != null ? respDTO.getCouponPayment() : calculationService.getGoodsPrice(respDTO);
            activityPaymentDTO.setPayment(payment);
            activityPaymentDTO.setUseCoupon(respDTO.getUseCoupon());
            activityPaymentDTO.setGainType(CommodityType.Combined_commodity.getCode().equals(respDTO.getCommodityType()) ? 3 : 0);
            activityPaymentDTO.setCommodityOrigin(CommodityOriginType.getCollectionType(respDTO.getOrigin()));
            activityPaymentDTO.setCollectionMark(LocalConst.STATUS_ONE.equals(respDTO.getSupplierReceiveMoney()));
            activityPaymentDTO.setCommodityCount(respDTO.getCount());
            paymentList.add(activityPaymentDTO);
            allAmount = allAmount.add(payment);
        }

        reqDTO.setPaymentList(paymentList);
        reqDTO.setAmount(allAmount);
        reqDTO.setEmpFlag(LocalConst.STATUS_ONE.toString().equals(ServletUtils.getHeaderIgnoreCase(LocalConst.HEAD_EMPLOYEE_FLAG)));
        if (!CollectionUtils.isEmpty(storeCodes)) {
            reqDTO.setStoreCode(storeCodes.get(0));
        }
        if (!StringUtils.isEmpty(spCode)) {
            reqDTO.setIspCode(spCode);
        }
        log.info("[couponsTakeInfo] req={}", JSON.toJSONString(reqDTO));
        ResponseBase<ConsumeCouponResponse> responseBase = couponBusClient.consumeCouponListPlus(reqDTO);
        if (!responseBase.checkSuccess()) {
            log.error("[couponsTakeInfo] invoke 'consumeCouponList' error  req={}", reqDTO);
            throw WarnException.builder().code(responseBase.getCode()).message(responseBase.getMsg()).build();
        }

        ConsumeCouponResponse response = responseBase.getData();
        if (CollectionUtils.isEmpty(response.getCommonCouponDetails()) && CollectionUtils.isEmpty(response.getAdditionCouponDetails())) {
            return null;
        }
        log.info("[couponsTakeInfo] resp={}", JSON.toJSONString(responseBase.getData()));

        ConsumeCouponAdapterResponse couponAdapterResponse = BeanCopyUtil.copy(response, ConsumeCouponAdapterResponse::new, (s, t) -> {
            if (!CollectionUtils.isEmpty(s.getCommonCouponDetails())) {
                t.setCommonCouponDetails(s.getCommonCouponDetails().stream().map(detail -> transfer(detail, couponProductList)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(s.getAdditionCouponDetails())) {
                t.setAdditionCouponDetails(s.getAdditionCouponDetails().stream().map(detail -> transfer(detail, couponProductList)).collect(Collectors.toList()));
            }
        });

        ConsumerCouponInitReqDTO couponInitReqDTO = ConsumerCouponInitReqDTO.builder()
                .storeCode(reqDTO.getStoreCode()).amount(reqDTO.getAmount()).paymentList(reqDTO.getPaymentList()).build();
        if (!StringUtils.isEmpty(spCode)) {
            couponInitReqDTO.setSpCode(spCode);
            couponInitReqDTO.setStoreCode(null);
        }
        couponAdapterResponse.setCouponInitReqDTO(couponInitReqDTO);

        return couponAdapterResponse;

    }

    /**
     * 有优惠券可领的门店集合
     *
     * @param dto dto
     * @return store list
     */
    @Override
    public List<String> couponsTag(CouponRuleQueryDTO dto) {
        log.info("[couponsTag] request={}", dto);
        StoreProductListDTO storeProductListDTO = new StoreProductListDTO();
        storeProductListDTO.setMerCode(dto.getMerCode());
        storeProductListDTO.setActivityType("1"); //free
        storeProductListDTO.setUserId(stringToLong(dto.getUserId()));
        storeProductListDTO.setEmpFlag(dto.getEmpFlag());
        Map<String, StoreResDTO> storeMapping = storeService
                .storeIdMapping
                        (dto.getStores().parallelStream().
                                map(CouponQueryStoreDTO::getStoreId).collect(Collectors.toList()));

        List<String> spCodeList = Lists.newArrayList();
        storeProductListDTO.setListData(dto.getStores().parallelStream().map(var -> {
            StoreProductListDTO.StoreProductReqDto var2 = new StoreProductListDTO.StoreProductReqDto();
            if (!StringUtils.isEmpty(var.getStoreId())) {
                var2.setStoreCode(storeMapping.getOrDefault(var.getStoreId(), new StoreResDTO()).getStCode());
            }
            if (!StringUtils.isEmpty(var.getSpCode())) {
                var2.setIspCode(var.getSpCode());
                spCodeList.add(var.getSpCode());
            }
            for (ProductAndTypeDto erpCode : var.getErpCodes()) {
                erpCode.setCommodityOrigin(StringUtils.isEmpty(var.getSpCode()) ? CommodityOriginType.HYDEE.getCollectionType() : CommodityOriginType.CLOUD.getCollectionType());
            }
            var2.setProductCodes(var.getErpCodes());
            return var2;
        }).collect(Collectors.toList()));

        log.debug("[couponsTag] Invoke 'listStoreCodes' request={}", storeProductListDTO);

        ResponseBase<StoreProductListDTO> responseBase = couponBusClient.listStoreCodes(storeProductListDTO);
        checkResult(responseBase);
        if (CollectionUtils.isEmpty(responseBase.getData().getListData())) {
            log.info("[couponsTag] Not found coupon for store request={}", storeProductListDTO);
            return Lists.newLinkedList();
        }

        Map<String, String> storeIdMapping =
                storeMapping.values().parallelStream().collect(Collectors.toMap(StoreResDTO::getStCode, StoreResDTO::getId,
                        (key1, key2) -> key1));

        if (!CollectionUtils.isEmpty(spCodeList)) {
            for (String spCode : spCodeList) {
                storeIdMapping.put(spCode, spCode);
            }
        }
        log.debug("[couponsTag] 'listStoreCodes' response={}  storeIdMapping={}", responseBase.getData(), storeIdMapping);

        return responseBase.getData().getListData().parallelStream()
                .map(storeCodes -> {
                    if (!StringUtils.isEmpty(storeCodes.getIspCode())) {
                        return storeIdMapping.get(storeCodes.getIspCode());
                    }
                    return storeIdMapping.get(storeCodes.getStoreCode());
                })
                .collect(Collectors.toList());
    }

    @Override
    public ConsumeCouponAdapterResponse chooseCoupon(ChooseCouponRequest request) {
        log.info("[chooseCoupon] req={}", JSON.toJSONString(request));
        ResponseBase<ConsumeCouponResponse> responseBase = couponBusClient.chooseCoupon(request);
        checkResult(responseBase);
        log.info("[chooseCoupon] resp={}", JSON.toJSONString(responseBase.getData()));
        ConsumeCouponResponse response = responseBase.getData();
        List<CartCommodityRespDTO> commodityRespDTOList = Lists.newArrayList();
        for (CouponProductDetailDTO couponProductDetailDTO : request.getPaymentList()) {
            CartCommodityRespDTO commodityRespDTO = new CartCommodityRespDTO();
            commodityRespDTO.setErpCode(couponProductDetailDTO.getProductCode());
            commodityRespDTOList.add(commodityRespDTO);
        }
        return BeanCopyUtil.copy(response, ConsumeCouponAdapterResponse::new, (s, t) -> {
            if (!CollectionUtils.isEmpty(s.getCommonCouponDetails())) {
                t.setCommonCouponDetails(s.getCommonCouponDetails().stream().map(detail -> transfer(detail, commodityRespDTOList)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(s.getAdditionCouponDetails())) {
                t.setAdditionCouponDetails(s.getAdditionCouponDetails().stream().map(detail -> transfer(detail, commodityRespDTOList)).collect(Collectors.toList()));
            }
        });
    }

    @Override
    public ProductDetailCouponAdapterResponse queryProductDetailCoupon(ProductDetailCouponRequest request) {
        ProductDetailCouponAdapterResponse response = new ProductDetailCouponAdapterResponse();
        if (StringUtils.isBlank(request.getStoreCode()) && StringUtils.isBlank(request.getSpCode())) {
            log.warn("queryProductDetailCoupon param = {}", JSON.toJSONString(request));
            return response;
        }
        request.setIspCode(request.getSpCode());
        ResponseBase<ProductDetailCouponResponse> queryProductDetailCouponResp = couponBusClient.queryProductDetailCoupon(request);
        if (!queryProductDetailCouponResp.checkSuccess()) {
            throw WarnException.builder().code(queryProductDetailCouponResp.getCode()).tipMessage(queryProductDetailCouponResp.getMsg()).build();
        }
        if (queryProductDetailCouponResp.getData() == null) {
            return response;
        }
        if (!CollectionUtils.isEmpty(queryProductDetailCouponResp.getData().getAvailableCoupons())) {
            response.setAvailableCoupons(queryProductDetailCouponResp.getData().getAvailableCoupons().parallelStream().map(details -> {
                try {
                    return transfer(details);
                } catch (Exception ex) {
                    log.error("[queryProductDetailCoupon] transfer error {}", details);
                    throw ex;
                }
            }).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(queryProductDetailCouponResp.getData().getCanReceiveCoupons())) {
            response.setCanReceiveCoupons(queryProductDetailCouponResp.getData().getCanReceiveCoupons().parallelStream().map(details -> {
                try {
                    return transfer(details);
                } catch (Exception ex) {
                    log.error("[queryProductDetailCoupon] transfer error {}", details);
                    throw ex;
                }
            }).collect(Collectors.toList()));
        }

        return response;
    }

    @Override
    public ConsumeCouponAdapterResponse couponsNewTakeInfo(List<String> storeCodes, List<CartCommodityRespDTO> couponProductList, OrderCouponCalcuReqDTO orderCouponCalcuReqDTO) {
        String merCode = orderCouponCalcuReqDTO.getMerCode();
        String userId = orderCouponCalcuReqDTO.getMemberId();
        String memberCard = orderCouponCalcuReqDTO.getMemberCard();
        String spCode = orderCouponCalcuReqDTO.getSpCode();
        Integer deliveryType = orderCouponCalcuReqDTO.getDeliveryType();

        ConsumerCouponListReqDTO reqDTO = new ConsumerCouponListReqDTO();
        reqDTO.setMemberCard(!StringUtils.isEmpty(memberCard) ? memberCard : foundMemberCardId(merCode, userId));
        reqDTO.setUserId(Long.valueOf(userId));
        reqDTO.setMerCode(merCode);
        reqDTO.setSceneRuleFlag(1);
        reqDTO.setReturnCouponRule(1);
        reqDTO.setCouponCode(orderCouponCalcuReqDTO.getCouponCode());

        BigDecimal allAmount = new BigDecimal(0);
        List<CouponProductDetailDTO> paymentList = Lists.newLinkedList();
        for (CartCommodityRespDTO respDTO : couponProductList) {
            CouponProductDetailDTO activityPaymentDTO = new CouponProductDetailDTO();
            activityPaymentDTO.setProductCode(respDTO.getErpCode());
            activityPaymentDTO.setTypeCode(respDTO.getTypeId());
            BigDecimal payment = respDTO.getCouponPayment() != null ? respDTO.getCouponPayment() : calculationService.getGoodsPrice(respDTO);

            //定金预售优惠券要除去定金
            if(orderValidatorService.checkPresaleDepositOrder(respDTO.getCommodityLevelActivities(),orderCouponCalcuReqDTO.getOrderPresaleRespDTO())){
                payment = payment.subtract(respDTO.getDepositPayAmount());
            }
            activityPaymentDTO.setOriginPayment(calculationService.getTotalAmount(respDTO));
            activityPaymentDTO.setPayment(payment);
            activityPaymentDTO.setUseCoupon(respDTO.getUseCoupon());
            activityPaymentDTO.setGainType(CommodityType.Combined_commodity.getCode().equals(respDTO.getCommodityType()) ? 3 : 0);
            activityPaymentDTO.setCommodityOrigin(CommodityOriginType.getCollectionType(respDTO.getOrigin()));
            activityPaymentDTO.setCollectionMark(LocalConst.STATUS_ONE.equals(respDTO.getSupplierReceiveMoney()));
            activityPaymentDTO.setCommodityCount(respDTO.getCount());
            activityPaymentDTO.setApplyPaidMemberPrice(respDTO.getApplyPaidMemberPrice());
            paymentList.add(activityPaymentDTO);
            allAmount = allAmount.add(payment);
        }

        reqDTO.setPaymentList(paymentList);
        reqDTO.setAmount(allAmount);
        reqDTO.setEmpFlag(LocalConst.STATUS_ONE.toString().equals(ServletUtils.getHeaderIgnoreCase(LocalConst.HEAD_EMPLOYEE_FLAG)));
        if (!CollectionUtils.isEmpty(storeCodes)) {
            reqDTO.setStoreCode(storeCodes.get(0));
        }
        if (!StringUtils.isEmpty(spCode)) {
            reqDTO.setIspCode(spCode);
        }
        // 设置当前配送方式
        if (Objects.nonNull(deliveryType)) {
            CouponDeliveryTypeEnum itemByDeliveryType = CouponDeliveryTypeEnum.getItemByDeliveryType(deliveryType);
            if (Objects.nonNull(itemByDeliveryType) && Objects.nonNull(itemByDeliveryType.getCode())) {
                reqDTO.setDeliveryType(Collections.singletonList(itemByDeliveryType.getCode()));
            }
        }
        log.info("[couponsTakeInfo] req={}", JSON.toJSONString(reqDTO));
        ResponseBase<ConsumeCouponResponse> responseBase = couponBusClient.consumeCouponListPlus(reqDTO);
        if (!responseBase.checkSuccess()) {
            log.error("[couponsTakeInfo] invoke 'consumeCouponList' error  req={}", reqDTO);
            throw WarnException.builder().code(responseBase.getCode()).message(responseBase.getMsg()).build();
        }

        ConsumeCouponResponse response = responseBase.getData();
        if (CollectionUtils.isEmpty(response.getCommonCouponDetails()) && CollectionUtils.isEmpty(response.getAdditionCouponDetails())) {
            return null;
        }
        log.info("[couponsTakeInfo] resp={}", JSON.toJSONString(responseBase.getData()));

        ConsumeCouponAdapterResponse couponAdapterResponse = BeanCopyUtil.copy(response, ConsumeCouponAdapterResponse::new, (s, t) -> {
            if (!CollectionUtils.isEmpty(s.getCommonCouponDetails())) {
                t.setCommonCouponDetails(s.getCommonCouponDetails().stream().map(detail -> transfer(detail, couponProductList)).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(s.getAdditionCouponDetails())) {
                t.setAdditionCouponDetails(s.getAdditionCouponDetails().stream().map(detail -> transfer(detail, couponProductList)).collect(Collectors.toList()));
            }
        });

        ConsumerCouponInitReqDTO couponInitReqDTO = ConsumerCouponInitReqDTO.builder()
                .storeCode(reqDTO.getStoreCode()).amount(reqDTO.getAmount()).paymentList(reqDTO.getPaymentList()).build();
        if (!StringUtils.isEmpty(spCode)) {
            couponInitReqDTO.setSpCode(spCode);
            couponInitReqDTO.setStoreCode(null);
        }
        couponAdapterResponse.setCouponInitReqDTO(couponInitReqDTO);

        return couponAdapterResponse;
    }

    private String foundMemberCardId(String merCode, String userId) {
        ResponseBase<MemberInfoCacheResDTO> memberResponse = memberInfoClient.getMemberInfoCacheByUserId(merCode, Long.parseLong(userId));
        checkResult(memberResponse);
        MemberInfoCacheResDTO memberDTO = memberResponse.getData();
        Assert.notNull(memberDTO, "Not found memberInfo by userId " + userId);
        return memberDTO.getMemberCard();
    }

    private CouponDTO transfer(CouponDetailSimpleResDTO details, List<CartCommodityRespDTO> paymentList) {
        log.debug("[couponsTakeInfo] market-response=> {}", details);
        CouponDTO couponDTO = new CouponDTO();
        couponDTO.setType(details.getCType());
        couponDTO.setId(Long.valueOf(details.getId()));
        couponDTO.setMoney(details.getDenomination().doubleValue());
        couponDTO.setIdprefix(details.getCouponCode());
        couponDTO.setBecomeEffectiveTime(DateUtil.parseToDate(details.getValidBeginTime()));
        couponDTO.setLoseEffectiveTime(DateUtil.parseToDate(details.getValidEndTime()));
        couponDTO.setMoneyLimit(details.getMaxPrice().doubleValue());
        couponDTO.setSendType(Long.valueOf(details.getProvideType()));
        couponDTO.setCName(details.getCName());
        couponDTO.setSceneRule(details.getSceneRule());
        couponDTO.setUseRule(details.getUseRule());
        couponDTO.setDenomination(details.getDenomination());
        couponDTO.setUsableReason(details.getUsableReason());
        couponDTO.setPageStatus(details.getPageStatus());
        couponDTO.setCType(details.getCType());
        couponDTO.setState(details.getState());
        couponDTO.setShopRule(details.getShopRule());
        couponDTO.setProductRule(details.getProductRule());
        couponDTO.setCName(details.getCName());
        couponDTO.setCombineProductRule(details.getCombineProductRule());
        couponDTO.setListCouponStoreEntity(details.getListCouponStore());
        couponDTO.setListCouponProductEntity(details.getListCouponProduct());
        couponDTO.setListCouponProductTypeEntity(details.getListCouponProductType());
        couponDTO.setSaleAmount(details.getSaleAmount());
        couponDTO.setCouponProductDetails(details.getCouponProductDetails());
        couponDTO.setCreateFrom(details.getCreateFrom());
        couponDTO.setLimitUsedNum(details.getLimitUsedNum());
        couponDTO.setTotalUsedNum(details.getTotalUsedNum());
        couponDTO.setDeliveryType(details.getDeliveryType());
        couponDTO.setGiftLimitNum(details.getGiftLimitNum());
        couponDTO.setUseRuleType(details.getCouponDiscountLadder().getUseRuleType());
        couponDTO.setSceneRuleList(details.getSceneRuleList());
        couponDTO.setDenominationType(details.getDenominationType());
        processProductLimit(couponDTO, paymentList, details.getProductRule(), details.getCombineProductRule(),
                details.getListCouponProduct(), details.getListCouponProductType(), details.getListCouponCombineProduct(), details.getCouponId());
        log.debug("[couponsTakeInfo] response=> {}", couponDTO);
        return couponDTO;
    }

    private void processProductLimit(CouponDTO couponDTO, List<CartCommodityRespDTO> proCodes,
                                     Integer proRule, Integer combineProductRule, List<CouponProductDTO> limitProIds, List<CouponProductTypeDTO> limitTypeIds,
                                     List<CouponProductDTO> listCouponCombineProduct, Integer couponId) {
        log.debug("couponId={} proRule={} limitProIds={} limitTypeIds={} productDetailList={}",
                couponId, proRule, limitProIds, limitTypeIds, proCodes);
        if (proRule.equals(1) && combineProductRule.equals(1)) {
            couponDTO.setIsLimitgoods(0L);
            return;
        }

        processCommonProductLimit(couponDTO, proCodes, proRule, limitProIds,limitTypeIds, couponId);
        processCombineProductLimit(couponDTO, proCodes, combineProductRule, listCouponCombineProduct, couponId);
        couponDTO.distinct();
    }

    private void processCommonProductLimit(CouponDTO couponDTO, List<CartCommodityRespDTO> proCodes,
                                           Integer proRule, List<CouponProductDTO> proIdList, List<CouponProductTypeDTO> typeIdList, Integer couponId) {
        switch (proRule) {
            case 1: //all available
                couponDTO.setIsLimitgoods(1L);
                couponDTO.addLimitGoods(proCodes.parallelStream().filter(pro ->
                                CommodityType.NORMAL.getCode().equals(pro.getCommodityType()))
                        .map(product -> {
                            MerCouponLimitGoodsNew goods = new MerCouponLimitGoodsNew();
                            goods.setCoupon_id(Long.valueOf(couponId));
                            goods.setProduct_code(product.getErpCode());
                            return goods;
                        }).collect(Collectors.toList()));
                break;

            case 2: // partial available
                couponDTO.setIsLimitgoods(1L);
                if (!CollectionUtils.isEmpty(proIdList)) {
                    couponDTO.addLimitGoods(proCodes.parallelStream()
                            .filter(product -> proIdList.parallelStream().anyMatch(pro -> StringUtils.equals(pro.getProCode(), product.getErpCode())))
                            .map(product -> {
                                MerCouponLimitGoodsNew goods = new MerCouponLimitGoodsNew();
                                goods.setCoupon_id(Long.valueOf(couponId));
                                goods.setProduct_code(product.getErpCode());
                                return goods;
                            }).collect(Collectors.toList()));
                }

                if (!CollectionUtils.isEmpty(typeIdList)) {
                    couponDTO.addLimitGoods(proCodes.parallelStream()
                            .filter(product -> typeIdList.parallelStream()
                                    .anyMatch(type -> StringUtils.equals(type.getTypeCode(), product.getTypeId())))
                            .map(product -> {
                                MerCouponLimitGoodsNew goods = new MerCouponLimitGoodsNew();
                                goods.setCoupon_id(Long.valueOf(couponId));
                                goods.setProduct_code(product.getErpCode());
                                return goods;
                            }).collect(Collectors.toList()));
                }
                break;

            case 3: //partial not available
                couponDTO.setIsLimitgoods(1L);
                couponDTO.addLimitGoods(proCodes.parallelStream().filter(pro ->
                                CommodityType.NORMAL.getCode().equals(pro.getCommodityType()))
                        .filter(good ->
                                proIdList.parallelStream()
                                        .noneMatch(pro -> StringUtils.equals(pro.getProCode(), good.getErpCode())) &&
                                        typeIdList.parallelStream()
                                                .noneMatch(type -> StringUtils.equals(type.getTypeCode(), good.getTypeId())))
                        .map(product -> {
                            MerCouponLimitGoodsNew goods = new MerCouponLimitGoodsNew();
                            goods.setCoupon_id(Long.valueOf(couponId));
                            goods.setProduct_code(product.getErpCode());
                            return goods;
                        }).collect(Collectors.toList()));
                break;
            default:
        }
    }

    private void processCombineProductLimit(CouponDTO couponDTO, List<CartCommodityRespDTO> proCodes,
                                            Integer combineProRule, List<CouponProductDTO> proIdList, Integer couponId) {
        switch (combineProRule) {
            case 1: //all available
                couponDTO.setIsLimitgoods(1L);
                couponDTO.addLimitGoods(proCodes.parallelStream().filter(pro ->
                                CommodityType.Combined_commodity.getCode().equals(pro.getCommodityType()))
                        .map(product -> {
                            MerCouponLimitGoodsNew goods = new MerCouponLimitGoodsNew();
                            goods.setCoupon_id(Long.valueOf(couponId));
                            goods.setProduct_code(product.getErpCode());
                            return goods;
                        }).collect(Collectors.toList()));
                break;

            case 2: // partial available
                couponDTO.setIsLimitgoods(1L);
                couponDTO.addLimitGoods(proCodes.parallelStream()
                        .filter(product -> proIdList.parallelStream().anyMatch(pro -> StringUtils.equals(pro.getProCode(), product.getErpCode())))
                        .map(product -> {
                            MerCouponLimitGoodsNew goods = new MerCouponLimitGoodsNew();
                            goods.setCoupon_id(Long.valueOf(couponId));
                            goods.setProduct_code(product.getErpCode());
                            return goods;
                        }).collect(Collectors.toList()));
                break;

            case 3: //partial not available
                couponDTO.setIsLimitgoods(1L);
                couponDTO.addLimitGoods(proCodes.parallelStream().filter(pro ->
                                CommodityType.Combined_commodity.getCode().equals(pro.getCommodityType()))
                        .filter(good ->
                                proIdList.parallelStream()
                                        .noneMatch(pro -> StringUtils.equals(pro.getProCode(), good.getErpCode())))
                        .map(product -> {
                            MerCouponLimitGoodsNew goods = new MerCouponLimitGoodsNew();
                            goods.setCoupon_id(Long.valueOf(couponId));
                            goods.setProduct_code(product.getErpCode());
                            return goods;
                        }).collect(Collectors.toList()));
                break;
            default:
        }
    }
}
