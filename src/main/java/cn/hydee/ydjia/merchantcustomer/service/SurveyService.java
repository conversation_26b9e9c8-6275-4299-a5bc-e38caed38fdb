package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.domain.SurveyAnswer;
import cn.hydee.ydjia.merchantcustomer.dto.SurveyDTO;

import cn.hydee.ydjia.merchantcustomer.enums.SurveyType;
import java.util.List;
import javax.validation.constraints.NotNull;

public interface SurveyService {

    /**
     * 查询问卷
     * @param id
     * @return
     */
    SurveyDTO getById(Long id);

    Long initSurvey(SurveyDTO surveyDTO);

    SurveyDTO getByType(@NotNull(message = "类型不能为空") SurveyType surveyType);

}
