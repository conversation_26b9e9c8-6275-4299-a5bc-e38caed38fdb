package cn.hydee.ydjia.merchantcustomer.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.watch.WatchException;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.ErrorException;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.BeanUtil;
import cn.hydee.ydjia.merchantcustomer.domain.OrderDetail;
import cn.hydee.ydjia.merchantcustomer.dto.activity.ActivityInfoThirdDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.distribution.UserInfoReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.medical.PresDetailRespDTO;
import cn.hydee.ydjia.merchantcustomer.enums.*;
import cn.hydee.ydjia.merchantcustomer.feign.PrescriptionClient;
import cn.hydee.ydjia.merchantcustomer.feign.PromoteClient;
import cn.hydee.ydjia.merchantcustomer.feign.SdpUserInfoClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.medical.PresDetailQueryDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.StoreListReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.PresStatusChangeNotifyDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.PromotionType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.feign.prescription.MessageData;
import cn.hydee.ydjia.merchantcustomer.feign.util.LocalError;
import cn.hydee.ydjia.merchantcustomer.feign.util.MathUtils;
import cn.hydee.ydjia.merchantcustomer.service.*;
import cn.hydee.ydjia.merchantcustomer.transInfo.dto.UserAddress;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订单处理服务类，用于订单待提交、提交订单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/14 14:31
 */
@Service
@Slf4j
public class OrderMainProcessServiceImpl extends AbstractOrderService {
    @Autowired
    private OrderCommitHandler orderCommitHandler;
    @Autowired
    private OrderDelChartHandler orderDelChartHandler;
    @Autowired
    private OrderFreightInitHandler orderFreightInitHandler;
    @Autowired
    private OrderCouponInitHandler orderCouponInitHandler;
    @Autowired
    private OrderValidatorService orderValidatorService;
    @Autowired
    private OrderAdaptService orderAdaptService;
    @Autowired
    private CalculationService calculationService;
    @Autowired
    private SdpUserInfoClient sdpUserInfoClient;
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private YdjStoreService ydjStoreService;
    @Autowired
    private FreightChargeService freightChargeService;
    @Autowired
    private PrescriptionClient prescriptionClient;
    @Autowired
    private PromoteClient promoteClient;
    @Autowired
    private PromoteRiskAlarmService promoteRiskAlarmService;
    @Autowired
    private OrderInfoService orderInfoService;

    /**
     * 初始化进入订单确认页面
     *
     * @param orderConfirmReqDTO 初始化进入订单确认页请求参数
     * @return OrderConfirmInitResDTO 初始化进入订单确认页返回参数
     */
    @Override
    public OrderConfirmInitResDTO orderConfirmInit(OrderConfirmReqDTO orderConfirmReqDTO) {
        /*-----------如果是处方单，需要进行处方状态效验----------------*/
        if (YesOrNoType.YES.getCode().equals(orderConfirmReqDTO.getPrescriptionSheetMark())) {
            PresDetailQueryDTO presDetailQueryDTO = new PresDetailQueryDTO();
            presDetailQueryDTO.setMerCode(orderConfirmReqDTO.getMerCode());
            presDetailQueryDTO.setPresNo(orderConfirmReqDTO.getPrescriptionApprovalAddDTO().getCfNo());
            MessageData<PresDetailRespDTO> messageData = prescriptionClient.getPresResult(presDetailQueryDTO);
            if (!messageData.isSuccess() || !messageData.isBizSuccess() || messageData.getData() == null) {
                throw WarnException.builder().code(LocalError.PRESCRIPTION_QUERY_ERROR.getCode())
                        .tipMessage(LocalError.PRESCRIPTION_QUERY_ERROR.getMsg()).build();
            }
            if (!PresStatusChangeNotifyDTO.Status.ACCEPT.getCode().equals(messageData.getData().getStatus()) || CollectionUtils.isEmpty(messageData.getData().getPresPicList())) {
                throw WarnException.builder().code(ErrorType.CONSULTATION_RECORD_ADD_ORDER_ERROR.getCode()).
                        tipMessage(ErrorType.CONSULTATION_RECORD_ADD_ORDER_ERROR.getMsg()).build();
            }
        }
        StopWatch sw = new StopWatch();
        sw.start("初始进入订单确认页面开始");
        log.info("初始进入订单确认页面开始：{}", JSON.toJSONString(orderConfirmReqDTO));
        String merCode = orderConfirmReqDTO.getMerCode();
        String userId = orderConfirmReqDTO.getMemberId();
        StoreResDTO storeResDTO = getStoreInfo(orderConfirmReqDTO.getMerCode(), orderConfirmReqDTO.getStoreId(), orderConfirmReqDTO.getIsB2cOrder(), orderConfirmReqDTO.getOrderType());
        orderConfirmReqDTO.setStoreId(storeResDTO.getId());
        OrderConfirmInitResDTO orderConfirmInitResDTO = new OrderConfirmInitResDTO();
        orderConfirmReqDTO.setReplaceCommodityNameFlag(true);
        orderConfirmReqDTO.setIsDiscountPrice(true);
        sw.stop();
        sw.start("查询商品数据");
        /*-----------1 查询商品数据----------------*/
        log.info("查询购物车商品数据开始 :{}", JSONObject.toJSONString(orderConfirmReqDTO));
        List<CartCommodityRespDTO> commonCommodityList = orderAdaptService.getCommonCommodityList(orderConfirmReqDTO);
        log.info("comm 1:{}", JSONObject.toJSONString(commonCommodityList));
        commonCommodityList.forEach(a->{
            if(YesOrNoType.YES.getCode().equals(a.getIsVip())){
                BigDecimal divide = MathUtils.divide(a.getVipDiscount(), BigDecimal.valueOf(a.getCount()));
                a.setBeforePrice(a.getBeforePrice().add(divide));
                a.setActivityDiscountAmont(a.getActivityDiscountAmont().add(a.getVipDiscount()));
//                a.setGoodsOriginPrice(a.getGoodsOriginPrice().add(divide));
                a.setPrice(a.getPrice().add(divide));
                a.setGoodsSalesPrice(a.getGoodsSalesPrice().add(divide));
            }
        });


        log.info("comm 2:{}", JSONObject.toJSONString(commonCommodityList));
        if (commonCommodityList.stream().filter(c -> !StringUtils.isEmpty(c.getMedicalInsuranceCode())).count() > 0) {
            orderConfirmInitResDTO.setIsCoverMedicalComm(true);
        } else {
            orderConfirmInitResDTO.setIsCoverMedicalComm(false);
        }
        sw.stop();
        sw.start("处理B2C推广商品");
        // 处理B2C推广商品
        orderConfirmInitResDTO.setIsDisplayDiscountCode(YesOrNoType.NO.getCode());
        List<CartCommodityRespDTO> collect = commonCommodityList.stream().filter(o -> YesOrNoType.YES.getCode().equals(o.getIsPromoteProducts())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            orderConfirmInitResDTO.setIsDisplayDiscountCode(YesOrNoType.YES.getCode());
            // 初始加载页面不展示专属折扣价
            commonCommodityList.forEach(o -> o.setIsDiscountPrice(YesOrNoType.NO.getCode()));
        }
        sw.stop();
        sw.start("处理预售订单");
        //处理预售订单
        OrderPresaleResDTO orderPresaleResDTO = new OrderPresaleResDTO();
        calculationService.handleDepositPayAmountInit(commonCommodityList, orderConfirmReqDTO, orderPresaleResDTO);
        orderConfirmInitResDTO.setOrderPresaleResDTO(orderPresaleResDTO);
        orderConfirmInitResDTO.setIsPresaleOrder(orderConfirmReqDTO.getIsPresaleOrder());

        orderConfirmInitResDTO.setPrescriptionSheetMark(orderConfirmReqDTO.getPrescriptionSheetMark() != null ? orderConfirmReqDTO.getPrescriptionSheetMark() : 0);
        sw.stop();
        sw.start("校验参数正确性");
        /*-----------2 校验参数正确性    ----------------*/
        orderValidatorService.orderConfirmInitCheck(commonCommodityList, storeResDTO);
        sw.stop();
        sw.start("计算商品总金额");
        //计算商品总金额
//        BigDecimal totalOrderAmount = calculationService.sumTotalAmount(commonCommodityList);
        BigDecimal totalOrderAmount = calculationService.sumTotalAmountV2(commonCommodityList);
        orderConfirmInitResDTO.setOrderType(orderConfirmReqDTO.getOrderType());
        orderConfirmInitResDTO.setCartCommodityRespDTOS(commonCommodityList);
        orderConfirmInitResDTO.setTotalOrderAmount(totalOrderAmount);
        orderConfirmInitResDTO.setIsPresaleOrder(orderConfirmReqDTO.getIsPresaleOrder());
        sw.stop();
        sw.start("获取默认收货地址");
        /*-----------3 获取默认收货地址、门店配送方式----------------*/
        UserAddress userAddress = orderAdaptService.getDefaultAddress(merCode, userId, orderConfirmInitResDTO);
        orderConfirmInitResDTO.setUserAddress(userAddress);
        // 总重量
        orderConfirmInitResDTO.setTotalGoodsWeight(calculationService.sumTotalGoodsWeight(commonCommodityList));
        orderConfirmInitResDTO.setStoreResDTO(storeResDTO);

        OrderFreightReqDTO orderFreightReqDTO = new OrderFreightReqDTO();
        BeanUtils.copyProperties(orderConfirmInitResDTO, orderFreightReqDTO);
        if (OrderType.DISTRIBUTION.getCode().equalsIgnoreCase(orderConfirmReqDTO.getOrderType())) {
            orderFreightReqDTO.setTotalActualOrderAmount(totalOrderAmount);
        }
        orderFreightReqDTO.setMerCode(merCode);
        orderFreightReqDTO.setMemberId(orderConfirmReqDTO.getMemberId());
        orderFreightReqDTO.setClientType(orderConfirmReqDTO.getClientType());
        orderFreightReqDTO.setIsB2cOrder(orderConfirmReqDTO.getIsB2cOrder());
        orderFreightReqDTO.setIsPresaleOrder(orderConfirmInitResDTO.getIsPresaleOrder());
        sw.stop();
        sw.start("获取配默认送方式");
        // 获取配默认送方式
        OrderFreightResDTO orderFreightResDTO = new OrderFreightResDTO();
        freightChargeService.handleDeliveryList(orderFreightReqDTO, orderFreightResDTO);
        sw.stop();
        sw.start("优惠券计算及获取优惠券初始化信息");
        /*-----------4 优惠券计算及获取优惠券初始化信息    ----------------*/
        List<CartCommodityRespDTO> couponCommodityList = calculationService.getUseCouponCommodityList(commonCommodityList, orderConfirmReqDTO.getOrderType());
        orderAdaptService.judgeMutualExclusion(orderConfirmReqDTO, commonCommodityList, couponCommodityList, true);
        OrderCouponCalcuReqDTO orderCouponCalcuReqDTO = new OrderCouponCalcuReqDTO();
        BeanUtils.copyProperties(orderConfirmReqDTO, orderCouponCalcuReqDTO);
        orderCouponCalcuReqDTO.setCouponCommodityList(couponCommodityList);
        orderCouponCalcuReqDTO.setOrderConfirmInit(true);
        orderCouponCalcuReqDTO.setCommodityRespDTOS(commonCommodityList);
        orderCouponCalcuReqDTO.setStCode(storeResDTO.getStCode());
        orderCouponCalcuReqDTO.setDeliveryType(orderFreightResDTO.getDeliveryType());
        orderCouponCalcuReqDTO.setCouponCode(orderConfirmReqDTO.getCouponCode());
        orderCouponCalcuReqDTO.setOrderPresaleRespDTO(orderPresaleResDTO);
        orderConfirmInitResDTO.setOrderPresaleResDTO(orderPresaleResDTO);
        OrderCouponCalcuResDTO orderCouponCalcuResDTO = orderCouponInitHandler.handleRequest(orderCouponCalcuReqDTO);
        if (!CollectionUtils.isEmpty(orderCouponCalcuResDTO.getCouponCodes())) {
            // 诺和优惠券判定
            setNovoOrder(orderCouponCalcuResDTO, orderConfirmInitResDTO);
        }
        orderConfirmInitResDTO.setCouponDeduction(orderCouponCalcuResDTO.getCouponDeduction());
        orderConfirmInitResDTO.setTotalActualOrderAmount(totalOrderAmount.subtract(orderCouponCalcuResDTO.getCouponDeduction()));
        orderConfirmInitResDTO.setCouponCodes(orderCouponCalcuResDTO.getCouponCodes());
        orderConfirmInitResDTO.setAllCoupon(orderCouponCalcuResDTO.getMerCouponNews());
        orderConfirmInitResDTO.setCashCoupons(orderCouponCalcuResDTO.getCashCoupons());
        orderConfirmInitResDTO.setCouponInitReqDTO(orderCouponCalcuResDTO.getCouponInitReqDTO());

        orderConfirmInitResDTO.setEpidemicRegistration(commonCommodityList.stream()
                .anyMatch(e -> YesOrNoType.YES.getCode().equals(e.getEpidemicRegistration())) ? YesOrNoType.YES
                .getCode() : YesOrNoType.NO.getCode());
        orderConfirmInitResDTO.setNeedHealthCode(commonCommodityList.stream()
                .anyMatch(e -> YesOrNoType.YES.getCode().equals(e.getNeedHealthCode())) ? YesOrNoType.YES
                .getCode() : YesOrNoType.NO.getCode());
        sw.stop();
        sw.start("门店及运费计算");
        /*-----------5 门店及运费计算----------------*/
        orderFreightInitHandler.handleOrdinaryOrderRequest(orderFreightReqDTO, orderFreightResDTO);
        BeanUtils.copyProperties(orderFreightResDTO, orderConfirmInitResDTO);
        /*-----------免运促销活动----------------*/
        boolean costFreeFlag = orderAdaptService.judgeCostFree(commonCommodityList, orderFreightResDTO.getDeliveryType(), orderConfirmReqDTO.getMutualExclusion(), true);
        boolean haveNotCostFreeGoodsFlag = orderAdaptService.judgeHaveNotCostFreeGoods(commonCommodityList, orderConfirmInitResDTO.getActualFreightAmount());
        orderConfirmInitResDTO.setHaveNotCostFreeGoodsFlag(haveNotCostFreeGoodsFlag);
        List<DeliveryDTO> deliveryList = orderConfirmInitResDTO.getDeliveryList();
        if (costFreeFlag) {
            orderConfirmInitResDTO.setDiscountFreightAmount(orderConfirmInitResDTO.getActualFreightAmount());
            orderConfirmInitResDTO.setActualFreightAmount(new BigDecimal(0));
            // 如果当前选中了配送类型,并且配送列表不为空
            if (!CollectionUtils.isEmpty(deliveryList)) {
                deliveryList.forEach(deliveryDTO -> {
                    if (orderFreightResDTO.getDeliveryType().equals(deliveryDTO.getDeliveryType())) {
                        deliveryDTO.setActualFreightAmount(new BigDecimal(0));
                    }
                });
            }
        }

        orderConfirmInitResDTO.setOtherDiscountAmont(BigDecimal.ZERO);
        orderConfirmInitResDTO.setActivityDiscountAmont(calculationService.cacuActivityDiscountAmont(commonCommodityList));
        sw.stop();
        sw.start("实际支付计算");
        //实际支付=订单金额-优惠券抵扣-活动优惠+配送费用
        BigDecimal actualTotalAmount = calculationService.cacuTotalOrderAmount(
                orderConfirmInitResDTO.getTotalActualOrderAmount(),
                orderConfirmInitResDTO.getActualFreightAmount(),
                orderConfirmInitResDTO.getActivityDiscountAmont(),
                false, false
        );
        orderConfirmInitResDTO.setTotalActualOrderAmount(actualTotalAmount);
        sw.stop();
        sw.start("定金预售尾款计算");
        //定金预售尾款=实际支付-定金
        if (OrderPresaleTypeEnum.DEPOSIT_PRESALE.getCode().equals(orderPresaleResDTO.getPresaleOrderType())) {
            orderPresaleResDTO.setTotalFinalPaymentAmount(orderConfirmInitResDTO.getTotalActualOrderAmount().subtract(orderPresaleResDTO.getTotalDepositPayAmount()));
        }
        sw.stop();
        sw.start("订单折价项");
        /*----------- 6 订单折价项----------------*/
        orderConfirmInitResDTO.setDiscountItems(getDiscountItems(orderConfirmInitResDTO.getCartCommodityRespDTOS()));
        sw.stop();
        sw.start("商品总额");
        /*----------- 7 商品总额，用作前端展示----------------*/
        orderConfirmInitResDTO.setTotalOrderAmountShow(getTotalOrderAmountForShow(orderConfirmInitResDTO.getTotalOrderAmount(), commonCommodityList, false));
        orderConfirmInitResDTO.setFlashPriceAmount(getTotalFlashPriceAmount(commonCommodityList));
        orderConfirmInitResDTO.setMoreDiscountPriceAmount(getTotalMoreDiscountPriceAmount(commonCommodityList));
        orderConfirmInitResDTO.setVipDiscount(commonCommodityList.stream().map(CartCommodityRespDTO::getVipDiscount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        orderConfirmInitResDTO.setVipDiscountPlus(commonCommodityList.stream().map(CartCommodityRespDTO::getVipDiscountPlus).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        orderConfirmInitResDTO.setDiscountRate(ObjectUtil.isEmpty(commonCommodityList.get(0).getDiscountRate()) ? BigDecimal.ZERO : commonCommodityList.get(0).getDiscountRate());
        sw.stop();
        sw.start("余额支付信息");
        /*----------- 8 余额支付信息----------------*/
        orderAdaptService.setBalanceAmountForInit(orderConfirmInitResDTO, orderConfirmReqDTO);
        sw.stop();
        sw.start("待支付");
        /*----------- 9 待支付-运费<=0，过滤医保支付----------------*/
        orderAdaptService.dealMedicarePayment(orderConfirmInitResDTO.getTotalActualOrderAmount(), orderConfirmInitResDTO.getActualFreightAmount(), deliveryList);
        sw.stop();
        log.info(sw.prettyPrint());
        if (CollUtil.isEmpty(deliveryList)) {
            log.info("初始进入订单确认页面结束：{}", JSON.toJSONString(orderConfirmInitResDTO));
            return orderConfirmInitResDTO;
        }
        DeliveryDTO deliveryDTO = deliveryList.stream().filter(e -> e.getIsOptional().equals(1)).findFirst().orElse(null);
        if (deliveryDTO == null) {
            return orderConfirmInitResDTO;
        }
//        BigDecimal activityDiscountAmount = Optional.ofNullable(orderConfirmInitResDTO.getActivityDiscountAmont()).orElse(BigDecimal.ZERO);
//        if ((orderConfirmInitResDTO.getPayBalanceAmount() == null || orderConfirmInitResDTO.getPayBalanceAmount().compareTo(BigDecimal.ZERO) == 0)
//                && actualTotalAmount.compareTo(BigDecimal.ZERO) == 0
//                && CollUtil.isNotEmpty(orderConfirmInitResDTO.getCouponCodes())
//                && (activityDiscountAmount.compareTo(BigDecimal.ZERO) == 0 || activityDiscountAmount.subtract(orderConfirmInitResDTO.getTotalOrderAmount()).compareTo(BigDecimal.ZERO) != 0)
//                && !ObjectUtil.equal(deliveryDTO.getDeliveryType(), DeliveryType.STORE_SELF.getCode())
//                && !ObjectUtil.equal(orderConfirmInitResDTO.getThirdOrderExtendType(), OrderExtendInfoEnum.NOVO.getType())
//        ) {
//            orderConfirmInitResDTO.setActualFreightAmount(new BigDecimal("0.01"));
//            orderConfirmInitResDTO.setTotalActualOrderAmount(orderConfirmInitResDTO.getActualFreightAmount());
//        }
        log.info("初始进入订单确认页面结束：{}", JSON.toJSONString(orderConfirmInitResDTO));


        return orderConfirmInitResDTO;
    }

    private void setNovoOrder(OrderCouponCalcuResDTO orderCouponCalcuResDTO, OrderConfirmInitResDTO orderConfirmInitResDTO) {
        ActivityInfoThirdDTO activityInfoThirdDTO = new ActivityInfoThirdDTO();
        activityInfoThirdDTO.setCouponCodes(orderCouponCalcuResDTO.getCouponCodes());
        ResponseBase<PageDTO<ActivityInfoThirdDTO>> search = promoteClient.search(activityInfoThirdDTO);
        if (!search.checkSuccess() || search.getData() == null) {
            throw WarnException.builder().code(ErrorType.OPERATOR_ERROR.getCode()).
                    tipMessage(ErrorType.OPERATOR_ERROR.getMsg()).build();
        }
        if (!CollectionUtils.isEmpty(search.getData().getData())) {
            orderConfirmInitResDTO.setThirdOrderExtendType(OrderExtendInfoEnum.NOVO.getType());
        }
    }

    /**
     * 订单确认
     *
     * @param orderConfirmReqDTO 订单确认请求对象
     * @return OrderConfirmResDTO 订单确认返回对象
     */
    @Override
    public OrderConfirmResDTO orderConfirm(OrderConfirmReqDTO orderConfirmReqDTO) {
        log.info("orderConfirm param：{}", JSON.toJSONString(orderConfirmReqDTO));
        OrderConfirmResDTO orderConfirmResDTO = new OrderConfirmResDTO();
        StoreResDTO storeResDTO = getStoreInfo(orderConfirmReqDTO.getMerCode(), orderConfirmReqDTO.getStoreId(), orderConfirmReqDTO.getIsB2cOrder(), orderConfirmReqDTO.getOrderType());
        orderConfirmReqDTO.setStoreId(storeResDTO.getId());
        OrderAdaptResDTO orderAdaptResDTO = orderAdaptService.orderAdapt(orderConfirmReqDTO, storeResDTO, false);

        /*----------- 待支付-运费<=0，过滤医保支付----------------*/
        orderAdaptService.dealMedicarePayment(orderAdaptResDTO.getTotalActualOrderAmount(), orderAdaptResDTO.getActualFreightAmount(), orderAdaptResDTO.getDeliveryList());
        // 过滤处方单的余额支付
        orderAdaptService.dealPresPayment(orderAdaptResDTO.getDeliveryList(), orderConfirmReqDTO.getPrescriptionSheetMark());

        BeanUtils.copyProperties(orderAdaptResDTO, orderConfirmResDTO);
        orderConfirmResDTO.setDiscountItems(getDiscountItems(orderConfirmResDTO.getCartCommodityRespDTOS()));
        orderConfirmResDTO.setTotalOrderAmountShow(getTotalOrderAmountForShow(orderConfirmResDTO.getTotalOrderAmount(), orderAdaptResDTO.getCartCommodityRespDTOS(), orderAdaptResDTO.getMutualExclusion()));
//        orderConfirmResDTO.setTotalOrderAmountShow(orderConfirmResDTO.getTotalOrderAmount());
        orderConfirmResDTO.setFlashPriceAmount(getTotalFlashPriceAmount(orderAdaptResDTO.getCartCommodityRespDTOS()));
        orderConfirmResDTO.setMoreDiscountPriceAmount(getTotalMoreDiscountPriceAmount(orderAdaptResDTO.getCartCommodityRespDTOS()));
        orderConfirmResDTO.setVipDiscount(orderAdaptResDTO.getCartCommodityRespDTOS().stream().map(CartCommodityRespDTO::getVipDiscount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        orderConfirmResDTO.setVipDiscountPlus(orderAdaptResDTO.getCartCommodityRespDTOS().stream().map(CartCommodityRespDTO::getVipDiscountPlus).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        orderConfirmResDTO.setDiscountRate(ObjectUtil.isEmpty(orderAdaptResDTO.getCartCommodityRespDTOS().get(0).getDiscountRate()) ? BigDecimal.ZERO : orderAdaptResDTO.getCartCommodityRespDTOS().get(0).getDiscountRate());
        log.info("orderConfirm end ：{}", JSON.toJSONString(orderConfirmResDTO));
//        if (judgeConvertZeroOrder(orderConfirmReqDTO, orderAdaptResDTO)) {
//            orderConfirmResDTO.setActualFreightAmount(new BigDecimal("0.01"));
//            orderConfirmResDTO.setTotalActualOrderAmount(orderConfirmResDTO.getActualFreightAmount());
//        }
        // 付费会员不能使用医保支付
        if(Objects.nonNull(orderConfirmResDTO.getVipDiscount()) && orderConfirmResDTO.getVipDiscount().compareTo(BigDecimal.ZERO)>0){
            for (DeliveryDTO deliveryDTO : orderAdaptResDTO.getDeliveryList()) {
                deliveryDTO.getPaySetList().removeIf(next -> PaySetEnum.MEDICARE_PAYMENT.getCode().equals(next.getPayType()));
            }
        }
        return orderConfirmResDTO;
    }

    private static boolean judgeConvertZeroOrder(OrderConfirmReqDTO orderConfirmReqDTO, OrderAdaptResDTO orderAdaptResDTO) {
        boolean convertZeroOrder = true;
        // 实际支付金额不为0则不处理
        if (orderAdaptResDTO.getTotalActualOrderAmount().compareTo(BigDecimal.ZERO) != 0) {
            convertZeroOrder = false;
        }
        // 选择余额支付不处理
        if (orderAdaptResDTO.getPayBalanceAmount() != null && orderAdaptResDTO.getPayBalanceAmount().compareTo(BigDecimal.ZERO) != 0) {
            convertZeroOrder = false;
        }
        //促销活动优惠金额减去订单原金额等于0则不处理
        BigDecimal activityDiscountAmount = Optional.ofNullable(orderAdaptResDTO.getActivityDiscountAmont()).orElse(BigDecimal.ZERO);
        if (activityDiscountAmount.compareTo(BigDecimal.ZERO) != 0
                && activityDiscountAmount.subtract(orderAdaptResDTO.getTotalOrderAmount()).compareTo(BigDecimal.ZERO) == 0
        ) {
            convertZeroOrder = false;
        }
        // 门店自提不处理
        if (ObjectUtil.equal(orderConfirmReqDTO.getDeliveryType(), DeliveryType.STORE_SELF.getCode())) {
            convertZeroOrder = false;
        }
        //诺和订单不处理
        if (ObjectUtil.equal(orderAdaptResDTO.getThirdOrderExtendType(), OrderExtendInfoEnum.NOVO.getType())) {
            convertZeroOrder = false;
        }
        return convertZeroOrder;
    }

    /**
     * 提交订单
     *
     * @param orderCommitReqDTO 订单提交对象
     * @return AddOrderResDTO 返回对象
     */
    @Override
    @GlobalTransactional
    public AddOrderResDTO addOrder(OrderCommitReqDTO orderCommitReqDTO) {
        //获取门店信息
        StoreResDTO storeResDTO = getStoreInfo(orderCommitReqDTO.getMerCode(), orderCommitReqDTO.getStoreId(), orderCommitReqDTO.getIsB2cOrder(), orderCommitReqDTO.getOrderType());
        orderCommitReqDTO.setStoreId(storeResDTO.getId());
        /*-------------------1 订单提交时基本参数校验-------------------------------*/
        orderValidatorService.normalCommitCheck(orderCommitReqDTO, storeResDTO);
        OrderConfirmReqDTO orderConfirmReqDTO = new OrderConfirmReqDTO();
        BeanUtils.copyProperties(orderCommitReqDTO, orderConfirmReqDTO);
        if (!StringUtils.isEmpty(orderCommitReqDTO.getDiscountCode())) {
            orderConfirmReqDTO.setIsDiscountPrice(Boolean.TRUE);
        }

        /*-------------------2 判断提交的订单总金额与订单后台再次计算的金额是否相等-------------------------------*/
        OrderAdaptResDTO orderAdaptResDTO = orderAdaptService.orderAdapt(orderConfirmReqDTO, storeResDTO, true);
        //新需求：如果不是余额支付，并且实际支付金额为0元，则赋值0.01元到邮费上
//        if (judgeConvertZeroOrder(orderConfirmReqDTO, orderAdaptResDTO)) {
//            orderAdaptResDTO.setActualFreightAmount(new BigDecimal("0.01"));
//            orderAdaptResDTO.setTotalActualOrderAmount(orderAdaptResDTO.getActualFreightAmount());
//        }
        // 检验0元单
        log.info("校验0元单开始,orderCommitReqDTO:{}",JSON.toJSONString(orderCommitReqDTO));
        if (Objects.equals(orderCommitReqDTO.getTotalActualOrderAmount(), BigDecimal.ZERO)) {
            checkZeroOrder(orderCommitReqDTO.getMemberId(),orderAdaptResDTO.getCartCommodityRespDTOS());
        }
        // 校验分销订单
        this.checkDistributionOrder(orderCommitReqDTO, orderAdaptResDTO);

        orderCommitReqDTO.setPrescriptionSheetMark(orderConfirmReqDTO.getPrescriptionSheetMark() != null ? orderConfirmReqDTO.getPrescriptionSheetMark() : 0);
        //校验用药人信息是否必传
        orderValidatorService.checkMedicalUserId(orderAdaptResDTO, orderCommitReqDTO);

        //如果优惠券跟促销活动互斥是，选择使用优惠券则需要重置前端传的规格数量
        if (Boolean.TRUE.equals(orderAdaptResDTO.getMutualExclusion())) {
            orderCommitReqDTO.setSpecCount(orderAdaptResDTO.getCartCommodityRespDTOS().size());
        }
        log.info("orderAdaptResDTO:{},orderCommitReqDTO:{}", JSON.toJSONString(orderAdaptResDTO), JSON.toJSONString(orderConfirmReqDTO));
        orderValidatorService.checkOrderCommitAmount(orderAdaptResDTO, orderCommitReqDTO);

        //订单id在C端生成
        orderAdaptResDTO.setId(orderAdaptService.getOrderId());
        //计算当前用户定位（非收货地址）与下单门店距离（单位：米）
        orderCommitReqDTO.setDistanceFromStore(orderAdaptService.calcDistanceFromStore(storeResDTO.getLongitude(), storeResDTO.getLatitude(),
                orderCommitReqDTO.getLongitude(), orderCommitReqDTO.getLatitude()));
        //B2C旗舰店订单，需要推广门店
        if (YesOrNoType.YES.getCode().equals(orderCommitReqDTO.getIsB2cOrder()) && !StringUtils.isEmpty(orderCommitReqDTO.getSpreadStoreId())) {
            ResponseBase<StoreResDTO> storeResp = storeClient.queryStore(orderCommitReqDTO.getSpreadStoreId());
            if (storeResp.checkSuccess() && storeResp.getData() != null) {
                orderAdaptResDTO.setSpreadStoreCode(storeResp.getData().getStCode());
            }
        }
        // 付费会员订单特殊处理金额
        if(!CollectionUtils.isEmpty(orderAdaptResDTO.getCartCommodityRespDTOS())){
            orderAdaptResDTO.getCartCommodityRespDTOS().forEach(a->{
                if(YesOrNoType.YES.getCode().equals(a.getIsMembershipPrice()) && Objects.nonNull(a.getOriginPrice()) && a.getOriginPrice().compareTo(BigDecimal.ZERO)>0){
                    a.setBeforePrice(a.getOriginPrice());
                    a.setGoodsOriginPrice(a.getOriginPrice());
                    a.setMPrice(a.getOriginPrice());
                }
            });
        }
        
        /*-------------------3 组装参数并提交订单-------------------------------*/
        OrderCommitAddReqDTO orderCommitAddReqDTO = new OrderCommitAddReqDTO();
        orderCommitAddReqDTO.setOrderAdaptResDTO(orderAdaptResDTO);
        orderCommitAddReqDTO.setOrderCommitReqDTO(orderCommitReqDTO);
        try {
            orderCommitAddReqDTO.setOrderCommodityDTOS(BeanUtil.copyList(orderAdaptResDTO.getCartCommodityRespDTOS(), OrderCommodityDTO.class));
        } catch (IllegalAccessException | InstantiationException e) {
            throw ErrorException.builder().cause(e).build();
        }

        AddOrderResDTO addOrderResDTO = orderCommitHandler.handleRequest(orderCommitAddReqDTO);
        /*-------------------4 清空购物车-------------------------------*/
        //非立即购买&非电子处方-问诊记录下单，则清空购货车
        if (StringUtils.isEmpty(orderCommitReqDTO.getSpecId()) && CollectionUtils.isEmpty(orderCommitReqDTO.getMultiCartCommodity())
                && !checkFromPrescriptionRecord(orderCommitReqDTO)) {
            orderDelChartHandler.handleRequest(orderCommitAddReqDTO);
        }
        if (!CollectionUtils.isEmpty(orderAdaptResDTO.getPromoteRiskFlag())) {
            orderAdaptResDTO.getPromoteRiskFlag().forEach(item -> {
                promoteRiskAlarmService.sendAlarmRobotNotice(item + ", 订单号：" + addOrderResDTO.getOrderId());
            });
        }
        return addOrderResDTO;
    }

    private void checkZeroOrder(String memberId,List<CartCommodityRespDTO> cartCommodityRespDTO) {
        // 获取当前时间减3分钟
        LocalDateTime perDateTime = LocalDateTime.now().minusMinutes(3);
        List<OrderDetail> orderDetails = orderInfoService.getOrderDetailByUserId(memberId, perDateTime);
        log.info("提交订单时校验0元单:request:{}，response:{}",perDateTime,JSON.toJSONString(orderDetails));
        if (CollUtil.isNotEmpty(orderDetails)) {
            // 查看是否存在相同商品
            Map<String, List<OrderDetail>> orderDetailMap = orderDetails.stream().collect(Collectors.groupingBy(OrderDetail::getOrderId));
            orderDetailMap.forEach((k,v) -> {
                List<String> ods = v.stream().map(OrderDetail::getCommodityId).collect(Collectors.toList());
                List<String> collect = cartCommodityRespDTO.stream().map(CartCommodityRespDTO::getCommodityId).filter(ods::contains).collect(Collectors.toList());
                // 取二者交集
                if (ods.size() == collect.size()) {
                    // 全部包含
                    throw WarnException.builder().code(ErrorType._20010.getCode()).tipMessage(ErrorType._20010.getMsg()).build();
                }
            });


        }
    }

    private void checkDistributionOrder(OrderCommitReqDTO orderCommitReqDTO, OrderAdaptResDTO orderAdaptResDTO) {
        if (!OrderType.DISTRIBUTION.getCode().equalsIgnoreCase(orderCommitReqDTO.getOrderType())) {
            return;
        }
        List<CartCommodityRespDTO> cartCommodityRespDTOS = orderAdaptResDTO.getCartCommodityRespDTOS();
        CartCommodityRespDTO cartCommodityRespDTO = cartCommodityRespDTOS.get(0);
        if (SourceChannelType.DISTRIBUTION.getCode().equals(cartCommodityRespDTO.getSourceChannelType())) {
            String shareCode = cartCommodityRespDTO.getSourceChannelId();
            if (StringUtils.isEmpty(shareCode)) {
                log.error("提交订单失败：req:{},", JSON.toJSONString(orderCommitReqDTO));
                throw WarnException.builder().code(ErrorType.DISTRIBUTION_ORDER_COMMIT_ERROR.getCode()).
                        tipMessage(ErrorType.DISTRIBUTION_ORDER_COMMIT_ERROR.getMsg()).build();
            }
            UserInfoReqDTO userInfoReqDTO = new UserInfoReqDTO();
            userInfoReqDTO.setMerCode(orderCommitReqDTO.getMerCode());
            userInfoReqDTO.setShareCode(shareCode);
            ResponseBase<Boolean> responseBase = sdpUserInfoClient.checkShareMember(userInfoReqDTO);
            if (responseBase == null || !responseBase.checkSuccess() || !responseBase.getData()) {
                log.warn("checkShareMember req = {}, res = {}", JSON.toJSONString(userInfoReqDTO),
                        JSON.toJSONString(responseBase));
                throw WarnException.builder().code(ErrorType.DISTRIBUTION_ORDER_COMMIT_ERROR.getCode()).
                        tipMessage(ErrorType.DISTRIBUTION_ORDER_COMMIT_ERROR.getMsg()).build();
            }
        }
    }

    /**
     * 订单初始化接口返回折扣项
     *
     * @param cartCommodityRespDTOS
     */
    private List<DiscountItem> getDiscountItems(List<CartCommodityRespDTO> cartCommodityRespDTOS) {
        List<DiscountItem> discountItems = Lists.newArrayList();
        BigDecimal discount = BigDecimal.ZERO;
        for (CartCommodityRespDTO cartCommodity : cartCommodityRespDTOS) {
            if (CollectionUtils.isEmpty(cartCommodity.getOrderLevelActivities())) {
                continue;
            }
            for (ActivityDTO orderLevelActivity : cartCommodity.getOrderLevelActivities()) {
                //立减只包含满减增优惠
                if (!PromotionType.FULL.getCode().equals(orderLevelActivity.getPmtType().getCode()) || orderLevelActivity.getActivityDiscountAmount() == null) {
                    continue;
                }
                discount = discount.add(orderLevelActivity.getActivityDiscountAmount());
            }
        }
        DiscountItem discountItem;
        if (discount.compareTo(BigDecimal.ZERO) > 0) {
            discountItem = new DiscountItem(DiscountItemEnum.DISTCOUNT.getMsg(), discount);
            discountItems.add(discountItem);
        }
        return discountItems;
    }

    /**
     * 把限时优惠、多买多惠的优惠计算到商品金额，用作前端展示
     * 20201110 增加会员日、会员价优惠
     * 相应需求：https://www.tapd.cn/59646846/bugtrace/bugs/view/1159646846001003560
     * 用作展示的商品总额
     *
     * @param cartCommodityRespDTOS
     * @return
     */
    private BigDecimal getTotalOrderAmountForShow(BigDecimal totalOrderAmount, List<CartCommodityRespDTO> cartCommodityRespDTOS, Boolean mutualExclusion) {
        if (CollectionUtils.isEmpty(cartCommodityRespDTOS)) {
            return totalOrderAmount;
        }
        BigDecimal orderAmount = BigDecimal.ZERO;
        for (CartCommodityRespDTO cartCommodity : cartCommodityRespDTOS) {

            if (YesOrNoType.YES.getCode().equals(cartCommodity.getIsVip())) {
                orderAmount = orderAmount.add(MathUtils.setSacle(BigDecimal.valueOf(cartCommodity.getCount()).multiply(cartCommodity.getOriginPrice()), LocalConst.SCALE));
                continue;
            }
            if (isDiscountPrice(cartCommodity)) {
                orderAmount = orderAmount.add(MathUtils.setSacle(BigDecimal.valueOf(cartCommodity.getCount()).multiply(cartCommodity.getBeforePrice()), LocalConst.SCALE));
                continue;
            }
            orderAmount = orderAmount.add(MathUtils.setSacle(BigDecimal.valueOf(cartCommodity.getCount()).multiply(cartCommodity.getPrice()), LocalConst.SCALE));
        }
        return orderAmount;
    }


    /** 限时优惠总金额
     * <AUTHOR>
     * @Description
     * @date 2024/11/28 18:26
     */
    private BigDecimal getTotalFlashPriceAmount(List<CartCommodityRespDTO> cartCommodityRespDTOS) {
        log.info("限时优惠总金额{}",JSON.toJSONString(cartCommodityRespDTOS));
        BigDecimal discount = BigDecimal.ZERO;

        for (CartCommodityRespDTO cartCommodity : cartCommodityRespDTOS) {
            if (!CollectionUtils.isEmpty(cartCommodity.getCommodityLevelActivities())) {
                for (ActivityDTO commodityLevelActivity : cartCommodity.getCommodityLevelActivities()) {
                    if ((PromotionType.FLASH_PRICE.getCode().equals(commodityLevelActivity.getPmtType().getCode())) && commodityLevelActivity.getActivityDiscountAmount() != null) {
                        discount = discount.add(commodityLevelActivity.getActivityDiscountAmount());
                    }
                }
            }
        }
        return discount;
    }

    /** 多买优惠
     * <AUTHOR>
     * @Description
     * @date 2024/11/28 18:26
     */
    private BigDecimal getTotalMoreDiscountPriceAmount(List<CartCommodityRespDTO> cartCommodityRespDTOS) {
        BigDecimal discount = BigDecimal.ZERO;

        for (CartCommodityRespDTO cartCommodity : cartCommodityRespDTOS) {
            if (!CollectionUtils.isEmpty(cartCommodity.getOrderLevelActivities())) {
                for (ActivityDTO orderLevelActivity : cartCommodity.getOrderLevelActivities()) {
                    if (PromotionType.MORE_DISCOUNT.getCode().equals(orderLevelActivity.getPmtType().getCode()) && orderLevelActivity.getActivityDiscountAmount() != null) {
                        discount = discount.add(orderLevelActivity.getActivityDiscountAmount());
                    }
                }
            }
        }
        return discount;
    }


    /**
     * 是否是折扣价格
     * 包含：限时优惠、会员日、会员价
     *
     * @param dto
     * @return
     */
    private boolean isDiscountPrice(CartCommodityRespDTO dto) {
        return !CollectionUtils.isEmpty(dto.getCommodityLevelActivities()) && dto.getCommodityLevelActivities().get(0).getPmtType() != null
                && (PromotionType.FLASH_PRICE.getCode().equals(dto.getCommodityLevelActivities().get(0).getPmtType().getCode())
                || PromotionType.MEMBER_DAY.getType().equals(dto.getCommodityLevelActivities().get(0).getPmtType().getType())
                || PromotionType.PRESALE.getType().equals(dto.getCommodityLevelActivities().get(0).getPmtType().getType()));
    }

    /**
     * 校验是否来源电子处方-问诊记录下单
     *
     * @param orderCommitReqDTO
     * @return
     */
    private Boolean checkFromPrescriptionRecord(OrderCommitReqDTO orderCommitReqDTO) {
        try {
            return orderCommitReqDTO.getPrescriptionApprovalAddDTO() != null && orderCommitReqDTO.getPrescriptionApprovalAddDTO().getFromPrescriptionRecord();
        } catch (Exception e) {
            return false;
        }
    }

    private StoreResDTO getStoreInfo(String merCode, String storeId, Integer isB2cOrder, String orderType) {
        StoreResDTO storeResDTO = new StoreResDTO();
        if (YesOrNoType.YES.getCode().equals(isB2cOrder)) {
            StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
            storeListReqDTO.setMerCode(merCode);
            StoreListResDTO centerStore = ydjStoreService.queryCenterStore(storeListReqDTO, true);
            if (centerStore == null) {
                log.error("旗舰店信息为空,{}", merCode);
                throw WarnException.builder().code(ErrorType.STORE_IS_NULL.getCode()).
                        tipMessage(ErrorType.STORE_IS_NULL.getMsg()).build();
            }
            BeanUtils.copyProperties(centerStore, storeResDTO);
            if (OrderType.DISTRIBUTION.getCode().equals(orderType)) {
                storeResDTO.setStName(centerStore.getStName());
            } else {
//                storeResDTO.setStName(LocalConst.MERCHANT_B2C_FIXED_STORE_NAME + "(" + centerStore.getStName() + ")");
                storeResDTO.setStName(LocalConst.MERCHANT_B2C_FIXED_STORE_NAME);
            }
        } else {
            storeResDTO = orderAdaptService.getStoreInfo(storeId);
        }
        return storeResDTO;
    }
}

