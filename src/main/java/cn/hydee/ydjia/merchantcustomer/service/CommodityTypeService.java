package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.TypeSearchDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.TypeTreeReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CommodityType;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.TypeTreeDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/4
 */
public interface CommodityTypeService {

    /**
     * 根据条件查询商家默认的分组信息 (暂时不用)
     *
     * @param reqDTO
     * @return
     */
    List<TypeTreeDTO> getTypeTree(TypeTreeReqDTO reqDTO);


    /**
     * 获取分类树
     *
     * @param typeSearchReqDTO
     * @return
     */
    ResponseBase<List<CommodityType>> getTypeTree(TypeSearchDTO typeSearchReqDTO);

    /**
     * 查询商户一级分类
     *
     * @param merCode
     * @param spFlag
     * @return
     */
    ResponseBase<List<CommodityType>> listFirstType(String merCode, Boolean spFlag);
}
