package cn.hydee.ydjia.merchantcustomer.service;


import cn.hydee.ydjia.merchantcustomer.domain.ShopGoodscollect;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

/**
 * (ShopGoodscollect)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-29 16:37:32
 */
public interface ShopGoodscollectService {

    /**
     * 通过ID查询单条数据
     *
     * @param guid 主键
     * @return 实例对象
     */
    ShopGoodscollect queryById(String guid);

    /**
     * 分页查询
     *
     * @param shopGoodscollect 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<ShopGoodscollect> queryByPage(ShopGoodscollect shopGoodscollect, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param shopGoodscollect 实例对象
     * @return 实例对象
     */
    ShopGoodscollect insert(ShopGoodscollect shopGoodscollect);

    /**
     * 修改数据
     *
     * @param shopGoodscollect 实例对象
     * @return 实例对象
     */
    ShopGoodscollect update(ShopGoodscollect shopGoodscollect);

    /**
     * 通过主键删除数据
     *
     * @param guid 主键
     * @return 是否成功
     */
    boolean deleteById(String guid);



}
