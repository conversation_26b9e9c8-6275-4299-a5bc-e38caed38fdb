package cn.hydee.ydjia.merchantcustomer.service;

import org.springframework.data.redis.core.ZSetOperations;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis 缓存服务
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/25 16:35
 */
public interface RedisService {

    boolean hasKey(String key);

    void setValueExpireMinute(String key, Object value, int minute);

    void setValueExpire(String key, Object value, int minute, TimeUnit timeUnit);

    void setValue(String key, Object value);

    Long increment(String key, long delta);

    Long decrement(String key, long delta);

    String get(String key);

    Object getObject(String key);

    void deleteKey(String merchantExcelKey);

    ZSetOperations getZSetOperations();

    Object getHashKeyValue(String key, Object hashKey);

    boolean addZSet(String k, String v, double score);

    Set getZSetByName(String key, int start, int end);

    long getZSetSize(String k);

    Long addSet(String k, String v);

    Long removeSet(String key, String value);

    Long getSetSize(String k);

    Boolean isMember(String key, String value);

    void  removeZset(String key, Object value);

    Set<Object> zsetr(String key, Integer start, Integer end);

    boolean setIfAbsent(String key, Object value, long time, TimeUnit timeUnit);

    void deleteKeysWithPrefix(String key);

    boolean hashKey(String key, Object hashKey);

    void put(String key, Object hashKey, Object hasValue);

    void setTimeout(String key, long timeout, TimeUnit timeUnit);
}
