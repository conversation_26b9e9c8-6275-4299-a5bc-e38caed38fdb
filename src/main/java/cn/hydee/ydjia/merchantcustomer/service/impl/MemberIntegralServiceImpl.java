package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.resp.member.ConsumeRewardIntegralRuleDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.member.IntegralCleanRule;
import cn.hydee.ydjia.merchantcustomer.dto.resp.member.IntegralRuleDTO;
import cn.hydee.ydjia.merchantcustomer.enums.ExchangeRuleType;
import cn.hydee.ydjia.merchantcustomer.feign.MemberIntegralClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.OrderExchangeRuleSetClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.YdjExchangeRuleSetDTO;
import cn.hydee.ydjia.merchantcustomer.service.MemberIntegralService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.TBean;
import com.yxt.middle.integral.api.integral.IntegralPointApi;
import com.yxt.middle.integral.api.integral.IntegralRecordApi;
import com.yxt.middle.integral.dto.req.record.IntegralDeductReqDTO;
import com.yxt.middle.integral.dto.resp.record.IntegralCalculateRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-05-21
 */
@Service
@Slf4j
public class MemberIntegralServiceImpl implements MemberIntegralService {

    @Autowired
    private MemberIntegralClient memberIntegralClient;
    @Autowired
    private OrderExchangeRuleSetClient orderExchangeRuleSetClient;
    @Autowired
    private IntegralPointApi integralPointApi;
    @Resource
    private IntegralRecordApi integralRecordApi;


    @Override
    public boolean checkUserIntegral(String merCode, String userId, Integer needIntegral) {
        //ResponseBase<Integer> responseBase = memberIntegralClient.queryOnlineIntegral(merCode, userId);
        com.yxt.lang.dto.api.ResponseBase<Integer> responseBase = integralPointApi.queryBalance(Long.valueOf(userId));
        int userIntegral = responseBase.getData() == null ? 0 : responseBase.getData();
        return needIntegral <= userIntegral;
    }

    @Override
    public IntegralRuleDTO queryIntegralRule(String merCode) {

        IntegralRuleDTO ruleDTO = new IntegralRuleDTO();

        ResponseBase<IntegralCleanRule> integralRuleResponse = memberIntegralClient.getIntegralManagerDetail(merCode);
        if (integralRuleResponse.checkSuccess() && integralRuleResponse.getData() != null
                && integralRuleResponse.getData().getClearRule().equals(LocalConst.STATUS_ONE)) {
            ruleDTO.setCleanRule(integralRuleResponse.getData());
        }

        ResponseBase<List<YdjExchangeRuleSetDTO>> consumeRuleResponse = orderExchangeRuleSetClient.getRules(merCode);
        if (consumeRuleResponse.checkSuccess() && !CollectionUtils.isEmpty(consumeRuleResponse.getData())) {

            List<ConsumeRewardIntegralRuleDTO> rewardRules = consumeRuleResponse.getData().stream().map(consumeRule -> {
                ExchangeRuleType ruleType = ExchangeRuleType.getByType(consumeRule.getRuleType());
                if (ruleType != null) {
                    switch (ruleType) {
                        case NUMBER:
                        case AMOUNT:
                        case FIXED_AMOUNT:
                            return TBean.copy(ConsumeRewardIntegralRuleDTO.class, consumeRule);

                        case SPEC:
                            ruleDTO.setSelectType(consumeRule.getSelectType());
                            break;
                        default:
                    }
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(rewardRules)) {
                ruleDTO.setRewardRules(rewardRules);
            }
        }

        if (ruleDTO.allIsNull()) {
            return null;
        } else {
            return ruleDTO;
        }
    }

    @Override
    public void deductIntegral(String userId, String orderId, String memberCard, Integer changeIntegral, String stCode){
        if(changeIntegral <= 0) return;
        log.info("用户下单积分扣减 orderId: {}", orderId);
        IntegralDeductReqDTO reqDTO = new IntegralDeductReqDTO();
        reqDTO.setBizSource(3);
        reqDTO.setSceneCode("ZC005");//积分扣减默认场景编码
        reqDTO.setUserId(Long.valueOf(userId));
        reqDTO.setBizId(orderId);
        reqDTO.setBizTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        reqDTO.setMemberCardNo(memberCard);
        reqDTO.setStoreCode(stCode);
        reqDTO.setChangeIntegral(changeIntegral);
        com.yxt.middle.integral.dto.req.record.IntegralDeductReqDTO reqDTO1 = BeanUtil.copyProperties(reqDTO, com.yxt.middle.integral.dto.req.record.IntegralDeductReqDTO.class);
        com.yxt.lang.dto.api.ResponseBase<IntegralCalculateRespDTO> responseBase = integralRecordApi.deductIntegral(reqDTO1);
        if (!responseBase.checkSuccess()) {
            throw WarnException.builder().code(responseBase.getCode()).tipMessage(responseBase.getMsg()).build();
        }
    }

    @Override
    public Integer selectIntegral(Long userId){
        com.yxt.lang.dto.api.ResponseBase<Integer> responseBase = integralPointApi.queryBalance(userId);
        if (!responseBase.checkSuccess()) {
            throw WarnException.builder().code(responseBase.getCode()).tipMessage(responseBase.getMsg()).build();
        }
        return responseBase.getData();
    }
}
