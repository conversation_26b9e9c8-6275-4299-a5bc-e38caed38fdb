package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.fegin.es.req.StoreCommodityTypeReq;
import cn.hydee.ydjia.merchantcustomer.dto.fegin.es.req.StoreEmployeeCommodityTypeReq;
import cn.hydee.ydjia.merchantcustomer.dto.fegin.es.resp.StoreCommodityTypeResp;
import cn.hydee.ydjia.merchantcustomer.dto.req.CommodityStockValidReq;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityStockValidResp;

import javax.validation.Valid;
import java.util.List;

public interface EsSearchServiceClient {

    List<StoreCommodityTypeResp> countStoreCommodityType(@Valid StoreCommodityTypeReq storeCommodityTypeReq);

    List<StoreCommodityTypeResp> countStoreEmployeeCommodityType(@Valid StoreEmployeeCommodityTypeReq storeEmployeeCommodityTypeReq);

    PageDTO<CommodityStockValidResp> hasStockAll(@Valid CommodityStockValidReq commodityStockValidReq);
}
