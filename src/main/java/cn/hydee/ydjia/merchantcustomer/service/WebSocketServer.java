package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.resp.LiveSendMessageDTO;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2020/12/16 14:43
 **/
@Slf4j
@ServerEndpoint("/web/socket/{liveId}/{userId}")
@Component
public class WebSocketServer {


    /**
     * concurrent 包的线程安全Set，设置初始长度，避免频繁扩容消耗资源
     * 根据liveId来获取对应的 session
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>(1024);

    private static ActivityLiveChatService liveChatService;

    private static LiveService liveService;

    private static RedisService redisService;

    private Session session;

    /**
     * 直播id
     */
    private String liveId;


    // 注入的时候，给类的 service 注入 (spring管理的bean为单例，与websocket多对象冲突)
    @Autowired
    public void setLiveChatService(ActivityLiveChatService liveChatService) {
        WebSocketServer.liveChatService = liveChatService;
    }

    @Autowired
    public void setLiveService(LiveService liveService) {
        WebSocketServer.liveService = liveService;
    }

    @Autowired
    public void setRedisService(RedisService redisService) {
        WebSocketServer.redisService = redisService;
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("liveId") String liveId, @PathParam("userId") String userId) {
        webSocketMap.remove(liveId + ":" + userId);
        webSocketMap.put(liveId + ":" + userId, this);
        this.session = session;
        this.liveId = liveId;
        /**
         * 用户id
         */
        this.addClusterSessionCount(liveId, userId);
        long onlineNum = this.getClusterSessionCount(liveId);
        log.info("直播:{},用户连接:{},当前在线人数为:{}", liveId, userId, onlineNum);
        long paramLiveId = Long.parseLong(liveId);
        long paramUserId = Long.parseLong(userId);
        Long anchorUserId = liveChatService.getAnchorUserId(paramLiveId);
        LiveSendMessageDTO message = liveChatService.getMessage(paramLiveId);
        message.setTerminal(3);
        message.setMsgDirection(2);
        message.setCommand("open");
        message.setLiveId(liveId);
        if (Objects.nonNull(anchorUserId) && anchorUserId.equals(paramUserId)) {
            Integer linkRequestNum = liveService.getLinkApplyUsers(paramLiveId);
            message.setLinkRequestNum(linkRequestNum);
        }
        sendMessage(session, JSON.toJSONString(message));
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session, @PathParam("liveId") String liveId, @PathParam("userId") String userId) {
        webSocketMap.remove(liveId + ":" + userId);
        this.removeClusterSessionCount(liveId, userId);
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     * @param session
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("liveId") String liveId, @PathParam("userId") String userId) {

//        log.info("收到来自直播：" + liveId + "，用户:" + userId + "的信息：" + message);

        if (StringUtils.isNotBlank(message)) {
            // 解析发送的报文
            JSONObject jsonObject = JSON.parseObject(message);
            String command = jsonObject.getString("command");
            if (StringUtils.isNotBlank(command) && webSocketMap.containsKey(liveId + ":" + userId)) {
                LiveSendMessageDTO messageDTO = liveChatService.getMessage(Long.parseLong(liveId));
                messageDTO.setTerminal(3);
                messageDTO.setMsgDirection(2);
                messageDTO.setCommand("heartbeat");
                sendMessage(webSocketMap.get(liveId + ":" + userId).session, JSON.toJSONString(messageDTO));
            }
        }
    }

    /**
     * @param error
     */
    @OnError
    public void onError(Throwable error) {
        log.error("连接错误：{}", error.getMessage());
    }


    /**
     * 添加集群的在线人数
     *
     * @param liveId
     * @param userId
     */
    private void addClusterSessionCount(String liveId, String userId) {
        redisService.addSet(LocalConst.REDIS_LIVE_WEBSOCKET_SESSION_LIST + liveId, userId);
    }

    /**
     * 移除集群的在线人数
     *
     * @param liveId
     * @param userId
     */
    private void removeClusterSessionCount(String liveId, String userId) {
        redisService.removeSet(LocalConst.REDIS_LIVE_WEBSOCKET_SESSION_LIST + liveId, userId);
    }

    /**
     * 获取集群的在线人数
     *
     * @param liveId
     * @return
     */
    private long getClusterSessionCount(String liveId) {
        return redisService.getSetSize(LocalConst.REDIS_LIVE_WEBSOCKET_SESSION_LIST + liveId);
    }

    /**
     * 实现服务器主动推送
     *
     * @param session
     * @param message
     */
    public static void sendMessage(Session session, String message) {

        try {
            session.getBasicRemote().sendText(message);
        } catch (IOException | IllegalStateException e) {
            log.error("发送消息失败，error={}", e.getMessage());
        }
    }

    /**
     * 群发自定义消息
     *
     * @param message
     */
    public static void sendClusterWebsocketMessage(LiveSendMessageDTO message) {

        if (Objects.isNull(message)) {
            return;
        }
        String liveId = message.getLiveId();
        Long anchorId = message.getAnchorId();
        String messageStr = JSON.toJSONString(message);
        log.info("发送消息到：{}，消息：{}", liveId, messageStr);
        if (Objects.nonNull(anchorId) && webSocketMap.containsKey(liveId + ":" + anchorId)) {
            sendMessage(webSocketMap.get(liveId + ":" + anchorId).session, messageStr);
            return;
        }
        // 遍历集合
        for (Map.Entry<String, WebSocketServer> entry : webSocketMap.entrySet()) {
            WebSocketServer webSocketServer = entry.getValue();
            if (webSocketServer != null && webSocketServer.liveId.equals(liveId)) {
                synchronized (webSocketServer.session) {
                    sendMessage(webSocketServer.session, messageStr);
                }
            }
        }
    }


}



