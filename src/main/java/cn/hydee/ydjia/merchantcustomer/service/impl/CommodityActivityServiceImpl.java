package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.BeanUtil;
import cn.hydee.starter.util.ExLogger;
import cn.hydee.ydjia.merchantcustomer.dto.activity.CommonCommodityActivityPriceQueryDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.dto.sdp.resp.SdpConfigInfo;
import cn.hydee.ydjia.merchantcustomer.dto.sp.SpSpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.sp.StoreSpSpecReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.sp.StoreSpSpecRespDTO;
import cn.hydee.ydjia.merchantcustomer.enums.CommodityType;
import cn.hydee.ydjia.merchantcustomer.enums.DistributionOrgTypeEnum;
import cn.hydee.ydjia.merchantcustomer.enums.PageType;
import cn.hydee.ydjia.merchantcustomer.enums.SourceChannelType;
import cn.hydee.ydjia.merchantcustomer.feign.*;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.CommodityOfflinePromotionPriceReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.PriceDiscountParamDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.coupon.ProductDiscountDetailDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.pmt.PmtRuleMoreDiscount;
import cn.hydee.ydjia.merchantcustomer.feign.dto.pmt.PmtRuleMoreDiscountDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.pmt.PmtRuleSeckillDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.pmt.rule.PmtRulePresale;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.PmtRuleRestrictSeckill;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.StoreListReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.PmtRuleDistribution;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.sharedWarehouse.SharedStockQueryReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.sharedWarehouse.SharedStockQueryRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.*;
import cn.hydee.ydjia.merchantcustomer.feign.service.CommonCommodityActivityService;
import cn.hydee.ydjia.merchantcustomer.feign.service.MerchantSwitchService;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonCommodityClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.feign.util.MathUtils;
import cn.hydee.ydjia.merchantcustomer.service.*;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.MemberPriceUtils;
import cn.hydee.ydjia.merchantcustomer.util.RedisKeyUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @date 2019/12/2 18:30
 */
@Service
@Slf4j
public class CommodityActivityServiceImpl implements CommodityActivityService {
    @Autowired
    private PromoteClient promoteClient;

    @Autowired
    private CommodityClient commodityClient;

    @Autowired
    private OrderActivityClient orderActivityClient;

    @Autowired
    private SdpConfigClient sdpConfigClient;

    @Autowired
    private MemberDiscountService memberDiscountService;

    @Autowired
    private CommoditySearchClient commoditySearchClient;

    @Autowired
    private LocalCacheService cacheService;

    @Autowired
    private MerchantSwitchService merchantSwitchService;

    @Autowired
    private YdjStoreService ydjStoreService;

    @Autowired
    private StoreClient storeClient;

    //是否限制查询商城促销 限制：ture,不限制：false
    @Value("${XZ_QUERY_PROMOTION:true}")
    private Boolean XZ_QUERY_PROMOTION;

    //是否限制查询会员促销活动 限制：ture,不限制：false
    @Value("${XZ_QUERY_MEMBER:true}")
    private Boolean XZ_QUERY_MEMBER;

    //是否限制查询营销优惠券活动 限制：ture,不限制：false
    @Value("${XZ_QUERY_COUPON:true}")
    private Boolean XZ_QUERY_COUPON;

    @Value("${XZ_MERCODES:999999}")
    private String XZ_MERCODES;

    @Value("${XZ_COMMODITY_IDS}")
    private String XZ_COMMODITY_IDS;

    @Value("${b2cSpecNoStock.redisCacheExpireTime}")
    private Integer b2cSpecNoStockRedisCacheExpireTime;

    @Lazy
    @Autowired
    private CommonCommodityActivityService commonCommodityActivityService;

    @Value("${switch.priceQuery.detail-page-no-coupon}")
    private Boolean detailNoCouponQuerySwitch;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CommonCommodityClientWrapperService commonCommodityClientWrapperService;

    /**
     * 根据商品ID获取商品规格信息及规格对应的活动
     *
     * @param dto
     * @return
     */
    @Override
    public StoreSpecRespDTO getSpecActivity(StoreSpecReqDTO dto, StoreSpecRespDTO respDTO) {
        if (respDTO == null) {
            respDTO = commodityClient.queryStoreSpec(dto).getData();
            if (respDTO == null || CollectionUtils.isEmpty(respDTO.getSpecList())) {
                return respDTO;
            }
            if (BooleanUtils.isTrue(dto.getHasShare())) {
                // 处理共享库存
                this.handleSharedStock(dto.getMerCode(), respDTO.getSpecList());
            }
        }

        // 参考价赋0
        respDTO.getSpecList().forEach(o -> o.setMPrice(BigDecimal.ZERO));
        List<Spec> specList = respDTO.getSpecList();
        // 获取当前商品所有erpCode
        List<String> erpCodeList = specList.stream().filter(o -> StringUtils.hasLength(o.getErpCode())).map(Spec::getErpCode).distinct().collect(toList());
        // 过滤掉下架规格
        specList = specList.stream().filter(spec -> spec.getStatus() > 0).collect(toList());
        // 过滤后再次非空判断
        if (CollectionUtils.isEmpty(specList)) {
            respDTO.setSpecList(specList);
            return respDTO;
        }

        //如果门店未开启医保支付，过滤医保编码属性
        if (!cacheService.queryMedicalPaymentConfig(dto.getMerCode(), dto.getStoreId())) {
            for (Spec spec : specList) {
                spec.setMedicalInsuranceCode(null);
            }
        }

        //根据已选择的sku键值对过滤规格（切换规格：第二次查询规格信息或切换到另一个规格）
        if (!CollectionUtils.isEmpty(dto.getSpecSkuList())) {
            specList = this.filterSpec(specList, dto.getSpecSkuList());
            if (!StringUtils.hasLength(dto.getSpecId())) {
                dto.setSpecId(specList.get(0).getId());
            }
        }

        //过滤后再次非空判断
        if (CollectionUtils.isEmpty(specList)) {
            respDTO.setSpecList(specList);
            return respDTO;
        }

        //组合商品
        if (CommodityType.Combined_commodity.getCode().equals(dto.getCommodityType())
                && !StringUtils.isEmpty(dto.getUserId())) {
            checkAssemblyLimitNum(dto.getMerCode(), dto.getUserId(), specList);
        }
        respDTO.setSpecList(specList);

        boolean specEmpty = false;
        boolean specInvalid = false;
        Spec defaultSpec = null;
        if (StringUtils.isEmpty(dto.getSpecId())) {
            specEmpty = true;
        } else {
            List<Spec> currentSpecs = respDTO.getSpecList().stream().filter(spec -> dto.getSpecId().equals(spec.getId()))
                    .collect(toList());
            if (CollectionUtils.isEmpty(currentSpecs)) {
                specInvalid = true;
            } else {
                defaultSpec = currentSpecs.get(0);
            }
        }


        // 赋值云集市商品业务属性供前端传递
        respDTO.getSpecList().stream().forEach(spec -> {
            spec.setCommodityOrigin(CommodityOriginType.getCollectionType(spec.getOrigin()));
            spec.setCollectionMark(LocalConst.STATUS_ONE.equals(spec.getSupplierReceiveMoney()) ? true : false);
        });

        // 是否需要查询促销中台接口
        boolean needQueryPmtFlag = Objects.isNull(dto.getNeedQueryPmtFlag()) ? false : dto.getNeedQueryPmtFlag();
        List<ActivitySpecDTO> activitySpecDTOS = respDTO.getActivitySpecDTOS();
        if (needQueryPmtFlag || CollectionUtils.isEmpty(activitySpecDTOS)) {
            activitySpecDTOS = this.handlePmtActivitySpecList(dto, respDTO);
            respDTO.setActivitySpecDTOS(activitySpecDTOS);
        }
        if (CollectionUtils.isEmpty(activitySpecDTOS)) {
            return respDTO;
        }

        Map<String, ActivitySpecDTO> specId2SpecMap = activitySpecDTOS.parallelStream()
                .collect(Collectors.toMap(ActivitySpecDTO::getSpecId, Function.identity(), (key1, key2) -> key1));
        // 赋值门店编码和商品编码
        activitySpecDTOS.forEach(activitySpecDTO -> {
            activitySpecDTO.setStoreCode(dto.getStoreCode());
            ActivitySpecDTO specDTO = specId2SpecMap.get(activitySpecDTO.getSpecId());
            if (specDTO == null) {
                return;
            }
            activitySpecDTO.setErpCode(specDTO.getErpCode());
            activitySpecDTO.setGoodsOriginPrice(specDTO.getGoodsOriginPrice());
            activitySpecDTO.setPaidMemberFlag(false);
        });
        PriceDiscountParamDTO priceDiscountReq = new PriceDiscountParamDTO();
        if (dto.getLoginUserDTO() != null) {
            BeanUtils.copyProperties(dto.getLoginUserDTO(), priceDiscountReq);
        }
        priceDiscountReq.setPageType(PageType.DETAIL_PAGE.getType());
        if (XZ_MERCODES.contains(dto.getMerCode()) && XZ_COMMODITY_IDS.contains(String.valueOf(dto.getCommodityId()))) {
            priceDiscountReq.setNoQueryCouponPrice(XZ_QUERY_COUPON);
            priceDiscountReq.setNoQueryMemberPrice(XZ_QUERY_MEMBER);
            priceDiscountReq.setNoQueryPlusPrice(XZ_QUERY_MEMBER);
        } else {
            priceDiscountReq.setNoQueryCouponPrice(detailNoCouponQuerySwitch);
        }

        // 限制个别商户、个别商品 查询促销活动
        // 查询会员日、会员权益、plus，封装成活动
        if (XZ_MERCODES.contains(dto.getMerCode()) && XZ_COMMODITY_IDS.contains(String.valueOf(dto.getCommodityId()))) {
            if (!XZ_QUERY_MEMBER || !XZ_QUERY_COUPON) {
                activitySpecDTOS = memberDiscountService.getMarketActivityDiscount(activitySpecDTOS, priceDiscountReq);
            }
        } else {
            activitySpecDTOS = memberDiscountService.getMarketActivityDiscount(activitySpecDTOS, priceDiscountReq);
        }
        if (CollectionUtils.isEmpty(activitySpecDTOS)) {
            return respDTO;
        }

        // 仅普通商品商详页初始化才执行异步通知拉取线下促销价
        if (CommodityType.NORMAL.getCode().equals(dto.getCommodityType()) && dto.getInitPage() != null && dto.getInitPage()) {
            this.commonCommodityActivityService.pullCommodityOfflinePromotionPrice(
                    CommodityOfflinePromotionPriceReqDTO.builder().merCode(dto.getMerCode()).storeId(dto.getStoreId())
                            .storeCode(dto.getStoreCode()).erpCodeList(erpCodeList).build()
            );
        }

        //k-v转换specList
        Map<String, ActivitySpecDTO> activitySpecMap = activitySpecDTOS.stream()
                .collect(Collectors.toMap(ActivitySpecDTO::getSpecId, a -> a, (k1, k2) -> k1));

        specList = specList.stream().filter(o -> {
            Boolean flag = activitySpecMap.keySet().contains(o.getId());
            if(Boolean.TRUE.equals(flag)){
                o.setApplyPaidMemberPrice(activitySpecMap.get(o.getId()).getApplyPaidMemberPrice());
            }
            return flag;
        }).collect(toList());
        if (CollectionUtils.isEmpty(specList)) {
            return respDTO;
        }
        String memberDiscountName = null;
        for (Spec spec : specList) {
            ActivitySpecDTO activitySpecDTO = activitySpecMap.get(spec.getId());
            if (activitySpecDTO == null) {
                continue;
            }
            spec.setPaidMemberFlag(activitySpecDTO.getPaidMemberFlag());
            spec.setPredThriftRespDTO(activitySpecDTO.getPredThriftRespDTO());
            this.compActivityForDetailPage(dto, activitySpecDTO, spec);
            if (CollectionUtils.isEmpty(activitySpecDTO.getActivityList())) {
                continue;
            }
            List<String> commodityActivityTypeNames = Lists.newArrayList();
            if (defaultSpec != null) {
                if (dto.getSpecId().equals(spec.getId())) {
                    // 促销和会员活动有标签，优享和券后价无标签
                    List<ActivityDTO> activityList = activitySpecDTO.getActivityList().stream().filter(o -> !StringUtils.isEmpty(o.getLabelName())).collect(toList());
                    // 促销活动
                    commodityActivityTypeNames.addAll(activityList.stream().filter(o -> !PromotionType.MEMBER_DAY.getType().equals(o.getPmtType().getType()))
                            .map(ActivityDTO::getLabelName).distinct().collect(toList()));
                    // 超级会员日、会员日、会员权益
                    memberDiscountName = activityList.stream().filter(o -> PromotionType.MEMBER_DAY.getType().equals(o.getPmtType().getType()))
                            .map(ActivityDTO::getLabelName).collect(Collectors.joining());
                    spec.setCommodityActivityTypeNames(commodityActivityTypeNames);
                    this.setIsCouponsTake(spec, activitySpecDTO.getActivityList());
                    break;
                }
            } else if (specEmpty || specInvalid) {
                List<ActivityDTO> activityList = activitySpecDTO.getActivityList().stream().filter(o -> !StringUtils.isEmpty(o.getLabelName())).collect(toList());
                // 促销活动
                commodityActivityTypeNames.addAll(activityList.stream().filter(o -> !PromotionType.MEMBER_DAY.getType().equals(o.getPmtType().getType()))
                        .map(ActivityDTO::getLabelName).distinct().collect(toList()));
                // 超级会员日、会员日、会员权益
                memberDiscountName = activityList.stream().filter(o -> PromotionType.MEMBER_DAY.getType().equals(o.getPmtType().getType()))
                        .map(ActivityDTO::getLabelName).collect(Collectors.joining());
                spec.setCommodityActivityTypeNames(commodityActivityTypeNames);
                this.setIsCouponsTake(spec, activitySpecDTO.getActivityList());
            }
        }
        respDTO.setSpecList(specList);
        respDTO.setMemberDiscountName(memberDiscountName);
        return respDTO;
    }

    /**
     * @param merCode
     * @param specList
     * @description TODO 处理共享仓库存
     * <AUTHOR>
     * @date 2023/5/9
     */
    private void handleSharedStock(String merCode, List<Spec> specList) {
        if (CollectionUtils.isEmpty(specList)) {
            return;
        }
        List<Spec> specs = specList.stream().filter(spec -> spec.getStatus() > 0).collect(toList());
        if (CollectionUtils.isEmpty(specs)) {
            return;
        }
        // 查询共享库存
        Map<Long, SharedStockQueryRespDTO> sharedStockQueryRespDTOMap = commonCommodityClientWrapperService.querySharedStock(
                SharedStockQueryReqDTO.builder().merCode(merCode)
                        .specIds(specs.stream().map(o -> Long.valueOf(o.getId())).collect(toList())).build()
        );
        if (CollectionUtils.isEmpty(sharedStockQueryRespDTOMap)) {
            return;
        }
        specList.stream().forEach(spec -> {
            SharedStockQueryRespDTO sharedStockQueryRespDTO = sharedStockQueryRespDTOMap.get(Long.valueOf(spec.getId()));
            if (Objects.isNull(sharedStockQueryRespDTO) || Objects.isNull(sharedStockQueryRespDTO.getStock())) {
                return;
            }
            // 设置共享仓库存
            spec.setSharedStock(sharedStockQueryRespDTO.getStock() < 0 ? 0 : sharedStockQueryRespDTO.getStock());
        });
    }

    /**
     * 处理规格共享库存逻辑
     *
     * @param merCode
     * @param specList
     */
    private void handleSpecSharedStock(String merCode, List<Spec> specList) {
        if (CollectionUtils.isEmpty(specList)) {
            return;
        }
        Map<String, Spec> erpCodeMap = specList.stream().filter(o -> StringUtils.hasLength(o.getErpCode())
                && Objects.nonNull(o.getSharedStock()) && o.getSharedStock() > 0).collect(Collectors.toMap(Spec::getErpCode, Function.identity(), (key1, key2) -> key1));
        if (CollectionUtils.isEmpty(erpCodeMap)) {
            return;
        }
        List<String> erpCodeList = new ArrayList<>(erpCodeMap.keySet());
        // 查询redis
        Set<String> needQueryErpCodes = Sets.newHashSet();
        Set<String> finalRedisErpCodes;
        String effectiveStockKey = RedisKeyUtil.getRedisB2cEFFECTIVEStockKey(merCode);
        Object object = redisService.getObject(effectiveStockKey);
        if (Objects.nonNull(object)) {
            Set<String> redisErpCodes = JSON.parseObject((String) object, new TypeReference<Set<String>>() {
            });
            finalRedisErpCodes = redisErpCodes;
            erpCodeList.stream().forEach(erpCode -> {
                if (redisErpCodes.contains(erpCode)) {
                    if (Objects.nonNull(erpCodeMap.get(erpCode))) {
                        erpCodeMap.get(erpCode).setSharedStock(0);
                    }
                    return;
                }
                needQueryErpCodes.add(erpCode);
            });
            finalRedisErpCodes.addAll(needQueryErpCodes);
        } else {
            needQueryErpCodes.addAll(erpCodeList);
            finalRedisErpCodes = needQueryErpCodes;
        }
        if (CollectionUtils.isEmpty(needQueryErpCodes)) {
            return;
        }
        List<String> needCacheErpCodes = Lists.newArrayList();
        specList.stream().forEach(spec -> {
            if (Objects.isNull(spec.getSharedStock()) || spec.getSharedStock() <= 0) {
                spec.setSharedStock(0);
                needCacheErpCodes.add(spec.getErpCode());
                return;
            }
        });
        if (CollectionUtils.isEmpty(needCacheErpCodes)) {
            return;
        }
        // 更新缓存
        redisService.setValueExpire(effectiveStockKey, JSON.toJSONString(finalRedisErpCodes), b2cSpecNoStockRedisCacheExpireTime, TimeUnit.MINUTES);
        ExLogger.logger().field("updateB2CStock").info("updateB2CStock，key={}，value={}", effectiveStockKey, JSON.toJSONString(finalRedisErpCodes));
    }

    /**
     * 按照返回活动，组装商详页需要展示的活动信息
     *
     * @param dto
     * @param activitySpec
     * @param spec
     */
    private void compActivityForDetailPage(StoreSpecReqDTO dto, ActivitySpecDTO activitySpec, Spec spec) {
        List<ActivityDTO> activityList = activitySpec.getActivityList();
        // 分销商品详情页过滤分销活动
        if (SourceChannelType.DISTRIBUTION.getCode().equals(dto.getSourceChannelType())) {
            if (CollectionUtils.isEmpty(activityList)) {
                return;
            }
            activityList = activityList.stream().filter(o -> PromotionType.DISTRIBUTION.getCode().equals(o.getPmtType().getCode())).collect(toList());
            // 分销价
            spec.setFlashPrice(activityList.get(0).getPmtDistributionRule().getDistributionPrice());
            spec.setMPrice(MathUtils.setSacle(activitySpec.getBeforePrice()));
            spec.setGoodsSalesPrice(MathUtils.setSacle(spec.getPrice()));
            spec.setGoodsOriginPrice(spec.getMPrice());
        } else {
            if (!CollectionUtils.isEmpty(activityList)) {
                activityList = activityList.stream().filter(o -> !PromotionType.DISTRIBUTION.getCode().equals(o.getPmtType().getCode())).collect(toList());
            }
            // 优惠价
            if (activitySpec.getDiscount() != null) {
                spec.setFlashPrice(activitySpec.getDiscount());
            }
            // 划线价初始化
            spec.setGoodsOriginPrice(MathUtils.setSacle(activitySpec.getGoodsOriginPrice()));
            // 各种优惠价赋值
            this.handleAllActivityPrice(activitySpec, activityList, spec);
        }

        spec.setBeforePrice(activitySpec.getBeforePrice());
        spec.setActivityList(activityList);
        activitySpec.setActivityList(activityList);
        if (CollectionUtils.isEmpty(activityList)) {
            return;
        }

        // 处理促销橱窗图
        SpecDrawRuleDTO specDrawRuleDTO = this.handlePmtWindowDiagram(activitySpec.getPmtFlag(), activitySpec.getActivityList());
        if (specDrawRuleDTO.getWindowDiagramUrl() != null) {
            spec.setWindowDiagramUrl(specDrawRuleDTO.getWindowDiagramUrl());
            spec.setTemplateId(specDrawRuleDTO.getTemplateId());
        }
        if (specDrawRuleDTO.getPmtRuleMoreDiscountDTO() != null) {
            spec.setPmtRuleMoreDiscountDTO(specDrawRuleDTO.getPmtRuleMoreDiscountDTO());
        }
        if (specDrawRuleDTO.getPmtRuleSeckillDTO() != null) {
            spec.setPmtRuleSeckillDTO(specDrawRuleDTO.getPmtRuleSeckillDTO());
        }

        // 处理预售
        if (PriceTagEnum.PRICE_SEVENTEEN.getCode().equals(spec.getPriceTag())) {
            ActivityDTO activityDTO = activityList.get(0);
            if (Objects.nonNull(activityDTO)) {
                spec.setPresaleType(activityDTO.getPresaleType());
                spec.setDeposit(activityDTO.getDeposit());
                spec.setBalanceDue((Objects.isNull(activityDTO.getBalanceDue())
                        || activityDTO.getBalanceDue().compareTo(BigDecimal.ZERO) < 0) ? BigDecimal.ZERO : activityDTO.getBalanceDue());
                PmtRulePresale pmtRulePresale = activityDTO.getPmtRulePresale();
                // 设置活动库存售罄标识
                if (LocalConst.STATUS_ONE.equals(pmtRulePresale.getLimitStockFlag())) {
                    // 限制库存
                    Integer limitStockQty = pmtRulePresale.getLimitStockQty();
                    Integer leftStock = pmtRulePresale.getLeftStock();
                    if (Objects.isNull(limitStockQty) || limitStockQty == 0 || Objects.isNull(leftStock) || leftStock == 0) {
                        spec.setPresaleStockSellOutFlag(true);
                    }
                }
                // 用户还能购买的数量
                spec.setBuyNumEd((pmtRulePresale == null || pmtRulePresale.getBuyNumEd() == null) ? 0 : pmtRulePresale.getBuyNumEd());
            }
            spec.setActivityDTO(activityDTO);
        }

        // 参加了限时优惠活动
        List<ActivityDTO> flashActivityList = activityList.stream().filter(o -> PromotionType.FLASH_PRICE.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (!CollectionUtils.isEmpty(flashActivityList)) {
            ActivityDTO activityDTO = flashActivityList.get(0);
            // 限时优惠的buyNumEd从活动规则赋值给商品
            if (activityDTO != null) {
                spec.setBuyNumEd((activityDTO.getPmtRuleRestrictSeckill() == null || activityDTO.getPmtRuleRestrictSeckill().getBuyNumEd() == null) ?
                        0 : activityDTO.getPmtRuleRestrictSeckill().getBuyNumEd());
            }
            spec.setActivityDTO(activityDTO);
        }
        // 详情页新增展示拼团活动标志
        List<ActivityDTO> groupList = activityList.stream().filter(o -> PromotionType.GROUP.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (!CollectionUtils.isEmpty(groupList)) {
            spec.setGroupDto(groupList.get(0));
        }
        // 参加了分销(分销活动与其他互斥)
        List<ActivityDTO> distributionActivityList = activityList.stream().filter(o -> PromotionType.DISTRIBUTION.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (!CollectionUtils.isEmpty(distributionActivityList)) {
            ActivityDTO activityDTO = distributionActivityList.get(0);
            PmtRuleDistribution pmtRuleDistribution = activityDTO.getPmtDistributionRule();
            // 设置分销活动商品规则
            this.setPmtDistributionRule(pmtRuleDistribution, null);
            spec.getActivityList().get(0).setPmtDistributionRule(pmtRuleDistribution);
        }
    }

    @Override
    public SpecDrawRuleDTO handlePmtWindowDiagram(Boolean pmtFlag, List<ActivityDTO> activityList) {
        SpecDrawRuleDTO specDrawRuleDTO = new SpecDrawRuleDTO();
        if (CollectionUtils.isEmpty(activityList)) {
            return specDrawRuleDTO;
        }
        boolean presaleFlag = PromotionType.PRESALE.getCode().equals(activityList.get(0).getPmtType().getCode());
        if (presaleFlag) {
            // 预售特殊处理橱窗图
            specDrawRuleDTO.setWindowDiagramUrl(activityList.get(0).getWindowDiagramUrl());
            specDrawRuleDTO.setTemplateId(activityList.get(0).getTemplateId());
        }
        if (pmtFlag == null || !pmtFlag) {
            return specDrawRuleDTO;
        }
        List<ActivityDTO> pmtActivityList = activityList.stream().filter(o -> !StringUtils.isEmpty(o.getLabelName())
                && !PromotionType.MEMBER_DAY.getType().equals(o.getPmtType().getType())).collect(toList());
        if (CollectionUtils.isEmpty(pmtActivityList)) {
            return specDrawRuleDTO;
        }
        // 橱窗图（新老小程序兼容）
        List<ActivityDTO> flashActivityList = pmtActivityList.stream().filter(o -> PromotionType.FLASH_PRICE.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (!CollectionUtils.isEmpty(flashActivityList)) {
            ActivityDTO activityDTO = flashActivityList.get(0);
            Integer windowDiagramMode = activityDTO.getWindowDiagramMode();
            if (!LocalConst.STATUS_ONE.equals(windowDiagramMode)) {
                specDrawRuleDTO.setWindowDiagramUrl(activityDTO.getWindowDiagramUrl());
                specDrawRuleDTO.setTemplateId(activityDTO.getTemplateId());
            } else {
                PmtRuleRestrictSeckill pmtRuleRestrictSeckill = activityDTO.getPmtRuleRestrictSeckill();
                if (pmtRuleRestrictSeckill != null) {
                    specDrawRuleDTO.setPmtRuleSeckillDTO(PmtRuleSeckillDTO.builder().pmtMode(pmtRuleRestrictSeckill.getPmtMode()).discount(pmtRuleRestrictSeckill.getDiscount()).build());
                }
            }
            return specDrawRuleDTO;
        }
        List<ActivityDTO> moreActivityList = pmtActivityList.stream().filter(o -> PromotionType.MORE_DISCOUNT.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (!CollectionUtils.isEmpty(moreActivityList)) {
            ActivityDTO activityDTO = moreActivityList.get(0);
            Integer windowDiagramMode = activityDTO.getWindowDiagramMode();
            if (!LocalConst.STATUS_ONE.equals(windowDiagramMode)) {
                specDrawRuleDTO.setWindowDiagramUrl(activityDTO.getWindowDiagramUrl());
                specDrawRuleDTO.setTemplateId(activityDTO.getTemplateId());
            } else {
                PmtRuleMoreDiscount pmtRuleMoreDiscount = activityDTO.getPmtRuleMoreDiscount();
                if (pmtRuleMoreDiscount != null) {
                    specDrawRuleDTO.setPmtRuleMoreDiscountDTO(PmtRuleMoreDiscountDTO.builder().ruleSelection(pmtRuleMoreDiscount.getRuleSelection()).threshold(pmtRuleMoreDiscount.getThreshold()).discount(pmtRuleMoreDiscount.getDiscount()).build());
                }
            }
            return specDrawRuleDTO;
        }
        List<ActivityDTO> fullActivityList = pmtActivityList.stream().filter(o -> PromotionType.FULL.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (!CollectionUtils.isEmpty(fullActivityList)) {
            specDrawRuleDTO.setWindowDiagramUrl(fullActivityList.get(0).getWindowDiagramUrl());
            specDrawRuleDTO.setTemplateId(fullActivityList.get(0).getTemplateId());
            return specDrawRuleDTO;
        }
        List<ActivityDTO> addActivityList = pmtActivityList.stream().filter(o -> PromotionType.ADD_PRICE.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (!CollectionUtils.isEmpty(addActivityList)) {
            specDrawRuleDTO.setWindowDiagramUrl(addActivityList.get(0).getWindowDiagramUrl());
            specDrawRuleDTO.setTemplateId(addActivityList.get(0).getTemplateId());
        }
        return specDrawRuleDTO;
    }

    @Override
    public void handleGroupCommonDetailPagePrice(CommodityGroupDTO commodityGroupDTO) {
        if (Objects.isNull(commodityGroupDTO) || Objects.isNull(commodityGroupDTO.getGroupCommodityRespDTO())) {
            return;
        }
        // 规格信息
        GroupCommodityRespDTO groupCommodityRespDTO = commodityGroupDTO.getGroupCommodityRespDTO();
        // 会员登录信息
        LoginUserDTO loginUserDTO = commodityGroupDTO.getLoginUserDTO();

        // 组装查询活动价参数
        CommonCommodityActivityPriceQueryDTO commonCommodityActivityPriceQueryDTO = new CommonCommodityActivityPriceQueryDTO();
        commonCommodityActivityPriceQueryDTO.setMerCode(commodityGroupDTO.getMerCode());
        commonCommodityActivityPriceQueryDTO.setStoreId(groupCommodityRespDTO.getStoreId());
        commonCommodityActivityPriceQueryDTO.setCommodityId(commodityGroupDTO.getId());
        commonCommodityActivityPriceQueryDTO.setCommodityType(commodityGroupDTO.getCommodityType());
        commonCommodityActivityPriceQueryDTO.setTypeId(commodityGroupDTO.getTypeId());
        commonCommodityActivityPriceQueryDTO.setLoginUserDTO(loginUserDTO);
        // 组装规格
        List<ActivitySpecDTO> activitySpecDTOS = Lists.newArrayList();
        ActivitySpecDTO activitySpecDTO = new ActivitySpecDTO();
        activitySpecDTO.setMerCode(commodityGroupDTO.getMerCode());
        activitySpecDTO.setStoreId(groupCommodityRespDTO.getStoreId());
        activitySpecDTO.setSpecId(groupCommodityRespDTO.getSpecId());
        activitySpecDTO.setUserId(Objects.nonNull(loginUserDTO) ? loginUserDTO.getUserId() : null);
        activitySpecDTO.setDiscount(groupCommodityRespDTO.getBeforePrice());
        activitySpecDTO.setBeforePrice(groupCommodityRespDTO.getBeforePrice());
        activitySpecDTO.setCount(LocalConst.DEFAULT_SPEC_COUNT);
        activitySpecDTO.setTypeId(commodityGroupDTO.getTypeId());
        activitySpecDTO.setCommodityType(commodityGroupDTO.getCommodityType());
        activitySpecDTO.setErpCode(groupCommodityRespDTO.getErpCode());
        activitySpecDTO.setGoodsOriginPrice(groupCommodityRespDTO.getMPrice());
        activitySpecDTO.setOfflinePromotionPriceInfo(groupCommodityRespDTO.getPromotionPrice(), groupCommodityRespDTO.getActivityStartTime(), groupCommodityRespDTO.getActivityEndTime());
        activitySpecDTO.setNoPresalePmtFlag(true);
        activitySpecDTOS.add(activitySpecDTO);
        commonCommodityActivityPriceQueryDTO.setActivitySpecDTOS(activitySpecDTOS);
        // 处理商品活动价
        this.handleCommonCommodityActivityPrice(commonCommodityActivityPriceQueryDTO);

        // 设置拼团商品划线价
        ActivitySpecDTO specDTO = commonCommodityActivityPriceQueryDTO.getActivitySpecDTOS().get(0);
        if (Objects.nonNull(specDTO.getPriceTag())) {
            commodityGroupDTO.getGroupCommodityRespDTO().setBeforePrice(specDTO.getGoodsSalesPrice());
        }
    }

    @Override
    public void handleCommonCommodityActivityPrice(CommonCommodityActivityPriceQueryDTO commonCommodityActivityPriceQueryDTO) {
        List<ActivitySpecDTO> activitySpecDTOS = commonCommodityActivityPriceQueryDTO.getActivitySpecDTOS();
        if (CollectionUtils.isEmpty(activitySpecDTOS)) {
            return;
        }
        LoginUserDTO loginUserDTO = commonCommodityActivityPriceQueryDTO.getLoginUserDTO();

        // 1、查询促销中台
        activitySpecDTOS = this.handlePmtActivitySpecList(commonCommodityActivityPriceQueryDTO);
        if (CollectionUtils.isEmpty(activitySpecDTOS)) {
            return;
        }
        // 2、查询其他活动
        PriceDiscountParamDTO priceDiscountReq = new PriceDiscountParamDTO();
        if (Objects.nonNull(loginUserDTO)) {
            BeanUtils.copyProperties(loginUserDTO, priceDiscountReq);
        }
        priceDiscountReq.setPageType(PageType.DETAIL_PAGE.getType());
        priceDiscountReq.setNoQueryPlusPrice(true);
        priceDiscountReq.setNoQueryCouponPrice(true);
        activitySpecDTOS = memberDiscountService.getMarketActivityDiscount(activitySpecDTOS, priceDiscountReq);
        if (CollectionUtils.isEmpty(activitySpecDTOS)) {
            return;
        }
        // 3、设置活动价
        this.setActivityPrice(activitySpecDTOS);

        commonCommodityActivityPriceQueryDTO.setActivitySpecDTOS(activitySpecDTOS);
    }

    @Override
    public StoreSpecRespDTO getDistributionSpecActivity(StoreSpecReqDTO specReqDTO, CommodityDTO commodityDTO) {
        ResponseBase<StoreSpecRespDTO> storeSpecRespDTOResponseBase = commodityClient.queryStoreSpec(specReqDTO);
        if (!storeSpecRespDTOResponseBase.checkSuccess() || Objects.isNull(storeSpecRespDTOResponseBase.getData())) {
            log.warn("/store-spec param = {}，res = {}", JSON.toJSONString(specReqDTO), JSON.toJSONString(storeSpecRespDTOResponseBase));
            return null;
        }
        StoreSpecRespDTO storeSpecResp = storeSpecRespDTOResponseBase.getData();
        if (CollectionUtils.isEmpty(storeSpecResp.getSpecList())) {
            return storeSpecResp;
        }

        // 过滤掉下架规格，取当前规格
        List<Spec> specList = storeSpecResp.getSpecList().stream().filter(spec -> YesOrNoType.YES.getCode().equals(spec.getStatus()) && specReqDTO.getSpecId().equals(spec.getId())).collect(toList());
        storeSpecResp.setSpecList(specList);
        if (CollectionUtils.isEmpty(specList)) {
            return storeSpecResp;
        }
        Integer supportMedicarePay = commodityDTO.getStoreDetail().getSupportMedicarePay();
        storeSpecResp.getSpecList().forEach(o -> {
            // 参考价赋0
            o.setMPrice(BigDecimal.ZERO);
            if (!YesOrNoType.YES.getCode().equals(supportMedicarePay)) {
                // 门店不支持医保支付，则置医保编码为空
                o.setMedicalInsuranceCode(null);
            }
        });

        if (BooleanUtils.isTrue(specReqDTO.getHasShare())) {
            // 处理共享库存
            this.handleDistributionSharedStock(specReqDTO.getMerCode(), storeSpecResp.getSpecList());
        }

        // 查询分销活动
        storeSpecResp.setActivitySpecDTOS(this.handlePmtDistributionActivitySpecList(specReqDTO, storeSpecResp));

        // 设置分销活动信息
        this.setDistributionInfo(storeSpecResp);
        return storeSpecResp;
    }

    @Override
    public void handleDistributionSpSpecActivity(StoreSpSpecReqDTO specReqDTO, StoreSpSpecRespDTO respDTO) {
        List<SpSpecDTO> specList = respDTO.getSpecList();
        if (CollectionUtils.isEmpty(specList)) {
            return;
        }

        // 查询分销活动
        this.handlePmtDistributionSpActivitySpecList(specReqDTO, respDTO);

        // 设置分销活动信息
        this.setSpDistributionInfo(respDTO);
    }

    @Override
    public void setPmtDistributionRule(PmtRuleDistribution pmtRuleDistribution, SdpConfigInfo sdpConfigInfo) {
        if (Objects.isNull(pmtRuleDistribution)) {
            return;
        }
        if (LocalConst.STATUS_TWO.equals(pmtRuleDistribution.getCommissionType())) {
            // 按固定金额
            pmtRuleDistribution.setCommission(pmtRuleDistribution.getOnePercentage());
            return;
        }
        // 按佣金比例
        if (YesOrNoType.YES.getCode().equals(pmtRuleDistribution.getCommissionMode())) {
            // 自定义
            pmtRuleDistribution.setCommission(pmtRuleDistribution.getDistributionPrice().multiply(pmtRuleDistribution.getOnePercentage()).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
        } else {
            // 系统默认
            if (Objects.isNull(sdpConfigInfo)) {
                // 查默认规则
                ResponseBase<SdpConfigInfo> sdpConfigInfoResponseBase = sdpConfigClient.queryConfig(pmtRuleDistribution.getMerCode());
                if (!sdpConfigInfoResponseBase.checkSuccess() || Objects.isNull(sdpConfigInfoResponseBase.getData())) {
                    log.warn("/sdp/sdp-config/{merCode}，res = {}", pmtRuleDistribution.getMerCode(), JSON.toJSONString(sdpConfigInfoResponseBase));
                    return;
                }
                sdpConfigInfo = sdpConfigInfoResponseBase.getData();
            }

            // 设置系统默认比例和预计收益
            pmtRuleDistribution.setStaffPercentage(sdpConfigInfo.getStaffPercentage());
            pmtRuleDistribution.setOnePercentage(BigDecimal.valueOf(sdpConfigInfo.getOnePercentage()));
            pmtRuleDistribution.setTwoPercentage(BigDecimal.valueOf(sdpConfigInfo.getTwoPercentage()));
            pmtRuleDistribution.setCommission(pmtRuleDistribution.getDistributionPrice().multiply(BigDecimal.valueOf(sdpConfigInfo.getOnePercentage())).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
        }
    }

    /**
     * @description TODO 设置分销活动信息
     * <AUTHOR>
     * @date 2023/9/4
     */
    private void setSpDistributionInfo(StoreSpSpecRespDTO storeSpSpecRespDTO) {
        if (CollectionUtils.isEmpty(storeSpSpecRespDTO.getActivitySpecDTOS())) {
            return;
        }
        Map<String, ActivitySpecDTO> activitySpecMap = storeSpSpecRespDTO.getActivitySpecDTOS().stream()
                .collect(Collectors.toMap(ActivitySpecDTO::getSpecId, a -> a, (k1, k2) -> k1));
        SpSpecDTO currentSpec = storeSpSpecRespDTO.getSpecList().get(0);
        ActivitySpecDTO activitySpecDTO = activitySpecMap.get(currentSpec.getId());
        if (Objects.isNull(activitySpecDTO) || CollectionUtils.isEmpty(activitySpecDTO.getActivityList())) {
            storeSpSpecRespDTO.setSpecList(Lists.newArrayList());
            return;
        }

        storeSpSpecRespDTO.getSpecList().stream().forEach(spec -> {
            ActivitySpecDTO activitySpec = activitySpecMap.get(spec.getId());
            if (Objects.isNull(activitySpec)) {
                return;
            }
            List<ActivityDTO> activityDTOS = activitySpec.getActivityList();
            if (CollectionUtils.isEmpty(activityDTOS)) {
                return;
            }
            // 设置分销各价格
            spec.setPrice(activityDTOS.get(0).getPmtDistributionRule().getDistributionPrice());
            spec.setMPrice(MathUtils.setSacle(activitySpec.getBeforePrice()));
            spec.setGoodsSalesPrice(MathUtils.setSacle(spec.getPrice()));
            spec.setGoodsOriginPrice(spec.getMPrice());

            ActivityDTO activityDTO = activityDTOS.get(0);
            spec.setActivityList(Lists.newArrayList(activityDTO));
            // 设置活动标签名称
            spec.setCommodityActivityTypeNames(Lists.newArrayList(StringUtils.hasLength(activityDTO.getLabelName()) ? activityDTO.getLabelName() : PromotionType.DISTRIBUTION.getName()));
            PmtRuleDistribution pmtRuleDistribution = activityDTO.getPmtDistributionRule();

            // 设置分销商品活动规则
            this.setPmtDistributionRule(pmtRuleDistribution, null);
            spec.getActivityList().get(0).setPmtDistributionRule(pmtRuleDistribution);
        });
    }

    /**
     * @param specReqDTO
     * @param respDTO
     * @description TODO 查询分销
     * <AUTHOR>
     * @date 2023/9/4
     */
    private void handlePmtDistributionSpActivitySpecList(StoreSpSpecReqDTO specReqDTO, StoreSpSpecRespDTO respDTO) {
        if (CollectionUtils.isEmpty(respDTO.getSpecList())) {
            return;
        }
        QuerySpecActivityReqDTO condition = new QuerySpecActivityReqDTO();
        List<ActivitySpecDTO> activitySpecList = new ArrayList<>();
        Map<String, SpSpecDTO> specIdSpecInfoMap = new HashMap<>();
        respDTO.getSpecList().forEach(o -> {
            ActivitySpecDTO activitySpecDTO = new ActivitySpecDTO();
            activitySpecDTO.setSpecId(o.getId());
            activitySpecDTO.setUserId(specReqDTO.getUserId());
            activitySpecDTO.setDiscount(o.getPrice());
            activitySpecDTO.setStoreId(specReqDTO.getSpCode());
            activitySpecDTO.setBeforePrice(o.getPrice());
            activitySpecDTO.setCount(LocalConst.DEFAULT_SPEC_COUNT);
            activitySpecDTO.setTypeId(specReqDTO.getTypeId());
            activitySpecDTO.setCommodityType(specReqDTO.getCommodityType());
            activitySpecDTO.setErpCode(o.getErpCode());
            activitySpecDTO.setGoodsOriginPrice(o.getMPrice());
            activitySpecDTO.setNoPresalePmtFlag(true);
            activitySpecList.add(activitySpecDTO);
            specIdSpecInfoMap.put(o.getId(), o);
        });
        condition.setSpecDTOS(activitySpecList);
        condition.setMerCode(specReqDTO.getMerCode());
        condition.setDetailPageType(LocalConst.STATUS_TWO);
        condition.setPageType(PageType.DETAIL_PAGE.getType());
        // 云仓商品分销
        condition.setDistributionType(DistributionOrgTypeEnum.DISTRIBUTION_ORG_SP.getCode());
        if (StringUtils.hasLength(specReqDTO.getUserId())) {
            condition.setUserId(specReqDTO.getUserId());
        }

        ResponseBase<List<ActivitySpecDTO>> activitySpecResp = promoteClient.searchForDetail(condition);
        if (!activitySpecResp.checkSuccess() || CollectionUtils.isEmpty(activitySpecResp.getData())) {
            log.error("/act/_searchForDetail param = {}，res = {}", JSON.toJSONString(condition), JSON.toJSONString(activitySpecResp));
            return;
        }

        activitySpecResp.getData().stream().forEach(o -> {
            if (!CollectionUtils.isEmpty(o.getActivityList())) {
                o.setActivityList(o.getActivityList().stream().distinct().filter(x -> PromotionType.DISTRIBUTION.equals(x.getPmtType())).collect(toList()));
            }
            if (Objects.isNull(o.getGoodsOriginPrice()) && StringUtils.hasLength(o.getSpecId())) {
                SpSpecDTO spec = specIdSpecInfoMap.get(o.getSpecId());
                if (Objects.nonNull(spec)) {
                    o.setGoodsOriginPrice(spec.getMPrice());
                }
            }
        });

        respDTO.setActivitySpecDTOS(activitySpecResp.getData());
    }

    /**
     * @return void
     * <AUTHOR>
     * @description //TODO 设置分销活动信息
     * @date 2023/9/4
     **/
    private void setDistributionInfo(StoreSpecRespDTO storeSpecResp) {
        if (CollectionUtils.isEmpty(storeSpecResp.getActivitySpecDTOS())) {
            return;
        }
        Map<String, ActivitySpecDTO> activitySpecMap = storeSpecResp.getActivitySpecDTOS().stream()
                .collect(Collectors.toMap(ActivitySpecDTO::getSpecId, a -> a, (k1, k2) -> k1));
        Spec currentSpec = storeSpecResp.getSpecList().get(0);
        ActivitySpecDTO activitySpecDTO = activitySpecMap.get(currentSpec.getId());
        if (Objects.isNull(activitySpecDTO) || CollectionUtils.isEmpty(activitySpecDTO.getActivityList())) {
            storeSpecResp.setSpecList(Lists.newArrayList());
            return;
        }

        storeSpecResp.getSpecList().stream().forEach(spec -> {
            ActivitySpecDTO activitySpec = activitySpecMap.get(spec.getId());
            if (Objects.isNull(activitySpec)) {
                return;
            }
            List<ActivityDTO> activityDTOS = activitySpec.getActivityList();
            if (CollectionUtils.isEmpty(activityDTOS)) {
                return;
            }
            // 设置分销各价格
            spec.setFlashPrice(activityDTOS.get(0).getPmtDistributionRule().getDistributionPrice());
            spec.setMPrice(MathUtils.setSacle(activitySpec.getBeforePrice()));
            spec.setGoodsSalesPrice(MathUtils.setSacle(spec.getPrice()));
            spec.setGoodsOriginPrice(spec.getMPrice());

            ActivityDTO activityDTO = activityDTOS.get(0);
            spec.setActivityList(Lists.newArrayList(activityDTO));
            // 设置活动标签名称
            spec.setCommodityActivityTypeNames(Lists.newArrayList(StringUtils.hasLength(activityDTO.getLabelName()) ? activityDTO.getLabelName() : PromotionType.DISTRIBUTION.getName()));
            PmtRuleDistribution pmtRuleDistribution = activityDTO.getPmtDistributionRule();

            // 设置分销商品活动规则
            this.setPmtDistributionRule(pmtRuleDistribution, null);
            spec.getActivityList().get(0).setPmtDistributionRule(pmtRuleDistribution);
        });
    }

    /**
     * @return java.util.List<cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO>
     * <AUTHOR>
     * @description //TODO 查询分销
     * @date 2023/9/3
     **/
    private List<ActivitySpecDTO> handlePmtDistributionActivitySpecList(StoreSpecReqDTO specReqDTO, StoreSpecRespDTO storeSpecResp) {
        if (CollectionUtils.isEmpty(storeSpecResp.getSpecList())) {
            return Lists.newArrayList();
        }
        QuerySpecActivityReqDTO condition = new QuerySpecActivityReqDTO();
        List<ActivitySpecDTO> activitySpecList = new ArrayList<>();
        Map<String, Spec> specIdSpecInfoMap = new HashMap<>();
        storeSpecResp.getSpecList().forEach(o -> {
            ActivitySpecDTO activitySpecDTO = new ActivitySpecDTO();
            activitySpecDTO.setSpecId(o.getId());
            activitySpecDTO.setUserId(specReqDTO.getUserId());
            activitySpecDTO.setDiscount(o.getPrice());
            activitySpecDTO.setStoreId(specReqDTO.getStoreId());
            activitySpecDTO.setBeforePrice(o.getPrice());
            activitySpecDTO.setCount(LocalConst.DEFAULT_SPEC_COUNT);
            activitySpecDTO.setTypeId(specReqDTO.getTypeId());
            activitySpecDTO.setCommodityType(specReqDTO.getCommodityType());
            activitySpecDTO.setErpCode(o.getErpCode());
            activitySpecDTO.setGoodsOriginPrice(o.getMPrice());
            activitySpecDTO.setNoPresalePmtFlag(true);
            activitySpecList.add(activitySpecDTO);
            specIdSpecInfoMap.put(o.getId(), o);
        });
        specReqDTO.setNeedQueryPmtFlag(false);
        if (!CommodityType.NORMAL.getCode().equals(specReqDTO.getCommodityType())) {
            return activitySpecList;
        }
        condition.setSpecDTOS(activitySpecList);
        condition.setMerCode(specReqDTO.getMerCode());
        condition.setDetailPageType(LocalConst.STATUS_TWO);
        condition.setPageType(PageType.DETAIL_PAGE.getType());
        // 普通商品分销
        condition.setDistributionType(DistributionOrgTypeEnum.DISTRIBUTION_ORG_STORE.getCode());
        if (StringUtils.hasLength(specReqDTO.getUserId())) {
            condition.setUserId(specReqDTO.getUserId());
        }

        ResponseBase<List<ActivitySpecDTO>> activitySpecResp = promoteClient.searchForDetail(condition);
        if (!activitySpecResp.checkSuccess() || CollectionUtils.isEmpty(activitySpecResp.getData())) {
            log.error("/act/_searchForDetail param = {}，res = {}", JSON.toJSONString(condition), JSON.toJSONString(activitySpecResp));
            return activitySpecList;
        }
        activitySpecResp.getData().stream().forEach(o -> {
            if (!CollectionUtils.isEmpty(o.getActivityList())) {
                o.setActivityList(o.getActivityList().stream().distinct().filter(x -> PromotionType.DISTRIBUTION.equals(x.getPmtType())).collect(toList()));
            }
            if (Objects.isNull(o.getGoodsOriginPrice()) && StringUtils.hasLength(o.getSpecId())) {
                Spec spec = specIdSpecInfoMap.get(o.getSpecId());
                if (Objects.nonNull(spec)) {
                    o.setGoodsOriginPrice(spec.getMPrice());
                }
            }
        });

        return activitySpecResp.getData();
    }

    /**
     * @param merCode, specList]
     * @return void
     * <AUTHOR>
     * @description //TODO 处理分销商品共享库存（目前仅旗舰走B2C店场景）
     * @date 2023/9/3
     **/
    private void handleDistributionSharedStock(String merCode, List<Spec> specList) {
        if (CollectionUtils.isEmpty(specList)) {
            return;
        }
        // 查询共享库存
        Map<Long, SharedStockQueryRespDTO> sharedStockQueryRespDTOMap = commonCommodityClientWrapperService.querySharedStock(
                SharedStockQueryReqDTO.builder().merCode(merCode)
                        .specIds(specList.stream().map(o -> Long.valueOf(o.getId())).collect(toList())).build()
        );
        if (CollectionUtils.isEmpty(sharedStockQueryRespDTOMap)) {
            return;
        }
        specList.stream().forEach(spec -> {
            // 旗舰店门店库存置0
            spec.setStock(0);
            SharedStockQueryRespDTO sharedStockQueryRespDTO = sharedStockQueryRespDTOMap.get(Long.valueOf(spec.getId()));
            if (Objects.isNull(sharedStockQueryRespDTO) || Objects.isNull(sharedStockQueryRespDTO.getStock())) {
                return;
            }
            // 设置共享仓库存
            spec.setSharedStock(sharedStockQueryRespDTO.getStock() < 0 ? 0 : sharedStockQueryRespDTO.getStock());
        });
    }

    /**
     * @param activitySpecDTOS
     * @description TODO 设置活动价
     * <AUTHOR>
     * @date 2023/6/20
     */
    private void setActivityPrice(List<ActivitySpecDTO> activitySpecDTOS) {
        if (CollectionUtils.isEmpty(activitySpecDTOS)) {
            return;
        }
        activitySpecDTOS.stream().forEach(activitySpecDTO -> this.handleAllActivityPrice(activitySpecDTO, activitySpecDTO.getActivityList()));
    }

    /**
     * @param activitySpecDTO
     * @param activityList
     * @description TODO 处理活动价标记信息
     * <AUTHOR>
     * @date 2023/6/21
     */
    private void handleAllActivityPrice(ActivitySpecDTO activitySpecDTO, List<ActivityDTO> activityList) {
        boolean presaleFlag = BooleanUtils.isTrue(activitySpecDTO.getPresaleFlag());
        boolean pmtFlag = BooleanUtils.isTrue(activitySpecDTO.getPmtFlag());
        boolean memberDayActivityFlag = BooleanUtils.isTrue(activitySpecDTO.getMemberDayActivityFlag());
        boolean hasVipDiscount = BooleanUtils.isTrue(activitySpecDTO.getHasVipDiscount());
        boolean memberPriceActivityFlag = BooleanUtils.isTrue(activitySpecDTO.getMemberPriceActivityFlag());
        boolean offlinePromotionFlag = BooleanUtils.isTrue(activitySpecDTO.getOfflinePromotionFlag());
        Integer priceTag = null;
        if (hasVipDiscount) {
            priceTag = PriceTagEnum.PRICE_TEN.getCode();
        }
        // 预售
        if (presaleFlag) {
            priceTag = PriceTagEnum.PRICE_SEVENTEEN.getCode();
        }
        // 促销
        if (pmtFlag) {
            if (CollectionUtils.isEmpty(activityList)) {
                return;
            }
            List<ActivityDTO> activityDTOS = activityList.stream().filter(o ->
                            !PromotionType.MEMBER_DAY.getType().equals(o.getPmtType().getType())
                                    && !PromotionType.COUPON_PRICE.getType().equals(o.getPmtType().getType()))
                    .distinct().collect(toList());
            if (CollectionUtils.isEmpty(activityDTOS)) {
                return;
            }
            List<ActivityDTO> flashActivityDTOS = activityDTOS.stream().filter(activityDTO ->
                    PromotionType.FLASH_PRICE.getCode().equals(activityDTO.getPmtType().getCode())).collect(toList());
            // 有限时优惠
            if (!CollectionUtils.isEmpty(flashActivityDTOS)) {
                priceTag = this.checkPmtPriceTag(flashActivityDTOS.get(0).getMemberType(), hasVipDiscount);
            }
        }
        // 不参与促销活动，参与超级会员日、会员日活动
        if (memberDayActivityFlag) {
            if (CollectionUtils.isEmpty(activityList)) {
                return;
            }
            List<ActivityDTO> activityDTOS = activityList.stream().filter(o ->
                            PromotionType.SUPER_MEMBER_DAY.getCode().equals(o.getPmtType().getCode())
                                    || PromotionType.MEMBER_DAY.getCode().equals(o.getPmtType().getCode()))
                    .distinct().collect(toList());
            if (CollectionUtils.isEmpty(activityDTOS)) {
                return;
            }
            if (PromotionType.SUPER_MEMBER_DAY.equals(activityDTOS.get(0).getPmtType())) {
                priceTag = PriceTagEnum.PRICE_FIVE.getCode();
                if (hasVipDiscount) {
                    priceTag = PriceTagEnum.PRICE_SIX.getCode();
                }
            }
            if (PromotionType.MEMBER_DAY.equals(activityDTOS.get(0).getPmtType())) {
                priceTag = PriceTagEnum.PRICE_SEVEN.getCode();
                if (hasVipDiscount) {
                    priceTag = PriceTagEnum.PRICE_EIGHT.getCode();
                }
            }
        }
        // 会员价活动
        if (memberPriceActivityFlag && !hasVipDiscount) {
            if (CollectionUtils.isEmpty(activityList)) {
                return;
            }
            priceTag = PriceTagEnum.PRICE_NINE.getCode();
        }
        // 线下促销价标记
        if (offlinePromotionFlag) {
            boolean noActivityFlag = !pmtFlag && !memberDayActivityFlag && !memberPriceActivityFlag;
            if (noActivityFlag) {
                priceTag = PriceTagEnum.PRICE_THIRTEEN.getCode();
                if (hasVipDiscount) {
                    priceTag = PriceTagEnum.PRICE_FOURTEEN.getCode();
                }
            }
        }
        this.setAllPrice(priceTag, activitySpecDTO);
        activitySpecDTO.setPriceTag(priceTag);
    }

    /**
     * @param priceTag
     * @param activitySpecDTO
     * @description TODO 根据价格标记设置价格信息
     * <AUTHOR>
     * @date 2023/6/21
     */
    private void setAllPrice(Integer priceTag, ActivitySpecDTO activitySpecDTO) {
        if (Objects.isNull(priceTag)) {
            activitySpecDTO.setGoodsSalesPrice(MathUtils.setSacle(activitySpecDTO.getBeforePrice()));
            return;
        }
        PriceTagEnum priceTagEnum = PriceTagEnum.getItem(priceTag);
        switch (priceTagEnum) {
            case PRICE_ONE:
            case PRICE_THREE:
            case PRICE_FIVE:
            case PRICE_SEVEN:
            case PRICE_NINE:
            case PRICE_SEVENTEEN:
                activitySpecDTO.setGoodsSalesPrice(MathUtils.setSacle(activitySpecDTO.getDiscount()));
                activitySpecDTO.setGoodsOriginPrice(MathUtils.setSacle(activitySpecDTO.getBeforePrice()));
                break;
            case PRICE_TWO:
            case PRICE_FOUR:
            case PRICE_SIX:
            case PRICE_EIGHT:
                activitySpecDTO.setGoodsSalesPrice(MathUtils.setSacle(activitySpecDTO.getDiscount()));
                activitySpecDTO.setOtherSalesPrice(MathUtils.setSacle(activitySpecDTO.getDiscount().multiply(activitySpecDTO.getDiscountRate().multiply(new BigDecimal(LocalConst.PMT_SPIKE_RATE)))));
                activitySpecDTO.setGoodsOriginPrice(MathUtils.setSacle(activitySpecDTO.getBeforePrice()));
                break;
            case PRICE_TEN:
            case PRICE_ELEVEN:
                activitySpecDTO.setGoodsSalesPrice(MathUtils.setSacle(activitySpecDTO.getDiscount()));
                activitySpecDTO.setOtherSalesPrice(MathUtils.setSacle(activitySpecDTO.getDiscount().multiply(activitySpecDTO.getDiscountRate().multiply(new BigDecimal(LocalConst.PMT_SPIKE_RATE)))));
                break;
            case PRICE_THIRTEEN:
                activitySpecDTO.setGoodsSalesPrice(MathUtils.setSacle(activitySpecDTO.getOfflinePromotionPrice()));
                activitySpecDTO.setGoodsOriginPrice(MathUtils.setSacle(activitySpecDTO.getBeforePrice()));
                break;
            case PRICE_FOURTEEN:
                activitySpecDTO.setGoodsSalesPrice(MathUtils.setSacle(activitySpecDTO.getOfflinePromotionPrice()));
                activitySpecDTO.setOtherSalesPrice(MathUtils.setSacle(activitySpecDTO.getOfflinePromotionPrice().multiply(activitySpecDTO.getDiscountRate().multiply(new BigDecimal(LocalConst.PMT_SPIKE_RATE)))));
                activitySpecDTO.setGoodsOriginPrice(MathUtils.setSacle(activitySpecDTO.getBeforePrice()));
                break;
            default:
                activitySpecDTO.setGoodsSalesPrice(MathUtils.setSacle(activitySpecDTO.getBeforePrice()));
                break;
        }
    }

    /**
     * @param commonCommodityActivityPriceQueryDTO
     * @return java.util.List<cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO>
     * @description TODO
     * <AUTHOR>
     * @date 2023/6/20
     */
    private List<ActivitySpecDTO> handlePmtActivitySpecList(CommonCommodityActivityPriceQueryDTO commonCommodityActivityPriceQueryDTO) {
        if (CollectionUtils.isEmpty(commonCommodityActivityPriceQueryDTO.getActivitySpecDTOS())) {
            return Lists.newArrayList();
        }
        LoginUserDTO loginUserDTO = commonCommodityActivityPriceQueryDTO.getLoginUserDTO();
        QuerySpecActivityReqDTO condition = new QuerySpecActivityReqDTO();
        condition.setMerCode(commonCommodityActivityPriceQueryDTO.getMerCode());
        condition.setUserId(Objects.nonNull(loginUserDTO) ? loginUserDTO.getUserId() : null);
        condition.setDetailPageType(LocalConst.STATUS_ONE);
        condition.setPageType(PageType.DETAIL_PAGE.getType());
        condition.setSpecDTOS(commonCommodityActivityPriceQueryDTO.getActivitySpecDTOS());
        ResponseBase<List<ActivitySpecDTO>> activitySpecResp = promoteClient.searchForDetail(condition);
        if (!activitySpecResp.checkSuccess() || CollectionUtils.isEmpty(activitySpecResp.getData())) {
            log.error("/act/_searchForDetail param = {}，res = {}", JSON.toJSONString(condition), JSON.toJSONString(activitySpecResp));
            return Lists.newArrayList();
        }
        // specId -> activitySpecDTO
        Map<String, ActivitySpecDTO> activitySpecDTOMap = commonCommodityActivityPriceQueryDTO.getActivitySpecDTOS().stream().collect(Collectors.toMap(ActivitySpecDTO::getSpecId, Function.identity(), (k1, k2) -> k1));

        activitySpecResp.getData().stream().forEach(o -> {
            // 不处理预售
            if (BooleanUtils.isTrue(o.getPresaleFlag())) {
                o.setPresaleFlag(false);
            }
            if (!CollectionUtils.isEmpty(o.getActivityList())) {
                o.setActivityList(o.getActivityList().stream().distinct().collect(toList()));
            }
            if (Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag() && CollectionUtils.isEmpty(o.getActivityList())) {
                // 预售活动标记为true但活动数据为空，设置标记为false
                o.setPresaleFlag(false);
            }
            if ((Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag()) && !CollectionUtils.isEmpty(o.getActivityList())) {
                // 预售活动标记为false但活动数据不为空，则清除预售活动数据
                o.setActivityList(o.getActivityList().stream().filter(x -> !PromotionType.PRESALE.equals(x.getPmtType()) && !PromotionType.DISTRIBUTION.equals(x.getPmtType())).collect(toList()));
            }

            if (!CollectionUtils.isEmpty(o.getActivityList())) {
                o.setActivityList(o.getActivityList().stream().filter(x -> PromotionType.checkPmtType(x.getPmtType())).collect(toList()));
            }

            if (Objects.isNull(o.getGoodsOriginPrice()) && StringUtils.hasLength(o.getSpecId())) {
                ActivitySpecDTO activitySpecDTO = activitySpecDTOMap.get(o.getSpecId());
                if (Objects.nonNull(activitySpecDTO)) {
                    o.setGoodsOriginPrice(activitySpecDTO.getGoodsOriginPrice());
                }
            }
        });
        return activitySpecResp.getData();
    }

    /**
     * 计算各种活动价
     *
     * @param activitySpec
     * @param activityList
     * @param spec
     */
    private void handleAllActivityPrice(ActivitySpecDTO activitySpec, List<ActivityDTO> activityList, Spec spec) {
        Boolean presaleFlag = activitySpec.getPresaleFlag() == null ? false : activitySpec.getPresaleFlag();
        Boolean pmtFlag = activitySpec.getPmtFlag() == null ? false : activitySpec.getPmtFlag();
        Boolean memberDayActivityFlag = activitySpec.getMemberDayActivityFlag() == null ? false : activitySpec.getMemberDayActivityFlag();
        Boolean hasVipDiscount = activitySpec.getHasVipDiscount() == null ? false : activitySpec.getHasVipDiscount() && activitySpec.getHasVipDiscount() != null;
        Boolean memberPriceActivityFlag = activitySpec.getMemberPriceActivityFlag() == null ? false : activitySpec.getMemberPriceActivityFlag();
        Boolean hasCouponPrice = activitySpec.getHasCouponPrice() == null ? false : activitySpec.getHasCouponPrice();
        Boolean offlinePromotionFlag = activitySpec.getOfflinePromotionFlag() == null ? false : activitySpec.getOfflinePromotionFlag();
        Integer priceTag = null;
        if (hasVipDiscount) {
            priceTag = PriceTagEnum.PRICE_TEN.getCode();
        }
        if (hasCouponPrice) {
            ProductDiscountDetailDTO couponPriceDetailDTO = activitySpec.getCouponPriceDetailDTO();
            spec.setCouponNotReceived(couponPriceDetailDTO.getCouponHasReceived() != null
                    && !couponPriceDetailDTO.getCouponHasReceived());
            spec.setCouponActivityId(couponPriceDetailDTO.getActivityId());
            spec.setCouponId(couponPriceDetailDTO.getCouponId());
            spec.setCouponSalesPrice(MathUtils.setSacle(couponPriceDetailDTO.getActualAmount()));
            priceTag = PriceTagEnum.PRICE_TWELVE.getCode();
            if (hasVipDiscount) {
                priceTag = PriceTagEnum.PRICE_ELEVEN.getCode();
            }
        }
        // 预售
        if (presaleFlag) {
            priceTag = PriceTagEnum.PRICE_SEVENTEEN.getCode();
        }
        // 促销
        if (pmtFlag) {
            if (CollectionUtils.isEmpty(activityList)) {
                return;
            }
            List<ActivityDTO> activityDTOS = activityList.stream().filter(o ->
                            !PromotionType.MEMBER_DAY.getType().equals(o.getPmtType().getType())
                                    && !PromotionType.COUPON_PRICE.getType().equals(o.getPmtType().getType()))
                    .distinct().collect(toList());
            if (CollectionUtils.isEmpty(activityDTOS)) {
                return;
            }
            List<ActivityDTO> flashActivityDTOS = activityDTOS.stream().filter(activityDTO ->
                    PromotionType.FLASH_PRICE.getCode().equals(activityDTO.getPmtType().getCode())).collect(toList());
            // 有限时优惠
            if (!CollectionUtils.isEmpty(flashActivityDTOS)) {
                priceTag = this.checkPmtPriceTag(flashActivityDTOS.get(0).getMemberType(), hasVipDiscount);
            }
        }
        // 不参与促销活动，参与超级会员日、会员日活动
        if (memberDayActivityFlag) {
            if (CollectionUtils.isEmpty(activityList)) {
                return;
            }
            List<ActivityDTO> activityDTOS = activityList.stream().filter(o ->
                            PromotionType.SUPER_MEMBER_DAY.getCode().equals(o.getPmtType().getCode())
                                    || PromotionType.MEMBER_DAY.getCode().equals(o.getPmtType().getCode()))
                    .distinct().collect(toList());
            if (CollectionUtils.isEmpty(activityDTOS)) {
                return;
            }
            if (PromotionType.SUPER_MEMBER_DAY.equals(activityDTOS.get(0).getPmtType())) {
                priceTag = PriceTagEnum.PRICE_FIVE.getCode();
                if (hasVipDiscount) {
                    priceTag = PriceTagEnum.PRICE_SIX.getCode();
                }
            }
            if (PromotionType.MEMBER_DAY.equals(activityDTOS.get(0).getPmtType())) {
                priceTag = PriceTagEnum.PRICE_SEVEN.getCode();
                if (hasVipDiscount) {
                    priceTag = PriceTagEnum.PRICE_EIGHT.getCode();
                }
            }
        }
        // 会员价活动
        if (memberPriceActivityFlag && !hasVipDiscount) {
            if (CollectionUtils.isEmpty(activityList)) {
                return;
            }
            priceTag = PriceTagEnum.PRICE_NINE.getCode();
        }
        // 线下促销价标记
        if (offlinePromotionFlag) {
            boolean noActivityFlag = !pmtFlag && !memberDayActivityFlag && !memberPriceActivityFlag;
            if (noActivityFlag) {
                priceTag = PriceTagEnum.PRICE_THIRTEEN.getCode();
                if (hasVipDiscount) {
                    priceTag = PriceTagEnum.PRICE_FOURTEEN.getCode();
                }
            }
        }
        MemberPriceUtils.priceHandlerSpec(priceTag, activitySpec, spec);
        spec.setPrice(spec.getGoodsSalesPrice());
        spec.setMPrice(spec.getGoodsOriginPrice());
        spec.setPriceTag(priceTag);
    }

    private Integer checkPmtPriceTag(Integer memberType, Boolean hasVipDiscount) {
        Integer priceTag;
        if (LocalConst.STATUS_ONE.equals(memberType)) {
            // 新人价
            priceTag = PriceTagEnum.PRICE_THREE.getCode();
            if (hasVipDiscount) {
                priceTag = PriceTagEnum.PRICE_FOUR.getCode();
            }
        } else {
            // 秒杀价
            priceTag = PriceTagEnum.PRICE_ONE.getCode();
            if (hasVipDiscount) {
                priceTag = PriceTagEnum.PRICE_TWO.getCode();
            }
        }
        return priceTag;
    }



    private void errorPriceNotice(PriceTagEnum priceTagEnum ,ActivitySpecDTO activitySpec) {
        try{
            if(Objects.nonNull(activitySpec.getOtherSalesPrice()) && activitySpec.getOtherSalesPrice().compareTo(activitySpec.getGoodsSalesPrice()) > 0) {
                log.info("价格标签{}, 价格对象：{}", priceTagEnum.getCode(), JSON.toJSONString(activitySpec));
                log.error("异常提示：付费会员价超过售价，及时排查");
            }
        }catch (Exception ex) {
            log.error("价格告警异常！！！");
        }

    }

    /**
     * 统计用户该组合商品已购数量
     *
     * @param userId
     * @param specId
     * @return
     */
    private Integer countNum(String userId, String specId) {
        CountMemberSpecReqDTO countMemberSpecReqDTO = new CountMemberSpecReqDTO();
        List<String> specIdsTemp = new ArrayList<>();
        specIdsTemp.add(specId);
        countMemberSpecReqDTO.setMemberId(userId);
        countMemberSpecReqDTO.setSpecIds(specIdsTemp);

        List<CountMemberSpecResDTO> list = orderActivityClient.countMemberSpec(countMemberSpecReqDTO).getData();
        if (!CollectionUtils.isEmpty(list)) {
            CountMemberSpecResDTO countMemberSpecResDTO = list.get(0);
            return countMemberSpecResDTO.getBuyNum();
        }
        return 0;
    }

    @Override
    public Integer getSpecCount(StoreSpecReqDTO dto) {
        StoreSpecRespDTO respDTO = commodityClient.queryStoreSpec(dto).getData();
        if (respDTO == null || CollectionUtils.isEmpty(respDTO.getSpecList())) {
            return 0;
        }
        List<Spec> specList = respDTO.getSpecList();
        //过滤规格
        specList = specList.stream()
                .filter(spec -> spec.getStatus() > 0)
                .collect(toList());
        //过滤后再次非空判断
        if (CollectionUtils.isEmpty(specList)) {
            return 0;
        }
        return specList.size();
    }

    /**
     * 原业务写在controller 迭代时间紧急，无奈不优化 抽service处理
     * 区分共享仓库存逻辑，如当前传入门店售罄，存在共享库存，包装成旗舰店信息返回
     *
     * @param specReqDTO
     * @return
     */
    @Override
    public StoreSpecRespDTO getSpecActivityForShared(StoreSpecReqDTO specReqDTO, CommodityDTO commodityDTO) {
        StoreSpecRespDTO storeSpecResp = commodityClient.queryStoreSpec(specReqDTO).getData();
        if (storeSpecResp == null || CollectionUtils.isEmpty(storeSpecResp.getSpecList())) {
            return storeSpecResp;
        }
        // 过滤掉下架规格
        List<Spec> specList = storeSpecResp.getSpecList().stream().filter(spec -> YesOrNoType.YES.getCode().equals(spec.getStatus())).collect(toList());
        storeSpecResp.setSpecList(specList);
        if (CollectionUtils.isEmpty(specList)) {
            return storeSpecResp;
        }
        if (BooleanUtils.isTrue(specReqDTO.getHasShare())) {
            // 处理共享库存
            this.handleSharedStock(specReqDTO.getMerCode(), storeSpecResp.getSpecList());
        }

        // 参考价赋0
        storeSpecResp.getSpecList().forEach(o -> o.setMPrice(BigDecimal.ZERO));
        Spec currentSpec = null;
        if (StringUtils.hasLength(specReqDTO.getSpecId())) {
            List<Spec> currentSpecList = specList.stream().filter(spec -> specReqDTO.getSpecId().equals(spec.getId())).collect(toList());
            if (!CollectionUtils.isEmpty(currentSpecList)) {
                currentSpec = currentSpecList.get(0);
                specReqDTO.setEffectiveCurrentSpecId(currentSpec.getId());
            }
        }
        Spec spec = Objects.isNull(currentSpec) ? specList.get(0) : currentSpec;

        /**促销查询前置*/
        List<ActivitySpecDTO> activitySpecList = this.handlePmtActivitySpecList(specReqDTO, storeSpecResp);
        if (CollectionUtils.isEmpty(activitySpecList)) {
            return storeSpecResp;
        }
        // 当前规格参与预售数据
        List<String> currentSpecIdPresaleList = activitySpecList.stream().filter(o -> spec.getId().equals(o.getSpecId())
                && Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag()).map(ActivitySpecDTO::getSpecId).collect(toList());
        // 判断当前规格是否参与预售，是-预售流程；否-非预售流程
        if (!CollectionUtils.isEmpty(currentSpecIdPresaleList)) {
            // 参与预售活动的所有规格数据
            List<ActivitySpecDTO> presaleActivitySpecList = activitySpecList.stream().filter(o -> Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag()).collect(toList());
            storeSpecResp.setActivitySpecDTOS(presaleActivitySpecList);
            List<String> presaleSpecIds = presaleActivitySpecList.stream().map(ActivitySpecDTO::getSpecId).collect(toList());
            // 重新赋值specList，仅返回参与预售的规格
            specList = specList.stream().filter(o -> presaleSpecIds.contains(o.getId())).collect(toList());
            // 参与预售时共享库存置为0
            specList.stream().forEach(o -> o.setSharedStock(0));
            storeSpecResp.setSpecList(specList);
            storeSpecResp.setPresaleSceneFlag(true);
            return this.getSpecActivity(specReqDTO, storeSpecResp);
        }

        storeSpecResp.setPresaleSceneFlag(false);
        // 非预售数据
        List<ActivitySpecDTO> notPresaleActivitySpecList = activitySpecList.stream().filter(o -> Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag()).collect(toList());
        if (CollectionUtils.isEmpty(notPresaleActivitySpecList)) {
            return storeSpecResp;
        }
        storeSpecResp.setActivitySpecDTOS(notPresaleActivitySpecList);
        List<String> notPresaleSpecIds = notPresaleActivitySpecList.stream().map(ActivitySpecDTO::getSpecId).collect(toList());
        // 重新赋值specList，仅返回不参与预售的规格
        specList = specList.stream().filter(o -> notPresaleSpecIds.contains(o.getId())).collect(toList());
        storeSpecResp.setSpecList(specList);

        // 初始化非预售场景，则要判断当前规格是否要走B2C
        // 上架&存在共享库存&&共享库存>门店库存，执行判断逻辑。如果判断不通过，把共享库存置为0
        boolean b2cSpecFlag = YesOrNoType.YES.getCode().equals(spec.getStatus()) && Objects.nonNull(spec.getSharedStock()) && spec.getSharedStock() > 0;
        if (!b2cSpecFlag) {
            // 不支持B2C
            return this.getSpecActivity(specReqDTO, storeSpecResp);
        }
        StoreListResDTO centerStore = this.checkSupportB2c(specReqDTO.getMerCode(), specReqDTO.getStoreId());
        // 如果校验支持B2C共享仓失败，共享库存返回0
        if (Objects.isNull(centerStore)) {
            // B2C门店校验不通过，所有规格共享库存置为0
            storeSpecResp.getSpecList().stream().forEach(o -> o.setSharedStock(0));
            return this.getSpecActivity(specReqDTO, storeSpecResp);
        }
        if (spec.getStock() > 0) {
            return this.getSpecActivity(specReqDTO, storeSpecResp);
        }
        // 当前门店售罄，按旗舰店返回
        // 处理当前规格共享仓库存逻辑
        Integer currentSpecSharedStock = spec.getSharedStock();
        if (CommodityType.NORMAL.getCode().equals(specReqDTO.getCommodityType())) {
            this.handleSpecSharedStock(specReqDTO.getMerCode(), specList);
            List<Spec> currentSpecs = specList.stream().filter(o -> spec.getId().equals(o.getId())).collect(toList());
            storeSpecResp.setSpecList(specList);
            if (!CollectionUtils.isEmpty(currentSpecs)) {
                if (Objects.isNull(currentSpecs.get(0).getSharedStock()) || currentSpecs.get(0).getSharedStock() <= 0) {
                    return this.getSpecActivity(specReqDTO, storeSpecResp);
                }
                currentSpecSharedStock = currentSpecs.get(0).getSharedStock();
            }
        }
        // 当前门店
        StoreListResDTO currentStoreDetail = commodityDTO.getStoreDetail();
        // 查询旗舰店规格
        specReqDTO.setStoreId(centerStore.getId());
        specReqDTO.setStoreCode(centerStore.getStCode());
        // StoreSpecRespDTO centerStoreSpecRespDTO = this.getSpecActivity(specReqDTO, null);
        StoreSpecRespDTO centerStoreSpecRespDTO = commodityClient.queryStoreSpec(specReqDTO).getData();
        // 旗舰店当前规格是否下架
        boolean centerStoreCurrentSpecOnlineStatus = false;
        if (Objects.isNull(centerStoreSpecRespDTO) || CollectionUtils.isEmpty(centerStoreSpecRespDTO.getSpecList())) {
            // 旗舰店所有规格下架
            centerStoreCurrentSpecOnlineStatus = true;
        } else {
            // 参考价赋0
            centerStoreSpecRespDTO.getSpecList().forEach(o -> o.setMPrice(BigDecimal.ZERO));
            centerStoreSpecRespDTO.setSpecList(centerStoreSpecRespDTO.getSpecList().stream().filter(o -> YesOrNoType.YES.getCode().equals(o.getStatus())).collect(toList()));
            List<Spec> centerStoreCurrentSpecs;
            if (StringUtils.hasLength(specReqDTO.getEffectiveCurrentSpecId())) {
                centerStoreCurrentSpecs = centerStoreSpecRespDTO.getSpecList().stream().filter(o -> specReqDTO.getEffectiveCurrentSpecId().equals(o.getId())).collect(toList());
            } else {
                centerStoreCurrentSpecs = centerStoreSpecRespDTO.getSpecList().stream().filter(o -> spec.getId().equals(o.getId())).collect(toList());
            }
            if (CollectionUtils.isEmpty(centerStoreCurrentSpecs)) {
                // 旗舰店当前规格下架
                centerStoreCurrentSpecOnlineStatus = true;
            } else {
                // 重新赋值当前规格共享库存
                Integer finalCurrentSpecSharedStock = currentSpecSharedStock;
                centerStoreSpecRespDTO.getSpecList().stream().forEach(o -> {
                    if (spec.getId().equals(o.getId())) {
                        o.setSharedStock(finalCurrentSpecSharedStock);
                    }
                });
            }
        }
        if (centerStoreCurrentSpecOnlineStatus) {
            specReqDTO.setStoreId(currentStoreDetail.getId());
            specReqDTO.setStoreCode(currentStoreDetail.getStCode());
            storeSpecResp.getSpecList().stream().forEach(o -> o.setSharedStock(0));
            return this.getSpecActivity(specReqDTO, storeSpecResp);
        }

        // 查询旗舰店规格促销信息
        List<ActivitySpecDTO> flagshipStoreActivitySpecList = this.handleB2CFlagshipStorePmtActivitySpecList(specReqDTO, centerStoreSpecRespDTO.getSpecList());
        if (CollectionUtils.isEmpty(flagshipStoreActivitySpecList)) {
            specReqDTO.setStoreId(currentStoreDetail.getId());
            specReqDTO.setStoreCode(currentStoreDetail.getStCode());
            return this.getSpecActivity(specReqDTO, storeSpecResp);
        }

        // 已转B2C，去除参与预售的规格
        List<ActivitySpecDTO> finalFlagshipStoreActivitySpecList = Lists.newArrayList();
        List<String> finalFlagshipStoreSpecIds = Lists.newArrayList();
        flagshipStoreActivitySpecList.stream().forEach(o -> {
            if (Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag()) {
                finalFlagshipStoreActivitySpecList.add(o);
                finalFlagshipStoreSpecIds.add(o.getSpecId());
            }
        });
        centerStoreSpecRespDTO.setActivitySpecDTOS(finalFlagshipStoreActivitySpecList);
        centerStoreSpecRespDTO.setSpecList(centerStoreSpecRespDTO.getSpecList().stream().filter(o -> finalFlagshipStoreSpecIds.contains(o.getId())).collect(toList()));
        commodityDTO.setStoreDetail(centerStore);
        return this.getSpecActivity(specReqDTO, centerStoreSpecRespDTO);
    }

    /**
     * 处理B2C旗舰店规格的活动
     *
     * @param specReqDTO
     * @param originalSpecList
     * @return
     */
    private List<ActivitySpecDTO> handleB2CFlagshipStorePmtActivitySpecList(StoreSpecReqDTO specReqDTO, List<Spec> originalSpecList) {
        if (CollectionUtils.isEmpty(originalSpecList)) {
            return Lists.newArrayList();
        }
        QuerySpecActivityReqDTO condition = new QuerySpecActivityReqDTO();
        List<ActivitySpecDTO> activitySpecList = new ArrayList<>();
        Map<String, Spec> specIdSpecInfoMap = new HashMap<>();
        originalSpecList.forEach(o -> {
            //todo 会员价2516 查询营销使用最低价
            ActivitySpecDTO activitySpecDTO = new ActivitySpecDTO();
            activitySpecDTO.setSpecId(o.getId());
            activitySpecDTO.setUserId(specReqDTO.getUserId());
            activitySpecDTO.setVipPrice(o.getVipPrice());
            activitySpecDTO.setDiscount(o.getPrice());
            activitySpecDTO.setBeforePrice(o.getPrice());
            if(o.isUseVipPrice(specReqDTO.getPaidMemberFlag())) {
                activitySpecDTO.setDiscount(o.getVipPrice());
                activitySpecDTO.setBeforePrice(o.getVipPrice());
            }
            activitySpecDTO.setStoreId(specReqDTO.getStoreId());
            activitySpecDTO.setCount(LocalConst.DEFAULT_SPEC_COUNT);
            activitySpecDTO.setTypeId(specReqDTO.getTypeId());
            activitySpecDTO.setCommodityType(specReqDTO.getCommodityType());
            activitySpecDTO.setErpCode(o.getErpCode());
            activitySpecDTO.setGoodsOriginPrice(o.getMPrice());
            activitySpecDTO.setOfflinePromotionPriceInfo(o.getPromotionPrice(), o.getActivityStartTime(), o.getActivityEndTime());
            if (o.getId().equals(specReqDTO.getEffectiveCurrentSpecId())) {
                // 当前规格不查询预售活动
                // 当前规格是共享库存B2C场景（1、初始化自动切换到B2C，2、页面手动切换到B2C），则设置当前规格不查询预售活动，不管该规格是否参与预售都要展示
                activitySpecDTO.setNoPresalePmtFlag(true);
            }
            activitySpecList.add(activitySpecDTO);
            specIdSpecInfoMap.put(o.getId(), o);
        });
        specReqDTO.setNeedQueryPmtFlag(false);
        if (!CommodityType.NORMAL.getCode().equals(specReqDTO.getCommodityType())) {
            return activitySpecList;
        }
        condition.setSpecDTOS(activitySpecList);
        condition.setMerCode(specReqDTO.getMerCode());
        condition.setDetailPageType(LocalConst.STATUS_ONE);
        condition.setPageType(PageType.DETAIL_PAGE.getType());
        if (SourceChannelType.DISTRIBUTION.getCode().equals(specReqDTO.getSourceChannelType())) {
            condition.setDetailPageType(LocalConst.STATUS_TWO);
        }
        if (StringUtils.hasLength(specReqDTO.getUserId())) {
            condition.setUserId(specReqDTO.getUserId());
        }

        // 限制个别商户、个别商品 查询促销活动
        if (XZ_MERCODES.contains(specReqDTO.getMerCode()) && XZ_COMMODITY_IDS.contains(String.valueOf(specReqDTO.getCommodityId()))) {
            if (XZ_QUERY_PROMOTION) {
                return condition.getSpecDTOS();
            }
        }
        ResponseBase<List<ActivitySpecDTO>> activitySpecResp = promoteClient.searchForDetail(condition);
        if (!activitySpecResp.checkSuccess() || CollectionUtils.isEmpty(activitySpecResp.getData())) {
            log.error("/act/_searchForDetail param = {}，res = {}", JSON.toJSONString(condition), JSON.toJSONString(activitySpecResp));
            this.flagVip(specReqDTO.getPaidMemberFlag(), activitySpecList, specIdSpecInfoMap);
            return activitySpecList;
        }
        activitySpecResp.getData().stream().forEach(o -> {
            if (!CollectionUtils.isEmpty(o.getActivityList())) {
                o.setActivityList(o.getActivityList().stream().distinct().collect(toList()));
            }
            if (Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag() && CollectionUtils.isEmpty(o.getActivityList())) {
                // 预售活动标记为true但活动数据为空，设置标记为false
                o.setPresaleFlag(false);
            }
            if ((Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag()) && !CollectionUtils.isEmpty(o.getActivityList())) {
                // 预售活动标记为false但活动数据不为空，则清除预售活动数据
                o.setActivityList(o.getActivityList().stream().filter(x -> !PromotionType.PRESALE.equals(x.getPmtType())).collect(toList()));
            }
            if (Objects.isNull(o.getGoodsOriginPrice()) && StringUtils.hasLength(o.getSpecId())) {
                Spec spec = specIdSpecInfoMap.get(o.getSpecId());
                if (Objects.nonNull(spec)) {
                    o.setGoodsOriginPrice(spec.getMPrice());
                }
            }
        });
        //todo 会员价2516 查询营销使用最低价
        this.flagVip(specReqDTO.getPaidMemberFlag(), activitySpecResp.getData(), specIdSpecInfoMap);
        return activitySpecResp.getData();
    }

    private void flagVip(final Boolean isPaidMember, final List<ActivitySpecDTO> activitySpecDTOList, final Map<String, Spec> specIdSpecInfoMap) {

            //1、使用VIP价条件
            //   1、付费会员
            //   2、会员价低于门店价
            //   3、无限时特惠
        activitySpecDTOList.forEach(activitySpecDTO -> {
            Spec spec = specIdSpecInfoMap.get(activitySpecDTO.getSpecId());
            if(Objects.nonNull(spec) ) {
                activitySpecDTO.setVipPrice(spec.getVipPrice());
                if(isPaidMember && activitySpecDTO.isUseVip()) {
                    activitySpecDTO.setFlagUserCommodityVipPrice(true);
                    activitySpecDTO.setBeforePrice(spec.getPrice());
                }
            }

        });
    }

    /**
     * 查询促销
     *
     * @param specReqDTO
     * @param storeSpecRespDTO
     * @return
     */
    private List<ActivitySpecDTO> handlePmtActivitySpecList(StoreSpecReqDTO specReqDTO, StoreSpecRespDTO storeSpecRespDTO) {
        if (CollectionUtils.isEmpty(storeSpecRespDTO.getSpecList())) {
            return Lists.newArrayList();
        }
        QuerySpecActivityReqDTO condition = new QuerySpecActivityReqDTO();
        List<ActivitySpecDTO> activitySpecList = new ArrayList<>();
        Map<String, Spec> specIdSpecInfoMap = new HashMap<>();
        storeSpecRespDTO.getSpecList().forEach(o -> {
            //todo 会员价2516 查询营销使用最低价
            ActivitySpecDTO activitySpecDTO = new ActivitySpecDTO();
            activitySpecDTO.setSpecId(o.getId());
            activitySpecDTO.setUserId(specReqDTO.getUserId());
            activitySpecDTO.setVipPrice(o.getVipPrice());
            activitySpecDTO.setDiscount(o.getPrice());
            activitySpecDTO.setBeforePrice(o.getPrice());
            if(o.isUseVipPrice(specReqDTO.getPaidMemberFlag())) {
                activitySpecDTO.setDiscount(o.getVipPrice());
                activitySpecDTO.setBeforePrice(o.getVipPrice());
            }
            activitySpecDTO.setStoreId(specReqDTO.getStoreId());
            activitySpecDTO.setCount(LocalConst.DEFAULT_SPEC_COUNT);
            activitySpecDTO.setTypeId(specReqDTO.getTypeId());
            activitySpecDTO.setCommodityType(specReqDTO.getCommodityType());
            activitySpecDTO.setErpCode(o.getErpCode());
            activitySpecDTO.setGoodsOriginPrice(o.getMPrice());
            activitySpecDTO.setOfflinePromotionPriceInfo(o.getPromotionPrice(), o.getActivityStartTime(), o.getActivityEndTime());
            boolean sharedStockB2cSceneFlag = StringUtils.hasLength(specReqDTO.getSpecId()) && specReqDTO.getSpecId().equals(o.getId())
                    && Objects.nonNull(specReqDTO.getSharedStockB2cSceneFlag()) && specReqDTO.getSharedStockB2cSceneFlag();
            // 当前规格是共享库存B2C场景（1、初始化自动切换到B2C，2、页面手动切换到B2C），则设置当前规格不查询预售活动，不管该规格是否参与预售都要展示
            activitySpecDTO.setNoPresalePmtFlag(sharedStockB2cSceneFlag);
            activitySpecList.add(activitySpecDTO);
            specIdSpecInfoMap.put(o.getId(), o);
        });
        specReqDTO.setNeedQueryPmtFlag(false);
        if (!CommodityType.NORMAL.getCode().equals(specReqDTO.getCommodityType())) {
            return activitySpecList;
        }
        condition.setSpecDTOS(activitySpecList);
        condition.setMerCode(specReqDTO.getMerCode());
        condition.setDetailPageType(LocalConst.STATUS_ONE);
        condition.setPageType(PageType.DETAIL_PAGE.getType());

        if (SourceChannelType.DISTRIBUTION.getCode().equals(specReqDTO.getSourceChannelType())) {
            condition.setDetailPageType(LocalConst.STATUS_TWO);
        }
        if (StringUtils.hasLength(specReqDTO.getUserId())) {
            condition.setUserId(specReqDTO.getUserId());
        }

        // 限制个别商户、个别商品 查询促销活动
        if (XZ_MERCODES.contains(specReqDTO.getMerCode()) && XZ_COMMODITY_IDS.contains(String.valueOf(specReqDTO.getCommodityId()))) {
            if (XZ_QUERY_PROMOTION) {
                return condition.getSpecDTOS();
            }
        }
        ResponseBase<List<ActivitySpecDTO>> activitySpecResp = promoteClient.searchForDetail(condition);
        if (!activitySpecResp.checkSuccess() || CollectionUtils.isEmpty(activitySpecResp.getData())) {
            log.error("/act/_searchForDetail param = {}，res = {}", JSON.toJSONString(condition), JSON.toJSONString(activitySpecResp));
            //todo 会员价2516 查询营销使用最低价
            this.flagVip(specReqDTO.getPaidMemberFlag(), activitySpecList, specIdSpecInfoMap);
            return activitySpecList;
        }
        activitySpecResp.getData().forEach(o -> {
            if (!CollectionUtils.isEmpty(o.getActivityList())) {
                o.setActivityList(o.getActivityList().stream().distinct().collect(toList()));
            }
            if (Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag() && CollectionUtils.isEmpty(o.getActivityList())) {
                // 预售活动标记为true但活动数据为空，设置标记为false
                o.setPresaleFlag(false);
            }
            if ((Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag()) && !CollectionUtils.isEmpty(o.getActivityList())) {
                // 预售活动标记为false但活动数据不为空，则清除预售活动数据
                o.setActivityList(o.getActivityList().stream().filter(x -> !PromotionType.PRESALE.equals(x.getPmtType())).collect(toList()));
            }
            if (Objects.isNull(o.getGoodsOriginPrice()) && StringUtils.hasLength(o.getSpecId())) {
                Spec spec = specIdSpecInfoMap.get(o.getSpecId());
                if (Objects.nonNull(spec)) {
                    o.setGoodsOriginPrice(spec.getMPrice());
                }
            }

        });
        //todo 会员价2516 查询营销使用最低价
        this.flagVip(specReqDTO.getPaidMemberFlag(), activitySpecResp.getData(), specIdSpecInfoMap);
        if (Objects.nonNull(specReqDTO.getInitPage()) && specReqDTO.getInitPage()) {
            return activitySpecResp.getData();
        }
        // 非初始化即切换规格时，根据不同场景过滤规格
        String currentSpecId = specReqDTO.getSpecId();
        if (Objects.isNull(specReqDTO.getPresaleSceneFlag())) {
            // 表示非商详页
            if (StringUtils.hasLength(specReqDTO.getSpecId())) {
                // 当前规格参与预售数据
                List<String> currentSpecIdPresaleList = activitySpecResp.getData().stream().filter(o -> currentSpecId.equals(o.getSpecId())
                        && Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag()).map(ActivitySpecDTO::getSpecId).collect(toList());
                if (CollectionUtils.isEmpty(currentSpecIdPresaleList)) {
                    return activitySpecResp.getData().stream().filter(o -> o.getSpecId().equals(currentSpecId) || (Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag())).collect(toList());
                } else {
                    return activitySpecResp.getData().stream().filter(o -> Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag()).collect(toList());
                }
            }
            return activitySpecResp.getData();
        }

        // 处理商详页
        // 是否为预售场景
        boolean presaleSceneFlag = specReqDTO.getPresaleSceneFlag();
        if (presaleSceneFlag) {
            // 仅返回参与预售规格
            return activitySpecResp.getData().stream().filter(o -> Objects.nonNull(o.getPresaleFlag()) && o.getPresaleFlag()).collect(toList());
        }
        // 非预售场景
        // 1、切换规格-第一次弹起（获取所有有效规格），第二次调用查询指定规格数据
        if (LocalConst.STATUS_ONE.equals(specReqDTO.getChangeSpecScene())) {
            // 第一次弹起场景，直接返回sku集合
            if (StringUtils.hasLength(currentSpecId)) {
                return activitySpecResp.getData().stream().filter(o -> o.getSpecId().equals(currentSpecId) || (Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag())).collect(toList());
            } else {
                return activitySpecResp.getData();
            }
        } else if (!StringUtils.hasLength(specReqDTO.getSpecId()) && CollectionUtils.isEmpty(specReqDTO.getSpecSkuList())) {
            // OBC门店首页、购物车第一次弹起：返回不参与预售的规格
            return activitySpecResp.getData().stream().filter(o -> Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag()).collect(toList());
        } else if (StringUtils.hasLength(specReqDTO.getSpecId())) {
            // 第二次访问，确定了规格（第二次查询规格信息或切换另一个规格）
            // 当前规格参与了预售但不查活动数据
            return activitySpecResp.getData().stream().filter(o -> o.getSpecId().equals(currentSpecId) || (Objects.isNull(o.getPresaleFlag()) || !o.getPresaleFlag())).collect(toList());
        }
        return activitySpecResp.getData();
    }

    /**
     * 切换规格接口，如果存在共享库存 且共享库存大于门店库存，需要返回共享仓
     *
     * @param respWrapperDTO
     * @param specReqDTO
     */
    @Override
    public void setSharedSpec(StoreSpecRespWrapperDTO respWrapperDTO, StoreSpecReqDTO specReqDTO) {
        Spec defaultSpec = respWrapperDTO.getDefaultSpec();
        if (defaultSpec == null) {
            return;
        }
        if (Objects.isNull(specReqDTO.getPresaleSceneFlag())) {
            // 表示非商详页
            if (!StringUtils.hasLength(specReqDTO.getSpecId()) && CollectionUtils.isEmpty(specReqDTO.getSpecSkuList())) {
                // 第一次弹起
                return;
            }
        }
        if (Objects.nonNull(specReqDTO.getPresaleSceneFlag()) && specReqDTO.getPresaleSceneFlag()) {
            // 预售场景
            return;
        }
        if (StringUtils.isEmpty(specReqDTO.getSpecId()) && CollectionUtils.isEmpty(specReqDTO.getSpecSkuList())) {
            // 非商详页切换规格第一次弹起
            return;
        }
        if (LocalConst.STATUS_ONE.equals(specReqDTO.getChangeSpecScene())) {
            // 商详页切换规格第一次弹起
            return;
        }

        // 预售场景走预售
        boolean presaleFlag = PriceTagEnum.PRICE_SEVENTEEN.getCode().equals(defaultSpec.getPriceTag());
        if (presaleFlag) {
            return;
        }

        if (Objects.isNull(defaultSpec.getSharedStock()) || defaultSpec.getSharedStock() <= defaultSpec.getStock()) {
            return;
        }
        StoreListResDTO centerStore = checkSupportB2c(specReqDTO.getMerCode(), specReqDTO.getStoreId());
        if (centerStore == null) {
            defaultSpec.setSharedStock(0);
            return;
        }

        // 处理当前规格共享仓库存逻辑
        List<Spec> specList = new ArrayList<>();
        specList.add(defaultSpec);
        this.handleSpecSharedStock(specReqDTO.getMerCode(), specList);
        defaultSpec = specList.get(0);
        respWrapperDTO.setDefaultSpec(defaultSpec);
        if (Objects.isNull(defaultSpec.getSharedStock()) || defaultSpec.getSharedStock() <= defaultSpec.getStock()) {
            return;
        }

        // 旗舰店即当前门店
        if (centerStore.getId().equals(specReqDTO.getStoreId())) {
            return;
        }

        // 赋值共享仓规格信息
        specReqDTO.setStoreId(centerStore.getId());
        specReqDTO.setStoreCode(centerStore.getStCode());
        // 查询B2C旗舰店规格信息不查预售
        specReqDTO.setSharedStockB2cSceneFlag(true);
        StoreSpecRespDTO centerStoreSpecRespDTO = this.getSpecActivity(specReqDTO, null);
        // 旗舰店当前规格是否下架
        boolean centerStoreCurrentSpecOnlineStatus = false;
        Spec centerStoreSpec = null;
        if (Objects.isNull(centerStoreSpecRespDTO) || CollectionUtils.isEmpty(centerStoreSpecRespDTO.getSpecList())) {
            // 旗舰店所有规格下架
            centerStoreCurrentSpecOnlineStatus = true;
        } else {
            Spec finalDefaultSpec = defaultSpec;
            List<Spec> centerStoreCurrentSpecs = centerStoreSpecRespDTO.getSpecList().stream().filter(o -> finalDefaultSpec.getId().equals(o.getId())).collect(toList());
            if (CollectionUtils.isEmpty(centerStoreCurrentSpecs)) {
                // 旗舰店当前规格下架
                centerStoreCurrentSpecOnlineStatus = true;
            } else {
                centerStoreSpec = centerStoreCurrentSpecs.get(0);
                centerStoreSpec.setSharedStock(defaultSpec.getSharedStock());
            }
        }
        if (centerStoreCurrentSpecOnlineStatus) {
            // 旗舰店当前规格下架 共享库存设为0
            defaultSpec.setSharedStock(0);
            return;
        }
        respWrapperDTO.setSharedSpec(centerStoreSpec);
        centerStore.setCenter(Boolean.TRUE);
        respWrapperDTO.setSharedStore(centerStore);
    }

    /**
     * 校验当前门店是否支持B2C共享仓
     * 若是，返回旗舰店信息
     *
     * @param merCode
     * @param storeId
     * @return
     */
    private StoreListResDTO checkSupportB2c(String merCode, String storeId) {
        //1、商户是否开启OBC
        boolean isOpenObc = merchantSwitchService.queryModelIsOpen(merCode);
        if (isOpenObc) {
            //2、当前门店是否支持B2C
            StoreResDTO store = storeClient.queryStore(storeId).getData();
            if (store != null && (ServiceMode.B2C.getCode().equals(store.getServiceMode()) || ServiceMode.O2OB2C.getCode().equals(store.getServiceMode()))) {
                //3、是否配置旗舰店&支持B2C
                StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
                storeListReqDTO.setMerCode(merCode);
                StoreListResDTO centerStore = ydjStoreService.queryCenterStore(storeListReqDTO, true);
                if (centerStore != null && (ServiceMode.B2C.getCode().equals(centerStore.getServiceMode()) || ServiceMode.O2OB2C.getCode().equals(centerStore.getServiceMode()))) {
                    centerStore.setCenter(true);
                    return centerStore;
                }
            }
        }
        return null;
    }

    /**
     * 校验组合商品限购
     *
     * @param merCode
     * @param userId
     * @param specList
     */
    private void checkAssemblyLimitNum(String merCode, String userId, List<Spec> specList) {
        List<Long> specIds = specList.stream().map(o -> Long.valueOf(o.getId())).collect(toList());
        SpecLimitQueryDTO specLimitQueryDTO = new SpecLimitQueryDTO();
        specLimitQueryDTO.setMerCode(merCode);
        specLimitQueryDTO.setSpecIds(specIds);
        //查询商品限制信息
        ResponseBase<List<CommoditySpecLimit>> specLimitResp = commoditySearchClient.querySpecByCode(specLimitQueryDTO);
        if (specLimitResp == null || !specLimitResp.checkSuccess()) {
            throw WarnException.builder().code(ErrorType.QUERY_COMMODITY_LIMIT_ERROR.getCode()).tipMessage(ErrorType.QUERY_COMMODITY_LIMIT_ERROR.getMsg()).build();
        }
        Map<Long, CommoditySpecLimit> specLimitMap = specLimitResp.getData().stream().collect(Collectors.toMap(CommoditySpecLimit::getSpecId, v -> v, (k1, k2) -> k1));
        specList.forEach(spec -> {
            CommoditySpecLimit commoditySpecLimit = specLimitMap.get(Long.valueOf(spec.getId()));
            //如果/spec-limit/_search未返回限购信息，表示未设置周期限购，store-spec的limitNum为每单限购，0/null表示不限购
            if (commoditySpecLimit == null) {
                return;
            }
            //组合商品，设置为每人限购
            if (commoditySpecLimit.getLimitType() != null && commoditySpecLimit.getLimitType() == 5) {
                String specId = spec.getId();
                //统计用户该组合商品已购数量
                Integer buyNum = countNum(userId, specId);
                int leftNum = commoditySpecLimit.getLimitNum() - buyNum;
                spec.setLimitNum(leftNum);
                if (leftNum <= 0) {
                    spec.setLimitZero(0);
                }
            }
        });
    }

    /**
     * 根据已选择的sku键值对过滤规格
     *
     * @param specSkuList
     */
    private List<Spec> filterSpec(List<Spec> specList, List<CommoditySpecReqSku> specSkuList) {
        specList = specList.stream().filter(spec -> {
            //规格属性键值对包含查询条件键值对，则符合条件，加入结果集
            List<CommoditySpecReqSku> temp = Lists.newArrayList();
            try {
                temp = BeanUtil.copyList(spec.getSpecSkuList(), CommoditySpecReqSku.class);
            } catch (IllegalAccessException e) {
                log.error(e.getMessage(), e);
            } catch (InstantiationException e) {
                log.error(e.getMessage(), e);
            }
            return temp.containsAll(specSkuList);
        }).collect(toList());
        return specList;
    }

    /**
     * 返回当前规格是否可领取优惠券
     *
     * @param spec
     * @param activityList
     */
    private void setIsCouponsTake(Spec spec, List<ActivityDTO> activityList) {
        if (CollectionUtils.isEmpty(activityList)) {
            return;
        }
        // 根据促销活动配置，返回是否可领取优惠券 默认0
        // 只要有促销活动可用优惠券，就返回可领取
        int isCouponsTake = 0;
        List<ActivityDTO> collect = activityList.stream().filter(o -> !PromotionType.GROUP.getCode().equals(o.getPmtType().getCode())
                && !PromotionType.MEMBER_PLUS.getCode().equals(o.getPmtType().getCode())
                && !PromotionType.COUPON_PRICE.getCode().equals(o.getPmtType().getCode())
                && !PromotionType.PMT_PRICE_PLAN.getCode().equals(o.getPmtType().getCode())).collect(toList());
        if (CollectionUtils.isEmpty(collect)) {
            spec.setIsCouponsTake(LocalConst.STATUS_ONE);
            return;
        }
        // 处理预售
        if (PriceTagEnum.PRICE_SEVENTEEN.getCode().equals(spec.getPriceTag())) {
            collect = collect.stream().filter(o -> PromotionType.PRESALE.getCode().equals(o.getPmtType().getCode())).collect(toList());
            if (CollectionUtils.isEmpty(collect)) {
                spec.setIsCouponsTake(LocalConst.STATUS_ONE);
                return;
            }
        }
        for (ActivityDTO activityDTO : collect) {
            // 拼团商详页区分了接口，这里是参与了拼团活动的商品普通商详页，放行领券
            if (PromotionType.GROUP.getCode().equals(activityDTO.getPmtType().getCode())
                    || PromotionType.MEMBER_PLUS.getCode().equals(activityDTO.getPmtType().getCode())
                    || PromotionType.COUPON_PRICE.getCode().equals(activityDTO.getPmtType().getCode())
                    || PromotionType.PMT_PRICE_PLAN.getCode().equals(activityDTO.getPmtType().getCode())) {
                isCouponsTake = LocalConst.STATUS_ONE;
                continue;
            }
            // NULL或''表示不可使用优惠
            if (StringUtils.isEmpty(activityDTO.getUserCoupons())) {
                // 此活动不可使用优惠券
                isCouponsTake = 0;
                continue;
            }
            // 配置可使用优惠券
            if (!StringUtils.isEmpty(activityDTO.getUserCoupons())) {
                List<String> userCouponList = Arrays.asList(activityDTO.getUserCoupons().split(LocalConst.COMMA_SPLIT));
                if (userCouponList.contains(String.valueOf(UserCouponType.COUPON.getCode()))) {
                    isCouponsTake = LocalConst.STATUS_ONE;
                    break;
                }
            }
        }
        if (LocalConst.STATUS_ZERO.equals(isCouponsTake)) {
            // 表示不能领券，则判断是否计算了券后价
            if (PriceTagEnum.PRICE_ELEVEN.getCode().equals(spec.getPriceTag())) {
                spec.setPriceTag(PriceTagEnum.PRICE_TEN.getCode());
            }
            if (PriceTagEnum.PRICE_TWELVE.getCode().equals(spec.getPriceTag())) {
                spec.setPriceTag(null);
            }
            spec.setCouponNotReceived(null);
            spec.setCouponSalesPrice(null);
            spec.setCouponActivityId(null);
            spec.setCouponId(null);
        }
        spec.setIsCouponsTake(isCouponsTake);
    }
}
