package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.config.CacheValidTimePeriodConfig;
import cn.hydee.ydjia.merchantcustomer.domain.NearByCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.StoreSalesListReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.collection.UserCollectionDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityImgDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.Spec;
import cn.hydee.ydjia.merchantcustomer.dto.resp.StoreSpecRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.enums.CacheEnum;
import cn.hydee.ydjia.merchantcustomer.enums.CommodityType;
import cn.hydee.ydjia.merchantcustomer.enums.PresType;
import cn.hydee.ydjia.merchantcustomer.feign.*;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.domain.AssembleCommodityRelate;
import cn.hydee.ydjia.merchantcustomer.feign.domain.SysStoreFunction;
import cn.hydee.ydjia.merchantcustomer.feign.domain.YdjStore;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.*;
import cn.hydee.ydjia.merchantcustomer.feign.prescription.MessageData;
import cn.hydee.ydjia.merchantcustomer.feign.service.MerchantSwitchService;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonCommodityClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonStoreClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.feign.util.MathUtils;
import cn.hydee.ydjia.merchantcustomer.obc.service.ObcYdjStoreService;
import cn.hydee.ydjia.merchantcustomer.service.*;
import cn.hydee.ydjia.merchantcustomer.util.CacheUtils;
import cn.hydee.ydjia.merchantcustomer.util.ConvertUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.feign.util.LocalConst.*;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.REDIS_LIVE_PUBLIC_COMMODITY_KEY;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/5/23
 */
@Slf4j
@Service
public class CommodityDetailPageServiceImpl implements CommodityDetailPageService {

    @Autowired
    private CommodityClient commodityClient;

    @Autowired
    private ESCommentClient eSCommentClient;

    @Autowired
    private StoreClient storeClient;

    @Autowired
    private StoreFunctionClient storeFunctionClient;

    @Autowired
    private OrderDetailClient orderDetailClient;

    @Autowired
    private PrescriptionClient prescriptionClient;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MerchantSwitchService merchantSwitchService;

    @Autowired
    private CommodityActivityService commodityActivityService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private UserCollectionService userCollectionService;

    @Autowired
    private LocalCacheService cacheService;

    @Autowired
    private CommonCommodityClientWrapperService commonCommodityClientWrapperService;

    @Value("${XZ_MERCODES:999999}")
    private String XZ_MERCODES;

    @Value("${XZ_COMMODITY_IDS}")
    private String XZ_COMMODITY_IDS;

    @Autowired
    private CacheValidTimePeriodConfig cacheValidTimePeriodConfig;
    @Autowired
    private CommonStoreClientWrapperService commonStoreClientWrapperService;
    @Autowired
    private PlatformInnerServerService platformInnerServerService;
    @Resource
    private ObcYdjStoreService obcYdjStoreService;

    @Override
    public CommodityDTO getCommodity(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO) {
        String merCode = queryCommodityDetailPageDTO.getMerCode();
        String commodityId = queryCommodityDetailPageDTO.getCommodityId();
        LoginUserDTO loginUserDTO = queryCommodityDetailPageDTO.getLoginUserDTO();
        String userId = Objects.nonNull(loginUserDTO) ? loginUserDTO.getUserId() : null;
        // 管控商品标记
        boolean controlCommodityFlag = XZ_MERCODES.contains(merCode) && XZ_COMMODITY_IDS.contains(commodityId) ? true : false;

        // 1、查询商品spu基础信息
        CommodityDTO commodityDTO = this.getCommodityDTO(merCode, commodityId, controlCommodityFlag);
        if (Objects.isNull(commodityDTO)) {
            log.warn("商品信息查询失败，{}", JSON.toJSONString(queryCommodityDetailPageDTO));
            return null;
        }
        // 2、处理基础信息
        this.handleCommodityDTO(queryCommodityDetailPageDTO, commodityDTO);
        // 3、处理组合商品子商品
        this.handleAssembleItem(queryCommodityDetailPageDTO, commodityDTO);

        // 4、组装查询spu下所有规格的参数
        StoreSpecReqDTO specReqDTO = new StoreSpecReqDTO();
        specReqDTO.setMerCode(merCode);
        specReqDTO.setUserId(userId);
        specReqDTO.setCommodityId(Long.parseLong(commodityDTO.getId()));
        specReqDTO.setStoreId(queryCommodityDetailPageDTO.getStoreId());
        specReqDTO.setCommodityType(commodityDTO.getCommodityType());
        specReqDTO.setOrigin(commodityDTO.getOrigin());
        specReqDTO.setTypeId(commodityDTO.getTypeId());
        specReqDTO.setSpecId(queryCommodityDetailPageDTO.getSpecId());
        specReqDTO.setSourceChannelType(queryCommodityDetailPageDTO.getSourceChannelType());
        specReqDTO.setLoginUserDTO(loginUserDTO);
        // 默认查询共享库存，管控商品不查
        specReqDTO.setHasShare(!controlCommodityFlag);
        specReqDTO.setInitPage(true);
        specReqDTO.setPaidMemberFlag(loginUserDTO.getPaidMemberFlag());
        // 处理门店信息
        StoreListResDTO storeDetail = this.getStoreDetail(queryCommodityDetailPageDTO);
        if (Objects.nonNull(storeDetail)) {
            specReqDTO.setStoreCode(storeDetail.getStCode());
            commodityDTO.setStoreDetail(storeDetail);
        }
        // 5、查询spu下规格，查询规格参与的活动等
        StoreSpecRespDTO respDTO = commodityActivityService.getSpecActivityForShared(specReqDTO, commodityDTO);
        // 6、处理规格信息
        if(Objects.nonNull(respDTO)) {
            this.handleStoreSpecRespDTO(respDTO, commodityDTO, queryCommodityDetailPageDTO.getSpecId());
        }
        // 7、设置商品橱窗图
        commodityDTO.setCommodityImgs(this.getCommodityImages(merCode, commodityId));
        // 8、处理处方药合规
        commodityDTO.setPrescriptionDrugComplianceVersion(queryCommodityDetailPageDTO.getPrescriptionDrugComplianceVersion());
        this.handlePrescriptionDrugCompliance(commodityDTO);
        // 9、设置购药提醒（包括处理用法用量字段）
        this.setGuideAndIsMedical(commodityDTO);
        // 10、处理门店药师标记
        this.handleStorePharmacistFlag(commodityDTO);
        // 12、设置商品销量
        if(Objects.nonNull(respDTO) && Objects.nonNull(respDTO.getDefaultSpec())) {
            commodityDTO.setCommoditySaleCount(platformInnerServerService.getCommodityClientService().volume(true, respDTO.getDefaultSpec().getErpCode()).getSaleVolume());
        }
        // 13、如果当前门店无库存，返回有库存的门店数量（spu维度）
        if (commodityDTO.getStock() == null || commodityDTO.getStock() <= 0) {
            specReqDTO.setAppletAppId(queryCommodityDetailPageDTO.getAppletAppId());
            this.getStoreSaleNum(commodityDTO, specReqDTO);
        }
        // 15、处理商品评论
        this.handleCommodityComment(commodityDTO, queryCommodityDetailPageDTO.getMerCode());
        return commodityDTO;
    }

    @Autowired
    private YdjStoreService ydjStoreService;

    @Value("${NEARBY_DISTANCE_MAX:5}")
    private int NEARBY_DISTANCE_MAX = 5;

    /**
     * 此方法本质是对getCommodity方法的包装，
     * 新增了当前门店没有库存时先去查询附近门店有无库存的库存。
     * 原来getCommodity方法的逻辑是，当前门店没有库存就去查旗舰店。
     * @param queryDTO
     * @return
     */
    @Override
    public CommodityDTO getCommodityWithNearBy(QueryCommodityDetailPageDTO queryDTO) {
        log.info("查询商品详情，入参：{}", JSON.toJSONString(queryDTO));

        // 0.查询旗舰店信息，后面使用
        StoreListReqDTO dto = new StoreListReqDTO();
        dto.setMerCode("500001");
        StoreListResDTO centerStore = ydjStoreService.queryCenterStore(dto, true);
        String centerStoreId = centerStore.getId();

        // 1.调用原来的getCommodity方法。
        // 原来getCommodity方法的逻辑：查当前门店，如果当前门店库存为0，查旗舰店
        CommodityDTO commodityDTO = getCommodity(queryDTO);
        if (commodityDTO == null) {
            log.warn("微商城商品详情页，未查到商品信息, 入参：{}", JSON.toJSONString(queryDTO));
            return null;
        }
        // 2.此处判断是直接返回commodityDTO，还是继续执行查询附近门店的逻辑
        String paramStoreId = queryDTO.getStoreId();
        String respStoreId = commodityDTO.getStoreDetail().getId();
        Integer stock = commodityDTO.getStock();
        // 以下两种情况，直接返回，不执行附近逻辑
        // a.传参是旗舰店，
        // b.调用原来的getCommodity方法，返回的门店是传参门店，说明当前门店有库存
        if (paramStoreId.equals(centerStoreId) || paramStoreId.equals(respStoreId) && stock != null && stock > 0 ) {
            return commodityDTO;
        }
        // 如果不是商品活动就查附近门店
        // 3.查附近门店
        boolean isDistance = StringUtils.hasLength(queryDTO.getLatitude()) && StringUtils.hasLength(queryDTO.getLongitude());
        CommodityStockValidReq commodityStockValidReq = new CommodityStockValidReq();
        commodityStockValidReq.setCommodityId(Objects.nonNull(queryDTO.getCommodityId()) ? Long.valueOf(queryDTO.getCommodityId()) : null);
        commodityStockValidReq.setSpecId(Objects.nonNull(queryDTO.getSpecId()) ? Long.valueOf(queryDTO.getSpecId()) : null);
        commodityStockValidReq.setMinStock(1);
        commodityStockValidReq.setLatitude(StringUtils.hasLength(queryDTO.getLatitude()) ? Double.valueOf(queryDTO.getLatitude()) : null);
        commodityStockValidReq.setLongitude(StringUtils.hasLength(queryDTO.getLongitude()) ? Double.valueOf(queryDTO.getLongitude()) : null);
        commodityStockValidReq.setDistance(isDistance ? (double) NEARBY_DISTANCE_MAX : null);
        commodityStockValidReq.setPageSize(100);
        commodityStockValidReq.setCurrentPage(1);
        List<StoreListResDTO> storeListResDTOS = storeService.canSaleStoreList(commodityStockValidReq);
        if(!CollectionUtils.isEmpty(storeListResDTOS)) {
            // 排除当前门店和旗舰店
            Optional<StoreListResDTO> optional = storeListResDTOS.stream()
                    .filter(o -> !o.getId().equals(queryDTO.getStoreId()) || !o.getId().equals(centerStoreId))
                    .findFirst();
            if(optional.isPresent()) {
                queryDTO.setStoreId(optional.get().getId());
                CommodityDTO commodityDTONearBy = getCommodity(queryDTO);
                commodityDTO.setNearByCommodityDTO(buildNewByCommodityDTO(commodityDTONearBy));
                return commodityDTO;
            }
        }
        // 4.附近门店没有库存，查旗舰店
        queryDTO.setStoreId(centerStoreId);
        CommodityDTO centerCommodityDTO = getCommodity(queryDTO);
        if (centerCommodityDTO != null) {
            return centerCommodityDTO;
        }
        // 5.旗舰店没查到，返回最原始的结果
        return commodityDTO;
    }


    private NearByCommodityDTO buildNewByCommodityDTO(CommodityDTO commodityDTO) {
        if (Objects.isNull(commodityDTO.getDefaultSpec())) {
            return null;
        }
        NearByCommodityDTO nearByCommodityDTO = new NearByCommodityDTO();
        nearByCommodityDTO.setStoreDetail(commodityDTO.getStoreDetail());
        CommoditySearchRespDTO commoditySearchRespDTO = new CommoditySearchRespDTO();
        BeanUtils.copyProperties(commodityDTO, commoditySearchRespDTO);
        commoditySearchRespDTO.setCommodityId(commodityDTO.getDefaultSpec().getCommodityId());
        commoditySearchRespDTO.setSpecId(commodityDTO.getDefaultSpec().getId());
        nearByCommodityDTO.setProduct(commoditySearchRespDTO);
        return nearByCommodityDTO;
    }

    @Override
    public CommodityDTO getDistributionCommodity(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO) {
        String merCode = queryCommodityDetailPageDTO.getMerCode();
        String commodityId = queryCommodityDetailPageDTO.getCommodityId();
        LoginUserDTO loginUserDTO = queryCommodityDetailPageDTO.getLoginUserDTO();
        String userId = Objects.nonNull(loginUserDTO) ? loginUserDTO.getUserId() : null;

        // 1、查询商品spu基础信息
        CommodityDTO commodityDTO = this.getCommodityDTO(merCode, commodityId, false);
        if (Objects.isNull(commodityDTO) || !CommodityType.NORMAL.getCode().equals(commodityDTO.getCommodityType())) {
            return null;
        }
        // 2、处理商品基础信息
        this.handleCommodityDTO(queryCommodityDetailPageDTO, commodityDTO);
        // 3、查询门店（不查医保配置？）
        queryCommodityDetailPageDTO.setNoQueryMedicalConfig(false);
        StoreListResDTO storeDetail = this.getStoreDetail(queryCommodityDetailPageDTO);
        if (Objects.isNull(storeDetail)) {
            return commodityDTO;
        }
        commodityDTO.setStoreDetail(storeDetail);

        if (!StringUtils.hasLength(queryCommodityDetailPageDTO.getSpecId())) {
            // 未传specId
            return commodityDTO;
        }
        // 校验门店是否支持B2C
        this.checkStoreB2c(commodityDTO, storeDetail, queryCommodityDetailPageDTO);

        // 4、组装查询spu下所有规格的参数
        StoreSpecReqDTO specReqDTO = new StoreSpecReqDTO();
        specReqDTO.setMerCode(merCode);
        specReqDTO.setUserId(userId);
        specReqDTO.setCommodityId(Long.parseLong(commodityDTO.getId()));
        specReqDTO.setStoreId(queryCommodityDetailPageDTO.getStoreId());
        specReqDTO.setCommodityType(commodityDTO.getCommodityType());
        specReqDTO.setOrigin(commodityDTO.getOrigin());
        specReqDTO.setTypeId(commodityDTO.getTypeId());
        specReqDTO.setSpecId(queryCommodityDetailPageDTO.getSpecId());
        specReqDTO.setSourceChannelType(queryCommodityDetailPageDTO.getSourceChannelType());
        specReqDTO.setLoginUserDTO(loginUserDTO);
        // 旗舰店且支持B2C时查询共享库存
        specReqDTO.setHasShare(BooleanUtils.isTrue(queryCommodityDetailPageDTO.getSupportB2cFlag()));
        specReqDTO.setInitPage(true);
        specReqDTO.setStoreCode(storeDetail.getStCode());

        // 5、查询spu下当前规格，参与的分销活动
        StoreSpecRespDTO respDTO = commodityActivityService.getDistributionSpecActivity(specReqDTO, commodityDTO);
        // 6、处理规格信息
        this.handleStoreSpecRespDTO(respDTO, commodityDTO, queryCommodityDetailPageDTO.getSpecId());

        // 7、设置商品橱窗图
        commodityDTO.setCommodityImgs(this.getCommodityImages(merCode, commodityId));
        // 8、处理处方药合规
        commodityDTO.setPrescriptionDrugComplianceVersion(queryCommodityDetailPageDTO.getPrescriptionDrugComplianceVersion());
        this.handlePrescriptionDrugCompliance(commodityDTO);
        // 9、设置购药提醒（包括处理用法用量字段）
        this.setGuideAndIsMedical(commodityDTO);
        // 10、处理门店药师标记
        this.handleStorePharmacistFlag(commodityDTO);
        // 12、设置商品销量
        commodityDTO.setCommoditySaleCount(orderDetailClient.commoditySaleCount(merCode, commodityId).getData());
        // 14、处理商品评论
        this.handleCommodityComment(commodityDTO, queryCommodityDetailPageDTO.getMerCode());

        return commodityDTO;
    }


    @Override
    public CommodityDTO getCommodityNuoHe(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO) {
        CommodityDTO commodity = this.getCommodity(queryCommodityDetailPageDTO);
        if (commodity!=null && commodity.getDefaultSpec()!=null){
            return commodity;
        }else {
            // 查旗舰店息
            StoreListReqDTO dto = new StoreListReqDTO();
            dto.setMerCode("500001");
            StoreListResDTO centerStore = ydjStoreService.queryCenterStore(dto, true);
            String centerStoreId = centerStore.getId();
            queryCommodityDetailPageDTO.setStoreId(centerStoreId);
            return getCommodity(queryCommodityDetailPageDTO);
        }

    }

    /**
     * @param commodityDTO
     * @param storeListResDTO
     * @param queryCommodityDetailPageDTO
     * @description TODO 校验门店是否支持B2C
     * <AUTHOR>
     * @date 2023/9/1
     */
    private void checkStoreB2c(CommodityDTO commodityDTO, StoreListResDTO storeListResDTO, QueryCommodityDetailPageDTO queryCommodityDetailPageDTO) {
        queryCommodityDetailPageDTO.setFlagshipStoreFlag(storeListResDTO.getCenter());
        commodityDTO.setB2cFlag(false);
        if (BooleanUtils.isNotTrue(queryCommodityDetailPageDTO.getFlagshipStoreFlag())) {
            return;
        }

        // 校验B2C：开启OBC+旗舰店支持B2C
        queryCommodityDetailPageDTO.setSupportB2cFlag(false);
        boolean isOpenObc = this.merchantSwitchService.queryModelIsOpen(queryCommodityDetailPageDTO.getMerCode());
        if (!isOpenObc) {
            return;
        }
        queryCommodityDetailPageDTO.setSupportB2cFlag(ServiceMode.B2C.getCode().equals(storeListResDTO.getServiceMode())
                || ServiceMode.O2OB2C.getCode().equals(storeListResDTO.getServiceMode()));
        commodityDTO.setB2cFlag(queryCommodityDetailPageDTO.getSupportB2cFlag());
    }

    /**
     * 处理处方药合规
     *
     * @param commodityDTO
     */
    private void handlePrescriptionDrugCompliance(CommodityDTO commodityDTO) {
        commodityDTO.setPrescriptionComplianceSwitch(false);
        if (!DrugType.PRESCRIPTION.getCode().equals(commodityDTO.getDrugType())) {
            return;
        }
        if (!merchantSwitchService.queryPrescriptionShowFlag(commodityDTO.getMerCode())) {
            return;
        }

        commodityDTO.setPrescriptionComplianceSwitch(true);
        if (Objects.isNull(commodityDTO.getPrescriptionDrugComplianceVersion())
                || PRESCRIPTION_DRUG_COMPLIANCE_VERSION_ONE.compareTo(commodityDTO.getPrescriptionDrugComplianceVersion()) > 0) {
            // 替换为默认图
            // 主图
            commodityDTO.setMainPic(PRESCRIPTION_DRUG_DEFAULT_PIC);
            // 轮播图
            List<CommodityImgDTO> commodityImgs = new ArrayList<>();
            CommodityImgDTO commodityImgDTO = new CommodityImgDTO();
            commodityImgDTO.setSort(0);
            commodityImgDTO.setPicUrl(PRESCRIPTION_DRUG_DEFAULT_PIC);
            commodityImgs.add(commodityImgDTO);
            commodityDTO.setCommodityImgs(commodityImgs);
            // 规格图
            Spec defaultSpec = commodityDTO.getDefaultSpec();
            if (Objects.nonNull(defaultSpec)) {
                defaultSpec.setPicUrl(PRESCRIPTION_DRUG_DEFAULT_PIC);
            }
        }

        // 设置功能主治和药品适应症字段为空
        commodityDTO.setKeyFeature(Strings.EMPTY);
        commodityDTO.setIndications(Strings.EMPTY);
    }

    /**
     * @param merCode
     * @param commodityId
     * @return java.util.List<cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityImgDTO>
     * @description TODO 获取商品橱窗图
     * <AUTHOR>
     * @date 2023/5/24
     */
    private List<CommodityImgDTO> getCommodityImages(String merCode, String commodityId) {
        ResponseBase<List<CommodityImgDTO>> responseBase = commodityClient.getCommodityImg(commodityId, merCode);
        if (!responseBase.checkSuccess()) {
            log.warn("/comm-img/{commodityId}，merCode = {}，res = {}", commodityId, merCode, JSON.toJSONString(responseBase));
            return Lists.newArrayList();
        }
        return responseBase.getData();
    }

    /**
     * @param respDTO
     * @param commodityDTO
     * @param specId
     * @description TODO 处理门店规格
     * <AUTHOR>
     * @date 2023/5/24
     */
    private void handleStoreSpecRespDTO(StoreSpecRespDTO respDTO, CommodityDTO commodityDTO, String specId) {
        if (Objects.isNull(respDTO)) {
            return;
        }
        // 聚合sku属性
        respDTO.aggregateSukProperty();
        if (StringUtils.hasLength(specId)) {
            respDTO.packDefaultSpec(specId);
        } else {
            // 取首尾价格及默认规格
            respDTO.setPricesDefault(true);
        }

        // 设置各属性（价格、库存等）
        commodityDTO.setPrices(respDTO);
    }

    /**
     * @param queryStore
     * @return cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO
     * @description TODO 查询门店信息
     * <AUTHOR>
     * @date 2023/5/24
     */
    private StoreListResDTO getStoreDetail(QueryCommodityDetailPageDTO queryStore) {
        StoreListResDTO storeListResDTO;
        boolean nearbyFlag = StringUtils.hasLength(queryStore.getLongitude())
                && StringUtils.hasLength(queryStore.getLatitude());
        if (!nearbyFlag) {
            ResponseBase<StoreResDTO> responseBase = storeClient.queryStore(queryStore.getStoreId());
            if (responseBase == null || !responseBase.checkSuccess() || responseBase.getData() == null) {
                log.warn("/store/{}，res = {}", queryStore.getStoreId(), JSON.toJSONString(responseBase));
                throw WarnException.builder().code(ErrorType.STORE_IS_NULL.getCode()).
                        tipMessage(ErrorType.STORE_IS_NULL.getMsg()).build();
            }
            storeListResDTO = new StoreListResDTO();
            BeanUtils.copyProperties(responseBase.getData(), storeListResDTO);
        } else {
            StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
            BeanUtils.copyProperties(queryStore, storeListReqDTO);
            storeListReqDTO.setStoreId(queryStore.getStoreId());
            PageDTO<StoreListResDTO> pageDTO = commonStoreClientWrapperService.pageStoreV3(storeListReqDTO);
            if (pageDTO == null || CollectionUtils.isEmpty(pageDTO.getData())) {
                throw WarnException.builder().code(ErrorType.STORE_IS_NULL.getCode()).
                        tipMessage(ErrorType.STORE_IS_NULL.getMsg()).build();
            }
            storeListResDTO = pageDTO.getData().get(0);
        }
        if (Objects.isNull(storeListResDTO)) {
            return null;
        }

        storeListResDTO.setOpenTime(ConvertUtil.getHourString(storeListResDTO.getOpenStartTime(), false));
        storeListResDTO.setEndTime(ConvertUtil.getHourString(storeListResDTO.getOpenEndTime(), true));
        YdjStore ydjStore = storeService.getCenterStore(queryStore.getMerCode());
        if (ydjStore != null && ydjStore.getStCode().equals(storeListResDTO.getStCode())) {
            storeListResDTO.setCenter(IsCenterStore.IS_CENTER_SOTRE.getCode().equals(ydjStore.getCenterStore()));
        }

        // 设置门店服务信息
        ResponseBase<List<SysStoreFunction>> responseBase = storeFunctionClient.getByStoreIds(Collections.singletonList(storeListResDTO.getId()));
        if (responseBase.checkSuccess() && CollectionUtils.isEmpty(responseBase.getData())) {
            storeListResDTO.setFunList(responseBase.getData());
        }

        // 门店是否支持医保
        if (BooleanUtils.isNotTrue(queryStore.getNoQueryMedicalConfig()) && cacheService.queryMedicalPaymentConfig(storeListResDTO.getMerCode(), storeListResDTO.getId())) {
            storeListResDTO.setSupportMedicarePay(YesOrNoType.YES.getCode());
        }

        // 校验门店进店规则
        storeService.checkStoreRuleStatus(queryStore.getMerCode(), queryStore.getLongitude(),
                queryStore.getLatitude(), Collections.singletonList(storeListResDTO));

        return storeListResDTO;
    }

    /**
     * @param reqDto
     * @param commodityDTO
     * @description TODO 处理组合商品子商品
     * <AUTHOR>
     * @date 2023/5/22
     */
    private void handleAssembleItem(QueryCommodityDetailPageDTO reqDto, CommodityDTO commodityDTO) {
        if (!CommodityType.Combined_commodity.getCode().equals(commodityDTO.getCommodityType())) {
            return;
        }
        // 查询组合子商品信息
        AssembleItemQueryDTO itemQueryDTO = new AssembleItemQueryDTO();
        itemQueryDTO.setCommodityIds(Collections.singletonList(reqDto.getCommodityId()));
        itemQueryDTO.setMerCode(reqDto.getMerCode());
        List<AssembleItemRespDTO> assembleItemRespDTOS = commonCommodityClientWrapperService.getAssembleItems(itemQueryDTO);
        if (CollectionUtils.isEmpty(assembleItemRespDTOS)) {
            return;
        }
        AssembleItemRespDTO assembleItemRespDTO = assembleItemRespDTOS.get(0);
        if (CollectionUtils.isEmpty(assembleItemRespDTO.getItems())) {
            return;
        }
        commodityDTO.setAssembleItems(assembleItemRespDTO.getItems());

        List<AssembleCommodityRelate> items = assembleItemRespDTO.getItems();
        List<Long> skuIds = items.stream().map(o -> Long.valueOf(o.getSpecId())).collect(Collectors.toList());
        SpecSearchDTO dto = new SpecSearchDTO();
        dto.setMerCode(reqDto.getMerCode());
        dto.setStoreIds(Collections.singletonList(reqDto.getStoreId()));
        dto.setIds(skuIds);
        dto.setHasStoreSpec(true);
        dto.setHasSpec(true);
        dto.setPageSize(skuIds.size());
        dto.setHasShare(false);
        PageDTO<StoreSpec> storeSpecPageDTO = commonCommodityClientWrapperService.queryStoreSpec(dto);
        boolean noDataFlag = Objects.isNull(storeSpecPageDTO) || CollectionUtils.isEmpty(storeSpecPageDTO.getData());
        if (noDataFlag) {
            return;
        }

        Map<String, StoreSpec> storeSpecMap = storeSpecPageDTO.getData().stream().collect(Collectors.toMap(StoreSpec::getSpecId, Function.identity(), (k1, k2) -> k1));
        items.stream().forEach(item -> {
            StoreSpec storeSpec = storeSpecMap.get(item.getSpecId());
            if (Objects.isNull(storeSpec)) {
                return;
            }

            if (!CommodityType.GIFT_COMMODITY.getCode().equals(commodityDTO.getCommodityType())
                    && CommodityType.GIFT_COMMODITY.getCode().equals(storeSpec.getCommodityType())) {
                // 主商品不是赠品且子商品含赠品，则设主商品为赠品
                commodityDTO.setCommodityType(CommodityType.GIFT_COMMODITY.getCode());
            }

            item.setStorePrice(MathUtils.setSacle(storeSpec.getPrice()));
            item.setDrugType(storeSpec.getDrugType());

            // 组合商品只要有一个子商品为处方药即设主商品药品类型为处方药
            if (!DrugType.PRESCRIPTION.getCode().equals(commodityDTO.getDrugType())) {
                if (DrugType.PRESCRIPTION.getCode().equals(item.getDrugType())) {
                    commodityDTO.setDrugType(DrugType.PRESCRIPTION.getCode());
                }
            }
        });
        commodityDTO.setAssembleItems(items);
    }

    /**
     * @param queryCommodityDetailPageDTO
     * @param commodityDTO
     * @description TODO 处理商品spu基础信息
     * <AUTHOR>
     * @date 2023/5/24
     */
    private void handleCommodityDTO(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO, CommodityDTO commodityDTO) {
        // 处理商品包装规格字段
        this.handlePackStandard(commodityDTO);
        // 设置商品个性化名称
        commodityDTO.setName(StringUtils.hasLength(commodityDTO.getShoppingViewWareName()) ? commodityDTO.getShoppingViewWareName() : commodityDTO.getName());
    }

    /**
     * 处理商品包装规格字段
     *
     * @param commodityDTO
     * <AUTHOR>
     * @date 2023/3/10
     */
    private void handlePackStandard(CommodityDTO commodityDTO) {
        String pack = commodityDTO.getPackStandard();
        if (!StringUtils.hasLength(pack)) {
            return;
        }
        // 替换*号
        pack = pack.replace(LocalConst.ASTERISK, Strings.EMPTY);
        if (!StringUtils.hasLength(pack)) {
            commodityDTO.setPackStandard(null);
        }
    }

    /**
     * @param merCode
     * @param commodityId
     * @param controlCommodityFlag
     * @return cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityDTO
     * @description TODO 获取商品spu维度基础信息
     * <AUTHOR>
     * @date 2023/5/23
     */
    private CommodityDTO getCommodityDTO(String merCode, String commodityId, boolean controlCommodityFlag) {
        if (!controlCommodityFlag) {
            // 非管控商品不走缓存
            ResponseBase<CommodityDTO> commodityDTOResponseBase = commodityClient.getCommodity(commodityId, merCode);
            if (!commodityDTOResponseBase.checkSuccess() || Objects.isNull(commodityDTOResponseBase.getData())) {
                log.warn("商品信息查询失败，/comm/{}/{} ，res = {}", commodityId, merCode, JSON.toJSONString(commodityDTOResponseBase));
                return null;
            }
            return commodityDTOResponseBase.getData();
        }

        String key = merCode.concat(LocalConst.SPLITER_UNDERLINE) + commodityId;
        String data = redisService.get(key);
        if (StringUtils.hasLength(data)) {
            log.info("商品基础信息走缓存，merCode：{}，commodityId：{}，redis 数据：{}", merCode, commodityId, data);
            return JSON.parseObject(data, CommodityDTO.class);
        }

        ResponseBase<CommodityDTO> commodityDTOResponseBase = commodityClient.getCommodity(commodityId, merCode);
        if (!commodityDTOResponseBase.checkSuccess() || Objects.isNull(commodityDTOResponseBase.getData())) {
            log.warn("/商品信息查询失败，comm/{}/{} ，res = {}", commodityId, merCode, JSON.toJSONString(commodityDTOResponseBase));
            return null;
        }
        redisService.setValueExpire(key, JSON.toJSON(commodityDTOResponseBase.getData()), 24, TimeUnit.HOURS);
        return commodityDTOResponseBase.getData();
    }

    /**
     * 处理门店药师标记
     *
     * @param commodityDTO
     */
    private void handleStorePharmacistFlag(CommodityDTO commodityDTO) {
        if (!DrugType.PRESCRIPTION.getCode().equals(commodityDTO.getDrugType()) || Objects.isNull(commodityDTO.getStoreDetail())) {
            return;
        }
        // 如果是处方药，查询门店是否配置在线审方药师

        try {
            StoreMedicalStaffQueryDTO storeMedicalStaffQueryDTO = new StoreMedicalStaffQueryDTO();
            storeMedicalStaffQueryDTO.setMerCode(commodityDTO.getMerCode());
            storeMedicalStaffQueryDTO.setStoreCode(commodityDTO.getStoreDetail().getStCode());
            storeMedicalStaffQueryDTO.setType(PresType.MEDICINE.getCode());
            MessageData<Boolean> hasPharmacistOnline = prescriptionClient.hasPharmacistOnline(storeMedicalStaffQueryDTO);
            if (hasPharmacistOnline == null || !hasPharmacistOnline.isSuccess() || !hasPharmacistOnline.isBizSuccess()) {
                commodityDTO.setHasPharmacistOnline(false);
            } else {
                commodityDTO.setHasPharmacistOnline(hasPharmacistOnline.getData());
                commodityDTO.setHasPharmacist(hasPharmacistOnline.getData());
            }
        } catch (Exception e) {
            log.error("查询药事云在线药师异常，req:{},{}", commodityDTO.getMerCode(), commodityDTO.getStoreDetail().getStCode(), e);
            commodityDTO.setHasPharmacistOnline(false);
        }
    }


    /**
     * 处理商品评论
     *
     * @param commodityDTO
     * @param merCode
     */
    private void handleCommodityComment(CommodityDTO commodityDTO, String merCode) {
        //展示商品评论数量
        //查询是否需要展示商品，捕获异常
        try {
            boolean showComment = CacheUtils.cache(CacheEnum._commodity_config_comment, () -> merchantSwitchService.queryCommentSwitch(merCode));
            //赋值是否需要展示评论
            commodityDTO.setShowComment(showComment);
        } catch (Exception e) {
            log.info("查询商品评价异常，异常信息：{}", e.getMessage());
        }
    }


    private void setGuideAndIsMedical(CommodityDTO commodityDTO) {
        commodityDTO.setIsMedical(LocalConst.TYPE_MEDICAL_ID.equals(commodityDTO.getFirstTypeId()));
        if (StringUtils.isEmpty(commodityDTO.getFirstTypeId())) {
            return;
        }
        String guide = (String) redisService.getHashKeyValue(LocalConst.TYPE_GUIDE_KEY,
                commodityDTO.getFirstTypeId());
        commodityDTO.setGuide(guide);

        if (BooleanUtils.isNotTrue(commodityDTO.getIsMedical())) {
            // 非中西药品分类：置空用法用量字段
            commodityDTO.setUsageAndDosage(Strings.EMPTY);
            return;
        }
        // 中西成药设置用法用量（默认值-详见说明书）
        if (!StringUtils.hasLength(commodityDTO.getUsageAndDosage())) {
            commodityDTO.setUsageAndDosage(LocalConst.COMMODITY_SPECIAL_PROPERTY_DEFAULT_VALUE);
        }
    }

    private void getStoreSaleNum(CommodityDTO commodityDTO, StoreSpecReqDTO specReqDTO) {
//        StoreSalesListReqDTO storeListReqDTO = new StoreSalesListReqDTO();
//        storeListReqDTO.setMerCode(commodityDTO.getMerCode());
//        storeListReqDTO.setCommodityType(commodityDTO.getCommodityType());
//        storeListReqDTO.setCommodityId(specReqDTO.getCommodityId());
//        storeListReqDTO.setPageType(LocalConst.STATUS_NEGATIVE_ONE);
//        storeListReqDTO.setAppletAppId(specReqDTO.getAppletAppId());
//        storeListReqDTO.setSpecId(Objects.nonNull(commodityDTO.getDefaultSpec()) ? Long.valueOf(commodityDTO.getDefaultSpec().getId()) : null);
//        List<StoreListResDTO> storeList = storeService.getStoreSaleList(storeListReqDTO);
//        commodityDTO.setStoreSaleNum(storeList == null ? 0 : storeList.size());
        commodityDTO.setStoreSaleNum(0);
    }

    private LoginUserDTO getLoginUserDto(String merCode, String userId, String memberCard, String empFlag) {
        return LoginUserDTO.builder().merCode(merCode).userId(userId).memberCard(memberCard).empFlag(STATUS_ONE.toString().equals(empFlag)).build();
    }

    @Override
    public List<AssembleStoreSpecDTO> getAssembleRecommend(AssembleRecommendReqDto assembleRecommendReqDto){
        StoreListResDTO store = obcYdjStoreService.queryCenterStore(LocalConst.COMMON_MERCODE, true);
        AssembleRelateSearchDTO dto = new AssembleRelateSearchDTO();
        dto.setMerCode(LocalConst.COMMON_MERCODE);
        if (assembleRecommendReqDto.getSpecId() != null) {
            dto.setSpecIds(Lists.newArrayList(assembleRecommendReqDto.getSpecId()));
        }
        if (assembleRecommendReqDto.getCommodityId() != null) {
            dto.setCommodityIds(Lists.newArrayList(assembleRecommendReqDto.getCommodityId()));
        }
        //根据子商品查询所有组合商品关系
        List<AssembleCommodityRelate> relates = commonCommodityClientWrapperService.queryAssembleRelateList(dto);
        if(CollUtil.isEmpty(relates)) {
            return Collections.emptyList();
        }
        List<Long> assembleIds = relates.stream().map(AssembleCommodityRelate::getAssembleId).map(Long::valueOf).distinct().collect(Collectors.toList());
        //根据关系查询所有组合商品和子商品
        dto.setSpecIds(null);
        dto.setCommodityIds(null);
        dto.setAssembleIds(assembleIds);
        List<AssembleCommodityRelate> allRelates = commonCommodityClientWrapperService.queryAssembleRelateList(dto);
        if(CollUtil.isEmpty(allRelates)) {
            return Collections.emptyList();
        }
        List<Long> allCommodityIds = allRelates.stream().map(AssembleCommodityRelate::getCommodityId).map(Long::valueOf).distinct().collect(Collectors.toList());
        allCommodityIds.addAll(assembleIds);
        //查询所有自核商品的店品信息
        SpecSearchDTO qDto = new SpecSearchDTO();
        qDto.setCommodityIds(allCommodityIds);
        qDto.setStoreIds(Lists.newArrayList(assembleRecommendReqDto.getStoreId().toString()));
        qDto.setPageSize(0);
        qDto.setMerCode(LocalConst.COMMON_MERCODE);
        qDto.setHasStoreSpec(true);
        qDto.setHasSpec(true);
        //是否旗舰店
        boolean isShip = store != null && Objects.equals(store.getId(), assembleRecommendReqDto.getStoreId().toString());
        //旗舰店查询共享仓库存
        qDto.setHasShare(isShip);
        PageDTO<StoreSpec> storeSpecPageDTO = commonCommodityClientWrapperService.queryStoreSpec(qDto);
        if (storeSpecPageDTO == null || CollectionUtils.isEmpty(storeSpecPageDTO.getData())) {
            return Collections.emptyList();
        }
        Map<String, StoreSpec> specMap = storeSpecPageDTO.getData().stream().collect(Collectors.toMap(StoreSpec::getCommodityId, Function.identity()));
        //父商品分组
        Map<String, List<AssembleCommodityRelate>> assembleMap = allRelates.stream().collect(Collectors.groupingBy(AssembleCommodityRelate::getAssembleId));
        List<AssembleStoreSpecDTO> assembleDtos = new ArrayList<>();
        assembleMap.forEach((assembleId, childRelates) -> {
            StoreSpec assembleSpec = specMap.get(assembleId);
            if (assembleSpec == null || StatusEnums.STOP_USE.isStatus(assembleSpec.getStatus())) {
                return;
            }
            Integer stock = isShip ? assembleSpec.getSharedStock() : assembleSpec.getStock();
            //过滤库存
            if (stock == null || stock <= 0) {
                return;
            }
            AtomicReference<AssembleStoreSpecDTO> currentSpec = new AtomicReference<>();
            AssembleStoreSpecDTO assembleSpecDTO = AssembleStoreSpecDTO.build(assembleSpec);
            List<AssembleStoreSpecDTO> children = childRelates.stream()
                    .filter(relate -> specMap.containsKey(relate.getCommodityId()))
                    .map(relate -> {
                        StoreSpec childSpecDTO = specMap.get(relate.getCommodityId());
                        AssembleStoreSpecDTO childDTO = AssembleStoreSpecDTO.build(childSpecDTO);
                        childDTO.setNumber(relate.getNumber());
                        if (assembleRecommendReqDto.getSpecId() != null && Objects.equals(childDTO.getSpecId(), assembleRecommendReqDto.getSpecId().toString())) {//当前商品排第一个
                            currentSpec.set(childDTO);
                        }
                        if (assembleRecommendReqDto.getCommodityId() != null && Objects.equals(childDTO.getCommodityId(), assembleRecommendReqDto.getCommodityId().toString())) {//当前商品排第一个
                            currentSpec.set(childDTO);
                        }
                        return childDTO;
            }).collect(Collectors.toList());
            if (childRelates.size() != children.size()) {
                //子商品不全
                return;
            }
            if (currentSpec.get() != null) {//当前商品排第一个
                children.remove(currentSpec.get());
                children.add(0, currentSpec.get());
            }
            children.stream().map(child -> child.getPrice().multiply(new BigDecimal(child.getNumber()))).reduce(BigDecimal::add).ifPresent(assembleSpecDTO::setUnderLinePrice);
            assembleSpecDTO.setChildCommodities(children);
            assembleDtos.add(assembleSpecDTO);
        });
        assembleDtos.sort(Comparator.comparing(AssembleStoreSpecDTO::getPrice));
        return assembleDtos;
    }

}
