package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.ydjia.merchantcustomer.dto.shop.*;
import cn.hydee.ydjia.merchantcustomer.enums.ShopSearchSceneEnum;

import java.util.List;

public interface HotwordService {
    PageDTO<ShopSearchKeywordFeignResp> page(ShopSearchKeywordFeignReq shopSearchKeywordApiReq);

    PageDTO<ShopSearchKeywordCommodityFeignResp> commodity(ShopSearchKeywordRelationCommodityFeignReq shopSearchKeywordRelationCommodityFeignReq);

    List<String> wordRelationCommodity(ShopSearchSceneEnum shopSearchSceneEnum, String keyword);

    ShopSearchKeywordSetFeignResp keywordSet(ShopSearchSceneEnum shopSearchSceneEnum, String keyword);
}
