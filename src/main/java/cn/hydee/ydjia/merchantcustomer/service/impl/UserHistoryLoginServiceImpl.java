package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hydee.ydjia.merchantcustomer.domain.UserHistoryLogin;
import cn.hydee.ydjia.merchantcustomer.dto.UserLastLoginDTO;
import cn.hydee.ydjia.merchantcustomer.repository.UserHistoryLoginRepo;
import cn.hydee.ydjia.merchantcustomer.service.RedisService;
import cn.hydee.ydjia.merchantcustomer.service.UserHistoryLoginService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UserHistoryLoginServiceImpl extends ServiceImpl<UserHistoryLoginRepo, UserHistoryLogin> implements UserHistoryLoginService {
    private final String prefix = "user:history:login";
    private final Integer timeWait = 5;

    @Autowired
    private RedisService redisService;


    @Override
    public UserHistoryLogin lastLoginInfo(String appid, String openid) {
        LambdaQueryWrapper<UserHistoryLogin> queryWrapper = new LambdaQueryWrapper<UserHistoryLogin>();
        queryWrapper.eq(UserHistoryLogin::getAppid, appid)
                .eq(UserHistoryLogin::getOpenid, openid)
                .orderByDesc(UserHistoryLogin::getLastLoginTime);
        return this.getOne(queryWrapper, false);
    }

    @Transactional
    @Override
    public void recordLastLoginInfo(UserLastLoginDTO userLastLoginDTO) {
        if(!redisService.setIfAbsent(recordKey(userLastLoginDTO), userLastLoginDTO.getUnionid(), timeWait, TimeUnit.SECONDS)) {
            log.warn("存在并发情况，{}", JSON.toJSONString(userLastLoginDTO));
            return ;
        }
        LambdaQueryWrapper<UserHistoryLogin> queryWrapper = new LambdaQueryWrapper<UserHistoryLogin>();
        queryWrapper.eq(UserHistoryLogin::getAppid, userLastLoginDTO.getAppid())
                .eq(UserHistoryLogin::getOpenid, userLastLoginDTO.getOpenid())
                .eq(UserHistoryLogin::getUserId, userLastLoginDTO.getUserId());
        UserHistoryLogin userHistoryLogin = this.getOne(queryWrapper);
        if(Objects.isNull(userHistoryLogin)) {
            userHistoryLogin = new UserHistoryLogin();
            BeanUtil.copyProperties(userLastLoginDTO, userHistoryLogin);
            userHistoryLogin.setLastLoginTime(new Date());
            this.save(userHistoryLogin);
            return ;
        }
        // 这里有分表建，所以会出现错误，必须将分表建
        queryWrapper.eq(UserHistoryLogin::getId, userHistoryLogin.getId());
        userHistoryLogin.setLastLoginTime(new Date());
        this.update(userHistoryLogin, queryWrapper);
    }

    private String recordKey(UserLastLoginDTO userLastLoginDTO) {
        return String.join(":", prefix, userLastLoginDTO.getAppid(), userLastLoginDTO.getOpenid(), String.valueOf(userLastLoginDTO.getUserId()));
    }
}
