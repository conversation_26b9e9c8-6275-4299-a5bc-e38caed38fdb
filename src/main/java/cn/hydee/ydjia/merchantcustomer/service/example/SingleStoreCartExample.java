package cn.hydee.ydjia.merchantcustomer.service.example;

import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityGetDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SingleStoreCartCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.service.SingleStoreShoppingCartService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 单门店购物车使用示例
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class SingleStoreCartExample {
    
    @Autowired
    private SingleStoreShoppingCartService singleStoreShoppingCartService;
    
    /**\n     * 基础使用示例 - O2O门店\n     */\n    public void basicO2OStoreExample() {\n        try {\n            // 1. 构建O2O门店请求参数\n            CartCommodityGetDTO request = buildO2OStoreRequest();\n            \n            // 2. 调用单门店服务\n            SingleStoreCartCommodityRespDTO response = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n            \n            // 3. 处理响应结果\n            handleO2OStoreResponse(response);\n            \n        } catch (Exception e) {\n            log.error(\"O2O门店购物车查询失败\", e);\n        }\n    }\n    \n    /**\n     * B2C门店使用示例\n     */\n    public void b2cStoreExample() {\n        try {\n            // 1. 构建B2C门店请求参数\n            CartCommodityGetDTO request = buildB2CStoreRequest();\n            \n            // 2. 调用单门店服务\n            SingleStoreCartCommodityRespDTO response = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n            \n            // 3. 处理响应结果\n            handleB2CStoreResponse(response);\n            \n        } catch (Exception e) {\n            log.error(\"B2C门店购物车查询失败\", e);\n        }\n    }\n    \n    /**\n     * 云仓门店使用示例\n     */\n    public void cloudStoreExample() {\n        try {\n            // 1. 构建云仓门店请求参数\n            CartCommodityGetDTO request = buildCloudStoreRequest();\n            \n            // 2. 调用单门店服务\n            SingleStoreCartCommodityRespDTO response = singleStoreShoppingCartService.getSingleStoreCommodity(request);\n            \n            // 3. 处理响应结果\n            handleCloudStoreResponse(response);\n            \n        } catch (Exception e) {\n            log.error(\"云仓门店购物车查询失败\", e);\n        }\n    }\n    \n    /**\n     * 错误处理示例\n     */\n    public void errorHandlingExample() {\n        try {\n            // 构建无效请求\n            CartCommodityGetDTO invalidRequest = new CartCommodityGetDTO();\n            // 故意不设置必要参数\n            \n            SingleStoreCartCommodityRespDTO response = singleStoreShoppingCartService.getSingleStoreCommodity(invalidRequest);\n            \n        } catch (IllegalArgumentException e) {\n            log.warn(\"参数验证失败: {}\", e.getMessage());\n            // 处理参数错误\n            \n        } catch (RuntimeException e) {\n            log.error(\"系统运行时错误: {}\", e.getMessage(), e);\n            // 处理系统错误\n            \n        } catch (Exception e) {\n            log.error(\"未知错误: {}\", e.getMessage(), e);\n            // 处理其他错误\n        }\n    }\n    \n    /**\n     * 构建O2O门店请求参数\n     */\n    private CartCommodityGetDTO buildO2OStoreRequest() {\n        CartCommodityGetDTO request = new CartCommodityGetDTO();\n        \n        // 基础信息\n        request.setMerCode(\"MERCHANT_001\");\n        request.setUserId(\"123456789\");\n        request.setCurrentStoreId(\"STORE_001\"); // O2O门店ID\n        request.setCurrentStoreName(\"测试O2O门店\");\n        request.setClientType(\"MINI_PROGRAM\");\n        \n        // 登录用户信息\n        LoginUserDTO loginUser = LoginUserDTO.builder()\n                .merCode(\"MERCHANT_001\")\n                .userId(\"123456789\")\n                .build();\n        request.setLoginUser(loginUser);\n        \n        return request;\n    }\n    \n    /**\n     * 构建B2C门店请求参数\n     */\n    private CartCommodityGetDTO buildB2CStoreRequest() {\n        CartCommodityGetDTO request = new CartCommodityGetDTO();\n        \n        // 基础信息\n        request.setMerCode(\"MERCHANT_001\");\n        request.setUserId(\"123456789\");\n        request.setCurrentStoreId(LocalConst.MERCHANT_B2C_FIXED_STORE); // B2C门店ID\n        request.setCurrentStoreName(LocalConst.MERCHANT_B2C_FIXED_STORE_NAME);\n        request.setClientType(\"MINI_PROGRAM\");\n        \n        // 登录用户信息\n        LoginUserDTO loginUser = LoginUserDTO.builder()\n                .merCode(\"MERCHANT_001\")\n                .userId(\"123456789\")\n                .build();\n        request.setLoginUser(loginUser);\n        \n        return request;\n    }\n    \n    /**\n     * 构建云仓门店请求参数\n     */\n    private CartCommodityGetDTO buildCloudStoreRequest() {\n        CartCommodityGetDTO request = new CartCommodityGetDTO();\n        \n        // 基础信息\n        request.setMerCode(\"MERCHANT_001\");\n        request.setUserId(\"123456789\");\n        request.setCurrentStoreId(LocalConst.CLOUD_FIXED_STORE + \"WSC001\"); // 云仓门店ID\n        request.setCurrentStoreName(LocalConst.CLOUD_FIXED_STORE_NAME);\n        request.setClientType(\"MINI_PROGRAM\");\n        \n        // 登录用户信息\n        LoginUserDTO loginUser = LoginUserDTO.builder()\n                .merCode(\"MERCHANT_001\")\n                .userId(\"123456789\")\n                .build();\n        request.setLoginUser(loginUser);\n        \n        return request;\n    }\n    \n    /**\n     * 处理O2O门店响应结果\n     */\n    private void handleO2OStoreResponse(SingleStoreCartCommodityRespDTO response) {\n        if (response == null) {\n            log.warn(\"O2O门店购物车响应为空\");\n            return;\n        }\n        \n        log.info(\"O2O门店购物车查询成功:\");\n        log.info(\"- 门店ID: {}\", response.getStoreId());\n        log.info(\"- 门店名称: {}\", response.getStoreName());\n        log.info(\"- 商品数量: {}\", response.getCommodities() != null ? response.getCommodities().size() : 0);\n        log.info(\"- 选中商品数量: {}\", response.getChooseCommodityCount());\n        log.info(\"- 商品总价: {}\", response.getTotalPrice());\n        log.info(\"- 优惠前总价: {}\", response.getBeforePrice());\n        log.info(\"- 预估优惠: {}\", response.getReducePrice());\n        \n        // 遍历商品\n        if (response.getCommodities() != null) {\n            response.getCommodities().forEach(commodity -> {\n                log.info(\"  商品: {} ({}), 价格: {}, 数量: {}\", \n                        commodity.getCommodityName(), commodity.getSpecId(), \n                        commodity.getPrice(), commodity.getCount());\n            });\n        }\n    }\n    \n    /**\n     * 处理B2C门店响应结果\n     */\n    private void handleB2CStoreResponse(SingleStoreCartCommodityRespDTO response) {\n        if (response == null) {\n            log.warn(\"B2C门店购物车响应为空\");\n            return;\n        }\n        \n        log.info(\"B2C门店购物车查询成功:\");\n        log.info(\"- 门店类型: B2C旗舰店\");\n        log.info(\"- 门店ID: {}\", response.getStoreId());\n        log.info(\"- 商品数量: {}\", response.getCommodities() != null ? response.getCommodities().size() : 0);\n        log.info(\"- 商品总价: {}\", response.getTotalPrice());\n        \n        // B2C门店特殊处理逻辑\n        if (response.getCommodities() != null) {\n            long b2cCommodityCount = response.getCommodities().stream()\n                    .filter(c -> c.getIsB2c() != null && c.getIsB2c() == 1)\n                    .count();\n            log.info(\"- B2C商品数量: {}\", b2cCommodityCount);\n        }\n    }\n    \n    /**\n     * 处理云仓门店响应结果\n     */\n    private void handleCloudStoreResponse(SingleStoreCartCommodityRespDTO response) {\n        if (response == null) {\n            log.warn(\"云仓门店购物车响应为空\");\n            return;\n        }\n        \n        log.info(\"云仓门店购物车查询成功:\");\n        log.info(\"- 门店类型: 云仓门店\");\n        log.info(\"- 门店ID: {}\", response.getStoreId());\n        log.info(\"- 供应商编码: {}\", response.getSpCode());\n        log.info(\"- 供应商名称: {}\", response.getSpName());\n        log.info(\"- 商品数量: {}\", response.getCommodities() != null ? response.getCommodities().size() : 0);\n        log.info(\"- 商品总价: {}\", response.getTotalPrice());\n        \n        // 云仓门店特殊处理逻辑\n        if (response.getCommodities() != null) {\n            response.getCommodities().forEach(commodity -> {\n                if (commodity.getSpCode() != null) {\n                    log.info(\"  云仓商品: {} ({}), 供应商: {}\", \n                            commodity.getCommodityName(), commodity.getSpecId(), commodity.getSpCode());\n                }\n            });\n        }\n    }\n    \n    /**\n     * 对比多门店vs单门店的差异\n     */\n    public void compareMultiStoreVsSingleStore() {\n        log.info(\"=== 多门店 vs 单门店购物车对比 ===\");\n        \n        log.info(\"多门店购物车特点:\");\n        log.info(\"- 返回多个门店的商品列表\");\n        log.info(\"- 需要按门店分组\");\n        log.info(\"- 需要计算门店距离和排序\");\n        log.info(\"- 数据结构复杂，包含门店列表\");\n        log.info(\"- 适用于用户可以选择多个门店的场景\");\n        \n        log.info(\"单门店购物车特点:\");\n        log.info(\"- 只返回指定门店的商品\");\n        log.info(\"- 无需门店分组和排序\");\n        log.info(\"- 数据结构简化，直接返回商品列表\");\n        log.info(\"- 性能更好，逻辑更简单\");\n        log.info(\"- 适用于用户已确定门店的场景\");\n        \n        log.info(\"使用建议:\");\n        log.info(\"- 购物车页面：使用多门店购物车\");\n        log.info(\"- 门店详情页：使用单门店购物车\");\n        log.info(\"- 订单确认页：使用单门店购物车\");\n        log.info(\"- 快速下单：使用单门店购物车\");\n    }\n}"
