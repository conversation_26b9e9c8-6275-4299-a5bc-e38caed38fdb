package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.req.OrderFreightReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.OrderFreightResDTO;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/1/21
 */
public interface DeductionService {
    /**
     * 判断是否支持心币抵邮费
     *
     * @param reqDTO  邮费计算请求对象
     * @param respDTO 邮费计算返回对象
     */
    Boolean isSupportDeduction(OrderFreightReqDTO reqDTO, OrderFreightResDTO respDTO);
}
