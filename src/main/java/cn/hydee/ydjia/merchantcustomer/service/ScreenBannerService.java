package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.domain.ScreenBanner;
import cn.hydee.ydjia.merchantcustomer.dto.resp.ScreenBannerConfigResDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 大屏开屏banner接口类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/28
 */
public interface ScreenBannerService extends IService<ScreenBanner> {

    /**
     * 获取大屏开屏banner配置数据
     *
     * @param merCode 商户编码
     * @return 大屏开屏banner配置数据
     */
    ScreenBannerConfigResDTO getDetail(String merCode);
}
