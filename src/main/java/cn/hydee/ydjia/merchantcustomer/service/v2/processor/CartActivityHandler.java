package cn.hydee.ydjia.merchantcustomer.service.v2.processor;

import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.PriceDiscountParamDTO;

import java.util.List;

/**
 * 购物车活动处理器
 * 负责各种促销活动的处理和价格计算
 * 
 * <AUTHOR>
 * @version 2.0
 */
public interface CartActivityHandler {
    
    /**
     * 获取促销活动信息
     * 
     * 包括：
     * 1. 商城促销活动
     * 2. 满减活动
     * 3. 加价购活动
     * 4. 限时优惠
     * 
     * @param productData 商品数据
     * @param request 请求参数
     * @return 活动规格列表
     */
    List<ActivitySpecDTO> getPromotionActivities(ShoppingCartProductHandleDTO productData, 
                                                ShoppingCartCommodityReqDTO request);
    
    /**
     * 处理会员活动优惠
     * 
     * 包括：
     * 1. Plus会员优惠
     * 2. 会员日活动
     * 3. 会员权益优惠
     * 
     * @param activitySpecs 活动规格列表
     * @param priceDiscountParam 价格优惠参数
     * @param request 请求参数
     * @return 处理后的活动规格列表
     */
    List<ActivitySpecDTO> handleMemberActivities(List<ActivitySpecDTO> activitySpecs,
                                                PriceDiscountParamDTO priceDiscountParam,
                                                ShoppingCartCommodityReqDTO request);
    
    /**
     * 处理优惠券优惠
     * 
     * @param activitySpecs 活动规格列表
     * @param priceDiscountParam 价格优惠参数
     * @param request 请求参数
     * @return 处理后的活动规格列表
     */
    List<ActivitySpecDTO> handleCouponDiscount(List<ActivitySpecDTO> activitySpecs,
                                              PriceDiscountParamDTO priceDiscountParam,
                                              ShoppingCartCommodityReqDTO request);
    
    /**
     * 处理赠品和换购商品
     * 
     * @param activitySpecs 活动规格列表
     * @param request 请求参数
     */
    void handleGiftAndRepurchaseProducts(List<ActivitySpecDTO> activitySpecs,
                                        ShoppingCartCommodityReqDTO request);
}
