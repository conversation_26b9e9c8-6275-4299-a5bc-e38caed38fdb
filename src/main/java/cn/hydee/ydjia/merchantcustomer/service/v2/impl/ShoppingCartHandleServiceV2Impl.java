package cn.hydee.ydjia.merchantcustomer.service.v2.impl;

import cn.hydee.starter.util.ExLogger;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.PriceDiscountParamDTO;
import cn.hydee.ydjia.merchantcustomer.service.v2.ShoppingCartHandleServiceV2;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartActivityHandler;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartCommodityProcessor;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartDataAssembler;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartPriceCalculator;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 购物车处理服务V2实现
 * 重构后的购物车商品处理服务实现
 * 
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Service
public class ShoppingCartHandleServiceV2Impl implements ShoppingCartHandleServiceV2 {
    
    @Autowired
    private CartCommodityProcessor commodityProcessor;
    
    @Autowired
    private CartActivityHandler activityHandler;
    
    @Autowired
    private CartPriceCalculator priceCalculator;
    
    @Autowired
    private CartDataAssembler dataAssembler;
    
    /**
     * 处方药合规版本常量
     */
    private static final String PRESCRIPTION_DRUG_COMPLIANCE_VERSION_ONE = "1.0.0";
    
    @Override
    public CartCommodityRespCommonDTO handleCartCommodityV2(ShoppingCartCommodityReqDTO request) {
        Long startTime = System.currentTimeMillis();
        
        try {
            log.info("购物车商品算价V2开始，入参：{}", JSON.toJSONString(request));
            
            // 1. 参数验证
            validateRequest(request);
            
            // 2. 预处理登录用户信息
            preprocessLoginUser(request);
            
            // 3. 获取商品基本信息
            ShoppingCartProductHandleDTO productData = commodityProcessor.processProductInfo(request);
            if (productData == null || !commodityProcessor.validateProductData(productData)) {
                log.info("无有效商品数据，返回空响应");
                return dataAssembler.createEmptyResponse(request);
            }
            
            // 4. 获取促销活动信息
            List<ActivitySpecDTO> activitySpecs = activityHandler.getPromotionActivities(productData, request);
            
            // 5. 处理赠品和换购商品
            activityHandler.handleGiftAndRepurchaseProducts(activitySpecs, request);
            
            // 6. 创建价格优惠参数
            PriceDiscountParamDTO priceDiscountParam = priceCalculator.createPriceDiscountParam(request);
            
            // 7. 处理会员活动优惠
            activitySpecs = activityHandler.handleMemberActivities(activitySpecs, priceDiscountParam, request);
            
            // 8. 处理优惠券优惠
            activitySpecs = activityHandler.handleCouponDiscount(activitySpecs, priceDiscountParam, request);
            
            // 9. 计算最终价格
            priceCalculator.calculateFinalPrices(productData, activitySpecs, request);
            
            // 10. 组装响应数据
            CartCommodityRespCommonDTO response = dataAssembler.assembleResponse(productData, activitySpecs, request);
            
            // 11. 处理处方药合规信息
            handlePrescriptionDrugComplianceIfNeeded(request, response);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("购物车商品算价V2完成，耗时：{} ms，响应：{}", duration, JSON.toJSONString(response));
            
            return response;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("购物车商品算价V2处理失败，耗时：{} ms，错误：", duration, e);
            ExLogger.logger().field("cart_v2_error").error("购物车处理失败", e);
            throw new RuntimeException("购物车商品处理失败", e);
        }
    }
    
    @Override
    public void validateRequest(ShoppingCartCommodityReqDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        
        if (!StringUtils.hasText(request.getMerCode())) {
            throw new IllegalArgumentException("商户编码不能为空");
        }
        
        if (!StringUtils.hasText(request.getUserId())) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        if (CollectionUtils.isEmpty(request.getCartRedisSpecs())) {
            log.warn("购物车商品列表为空，用户：{}", request.getUserId());
        }
    }
    
    /**
     * 预处理登录用户信息
     */
    private void preprocessLoginUser(ShoppingCartCommodityReqDTO request) {
        if (request.getLoginUser() == null && request.getUserId() != null) {
            // 创建默认登录用户信息
            request.setLoginUser(createDefaultLoginUser(request));
        }
    }
    
    /**
     * 创建默认登录用户信息
     */
    private cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO createDefaultLoginUser(ShoppingCartCommodityReqDTO request) {
        return cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO.builder()
                .merCode(request.getMerCode())
                .userId(request.getUserId())
                .build();
    }
    
    /**
     * 处理处方药合规信息（如果需要）
     */
    private void handlePrescriptionDrugComplianceIfNeeded(ShoppingCartCommodityReqDTO request, 
                                                         CartCommodityRespCommonDTO response) {
        if (shouldHandlePrescriptionDrugCompliance(request)) {
            dataAssembler.handlePrescriptionDrugCompliance(response, request.getMerCode());
        }
    }
    
    /**
     * 判断是否需要处理处方药合规
     */
    private boolean shouldHandlePrescriptionDrugCompliance(ShoppingCartCommodityReqDTO request) {
        return Objects.isNull(request.getPrescriptionDrugComplianceVersion()) ||
               PRESCRIPTION_DRUG_COMPLIANCE_VERSION_ONE.compareTo(request.getPrescriptionDrugComplianceVersion()) > 0;
    }
}
