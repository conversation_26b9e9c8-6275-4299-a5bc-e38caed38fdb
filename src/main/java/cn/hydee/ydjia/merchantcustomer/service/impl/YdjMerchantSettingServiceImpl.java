package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hydee.ydjia.merchantcustomer.domain.YdjMerchantSetting;
import cn.hydee.ydjia.merchantcustomer.repository.YdjMerchantSettingRepo;
import cn.hydee.ydjia.merchantcustomer.service.YdjMerchantSettingService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/05/13 11:56
 */
@Service
@AllArgsConstructor
public class YdjMerchantSettingServiceImpl implements YdjMerchantSettingService {

    private final YdjMerchantSettingRepo merchantSettingRepo;

    @Override
    public List<YdjMerchantSetting> queryByMerCodeAndKey(String merCode, String sysKey) {
        return merchantSettingRepo.queryByMerCodeAndKey(merCode, sysKey);
    }
}
