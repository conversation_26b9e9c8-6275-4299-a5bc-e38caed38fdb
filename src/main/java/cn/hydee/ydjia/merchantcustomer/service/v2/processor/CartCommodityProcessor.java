package cn.hydee.ydjia.merchantcustomer.service.v2.processor;

import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;

/**
 * 购物车商品处理器
 * 负责商品基础信息的获取、验证和预处理
 * 
 * <AUTHOR>
 * @version 2.0
 */
public interface CartCommodityProcessor {
    
    /**
     * 处理商品基础信息
     * 
     * 功能包括：
     * 1. 从Redis获取购物车商品数据
     * 2. 调用商品中台获取商品详情
     * 3. 验证商品有效性（上下架、库存等）
     * 4. 处理集团化数据隔离
     * 5. 清理无效商品
     * 
     * @param request 购物车请求参数
     * @return 处理后的商品数据，如果无有效商品则返回null
     */
    ShoppingCartProductHandleDTO processProductInfo(ShoppingCartCommodityReqDTO request);
    
    /**
     * 验证商品数据有效性
     * 
     * @param productData 商品数据
     * @return 是否有效
     */
    boolean validateProductData(ShoppingCartProductHandleDTO productData);
    
    /**
     * 清理无效商品
     * 清理下架、库存不足、失效门店的商品
     * 
     * @param request 请求参数
     * @return 清理后的有效商品数据
     */
    ShoppingCartProductHandleDTO cleanInvalidProducts(ShoppingCartCommodityReqDTO request);
}
