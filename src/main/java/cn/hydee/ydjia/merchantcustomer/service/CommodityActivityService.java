package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.activity.CommonCommodityActivityPriceQueryDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.StoreSpecReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.dto.sdp.resp.SdpConfigInfo;
import cn.hydee.ydjia.merchantcustomer.dto.sp.StoreSpSpecReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.sp.StoreSpSpecRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.PmtRuleDistribution;

import java.util.List;

public interface CommodityActivityService {

    /**
     * 根据商品ID获取商品规格信息及规格对应的促销活动
     *
     * @param dto
     * @return
     */
    StoreSpecRespDTO getSpecActivity(StoreSpecReqDTO dto, StoreSpecRespDTO respDTO);

    /**
     * 某商品门店上架规格数量
     *
     * @param dto
     * @return
     */
    Integer getSpecCount(StoreSpecReqDTO dto);

    /**
     * 根据商品规格查询规格信息及活动信息
     * 区分共享仓库存逻辑，如当前传入门店售罄，存在共享库存，包装成旗舰店信息返回
     *
     * @param specReqDTO
     * @return
     */
    StoreSpecRespDTO getSpecActivityForShared(StoreSpecReqDTO specReqDTO, CommodityDTO commodityDTO);

    /**
     * 切换规格接口，如果存在共享库存 且共享库存大于门店库存，需要返回共享仓
     *
     * @param respWrapperDTO
     * @param dto
     */
    void setSharedSpec(StoreSpecRespWrapperDTO respWrapperDTO, StoreSpecReqDTO dto);

    /**
     * 处理商品主图打标、橱窗图
     *
     * @param pmtFlag
     * @param activityList
     * @return
     */
    SpecDrawRuleDTO handlePmtWindowDiagram(Boolean pmtFlag, List<ActivityDTO> activityList);

    /**
     * @param commodityGroupDTO
     * @description TODO 处理拼团商详页单独购买价格
     * <AUTHOR>
     * @date 2023/6/19
     */
    void handleGroupCommonDetailPagePrice(CommodityGroupDTO commodityGroupDTO);

    /**
     * @param commonCommodityActivityPriceQueryDTO
     * @description TODO 处理商品规格活动价
     * <AUTHOR>
     * @date 2023/6/20
     */
    void handleCommonCommodityActivityPrice(CommonCommodityActivityPriceQueryDTO commonCommodityActivityPriceQueryDTO);

    /**
     * @param specReqDTO
     * @param commodityDTO
     * @return cn.hydee.ydjia.merchantcustomer.dto.resp.StoreSpecRespDTO
     * @description TODO 处理分销规格活动信息
     * <AUTHOR>
     * @date 2023/9/1
     */
    StoreSpecRespDTO getDistributionSpecActivity(StoreSpecReqDTO specReqDTO, CommodityDTO commodityDTO);

    /**
     * @param specReqDTO
     * @param respDTO
     * @description TODO 处理分销云仓商品活动信息
     * <AUTHOR>
     * @date 2023/9/4
     */
    void handleDistributionSpSpecActivity(StoreSpSpecReqDTO specReqDTO, StoreSpSpecRespDTO respDTO);

    /**
     * @param pmtDistributionRule
     * @param sdpConfigInfo
     * @description TODO 设置分销商品活动规则
     * <AUTHOR>
     * @date 2023/9/5
     */
    void setPmtDistributionRule(PmtRuleDistribution pmtDistributionRule, SdpConfigInfo sdpConfigInfo);
}
