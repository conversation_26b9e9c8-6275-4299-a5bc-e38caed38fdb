package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityGetDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SingleStoreCartCommodityRespDTO;

/**
 * 单门店购物车服务接口
 * 重构自多门店购物车，专门处理单门店场景
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
public interface SingleStoreShoppingCartService {
    
    /**
     * 获取单门店购物车商品
     * 
     * 主要功能：
     * 1. 获取指定门店的购物车商品
     * 2. 处理商品信息和活动优惠
     * 3. 计算价格和优惠
     * 4. 返回单门店格式的数据
     * 
     * @param dto 购物车查询参数，必须包含门店ID
     * @return 单门店购物车商品响应数据
     * @throws IllegalArgumentException 当门店ID为空时
     */
    SingleStoreCartCommodityRespDTO getSingleStoreCommodity(CartCommodityGetDTO dto);
    
    /**
     * 验证单门店请求参数
     * 
     * @param dto 请求参数
     * @throws IllegalArgumentException 当参数无效时
     */
    void validateSingleStoreRequest(CartCommodityGetDTO dto);
}
