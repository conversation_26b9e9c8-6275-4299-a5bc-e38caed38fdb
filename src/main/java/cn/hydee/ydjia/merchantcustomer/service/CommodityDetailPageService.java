package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.req.AssembleRecommendReqDto;
import cn.hydee.ydjia.merchantcustomer.dto.req.QueryCommodityDetailPageDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.AssembleStoreSpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/5/23
 */
public interface CommodityDetailPageService {

    /**
     * @param queryCommodityDetailPageDTO
     * @return cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityDTO
     * @description TODO 商详页初始化
     * <AUTHOR>
     * @date 2023/5/23
     */
    CommodityDTO getCommodity(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO);

    CommodityDTO getCommodityWithNearBy(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO);

    /**
     * @param queryCommodityDetailPageDTO
     * @return cn.hydee.ydjia.merchantcustomer.dto.resp.CommodityDTO
     * @description TODO 分销详情
     * <AUTHOR>
     * @date 2023/9/1
     */
    CommodityDTO getDistributionCommodity(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO);


    /**
     * 值给诺和使用
     * @param queryCommodityDetailPageDTO
     * @return
     */
    CommodityDTO getCommodityNuoHe(QueryCommodityDetailPageDTO queryCommodityDetailPageDTO);

    /**
     * 子商品推荐关联组合商品
     * @param assembleRecommendReqDto
     * @return
     */
    List<AssembleStoreSpecDTO> getAssembleRecommend(AssembleRecommendReqDto assembleRecommendReqDto);
}
