package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.enums.ActivityHandleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType.COUPONS_TYPE_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/30 16:51
 */
@Component
@Slf4j
public class ActivityReceiveCouponFactory {

    @Autowired
    private Map<String, ActivityReceiveCouponService> couponServiceMap;

    public ActivityReceiveCouponService foundReceiveCouponService(Integer var2) {
        String var1 = ActivityHandleType.getTypeMsgByCode(var2);
        if (StringUtils.isNotBlank(var1)) {
            ActivityReceiveCouponService couponService = couponServiceMap.get(var1);
            if (couponService != null) {
                return couponService;
            }
        }

        log.warn("Invalid activity coupon type [{}-{}]", var2, var1);
        throw WarnException.builder().code(COUPONS_TYPE_ERROR.getCode())
                .message(COUPONS_TYPE_ERROR.getMsg()).build();
    }
}
