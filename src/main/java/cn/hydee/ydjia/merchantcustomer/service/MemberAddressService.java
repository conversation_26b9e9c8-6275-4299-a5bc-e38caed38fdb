package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.dto.SwitchStoresReq;
import cn.hydee.ydjia.merchantcustomer.dto.MemberAddressDTO;
import cn.hydee.ydjia.merchantcustomer.dto.SwitchStoresResDTO;
import com.yxt.middle.baseinfo.res.store.StoreApiResDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-22
 */
public interface MemberAddressService {

    /**
     * 查询商户下顾客收货地址
     *
     * @param userId
     * @param storeId
     * @param merCode
     * @param orderType
     * @param isCloudGoods
     * @return
     */
    List<MemberAddressDTO> getAddressList(Long userId, String storeId, String merCode, String orderType, String clientType, Integer isB2cOrder, Integer isCloudGoods);

    MemberAddressDTO checkDistribute(String storeId, MemberAddressDTO memberAddressDTO);


    /** 切换门店
     * <AUTHOR>
     * @Description
     * @date 2024/9/2 18:08
     */
    SwitchStoresResDTO switchStores(SwitchStoresReq switchStoresReq);

}
