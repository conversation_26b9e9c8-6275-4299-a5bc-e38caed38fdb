package cn.hydee.ydjia.merchantcustomer.service;

/**
 * 父处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/28 14:34
 */
public abstract class AbstractHandler<T,V> {
    private AbstractHandler next;
    public void setNext(AbstractHandler next)
    {
        this.next=next;
    }
    public AbstractHandler getNext()
    {
        return next;
    }
    //处理请求的方法
    public abstract  V handleRequest(T obj);
}
