package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.starter.configuration.DisLockConfiguration;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.starter.util.ExLogger;
import cn.hydee.ydjia.merchantcustomer.config.CartConfiguration;
import cn.hydee.ydjia.merchantcustomer.config.SpringContextHolder;
import cn.hydee.ydjia.merchantcustomer.dto.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.TransferCommodityRemindReq.CommodityParam;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.TransferCommodityRemindResp.CommodityInfo;
import cn.hydee.ydjia.merchantcustomer.enums.OrderType;
import cn.hydee.ydjia.merchantcustomer.feign.*;
import cn.hydee.ydjia.merchantcustomer.enums.LimitTypeEnum;
import cn.hydee.ydjia.merchantcustomer.feign.OrderActivityClient;
import cn.hydee.ydjia.merchantcustomer.feign.PromoteClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.SPMerchantQueryClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.CommodityQueryAssembleParamDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.collectivization.MerchantCollectivizationParamDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.QueryStoreDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.StoreListReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreSpec;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.MerchantSwitchType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.feign.mdmmiration.EmployeeClientAdapter;
import cn.hydee.ydjia.merchantcustomer.feign.service.MallMerchantConfigService;
import cn.hydee.ydjia.merchantcustomer.feign.service.MerchantSwitchService;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonStoreClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.service.InPurchaseCartService;
import cn.hydee.ydjia.merchantcustomer.service.RedisService;
import cn.hydee.ydjia.merchantcustomer.service.ShoppingCartService;
import cn.hydee.ydjia.merchantcustomer.service.YdjStoreService;
import cn.hydee.ydjia.merchantcustomer.util.AssertUtils;
import cn.hydee.ydjia.merchantcustomer.util.BeanCopyUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType.CART_FULL;
import static cn.hydee.ydjia.merchantcustomer.feign.enums.StatusEnums.ENABLING;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * 内购商城购物车业务
 *
 * <AUTHOR>
 * @Date 2024-02-19 16:43
 * @Version 1.0
 **/

@Slf4j
@Service
public class InPurchaseCartServiceImpl implements InPurchaseCartService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Resource
    private CartConfiguration cartConfiguration;

    @Resource
    private ShoppingCartService shoppingCartService;

    @Resource
    private PromoteClient promoteClient;

    @Resource
    private OrderActivityClient orderActivityClient;

    @Resource
    private StoreClient storeClient;

    @Resource
    private SPMerchantQueryClient spMerchantQueryClient;

    @Resource
    private MerchantSwitchService merchantSwitchService;

    @Resource
    private MallMerchantConfigService mallMerchantConfigService;

    @Resource
    private CommonStoreClientWrapperService commonStoreClientWrapperService;

    @Resource
    private YdjStoreService ydjStoreService;

    @Resource
    private OrderInfoClient orderInfoClient;

    @Resource
    private MemberInfoClient memberInfoClient;

    @Resource
    private EmployeeClientAdapter employeeClientAdapter;

    @Value("${order-slideshow.time-day:3}")
    private Long timeDay;

    @Value("${order-slideshow.count:300}")
    private Integer slideShowCount;
    private JedisPoolConfig getMallRedisConfiguration;

    @Resource
    private RedisService redisService;

    // 旗舰店信息
    private final String CENTER_STORE_KEY = "in_purchase:center_store";

    //门店自提消息模板id
    @Value("${storeSelf.messageTemplateId:YBth-_j8OjdQG_L6gSZrtGA_3T3x28qKez60mG6AFX4}")
    private String storeSelfMessageTemplateId;

    public static String buildHashKey(String storeId, String spCode, String specId) {
        if (StringUtils.isEmpty(spCode)) {
            return specId +
                    LocalConst.SPLITER_UNDERLINE +
                    storeId;
        } else {
            return specId +
                    LocalConst.SPLITER_UNDERLINE +
                    spCode;
        }
    }

    /**
     * 以merCode:userId 为购物车唯一键
     */
    public static String buildCartCacheKey(String merCode, String userId) {
        return LocalConst.REDIS_IN_PURCHASE_CART +
                merCode +
                LocalConst.COLON_SPLIT +
                userId;
    }

    private static int integerToInt(Integer var) {
        return var != null ? var : 0;
    }

    /**
     * 购物车商品添加
     *
     * @param cartDTO
     * @return
     */
    @Override
    public ActivityIntegralCartCommonRespDTO add(InPurchaseCartCommodityRequest cartDTO) {
        // 初始化参数
        cartDTO.init();

        // 查询用户购物车所有商品,判断当前商品是否在购物车,不存在则新增并添加,存在则更新加购数量
        ActivityIntegralCartCommodityDTO processCartCommodity;

        ActivityIntegralCartCommodityDTO cacheCartCommodityDTO =
                getCommodity(cartDTO.buildRedisKey(), buildHashKey(cartDTO.getStoreId(), cartDTO.getSpCode(), cartDTO.getSpecId()));

        if (cartDTO.openLock()) {
            processCartCommodity = SpringContextHolder.getBean(InPurchaseCartServiceImpl.class).processCommodityNum(cartDTO);
        } else {
            processCartCommodity = processCommodityNum(cartDTO);
        }

        List<ActivityIntegralCartCommodityDTO> allCartCommodityList = getUserCartAllCommodity(buildCartCacheKey(cartDTO.getMerCode(), cartDTO.getUserId()));
        List<ActivityIntegralCartCommodityDTO> cartCommodityList = getCommodityList(cartDTO, allCartCommodityList);
        // 组装参数,刷新变更数量的门店下商品,并判断个人限购数量、门店剩余库存
        CartCommodityHandleContext context = BeanCopyUtil.copy(cartDTO, CartCommodityHandleContext::new, (s, t) -> {
            t.setAction(1);
            if (processCartCommodity != null) {
                t.setProcessCommodityList(cartCommodityList);
                t.setStoreIds(Lists.newArrayList(processCartCommodity.getStoreId()));
            }
        });
        try {
            return buildCartCommonResponse(context);
        } catch (Exception e) {
            log.info("内购新增报错删除当条缓存");
            if (ObjectUtil.isNotEmpty(cacheCartCommodityDTO)) {
                putCommodity(buildCartCacheKey(cartDTO.getMerCode(), cartDTO.getUserId()), buildHashKey(cartDTO.getStoreId(), cartDTO.getSpCode(), cartDTO.getSpecId()), cacheCartCommodityDTO);
            }
            throw e;
        }
    }

    /**
     * 获取购物车商品列表
     *
     * @param merCode     商户编码
     * @param userId      用户ID
     * @param appletAppId 小程序ID
     * @return
     */
    @Override
    public ActivityIntegralCartCommonRespDTO get(String merCode, String userId, String appletAppId) {
        List<ActivityIntegralCartCommodityDTO> cartCommodityList = getUserCartAllCommodity(buildCartCacheKey(merCode, userId));
        if (CollectionUtils.isEmpty(cartCommodityList)) {
            return null;
        }

        if (StringUtils.isNotBlank(appletAppId)) {
            // 集团化处理
            CartCollectivizationHandlerParamDTO cartCollectivizationHandlerParamDTO = new CartCollectivizationHandlerParamDTO();
            cartCollectivizationHandlerParamDTO.setMerCode(merCode);
            cartCollectivizationHandlerParamDTO.setAppletAppId(appletAppId);
            cartCommodityList = this.handleCollectivization(cartCollectivizationHandlerParamDTO, cartCommodityList);
            if (CollectionUtils.isEmpty(cartCommodityList)) {
                return null;
            }
        }

        CartCommodityHandleContext context = new CartCommodityHandleContext();
        context.setMerCode(merCode);
        context.setUserId(userId);
        context.setAction(0);
        context.setProcessCommodityList(cartCommodityList);
        context.setStoreIds(context.getProcessCommodityList().parallelStream()
                .map(ActivityIntegralCartCommodityDTO::getStoreId).distinct().collect(toList()));

        return buildCartCommonResponse(context);
    }

    @Override
    public Boolean delete(ActivityIntegralCartCommodityDeleteRequest request) {
        log.info("[deleteCommodity] {}", request);
        if (request.isAllDelete()) {
            deleteAllCommodity(buildCartCacheKey(request.getMerCode(), request.getUserId()));
        } else {
            request.getSpecStores().forEach(specStore ->
                    deleteCommodity(buildCartCacheKey(request.getMerCode(), request.getUserId()),
                            buildHashKey(specStore.getStoreId(), specStore.getSpCode(), specStore.getSpecId())));
        }
        return true;
    }

    @Override
    public ActivityIntegralCartCommonRespDTO choose(ActivityIntegralCartCommodityChooseRequest request) {
        List<String> specStoreJoinList = request.getSpecStores()
                .parallelStream().map(specStore -> buildHashKey(specStore.getStoreId(), specStore.getSpCode(), specStore.getSpecId()))
                .collect(toList());
        String cacheKey = buildCartCacheKey(request.getMerCode(), request.getUserId());
        Map<String, ActivityIntegralCartCommodityDTO> allCartCommodityMap =
                getUserCartAllCommodityMap(cacheKey);

        if (request.isEditFlag()) {
            // 通过编辑的方式勾选, 保留入参中勾选状态的商品,其余全部取消勾选
            allCartCommodityMap.forEach((key, commodityDTO) -> {
                boolean flag = false;
                if (specStoreJoinList.contains(key)) {
                    if (commodityDTO.getChoseFlag().equals(0)) {
                        flag = true;
                        commodityDTO.setChoseFlag(1);
                    }
                } else {
                    if (commodityDTO.getChoseFlag().equals(1)) {
                        flag = true;
                        commodityDTO.setChoseFlag(0);
                    }
                }

                if (flag) {
                    putCommodity(cacheKey, buildHashKey(commodityDTO.getStoreId(), commodityDTO.getSpCode(), commodityDTO.getSpecId()), commodityDTO);
                }
            });

        } else {
            //单选
            request.getSpecStores().forEach(specStore ->
                    Optional.ofNullable(allCartCommodityMap.get(buildHashKey(specStore.getStoreId(), specStore.getSpCode(), specStore.getSpecId())))
                            .ifPresent(commodityDTO -> {
                                if (!commodityDTO.getChoseFlag().equals(request.getChoseFlag())) {
                                    commodityDTO.setChoseFlag(request.getChoseFlag());
                                    String hashKey = buildHashKey(commodityDTO.getStoreId(), specStore.getSpCode(), commodityDTO.getSpecId());
                                    putCommodity(cacheKey, hashKey, commodityDTO);
                                    allCartCommodityMap.put(hashKey, commodityDTO);
                                }
                            }));
        }
        List<String> storeIds = request.getSpecStores().parallelStream()
                .map(ActivityIntegralCartCommodityChooseRequest.SpecStore::getStoreId).distinct().collect(toList());
        CartCommodityHandleContext context = BeanCopyUtil.copy(request, CartCommodityHandleContext::new, (s, t) -> {
            t.setAction(2);
            t.setStoreIds(storeIds);
            t.setProcessCommodityList(Lists.newArrayList(allCartCommodityMap.values()).parallelStream()
                    .filter(cartCommodityDTO -> storeIds.contains(cartCommodityDTO.getStoreId())).collect(toList()));
        });

        return buildCartCommonResponse(context);
    }

    @Override
    public Long getCount(String merCode, String userId, String appletAppId) {
        if (StringUtils.isBlank(appletAppId)) {
            return getUserCartCommodityNum(buildCartCacheKey(merCode, userId));
        }

        // 集团化处理
        MerchantCollectivizationParamDTO merchantCollectivizationParamDTO = new MerchantCollectivizationParamDTO();
        if (mallMerchantConfigService.checkMerchantCollectivizationAndNoBound(merCode, appletAppId, merchantCollectivizationParamDTO)) {
            // 集团化套餐开启且非总部小程序未绑定机构
            return 0L;
        }
        if (BooleanUtils.isNotTrue(merchantCollectivizationParamDTO.getPackageMark())
                || BooleanUtils.isNotTrue(merchantCollectivizationParamDTO.getNonGroupApplet())) {
            // 未开通套餐或者是总部小程序
            return getUserCartCommodityNum(buildCartCacheKey(merCode, userId));
        }
        List<ActivityIntegralCartCommodityDTO> cartCommodityList = getUserCartAllCommodity(buildCartCacheKey(merCode, userId));
        if (CollectionUtils.isEmpty(cartCommodityList)) {
            return 0L;
        }

        // 门店ID
        List<String> o2oStoreIds = cartCommodityList.stream().filter(o -> StringUtils.isNotBlank(o.getStoreId())).map(ActivityIntegralCartCommodityDTO::getStoreId).distinct().collect(toList());
        if (CollectionUtils.isEmpty(o2oStoreIds)) {
            return 0L;
        }

        // 根据分公司编码查询门店
        StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
        storeListReqDTO.setMerCode(merCode);
        storeListReqDTO.setOnlineStatus(YesOrNoType.YES.getCode());
        storeListReqDTO.setPageFlag(LocalConst.STATUS_ONE);
        storeListReqDTO.setList(o2oStoreIds);
        storeListReqDTO.setAppletAppId(appletAppId);
        storeListReqDTO.setMerchantCollectivizationParamDTO(merchantCollectivizationParamDTO);
        PageDTO<StoreListResDTO> storeListResDTOPageDTO = commonStoreClientWrapperService.queryStoreList(storeListReqDTO);
        if (Objects.isNull(storeListResDTOPageDTO) || CollectionUtils.isEmpty(storeListResDTOPageDTO.getData())) {
            // 无有效门店
            return 0L;
        }

        List<String> effectiveStoreIds = storeListResDTOPageDTO.getData().stream().map(StoreListResDTO::getId).distinct().collect(toList());
        List<ActivityIntegralCartCommodityDTO> activityIntegralCartCommodityDTOS = cartCommodityList.stream().filter(o -> StringUtils.isNotBlank(o.getStoreId()) && effectiveStoreIds.contains(o.getStoreId())).collect(toList());
        if (CollectionUtils.isEmpty(activityIntegralCartCommodityDTOS)) {
            return 0L;
        }
        return (long) activityIntegralCartCommodityDTOS.size();
    }

    /**
     * 用于下单后扣减购物车商品数量
     *
     * @param request
     * @return
     */
    @Override
    public Boolean reduceCartCommodityCount(ActivityIntegralCartCommodityReduceRequest request) {
        request.getStoreSpecList().forEach(storeSpec ->
                this.add(BeanCopyUtil.copy(storeSpec, InPurchaseCartCommodityRequest::new, (s, t) -> {
                    t.setCount(Math.abs(s.getCount()) * -1);
                    t.setMerCode(request.getMerCode());
                    t.setUserId(request.getUserId());
                    t.setSpCode(s.getSpCode());
                    t.setRefreshCart(false);
                })));
        return null;
    }

    /**
     * 查询用户所在商户购物车加购商品清单
     * 根据门店查询，不包含赠品、换购商品
     *
     * @param reqDTO
     * @return
     */
    @Override
    public CartSingleStoreSpecDTO getUserGoodsListByStore(CartSingleStoreReqDTO reqDTO) {
        log.info("getUserGoodsListByStore request:{}", JSON.toJSONString(reqDTO));
        CartSingleStoreSpecDTO cartSingleStoreSpec = new CartSingleStoreSpecDTO();
        cartSingleStoreSpec.setMerCode(reqDTO.getMerCode());
        cartSingleStoreSpec.setStoreId(reqDTO.getStoreId());
        // 查询旗舰店信息
        StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
        storeListReqDTO.setMerCode(reqDTO.getMerCode());
        StoreListResDTO centerStore = ydjStoreService.queryCenterStore(storeListReqDTO, true);
        if (centerStore != null && reqDTO.getStoreId().equals(centerStore.getId())) {
            reqDTO.setStoreId(LocalConst.MERCHANT_B2C_FIXED_STORE);
        }
        List<ActivityIntegralCartCommodityDTO> redisSpecs = getUserCartAllCommodity(reqDTO.getInPurchaseRedisKey());
        redisSpecs = redisSpecs.stream()
                .filter(o -> ObjectUtil.isNotEmpty(o.getStoreId()) && reqDTO.getStoreId().equals(o.getStoreId()))
                .collect(toList());
        if (CollectionUtils.isEmpty(redisSpecs)) {
            return cartSingleStoreSpec;
        }
        List<Long> specIds = redisSpecs.stream().map(o -> Long.valueOf(o.getSpecId())).distinct().collect(toList());
        List<String> storeIds = redisSpecs.stream().map(ActivityIntegralCartCommodityDTO::getStoreId).distinct().collect(toList());
        storeIds.add(cartSingleStoreSpec.getStoreId());
        PageDTO<StoreSpec> storeSpecPageResp = shoppingCartService.queryStoreSpecByClient(specIds, storeIds, reqDTO.getMerCode(),
                CommodityQueryAssembleParamDTO.builder().hasShare(false).replaceCommodityNameFlag(true)
                        .build());
        log.info("getUserGoodsListByStore queryStoreSpecByClient request:{},response:{}", JSON.toJSONString(reqDTO), JSON.toJSONString(storeSpecPageResp));
        //商品中台无结果
        if (storeSpecPageResp == null || CollectionUtils.isEmpty(storeSpecPageResp.getData())) {
            return cartSingleStoreSpec;
        }
        List<CartSingleSpecDTO> resultList;
        //中台查到门店商品规格
        Map<String, StoreSpec> clientSpecMap = storeSpecPageResp.getData().stream().collect(Collectors.toMap(StoreSpec::getSpecId, a -> a, (k1, k2) -> k1));
        Map<String, CartSingleSpecDTO> cartSingleSpecMap = Maps.newHashMap();
        for (ActivityIntegralCartCommodityDTO redisSpec : redisSpecs) {
            CartSingleSpecDTO cartSingleSpec = cartSingleSpecMap.get(redisSpec.getSpecId());
            //如果已存在，即多门店加购，更新数量
            if (cartSingleSpec != null) {
                cartSingleSpec.setCartAddCount(cartSingleSpec.getCartAddCount() + redisSpec.getCount());
            } else {
                cartSingleSpec = new CartSingleSpecDTO();
                StoreSpec spec = clientSpecMap.get(redisSpec.getSpecId());
                if (spec == null) {
                    continue;
                }
                BeanUtils.copyProperties(spec, cartSingleSpec);
                cartSingleSpec.setCartAddCount(redisSpec.getCount());
                cartSingleSpec.setCartAddTime(DateUtil.format(redisSpec.getAddTime() != null ? new Date(redisSpec.getAddTime()) : new Date(), DateUtil.CN_LONG_FORMAT));
                if (!CollectionUtils.isEmpty(spec.getSpecSkuList())) {
                    cartSingleSpec.setSkuValue(spec.getSpecSkuList().get(0).getSkuValue());
                }
            }
            cartSingleSpecMap.put(redisSpec.getSpecId(), cartSingleSpec);
        }
        // map -> list
        resultList = new ArrayList<>(cartSingleSpecMap.values());
        //按照加购时间逆序
        resultList.sort(Comparator.comparing(CartSingleSpecDTO::getCartAddTime).reversed());
        cartSingleStoreSpec.setCartSingleSpecDTOs(resultList);
        log.info("getUserGoodsListByStore response:{}", JSON.toJSONString(cartSingleStoreSpec));
        return cartSingleStoreSpec;
    }

    @Override
    public List<OrderSlideShowResDTO> getOrderListByErpCode(String erpCode, String orderType) {
        log.info("内购商城 获取商品订单,erpCode:{}", erpCode);
        GetRecentOrderInfoReqDTO dto = new GetRecentOrderInfoReqDTO();
        dto.setErpCode(erpCode);
        dto.setOrderType(orderType);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算N个月前的日期
        LocalDate previousDate = currentDate.minusMonths(timeDay);
        dto.setBeginTime(previousDate);
        dto.setEndTime(currentDate);
        ResponseBase<List<GetRecentOrderInfoResDTO>> responseBase = orderInfoClient.getOrderListByErpCode(dto);
        if (!responseBase.checkSuccess() || !CollUtil.isNotEmpty(responseBase.getData())) {
            return Collections.emptyList();
        }
        List<String> memberPhones = responseBase.getData().stream().map(GetRecentOrderInfoResDTO::getMemberPhone).collect(toList());
        ResponseBase<List<EmpDTO>> base = employeeClientAdapter.listEmpByMobiles(LocalConst.COMMON_MERCODE, memberPhones);
        if (!base.checkSuccess() || !CollUtil.isNotEmpty(base.getData())) {
            return Collections.emptyList();
        }
        // 存在一个手机号 两个员工同时用的情况
        Map<String, List<EmpDTO>> empMap = base.getData().stream().collect(Collectors.groupingBy(EmpDTO::getMobile));
        List<OrderSlideShowResDTO> resultList = Lists.newArrayList();
        responseBase.getData().forEach(o -> {
            List<EmpDTO> empDTOS = empMap.get(o.getMemberPhone());
            if (CollUtil.isEmpty(empDTOS)) {
                return;
            }
            EmpDTO empDTO = empDTOS.get(0);
            OrderSlideShowResDTO resDto = new OrderSlideShowResDTO();
            resDto.setEmpCode(empDTO.getEmpCode());
            resDto.setCount(o.getCommodityCount());
            resultList.add(resDto);
        });
        return resultList.size() >= slideShowCount ? resultList.subList(0, slideShowCount) : resultList;
    }

    @Override
    public TransferCommodityRemindResp getTransferCommodityRemind(String merCode, TransferCommodityRemindReq req) {
        log.info("获取到货商品提醒的商品信息参数:{}",JSONObject.toJSONString(req));
        if(Objects.isNull(req) || CollUtil.isEmpty(req.getParams())){
            return null;
        }
        if(OrderType.IN_PURCHASE.getCode().equals(req.getOrderType())){
            //内购订单校验活动库存
            List<PurchasePmtReqDTO> purchasePmtReqDTOList = new ArrayList<>();
            for (CommodityParam param : req.getParams()) {
                PurchasePmtReqDTO purchasePmt = new PurchasePmtReqDTO();
                purchasePmt.setMerCode(merCode);
                purchasePmt.setSpecId(Long.valueOf(param.getSpecId()));
                purchasePmt.setStoreId(param.getStoreId());
                purchasePmtReqDTOList.add(purchasePmt);
            }
            ResponseBase<List<PurchaseCommodityRespDTO>> responseBase = promoteClient.searchPurchaseCommodityBySpecIds(purchasePmtReqDTOList);
            log.info("获取到货商品提醒的商品信息->查询商品活动信息结果:{}",JSONObject.toJSONString(responseBase));
            if(Objects.isNull(responseBase) || !responseBase.checkSuccess() || Objects.isNull(responseBase.getData())){
                return null;
            }
            Map<String,Integer> activityStockLow = new HashMap<>();
            Map<String, Integer> specCount = req.getParams().stream().collect(toMap(CommodityParam::getSpecId, CommodityParam::getCommodityCount));
            for (PurchaseCommodityRespDTO purchaseCommodityRespDTO : responseBase.getData()) {
                Integer count = specCount.get(String.valueOf(purchaseCommodityRespDTO.getSpecId()));
                if(count > purchaseCommodityRespDTO.getSurplusStock()){
                    activityStockLow.put(purchaseCommodityRespDTO.getName(),purchaseCommodityRespDTO.getSurplusStock());
                }
            }
            if(CollUtil.isNotEmpty(activityStockLow)){
                List<String> tips = new ArrayList<>();
                for (Entry<String, Integer> entry : activityStockLow.entrySet()) {
                    tips.add(entry.getKey()+"最多只能购买"+entry.getValue()+"件");
                }
                throw WarnException.builder().code(ErrorType.CART_LIMIT_NUM.getCode())
                    .tipMessage(String.join("；", tips))
                    .build();
            }
        }

        //校验门店库存
        List<Long> specIds = req.getParams().stream().map(c -> Long.parseLong(c.getSpecId())).distinct().collect(toList());
        List<String> storeIds = req.getParams().stream().map(CommodityParam::getStoreId).filter(storeId -> !StringUtils.isEmpty(storeId)).distinct().collect(toList());
        List<String> spCodes = req.getParams().stream().map(CommodityParam::getSpCode).distinct().collect(toList());
        Map<String, String> spSpecCode = new HashMap<>();
        req.getParams().forEach(o -> {
            if (!StringUtils.isEmpty(o.getSpCode())) {
                spSpecCode.put(o.getSpecId(), o.getSpCode());
            }
        });
        PageDTO<StoreSpec> pageDTO = shoppingCartService.querySpecByClient(specIds, storeIds, spCodes, spSpecCode, merCode,
            CommodityQueryAssembleParamDTO.builder().hasShare(true).replaceCommodityNameFlag(false).replacePrescriptionDrugPicFlag(false).build());
        log.info("获取到货商品提醒的商品信息->查询门店商品信息结果:{}",JSONObject.toJSONString(pageDTO));
        if(Objects.isNull(pageDTO) || CollUtil.isEmpty(pageDTO.getData())){
            return null;
        }
        Map<String, Integer> storeSpecCountMap = req.getParams().stream().collect(Collectors.toMap(param -> param.getStoreId() +"_"+ param.getSpecId(), CommodityParam::getCommodityCount, (existingValue, newValue) -> existingValue));
        List<StoreSpec> storeSpecs = pageDTO.getData();
        Map<String, StoreSpec> storeSpecMap = storeSpecs.stream().collect(Collectors.toMap(storeSpec -> storeSpec.getStoreId() + "_" + storeSpec.getSpecId(), Function.identity(), (existingValue, newValue) -> existingValue));
        TransferCommodityRemindResp resp = new TransferCommodityRemindResp();
        resp.setStoreSelfMessageTemplateId(storeSelfMessageTemplateId);
        List<CommodityInfo> commodityInfos = new ArrayList<>();
        for (Entry<String, Integer> entry : storeSpecCountMap.entrySet()) {
            StoreSpec storeSpec = storeSpecMap.get(entry.getKey());
            if(Objects.isNull(storeSpec)){
                continue;
            }
            if(storeSpec.getStock() < entry.getValue()){
                //库存不足
                CommodityInfo commodityInfo = new CommodityInfo();
                commodityInfo.setCommodityName(storeSpec.getName());
                commodityInfo.setCommodityPicture(storeSpec.getPicUrl());
                commodityInfo.setCommoditySpec(storeSpec.getSpecValue());
                commodityInfo.setCommodityCount(entry.getValue());
                commodityInfo.setCommodityStock(storeSpec.getStock());
                commodityInfos.add(commodityInfo);
            }
        }
        resp.setCommodityInfos(commodityInfos);
        return resp;
    }

    private List<ActivityIntegralCartCommodityDTO> getCommodityList(InPurchaseCartCommodityRequest cartDTO, List<ActivityIntegralCartCommodityDTO> allCartCommodityList) {
        if (ObjectUtil.isNotEmpty(cartDTO.getSpCode())) {
            return allCartCommodityList.stream().filter(c -> !StringUtils.isEmpty(c.getSpCode()) && c.getSpCode().equals(cartDTO.getSpCode())).collect(toList());
        } else if (ObjectUtil.isNotEmpty(cartDTO.getStoreId())) {
            return allCartCommodityList.stream().filter(c -> !StringUtils.isEmpty(c.getStoreId()) && c.getStoreId().equals(cartDTO.getStoreId())).collect(toList());
        } else {
            return Collections.emptyList();
        }
    }


    /**
     * 集团化处理（数据隔离）
     *
     * @param cartCollectivizationHandlerParamDTO
     * @param cartCommodityList
     * @return java.util.List<cn.hydee.ydjia.merchantcustomer.dto.resp.ActivityIntegralCartCommodityDTO>
     * <AUTHOR>
     * @date 2023/4/12
     */
    private List<ActivityIntegralCartCommodityDTO> handleCollectivization(CartCollectivizationHandlerParamDTO cartCollectivizationHandlerParamDTO, List<ActivityIntegralCartCommodityDTO> cartCommodityList) {
        if (CollectionUtils.isEmpty(cartCommodityList) || StringUtils.isBlank(cartCollectivizationHandlerParamDTO.getAppletAppId())) {
            return cartCommodityList;
        }
        String merCode = cartCollectivizationHandlerParamDTO.getMerCode();
        String appletAppId = cartCollectivizationHandlerParamDTO.getAppletAppId();

        MerchantCollectivizationParamDTO merchantCollectivizationParamDTO = new MerchantCollectivizationParamDTO();
        // 查询过套餐标记
        merchantCollectivizationParamDTO.setInquiredPackageFlag(true);
        if (!mallMerchantConfigService.checkMerchantCollectivizationPackageMark(merCode)) {
            // 未开启集团化套餐
            return cartCommodityList;
        }
        // 集团化套餐开启
        merchantCollectivizationParamDTO.setPackageMark(true);
        // 校验过是否总部小程序
        merchantCollectivizationParamDTO.setCheckedGroupAppletFlag(true);
        if (mallMerchantConfigService.checkAppIdEqualsGroupAppId(merCode, appletAppId)) {
            // 总部小程序
            return cartCommodityList;
        }
        merchantCollectivizationParamDTO.setNonGroupApplet(true);
        // 查询过绑定的分公司
        merchantCollectivizationParamDTO.setInquiredCompanyCodeFlag(true);
        List<String> appletBoundBranchCodes = mallMerchantConfigService.getAppletBoundBranchCodes(merCode, appletAppId);
        if (CollectionUtils.isEmpty(appletBoundBranchCodes)) {
            return null;
        }
        merchantCollectivizationParamDTO.setCompanyCodeList(appletBoundBranchCodes);

        // 门店ID
        List<String> o2oStoreIds = cartCommodityList.stream().filter(o -> StringUtils.isNotBlank(o.getStoreId())).map(ActivityIntegralCartCommodityDTO::getStoreId).distinct().collect(toList());
        if (CollectionUtils.isEmpty(o2oStoreIds)) {
            return null;
        }

        // 根据分公司编码查询门店
        StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
        storeListReqDTO.setMerCode(cartCollectivizationHandlerParamDTO.getMerCode());
        storeListReqDTO.setOnlineStatus(YesOrNoType.YES.getCode());
        storeListReqDTO.setPageFlag(LocalConst.STATUS_ONE);
        storeListReqDTO.setList(o2oStoreIds);
        storeListReqDTO.setAppletAppId(appletAppId);
        storeListReqDTO.setMerchantCollectivizationParamDTO(merchantCollectivizationParamDTO);
        PageDTO<StoreListResDTO> storeListResDTOPageDTO = commonStoreClientWrapperService.queryStoreList(storeListReqDTO);
        if (Objects.isNull(storeListResDTOPageDTO) || CollectionUtils.isEmpty(storeListResDTOPageDTO.getData())) {
            // 无有效门店
            return null;
        }

        List<String> effectiveStoreIds = storeListResDTOPageDTO.getData().stream().map(StoreListResDTO::getId).distinct().collect(toList());
        return cartCommodityList.stream().filter(o -> StringUtils.isNotBlank(o.getStoreId()) && effectiveStoreIds.contains(o.getStoreId())).collect(toList());
    }


    private ActivityIntegralCartCommonRespDTO buildCartCommonResponse(CartCommodityHandleContext context) {
        // 通过规格id和门店id查询商品中台, 清理购物车内无效的商品,针对没有库存的商品,更新状态
        List<StoreSpec> storeSpecList = queryAndCheckStoreSpec(context);
        log.info("buildCartCommonResponse storeSpecList:{}", JSON.toJSONString(storeSpecList));
        if (CollectionUtils.isEmpty(storeSpecList)) {
            return null;
        }

        filteringOfflineStores(context);
        log.info("buildCartCommonResponse processCommodityList:{}", JSON.toJSONString(context.getProcessCommodityList()));
        if (CollectionUtils.isEmpty(context.getProcessCommodityList())) {
            return null;
        }

        // 查询促销中台,内购商品活动库存,限购数量等数据
        queryPurchaseCommodityList(context);
        log.info("查询内购商品规格：{}", JSON.toJSONString(context.getPurchaseCommodityRespDTOList()));
        // 查询用户商品购买数量
        Map<Long, List<CountMemberSpecResDTO>> memberSpecBuyRecordMap = queryMemberSpecBuyRecord(context.getPurchaseCommodityRespDTOList(), context);
        //对于购物车查询接口,需针对所有商品校验数量(库存数量、个人最大兑换数量)
        //非购物车查询接口,针对变更的商品,校验商品数量(库存数量、个人最大兑换数量)
        Map<String, ActivityIntegralCartCommodityDTO> cartCommodityMap =
                context.getProcessCommodityList().parallelStream().collect(Collectors.toMap(
                        commodityDTO -> buildHashKey(commodityDTO.getStoreId(), commodityDTO.getSpCode(), commodityDTO.getSpecId()),
                        Function.identity(),
                        (key1, key2) -> key1));
        Map<String, StoreSpec> storeSpecMap =
                storeSpecList.parallelStream().collect(Collectors.toMap(
                        storeSpec -> buildHashKey(storeSpec.getStoreId(), storeSpec.getSpCode(), storeSpec.getSpecId()),
                        Function.identity(),
                        (key1, key2) -> key1));
        Map<Long, PurchaseCommodityRespDTO> purchaseSpecMap = context.getPurchaseCommodityRespDTOList().parallelStream().collect(Collectors.toMap(
                PurchaseCommodityRespDTO::getSpecId,
                Function.identity(),
                (key1, key2) -> key1));

        List<ActivityIntegralCartCommodityRespDTO> cartCommodityRespList = Lists.newArrayList();

        String cacheKey = buildCartCacheKey(context.getMerCode(), context.getUserId());
        cartCommodityMap.forEach((key, commodityDTO) -> {
            StoreSpec storeSpec = storeSpecMap.get(key);
            PurchaseCommodityRespDTO purchaseSpecDTO = purchaseSpecMap.get(Long.valueOf(commodityDTO.getSpecId()));
            log.info("storeSpec dto:{},purchaseSpecDTO:{}", JSON.toJSONString(storeSpec), JSON.toJSONString(purchaseSpecDTO));

            if (storeSpec == null || purchaseSpecDTO == null) {
                log.info("[DeleteCommodity] storeSpec and purchaseSpecDTO both null cacheKey={} - {}", cacheKey, key);
                deleteCommodity(cacheKey, key);
                commodityDTO.setStatus(0);
                return;
            }
            // 深拷贝一个新的purchaseSpecDTO
            String purchaseSpec = JSON.toJSONString(purchaseSpecDTO);
            PurchaseCommodityRespDTO newPurchaseSpecDTO = JSON.parseObject(purchaseSpec, PurchaseCommodityRespDTO.class);

            // 修改限购逻辑
            dealLimitAmount(commodityDTO, memberSpecBuyRecordMap, newPurchaseSpecDTO, storeSpec, cacheKey, key);
            if (!Objects.equals(newPurchaseSpecDTO.getLimitType(), LimitTypeEnum.LIMIT_NO.getCode()) && newPurchaseSpecDTO.getLimitAmount() == 0 && context.getAction() == 0) {
                log.info("查询购物车列表时，商品限购类型为不限购，但是限购数量为0，删除");
                deleteCommodity(cacheKey, key);
                return;
            }
            log.info("修改限购逻辑后的purchaseSpecDTO:{}", JSON.toJSONString(newPurchaseSpecDTO));
            //1.3.1版本需求 内购O2O门店备货中状态
            //内购商城O2O店铺添加商品到购物车时不校验门店库存
            boolean isO2OStore = false;
            StoreResDTO centerStore = getCenterStore(commodityDTO.getMerCode());
            if(ObjectUtil.isNull(centerStore) || !commodityDTO.getStoreId().equals(centerStore.getId())){
                // 如果是非旗舰店 就是O2O店铺
                isO2OStore = true;
            }
            // 校验起售
            checkMinSaleNum(commodityDTO, newPurchaseSpecDTO, cacheKey, key,context.getAction(),isO2OStore);
            Integer stock = storeSpec.getStock();
            // 当门店库存为0时,并且有共享库存,则取共享库存,否则取门店库存
            if (stock == 0 && (Objects.nonNull(storeSpec.getSharedStock()) && storeSpec.getSharedStock() != 0)) {
                stock = storeSpec.getSharedStock();
            }
            // 获取门店库存与活动库存二者的最小库存
            int minStock = Math.min(integerToInt(stock), integerToInt(newPurchaseSpecDTO.getSurplusStock()));

            // 商品为下架状态 或者 该内购商品对应的门店未参与,标记为下架,不可勾选
            if (newPurchaseSpecDTO.getStatus() == 0 || Boolean.FALSE.equals(newPurchaseSpecDTO.getIsPartition())) {
                minStock = Math.min(minStock, newPurchaseSpecDTO.getLimitAmount());
                commodityDTO.setStatus(0);
                commodityDTO.setChoseFlag(0);
                putCommodity(cacheKey, key, commodityDTO);
            } else {
                // 当门店库存与活动库存之间有一个为0,则商品为库存不足,不可勾选
                if (minStock <= 0 || minStock < newPurchaseSpecDTO.getMinSellNum()) {
                    commodityDTO.setStatus(3);
                    commodityDTO.setChoseFlag(0);
                    putCommodity(cacheKey, key, commodityDTO);
                } else {
                    minStock = checkAvailableStock(newPurchaseSpecDTO, commodityDTO, context, minStock, key, isO2OStore);
                }
            }

            // 构建返回的数据,并根据首次加购时间排序
            Integer finalStock = stock;
            BigDecimal price;
            if (YesOrNoType.NO.getCode().equals(newPurchaseSpecDTO.getUsePrice())) {
                price = newPurchaseSpecDTO.getActPrice();
            } else {
                price = ObjectUtil.isNotEmpty(storeSpec.getEmployPrice()) ? storeSpec.getEmployPrice() : storeSpec.getPrice();
            }
            BigDecimal finalPrice = ObjectUtil.isNotEmpty(price) ? price : BigDecimal.ZERO;
            ActivityIntegralCartCommodityRespDTO cartCommodityRespDTO = BeanCopyUtil.copy(commodityDTO, ActivityIntegralCartCommodityRespDTO::new,
                    (s, t) -> {
                        t.copyStoreSpecParams(storeSpec);
                        t.setStatus(s.getStatus());
                        t.setIntegral(0);
                        t.setPrice(finalPrice);
                        t.setExchangeLimitNum(newPurchaseSpecDTO.getLimitAmount());
                        t.setStock(finalStock);
                        t.setLeftAmount(newPurchaseSpecDTO.getSurplusStock());
                    });
            cartCommodityRespDTO.setLimitNum(minStock);
            BigDecimal storePrice = storeSpec.getMPrice() != null ? storeSpec.getMPrice() : new BigDecimal(0);
            // 补贴价格 = 门店价格 - 内购价
            BigDecimal allowancePrice = storePrice.subtract(cartCommodityRespDTO.getPrice()).multiply(new BigDecimal(cartCommodityRespDTO.getCount()));
            // 如果补贴价格小于0，设置为0
            if (allowancePrice.compareTo(new BigDecimal(0)) < 0) {
                allowancePrice = BigDecimal.ZERO;
            }
            cartCommodityRespDTO.setAllowancePrice(allowancePrice);
            cartCommodityRespDTO.setIsDiscount(newPurchaseSpecDTO.getIsDiscount());
            cartCommodityRespDTO.setMinSellNum(newPurchaseSpecDTO.getMinSellNum());
            cartCommodityRespDTO.setExpiring(newPurchaseSpecDTO.getExpiring());
            cartCommodityRespList.add(cartCommodityRespDTO);
            if (log.isDebugEnabled()) {
                log.debug(" in purchase spec:{} cartCommodity:{}", newPurchaseSpecDTO, cartCommodityRespDTO);
            }
        });

        //设置服务商名称
        List<String> spCodes = new ArrayList<>();
        cartCommodityRespList.forEach(cartCommodityRespDTO -> {
            if (!spCodes.contains(cartCommodityRespDTO.getSpCode()) && !StringUtils.isEmpty(cartCommodityRespDTO.getSpCode())) {
                spCodes.add(cartCommodityRespDTO.getSpCode());
            }
        });
        if (!ObjectUtils.isEmpty(spCodes)) {
            List<SPMerchantDetailInfoResDTO> detailInfoResDTOS = spMerchantQueryClient.getSPDetailByList(spCodes).getData();
            Map<String, String> spMap = detailInfoResDTOS.stream().collect(Collectors.toMap(SPMerchantDetailInfoResDTO::getMerCode, SPMerchantDetailInfoResDTO::getMerName));
            cartCommodityRespList.forEach(cartCommodityRespDTO -> {
                if (spMap.containsKey(cartCommodityRespDTO.getSpCode())) {
                    cartCommodityRespDTO.setSpName(spMap.get(cartCommodityRespDTO.getSpCode()));
                }
            });
        }

        return buildResponse(cartCommodityRespList);
    }

    private void dealLimitAmount(ActivityIntegralCartCommodityDTO commodityDTO, Map<Long, List<CountMemberSpecResDTO>> memberSpecBuyRecordMap,
                                 PurchaseCommodityRespDTO purchaseSpecDTO, StoreSpec storeSpec, String cacheKey, String key) {
        List<CountMemberSpecResDTO> countMemberSpecResDTOS = memberSpecBuyRecordMap.get(Long.valueOf(commodityDTO.getSpecId()));
        int count = 0;
        switch (LimitTypeEnum.toEnum(purchaseSpecDTO.getLimitType())) {
            case LIMIT_NO:
                // 不限购时,限购数量为库存数量(门店库存和活动库存取最小)
                // 当门店库存为0时,并且有共享库存,则取共享库存,否则取门店库存
                Integer stock = storeSpec.getStock();
                if (stock == 0 && (Objects.nonNull(storeSpec.getSharedStock()) && storeSpec.getSharedStock() != 0)) {
                    stock = storeSpec.getSharedStock();
                }
                purchaseSpecDTO.setLimitAmount(Math.min(stock, purchaseSpecDTO.getSurplusStock()));
                break;
            case LIMIT_ORDER:
                // 每笔订单限购，取限购数量
                break;
            case LIMIT_DAY:
                // 每天限购，查询订单，并筛选当天订单，若无订单，则取限购数量，否则取限购数量-已购买数量
                if (CollUtil.isNotEmpty(countMemberSpecResDTOS)) {
                    count = countMemberSpecResDTOS.stream()
                            .filter(o -> o.getBuyDate().equals(LocalDate.now()))
                            .map(CountMemberSpecResDTO::getBuyNum)
                            .reduce(0, Integer::sum);
                }
                purchaseSpecDTO.setLimitAmount(Math.max(purchaseSpecDTO.getLimitAmount() - count, 0));
                break;
            case LIMIT_WEEK:
                // 每周限购
                LocalDate weekDate = LocalDate.now().minusWeeks(1);
                if (CollUtil.isNotEmpty(countMemberSpecResDTOS)) {
                    count = countMemberSpecResDTOS.stream()
                            .filter(o -> !o.getBuyDate().isAfter(LocalDate.now())
                                    && !o.getBuyDate().isBefore(weekDate))
                            .map(CountMemberSpecResDTO::getBuyNum)
                            .reduce(0, Integer::sum);
                }
                purchaseSpecDTO.setLimitAmount(Math.max(purchaseSpecDTO.getLimitAmount() - count, 0));
                break;
            case LIMIT_MONTH:
                // 每月限购
                LocalDate monthDate = LocalDate.now().minusMonths(1);
                if (CollUtil.isNotEmpty(countMemberSpecResDTOS)) {
                    count = countMemberSpecResDTOS.stream()
                            .filter(o -> !o.getBuyDate().isAfter(LocalDate.now())
                                    && !o.getBuyDate().isBefore(monthDate))
                            .map(CountMemberSpecResDTO::getBuyNum)
                            .reduce(0, Integer::sum);
                }
                purchaseSpecDTO.setLimitAmount(Math.max(purchaseSpecDTO.getLimitAmount() - count, 0));
                break;
            default:
                break;
        }
    }

    /**
     * 校验起售数量与库存
     *
     * @param commodityDTO
     * @param purchaseCommodityRespDTO
     * @param cacheKey
     * @param key
     */
    private void checkMinSaleNum(ActivityIntegralCartCommodityDTO commodityDTO, PurchaseCommodityRespDTO purchaseCommodityRespDTO, String cacheKey, String key, Integer action, boolean isO2OStore) {
        // 起售字段校验
        if (ObjectUtil.isEmpty(purchaseCommodityRespDTO.getMinSellNum())) {
            return;
        }

        // 1.加购数量小于起售或者不是起售数量的倍数则抛错
        if (commodityDTO.getCount() < purchaseCommodityRespDTO.getMinSellNum() || commodityDTO.getCount() % purchaseCommodityRespDTO.getMinSellNum() != 0) {
            // 加购数量小于起售或者不是起售数量的倍数则抛错
            deleteCommodity(cacheKey, key);
            throw WarnException.builder().code(ErrorType.CART_ADD_SELL_NUM_ERROR.getCode())
                    .tipMessage(String.format(ErrorType.CART_ADD_SELL_NUM_ERROR.getMsg(), purchaseCommodityRespDTO.getMinSellNum(), purchaseCommodityRespDTO.getMinSellNum())).build();
        }

        if(isO2OStore){
            //内购商城O2O订单 校验限购和活动剩余库存
            if(LimitTypeEnum.LIMIT_NO.getCode().equals(purchaseCommodityRespDTO.getLimitType())){
                //dealLimitAmount方法：如果不限购 限购数量取门店库存 这时候需要替换成商品活动剩余库存
                purchaseCommodityRespDTO.setLimitAmount(purchaseCommodityRespDTO.getSurplusStock());
            }
        }

        // 如果该商品有起售限制时,最好先判断库存是否充足
        // 2.库存与起售校验
        if (purchaseCommodityRespDTO.getStock() < purchaseCommodityRespDTO.getMinSellNum() || purchaseCommodityRespDTO.getSurplusStock() < purchaseCommodityRespDTO.getMinSellNum()) {
            deleteCommodity(cacheKey, key);
            throw WarnException.builder().code(ErrorType.STOCK_CHANGED.getCode()).
                    tipMessage(ErrorType.STOCK_CHANGED.getMsg()).build();
        }
        // 3.限购数量与起售数量的校验
        if (purchaseCommodityRespDTO.getLimitAmount() < purchaseCommodityRespDTO.getMinSellNum()) {
            deleteCommodity(cacheKey, key);
            throw WarnException.builder().code(ErrorType.CART_LIMIT_NUM.getCode())
                    .tipMessage(LimitTypeEnum.toEnum(purchaseCommodityRespDTO.getLimitType()).getType()
                            + ErrorType.CART_LIMIT_NUM.getMsg()
                            + purchaseCommodityRespDTO.getLimitAmount()
                            + "件哦!")
                    .build();
        }
        // 商品数量 大于了限购数量 并且有起售限制
        if (commodityDTO.getCount() > purchaseCommodityRespDTO.getLimitAmount()) {
            // 获取限购数量以内最大商品数
            int count = purchaseCommodityRespDTO.getLimitAmount() / purchaseCommodityRespDTO.getMinSellNum() * purchaseCommodityRespDTO.getMinSellNum();
            commodityDTO.setCount(count);
            putCommodity(cacheKey, key, commodityDTO);

            // 查询购物车时不报错
            if (action != 0) {
                throw WarnException.builder().code(ErrorType.CART_LIMIT_NUM.getCode())
                        .tipMessage(LimitTypeEnum.toEnum(purchaseCommodityRespDTO.getLimitType()).getType()
                                + ErrorType.CART_LIMIT_NUM.getMsg()
                                + purchaseCommodityRespDTO.getLimitAmount()
                                + "件哦!")
                        .build();
            }
        }
    }

    private void filteringOfflineStores(CartCommodityHandleContext context) {
        QueryStoreDTO queryStore = new QueryStoreDTO();
        queryStore.setMerCode(context.getMerCode());
        queryStore.setList(context.getStoreIds());
        queryStore.setStatus(1);
        queryStore.setOnlineStatus(1);
        queryStore.setPageSize(context.getStoreIds().size());
        ResponseBase<PageDTO<StoreResDTO>> responseBase = storeClient.queryStoreByCondition(queryStore);
        if (!responseBase.checkSuccess()) {
            log.error("queryStoreByCondition error {}", queryStore);
            return;
        }

        List<String> storeList;
        if (responseBase.getData() == null || responseBase.getData().getData() == null) {
            storeList = Lists.newLinkedList();
        } else {
            storeList = responseBase.getData().getData().parallelStream()
                    .map(StoreResDTO::getId).distinct().collect(toList());
        }

        Iterator<ActivityIntegralCartCommodityDTO> iterator = context.getProcessCommodityList().iterator();
        while (iterator.hasNext()) {
            ActivityIntegralCartCommodityDTO cartCommodityDTO = iterator.next();
            if (!storeList.contains(cartCommodityDTO.getStoreId()) && StringUtils.isEmpty(cartCommodityDTO.getSpCode())) {
                deleteCommodity(buildCartCacheKey(context.getMerCode(), context.getUserId()),
                        buildHashKey(cartCommodityDTO.getStoreId(), cartCommodityDTO.getSpCode(), cartCommodityDTO.getSpecId()));
                iterator.remove();
            }
        }
    }

    private int checkAvailableStock(PurchaseCommodityRespDTO purchaseSpecDTO,
                                    ActivityIntegralCartCommodityDTO commodityDTO,
                                    CartCommodityHandleContext context,
                                    int stock,
                                    String hashKey,
                                    boolean isO2OStore) {
        // 校验内购商品数量(库存、个人最大兑换数量)
        Integer limitNum = purchaseSpecDTO.getLimitAmount();

        if (limitNum > 0) {
//            // 获取用户在该商品规格下的购买数量
//            if (context.getMemberSpecBuyRecordMap() == null) {
//                context.setMemberSpecBuyRecordMap(queryMemberSpecBuyRecord(context.getPurchaseCommodityRespDTOList(), context));
//            }
//            Integer memberBuySpecNum = getMemberHasExchangeSpecNum(
//                    context.getMemberSpecBuyRecordMap().get(Long.valueOf(commodityDTO.getSpecId())), purchaseSpecDTO);
//            limitNum -= memberBuySpecNum;
            stock = Math.min(limitNum, stock);
            if (stock < 0) {
                stock = 0;
            }
        }

        if (!isO2OStore && commodityDTO.getCount() > stock) {
            //minStock == 0 & limitNum > 0 表示达到最大限购量,statue=2表示此商品达到最大限购数
            if (stock == 0) {
                commodityDTO.setCount(1);
                commodityDTO.setChoseFlag(0);
                commodityDTO.setStatus(purchaseSpecDTO.getLimitAmount() > 0 ? 2 : 3);
            } else {
                commodityDTO.setStatus(1);
                commodityDTO.setCount(stock);
            }
            if (ObjectUtil.isNotEmpty(purchaseSpecDTO.getMinSellNum()) && stock != 0) {
                commodityDTO.setStatus(1);
                // 获取库存数量以内最大商品数
                int count = stock / purchaseSpecDTO.getMinSellNum() * purchaseSpecDTO.getMinSellNum();
                commodityDTO.setCount(count);
                putCommodity(buildCartCacheKey(context.getMerCode(), context.getUserId()), hashKey, commodityDTO);
                throw WarnException.builder().code(ErrorType.CART_LIMIT_NUM.getCode())
                        .tipMessage(LimitTypeEnum.toEnum(purchaseSpecDTO.getLimitType()).getType() + ErrorType.CART_LIMIT_NUM.getMsg() + purchaseSpecDTO.getLimitAmount() + "件哦!").build();
            }
            putCommodity(buildCartCacheKey(context.getMerCode(), context.getUserId()), hashKey, commodityDTO);

        } else {

            // 加购后商品下架或售罄, status不为1, 等商品再次上架或加库存后,此时需检查status
            if (commodityDTO.getStatus() != 1) {
                commodityDTO.setStatus(1);
                putCommodity(buildCartCacheKey(context.getMerCode(), context.getUserId()), hashKey, commodityDTO);
            }
        }
        //对于内购的O2O订单 取活动剩余库存和限购数量中最小的
        if(isO2OStore){
            stock = Integer.valueOf(0).equals(limitNum)?purchaseSpecDTO.getSurplusStock():Math.min(purchaseSpecDTO.getSurplusStock(),limitNum);
        }
        return stock;
    }

    private ActivityIntegralCartCommonRespDTO buildResponse(List<ActivityIntegralCartCommodityRespDTO> cartCommodityRespList) {
        List<ActivityIntegralCartStoreDTO> commodityList = new ArrayList<>();
        ActivityIntegralCartCommonRespDTO response = new ActivityIntegralCartCommonRespDTO();
        response.buildParams(cartCommodityRespList);

        List<ActivityIntegralCartCommodityRespDTO> cartCommodityRespListByStore = new ArrayList<>();
        List<ActivityIntegralCartCommodityRespDTO> cartCommodityRespListBySp = new ArrayList<>();
        cartCommodityRespList.forEach(o -> {
            if (!StringUtils.isEmpty(o.getStoreId())) {
                cartCommodityRespListByStore.add(o);
            } else {
                cartCommodityRespListBySp.add(o);
            }
        });
        if (!CollectionUtils.isEmpty(cartCommodityRespListByStore)) {
            Map<String, List<ActivityIntegralCartCommodityRespDTO>> cartCommodityStoreMap =
                    cartCommodityRespListByStore.parallelStream().collect(Collectors.groupingBy(ActivityIntegralCartCommodityRespDTO::getStoreId));
            cartCommodityStoreMap.values().forEach(commodityRespList ->
                    cartCommodityRespListByStore.sort(Comparator.comparing(ActivityIntegralCartCommodityRespDTO::getAddTime).reversed()));


            List<String> sortStoreIds = cartCommodityStoreMap.keySet().stream().sorted((o1, o2) ->
                    (int) (cartCommodityStoreMap.get(o1).get(0).getAddTime() - cartCommodityStoreMap.get(o2).get(0).getAddTime())).collect(toList());
            if (CollectionUtils.isEmpty(sortStoreIds)) {
                return response;
            }
            ResponseBase<List<StoreResDTO>> responseBase = storeClient.queryStore(sortStoreIds);
            checkResult(responseBase);

            if (CollectionUtils.isEmpty(responseBase.getData())) {
                log.warn("Store info is null storeIds={}", sortStoreIds);
                return response;
            }
            Map<String, String> storeMap =
                    responseBase.getData().parallelStream().collect(Collectors.toMap(StoreResDTO::getId, StoreResDTO::getStName));
            // 查询旗舰店
            StoreListReqDTO dto = new StoreListReqDTO();
            dto.setMerCode(LocalConst.COMMON_MERCODE);
            StoreListResDTO centerStore = ydjStoreService.queryCenterStore(dto, true);
            commodityList.addAll(sortStoreIds.stream().map(storeId ->
                            getOnlineActivityIntegralDTO(storeId, storeMap.get(storeId), cartCommodityStoreMap.get(storeId), centerStore))
                    .collect(toList()));
        }
        if (!CollectionUtils.isEmpty(cartCommodityRespListBySp)) {
            Map<String, List<ActivityIntegralCartCommodityRespDTO>> cartCommoditySpMap =
                    cartCommodityRespListBySp.parallelStream().collect(Collectors.groupingBy(ActivityIntegralCartCommodityRespDTO::getSpCode));
            cartCommoditySpMap.values().forEach(commodityRespList ->
                    cartCommodityRespListBySp.sort(Comparator.comparing(ActivityIntegralCartCommodityRespDTO::getAddTime).reversed()));
            List<String> sortSpCodes = cartCommoditySpMap.keySet().stream().sorted((o1, o2) ->
                    (int) (cartCommoditySpMap.get(o1).get(0).getAddTime() - cartCommoditySpMap.get(o2).get(0).getAddTime())).collect(toList());

            List<SPMerchantDetailInfoResDTO> spList = spMerchantQueryClient.getSPDetailByList(sortSpCodes).getData();
            Map<String, String> spMap =
                    spList.parallelStream().collect(Collectors.toMap(SPMerchantDetailInfoResDTO::getMerCode, SPMerchantDetailInfoResDTO::getMerName));
            commodityList.addAll(sortSpCodes.stream().map(storeId ->
                            getCloudActivityIntegralDTO(storeId, spMap.get(storeId), cartCommoditySpMap.get(storeId)))
                    .collect(toList()));
        }


        response.setStoreCommodityList(commodityList);

        return response;
    }

    private static ActivityIntegralCartStoreDTO getCloudActivityIntegralDTO(String spCode, String spName, List<ActivityIntegralCartCommodityRespDTO> commodityRespDTOS) {
        ActivityIntegralCartStoreDTO cartStoreDTO = new ActivityIntegralCartStoreDTO();
        cartStoreDTO.setSpCode(spCode);
        cartStoreDTO.setCommodities(commodityRespDTOS.stream()
                .sorted(Comparator.comparing(ActivityIntegralCartCommodityRespDTO::getAddTime))
                .collect(Collectors.toList()));
        cartStoreDTO.setSpName(spName);
        cartStoreDTO.setStoreName(LocalConst.CLOUD_FIXED_STORE_NAME);
        cartStoreDTO.setStoreId(LocalConst.CLOUD_FIXED_STORE + spCode);
        commodityRespDTOS.forEach(c -> {
            c.setStoreId(LocalConst.CLOUD_FIXED_STORE + spCode);
            c.setStoreName(LocalConst.CLOUD_FIXED_STORE_NAME);
        });
        cartStoreDTO.setCommodities(commodityRespDTOS);

        cartStoreDTO.buildParams(commodityRespDTOS.parallelStream().filter(dto -> dto.getChoseFlag() == 1).collect(toList()));
        return cartStoreDTO;
    }

    private static ActivityIntegralCartStoreDTO getOnlineActivityIntegralDTO(String storeId, String storeName, List<ActivityIntegralCartCommodityRespDTO> cartCommodityRespDTOList, StoreListResDTO centerStore) {
        ActivityIntegralCartStoreDTO cartStoreDTO = new ActivityIntegralCartStoreDTO();
        cartStoreDTO.setStoreId(storeId);
        cartStoreDTO.setCommodities(cartCommodityRespDTOList.stream()
                .sorted(Comparator.comparing(ActivityIntegralCartCommodityRespDTO::getAddTime))
                .collect(Collectors.toList()));
        cartStoreDTO.setStoreName(storeName);
        if (centerStore != null && centerStore.getId().equals(storeId)) {
            cartStoreDTO.setStoreName(LocalConst.MERCHANT_B2C_FIXED_STORE_NAME);
            cartStoreDTO.setCenterStoreFlag(true);
        }
        List<ActivityIntegralCartCommodityRespDTO> cartCommodityChoseList =
                cartCommodityRespDTOList.parallelStream().filter(dto -> dto.getChoseFlag() == 1).collect(toList());
        cartStoreDTO.buildParams(cartCommodityChoseList);
        return cartStoreDTO;
    }

    private Integer getMemberHasExchangeSpecNum(List<CountMemberSpecResDTO> memberSpecList,
                                                PurchaseCommodityRespDTO purchaseSpecDTO) {
        if (CollectionUtils.isEmpty(memberSpecList)) {
            return 0;
        }
        Optional<CountMemberSpecResDTO> memberSpecOptional = memberSpecList.parallelStream().filter(memberBuySpec ->
                        StringUtils.equals(String.valueOf(memberBuySpec.getSpecId()), String.valueOf(purchaseSpecDTO.getSpecId()))
                                && StringUtils.equals(String.valueOf(memberBuySpec.getPromotionId()), String.valueOf(purchaseSpecDTO.getId())))
                .findFirst();
        return memberSpecOptional.isPresent() ? memberSpecOptional.get().getBuyNum() : 0;
    }

    private Map<Long, List<CountMemberSpecResDTO>> queryMemberSpecBuyRecord(List<PurchaseCommodityRespDTO> purchaseCommodityRespDTOList,
                                                                            CartCommodityHandleContext context) {
        CountMemberSpecReqDTO countMemberSpecReqDTO = new CountMemberSpecReqDTO();
        countMemberSpecReqDTO.setMemberId(context.getUserId());
        countMemberSpecReqDTO.setSpecIds(purchaseCommodityRespDTOList.parallelStream()
                .filter(purchaseCommodityRespDTO -> purchaseCommodityRespDTO.getLimitAmount() > 0)
                .map(o -> String.valueOf(o.getSpecId()))
                .collect(toList()));
        if (CollectionUtils.isEmpty(countMemberSpecReqDTO.getSpecIds())) {
            return Maps.newHashMap();
        }
        countMemberSpecReqDTO.setIds(purchaseCommodityRespDTOList.parallelStream()
                .filter(activityIntegralSpecDTO -> activityIntegralSpecDTO.getLimitAmount() > 0)
                .map(o -> String.valueOf(o.getId()))
                .collect(toList()));
        ResponseBase<List<CountMemberSpecResDTO>> responseBase = this.orderActivityClient.countMemberSpec(countMemberSpecReqDTO);
        checkResult(responseBase);
        List<CountMemberSpecResDTO> memberSpecBuyRecordList = responseBase.getData();
        if (CollectionUtils.isEmpty(memberSpecBuyRecordList)) {
            return Maps.newHashMap();
        } else {
            Map<Long, List<CountMemberSpecResDTO>> countMemberSpecMap =
                    memberSpecBuyRecordList.parallelStream().collect(Collectors.groupingBy(CountMemberSpecResDTO::getSpecId));
            if (log.isDebugEnabled()) {
                log.debug("Count member spec map {}", countMemberSpecMap);
            }
            return countMemberSpecMap;
        }
    }

    private void queryPurchaseCommodityList(CartCommodityHandleContext context) {
        List<PurchasePmtReqDTO> purchasePmtReqDTOList = Lists.newArrayList();
        Map<Long, ActivityIntegralCartCommodityDTO> activityIntegralCartCommodityDTOMap = context.getProcessCommodityList().parallelStream()
                .collect(Collectors.toMap(
                        commodityDTO -> Long.valueOf(commodityDTO.getSpecId()), Function.identity(), (key1, key2) -> key1));

        List<Long> specIds = context.getProcessCommodityList().parallelStream()
                .map(commodityDTO -> Long.valueOf(commodityDTO.getSpecId())).collect(toList());
        for (Long specId : specIds) {
            PurchasePmtReqDTO purchasePmt = new PurchasePmtReqDTO();
            purchasePmt.setMerCode(context.getMerCode());
            purchasePmt.setSpecId(specId);
            if (activityIntegralCartCommodityDTOMap.get(specId) != null) {
                if (activityIntegralCartCommodityDTOMap.get(specId).getStoreId() != null) {
                    purchasePmt.setStoreId(activityIntegralCartCommodityDTOMap.get(specId).getStoreId());
                } else {
                    purchasePmt.setStoreId(LocalConst.CLOUD_FIXED_STORE);
                }
            }
            purchasePmtReqDTOList.add(purchasePmt);
        }
        ResponseBase<List<PurchaseCommodityRespDTO>> responseBase = promoteClient.searchPurchaseCommodityBySpecIds(purchasePmtReqDTOList);
        checkResult(responseBase);
        context.setPurchaseCommodityRespDTOList(responseBase.getData() != null ? responseBase.getData() : Lists.newLinkedList());
    }


    private void deleteCartCommodity(List<ActivityIntegralCartCommodityDTO> commodityList,
                                     CartCommodityHandleContext context) {
        // commodityList==null表示全删
        if (commodityList == null) {
            deleteAllCommodity(buildCartCacheKey(context.getMerCode(), context.getUserId()));
        } else {
            commodityList.forEach(commodity -> deleteCommodity(buildCartCacheKey(context.getMerCode(), context.getUserId()),
                    commodity.buildRedisHashKey()));
        }

    }

    private List<StoreSpec> queryAndCheckStoreSpec(CartCommodityHandleContext context) {
        if (CollectionUtils.isEmpty(context.getProcessCommodityList())) {
            return null;
        }
        List<Long> specIds = context.getProcessCommodityList().stream()
                .map(c -> Long.parseLong(c.getSpecId())).distinct().collect(toList());
        List<String> storeIds = context.getProcessCommodityList().stream().map(ActivityIntegralCartCommodityDTO::getStoreId)
                .filter(storeId -> !StringUtils.isEmpty(storeId)).distinct().collect(toList());
        List<String> spCodes = context.getProcessCommodityList().stream()
                .map(ActivityIntegralCartCommodityDTO::getSpCode).distinct().collect(toList());
        Map<String, String> spSpecCode = new HashMap<>();
        context.getProcessCommodityList().forEach(o -> {
            if (!StringUtils.isEmpty(o.getSpCode())) {
                spSpecCode.put(o.getSpecId(), o.getSpCode());
            }
        });


        PageDTO<StoreSpec> pageDTO = shoppingCartService.querySpecByClient(specIds, storeIds, spCodes, spSpecCode, context.merCode,
                CommodityQueryAssembleParamDTO.builder().hasShare(true).replaceCommodityNameFlag(false).replacePrescriptionDrugPicFlag(false).build());
        context.setStoreSpecList(pageDTO != null ? pageDTO.getData() : Lists.newLinkedList());
        List<StoreSpec> storeSpecList = context.getStoreSpecList();
        //为空表示查询的商品不存在,直接删除返回
        if (CollectionUtils.isEmpty(storeSpecList)) {
            log.info("[deleteCartCommodity] not found any store spec {}", context);
            deleteCartCommodity(context.getProcessCommodityList(), context);
        }
        return storeSpecList;
    }

    @DisLockConfiguration.DisLock(keyExpr = "'in_purchase_cart_add_lock:' + #cartCommodityDTO.userId +':'+ #cartCommodityDTO.specId+':'+#cartCommodityDTO.storeId",
            lockType = DisLockConfiguration.DisLock.LockType.PESSIMISTIC, tipMessage = "操作频繁,稍安勿躁", expireTime = 3000, waitTime = 2000)
    public ActivityIntegralCartCommodityDTO processCommodityNum(InPurchaseCartCommodityRequest cartCommodityDTO) {

        ActivityIntegralCartCommodityDTO cacheCartCommodityDTO =
                getCommodity(cartCommodityDTO.buildRedisKey(), buildHashKey(cartCommodityDTO.getStoreId(), cartCommodityDTO.getSpCode(), cartCommodityDTO.getSpecId()));
        if (cacheCartCommodityDTO == null) {
            if (cartCommodityDTO.getCount() <= 0) {
                return null;
            }
            AssertUtils.state(getUserCartCommodityNum(cartCommodityDTO.buildRedisKey()) < cartConfiguration.getLimitCount(), CART_FULL);
            cacheCartCommodityDTO = BeanCopyUtil.copy(cartCommodityDTO, ActivityIntegralCartCommodityDTO::new,
                    (s, t) -> {
                        t.setStatus(1);
                        t.setChoseFlag(ENABLING.getCode());
                        t.setAddTime(s.getUpdateTime());
                    });
        } else {

            cacheCartCommodityDTO.setChoseFlag(ENABLING.getCode());
            cacheCartCommodityDTO.increaseCount(cartCommodityDTO.getCount());
            cacheCartCommodityDTO.setUpdateTime(cacheCartCommodityDTO.getUpdateTime());
            cacheCartCommodityDTO.setSpCode(cartCommodityDTO.getSpCode());
            if (cacheCartCommodityDTO.needDelete()) {
                log.info("[deleteCommodity] commodity count is zero {}", cacheCartCommodityDTO);
                deleteCommodity(cartCommodityDTO.buildRedisKey(), buildHashKey(cartCommodityDTO.getStoreId(), cartCommodityDTO.getSpCode(), cartCommodityDTO.getSpecId()));
                return null;
            }
        }

        if (cacheCartCommodityDTO.getCount() > 0) {
            checkStock(cacheCartCommodityDTO);
            putCommodity(cartCommodityDTO.buildRedisKey(), buildHashKey(cartCommodityDTO.getStoreId(), cartCommodityDTO.getSpCode(), cartCommodityDTO.getSpecId()), cacheCartCommodityDTO);
        }

        return cacheCartCommodityDTO;
    }

    public ActivityIntegralCartCommodityDTO getCommodity(String key, String hashKey) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        String cartCommodityJson = hashOperations.get(key, hashKey);
        if (StringUtils.isBlank(cartCommodityJson)) {
            return null;
        }
        return JSONObject.parseObject(cartCommodityJson, ActivityIntegralCartCommodityDTO.class);
    }

    public List<ActivityIntegralCartCommodityDTO> getUserCartAllCommodity(String key) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        Map<String, String> entries = hashOperations.entries(key);
        deleteNullKeysCache(key, entries, hashOperations);
        return hashOperations.values(key).parallelStream().map(s -> JSONObject.parseObject(s, ActivityIntegralCartCommodityDTO.class))
                .collect(toList());
    }

    public Map<String, ActivityIntegralCartCommodityDTO> getUserCartAllCommodityMap(String key) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        Map<String, String> map = hashOperations.entries(key);
        deleteNullKeysCache(key, map, hashOperations);
        Map<String, ActivityIntegralCartCommodityDTO> cartCommodityMap = Maps.newHashMapWithExpectedSize(map.size());
        map.forEach((key0, value) ->
                cartCommodityMap.put(key0, JSONObject.parseObject(value, ActivityIntegralCartCommodityDTO.class)));
        return cartCommodityMap;
    }

    public Long getUserCartCommodityNum(String key) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        Map<String, String> entries = hashOperations.entries(key);
        deleteNullKeysCache(key, entries, hashOperations);
        return hashOperations.size(key);
    }

    private static void deleteNullKeysCache(String key, Map<String, String> entries, HashOperations<String, String, String> hashOperations) {
        List<String> nullKeys = entries.keySet().stream().filter(k -> k.contains("_null")).collect(toList());
        if (!CollectionUtils.isEmpty(nullKeys)) {
            hashOperations.delete(key, nullKeys.toArray());
        }
    }

    public boolean deleteCommodity(String key, String hashKey) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        return hashOperations.delete(key, hashKey) > 0;
    }

    public void deleteAllCommodity(String key) {
        redisTemplate.delete(key);
    }

    public void putCommodity(String key, String hashKey, ActivityIntegralCartCommodityDTO commodityDTO) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        hashOperations.put(key, hashKey, JSONObject.toJSONString(commodityDTO));
    }

    @Getter
    @ToString
    @Setter
    private static class CartCommodityHandleContext {
        /**
         * 用户操作的商品 ()
         */
        private List<ActivityIntegralCartCommodityDTO> processCommodityList;
        /**
         * 操作商品涉及的门店ID
         */
        private List<String> storeIds;
        /**
         * 触发来源
         * 0: 查询购物车列表
         * 1: 加减购
         * 2: 勾选商品
         * 3: 下单确认商品
         */
        private int action;

        private String merCode;

        private String userId;
        /**
         * 用户购买的商品记录
         * key-> 规格Id
         * value -> 对应的购买记录
         */
        private Map<Long, List<CountMemberSpecResDTO>> memberSpecBuyRecordMap;

        /**
         * 兑换商品信息,兑换价格/限购数等
         */
        private List<ActivityIntegralSpecDTO> activityIntegralSpecList;

        /**
         * 内购商品信息,活动库存/限购数等
         */
        private List<PurchaseCommodityRespDTO> purchaseCommodityRespDTOList;
        /**
         * 门店商品信息
         */
        private List<StoreSpec> storeSpecList;
    }

    /**
     * 校验当前门店库存
     */
    private void checkStock(ActivityIntegralCartCommodityDTO cartDTO) {
        //获取是否验证无库存加购开关
        List<Integer> types = new ArrayList<>();
        types.add(MerchantSwitchType.NO_STOCK_ADD_CART.getCode());
        Map<Integer, Boolean> resultMap = merchantSwitchService.queryByTypes(cartDTO.getMerCode(), types);

        if (resultMap != null) {
            Boolean bool = resultMap.get(MerchantSwitchType.NO_STOCK_ADD_CART.getCode());
            if (!bool) {
                return;
            }
        }
        //1.3.1版本需求 内购O2O门店备货中状态
        //内购商城O2O店铺添加商品到购物车时不校验门店库存 后续会校验限购和活动剩余库存
        StoreResDTO centerStore = getCenterStore(cartDTO.getMerCode());
        // 如果是非旗舰店 就是O2O店铺
        if(ObjectUtil.isNull(centerStore) || !cartDTO.getStoreId().equals(centerStore.getId())){
            return;
        }

        List<Long> specIds = new ArrayList<>();
        specIds.add(Long.parseLong(cartDTO.getSpecId()));
        List<String> storeIds = new ArrayList<>();
        storeIds.add(cartDTO.getStoreId());
        PageDTO<StoreSpec> pageDTO;
        if (!StringUtils.isEmpty(cartDTO.getSpCode())) {
            pageDTO = shoppingCartService.querySpStoreSpecByClient(specIds, storeIds, cartDTO.getMerCode(), cartDTO.getSpCode());
        } else {
            pageDTO = shoppingCartService.queryStoreSpecByClient(specIds, storeIds, cartDTO.getMerCode(),
                    CommodityQueryAssembleParamDTO.builder().hasShare(true).replaceCommodityNameFlag(false).replacePrescriptionDrugPicFlag(false).build());
        }
        if (Objects.isNull(pageDTO)) {
            throw WarnException.builder().code(ErrorType.QUERY_COMMODITY_ERROR.getCode()).
                    tipMessage(ErrorType.QUERY_COMMODITY_ERROR.getMsg()).build();
        }
        List<StoreSpec> storeSpecs = pageDTO.getData();
        if (CollectionUtils.isEmpty(storeSpecs)) {
            throw WarnException.builder().code(ErrorType.QUERY_COMMODITY_ERROR.getCode()).
                    tipMessage(ErrorType.QUERY_COMMODITY_ERROR.getMsg()).build();
        }
        StoreSpec storeSpec = storeSpecs.get(0);
        Integer stock = storeSpec.getStock();
        // 当门店库存为0时,并且有共享库存,则取共享库存,否则取门店库存
        if (stock == 0 && (Objects.nonNull(storeSpec.getSharedStock()) && storeSpec.getSharedStock() != 0)) {
            stock = storeSpec.getSharedStock();
        }
        if (stock < cartDTO.getCount()) {
            throw WarnException.builder().code(ErrorType.CART_ADD_STOCK_ERROR.getCode()).
                    tipMessage(ErrorType.CART_ADD_STOCK_ERROR.getMsg()).build();
        }
    }

    /**
     * 获取旗舰店
     * @param merCode
     * @return
     */
    private StoreResDTO getCenterStore(String merCode) {
        StoreResDTO storeResDTO = new StoreResDTO();
        String storeInfo = redisService.get(CENTER_STORE_KEY);
        if (ObjectUtil.isEmpty(storeInfo)) {
            StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
            storeListReqDTO.setMerCode(merCode);
            StoreListResDTO centerStore = ydjStoreService.queryCenterStore(storeListReqDTO, true);
            if (centerStore == null) {
                log.error("旗舰店信息为空,{}", merCode);
                throw WarnException.builder().code(ErrorType.STORE_IS_NULL.getCode()).
                    tipMessage(ErrorType.STORE_IS_NULL.getMsg()).build();
            }
            BeanUtils.copyProperties(centerStore, storeResDTO);
            storeResDTO.setStName(LocalConst.MERCHANT_B2C_FIXED_STORE_NAME);
            redisService.setValueExpire(CENTER_STORE_KEY,JSON.toJSONString(storeResDTO),30, TimeUnit.DAYS);
        } else{
            try {
                return JSON.parseObject(storeInfo, StoreResDTO.class);
            } catch (Exception e) {
                ExLogger.logger().field("inPurchase").info("获取旗舰店失败,key{}：", CENTER_STORE_KEY, e);
            }
        }
        return storeResDTO;
    }
}
