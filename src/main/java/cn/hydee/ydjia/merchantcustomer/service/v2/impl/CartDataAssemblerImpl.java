package cn.hydee.ydjia.merchantcustomer.service.v2.impl;

import cn.hydee.ydjia.merchantcustomer.dto.CurrentStoreInfoDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityStoreDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.enums.DrugType;
import cn.hydee.ydjia.merchantcustomer.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreSpec;
import cn.hydee.ydjia.merchantcustomer.service.MemberInfoService;
import cn.hydee.ydjia.merchantcustomer.service.MerchantSwitchService;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartDataAssembler;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 购物车数据组装器实现
 *
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Component
public class CartDataAssemblerImpl implements CartDataAssembler {

    @Autowired
    private MerchantSwitchService merchantSwitchService;

    @Autowired
    private MemberInfoService memberInfoService;

    /**
     * 处方药默认图片
     */
    private static final String PRESCRIPTION_DRUG_DEFAULT_PIC = "https://default-prescription-pic.jpg";

    @Override
    public CartCommodityRespCommonDTO assembleResponse(ShoppingCartProductHandleDTO productData,
                                                      List<ActivitySpecDTO> activitySpecs,
                                                      ShoppingCartCommodityReqDTO request) {
        try {
            log.info("开始组装购物车响应数据，用户：{}", request.getUserId());

            if (productData == null || CollectionUtils.isEmpty(productData.getRedisSpecs())) {
                log.warn("商品数据为空，返回空响应，用户：{}", request.getUserId());
                return createEmptyResponse(request);
            }

            CartCommodityRespCommonDTO response = new CartCommodityRespCommonDTO();

            // 1. 按门店分组并组装商品数据
            List<CartCommodityStoreDTO> storeCommodityList = assembleStoreCommodities(
                    productData, activitySpecs, request);

            response.setStoreCommodityList(storeCommodityList);

            // 2. 设置价格信息
            setPriceInfo(response);

            log.info("购物车响应数据组装完成，门店数量：{}，用户：{}",
                    storeCommodityList.size(), request.getUserId());

            return response;

        } catch (Exception e) {
            log.error("组装购物车响应数据失败，用户：{}", request.getUserId(), e);
            throw new RuntimeException("数据组装失败", e);
        }
    }

    @Override
    public void handlePrescriptionDrugCompliance(CartCommodityRespCommonDTO response, String merCode) {
        try {
            if (!StringUtils.hasLength(merCode) || Objects.isNull(response) ||
                CollectionUtils.isEmpty(response.getStoreCommodityList())) {
                return;
            }

            // 查询处方药合规信息显示开关
            if (!merchantSwitchService.queryPrescriptionShowFlag(merCode)) {
                return;
            }

            log.info("开始处理处方药合规信息，商户：{}", merCode);

            response.getStoreCommodityList().forEach(store -> {
                if (CollectionUtils.isEmpty(store.getCommodities())) {
                    return;
                }

                store.getCommodities().forEach(commodity -> {
                    if (DrugType.PRESCRIPTION.getCode().equals(commodity.getDrugType())) {
                        // 设置处方药默认图片
                        commodity.setPicUrl(PRESCRIPTION_DRUG_DEFAULT_PIC);
                        commodity.setMainPic(commodity.getPicUrl());
                    }
                });
            });

            log.info("处方药合规信息处理完成，商户：{}", merCode);

        } catch (Exception e) {
            log.error("处理处方药合规信息失败，商户：{}", merCode, e);
            // 处方药合规处理失败不影响主流程
        }
    }

    @Override
    public CartCommodityRespCommonDTO createEmptyResponse(ShoppingCartCommodityReqDTO request) {
        try {
            CurrentStoreInfoDTO currentStoreInfo = request.getCurrentStoreInfo();
            if (currentStoreInfo == null) {
                return new CartCommodityRespCommonDTO();
            }

            CartCommodityRespCommonDTO response = new CartCommodityRespCommonDTO();
            CartCommodityStoreDTO storeDTO = createEmptyStoreDTO(request);

            BeanUtils.copyProperties(currentStoreInfo, storeDTO);

            List<CartCommodityStoreDTO> storeCommodityList = Lists.newArrayList();
            storeCommodityList.add(storeDTO);

            response.setStoreCommodityList(storeCommodityList);
            setPriceInfo(response);

            return response;

        } catch (Exception e) {
            log.error("创建空响应失败，用户：{}", request.getUserId(), e);
            return new CartCommodityRespCommonDTO();
        }
    }

    @Override
    public void setPriceInfo(CartCommodityRespCommonDTO response) {
        try {
            if (response == null || CollectionUtils.isEmpty(response.getStoreCommodityList())) {
                return;
            }

            // 调用原有的价格设置逻辑
            response.setPrice(response.getStoreCommodityList());

        } catch (Exception e) {
            log.error("设置价格信息失败", e);
            // 价格设置失败不影响主流程
        }
    }

    /**
     * 按门店分组并组装商品数据
     */
    private List<CartCommodityStoreDTO> assembleStoreCommodities(ShoppingCartProductHandleDTO productData,
                                                                List<ActivitySpecDTO> activitySpecs,
                                                                ShoppingCartCommodityReqDTO request) {
        try {
            log.info("开始按门店分组并组装商品数据，用户：{}", request.getUserId());

            List<CartCommodityStoreDTO> storeCommodityList = Lists.newArrayList();

            // 1. 获取基础数据
            List<CartCommodityDTO> redisSpecs = productData.getRedisSpecs();
            List<StoreSpec> storeSpecs = productData.getStoreSpecs();
            Map<String, StoreSpec> storeSpecMap = productData.getStoreSpecMap();
            Map<String, CartCommodityDTO> redisSpecMap = productData.getRedisSpecMap();

            // 2. 构建活动规格Map
            Map<String, ActivitySpecDTO> activitySpecMap = buildActivitySpecMap(activitySpecs);

            // 3. 获取会员信息
            boolean isVip = getMemberInfo(request.getUserId());

            // 4. 按门店分组Redis商品
            Map<String, List<CartCommodityDTO>> storeGroupedSpecs = groupSpecsByStore(redisSpecs);

            // 5. 为每个门店组装商品数据
            for (Map.Entry<String, List<CartCommodityDTO>> entry : storeGroupedSpecs.entrySet()) {
                String storeId = entry.getKey();
                List<CartCommodityDTO> storeRedisSpecs = entry.getValue();

                CartCommodityStoreDTO storeDTO = assembleStoreData(storeId, storeRedisSpecs,
                        storeSpecMap, redisSpecMap, activitySpecMap, isVip, request);

                if (storeDTO != null && !CollectionUtils.isEmpty(storeDTO.getCommodities())) {
                    storeCommodityList.add(storeDTO);
                }
            }

            log.info("门店商品组装完成，用户：{}，门店数量：{}", request.getUserId(), storeCommodityList.size());

            return storeCommodityList;

        } catch (Exception e) {
            log.error("组装门店商品数据失败，用户：{}", request.getUserId(), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 构建活动规格Map
     */
    private Map<String, ActivitySpecDTO> buildActivitySpecMap(List<ActivitySpecDTO> activitySpecs) {
        if (CollectionUtils.isEmpty(activitySpecs)) {
            return Maps.newHashMap();
        }

        return activitySpecs.stream().collect(Collectors.toMap(
                spec -> spec.getSpecId() + "_" + spec.getStoreId(),
                spec -> spec,
                (existing, replacement) -> existing
        ));
    }

    /**
     * 按门店分组商品规格
     */
    private Map<String, List<CartCommodityDTO>> groupSpecsByStore(List<CartCommodityDTO> redisSpecs) {
        if (CollectionUtils.isEmpty(redisSpecs)) {
            return Maps.newHashMap();
        }

        return redisSpecs.stream().collect(Collectors.groupingBy(CartCommodityDTO::getStoreId));
    }

    /**
     * 组装单个门店的数据
     */
    private CartCommodityStoreDTO assembleStoreData(String storeId,
                                                    List<CartCommodityDTO> storeRedisSpecs,
                                                    Map<String, StoreSpec> storeSpecMap,
                                                    Map<String, CartCommodityDTO> redisSpecMap,
                                                    Map<String, ActivitySpecDTO> activitySpecMap,
                                                    boolean isVip,
                                                    ShoppingCartCommodityReqDTO request) {
        try {
            CartCommodityStoreDTO storeDTO = new CartCommodityStoreDTO();
            List<CartCommodityRespDTO> commodities = Lists.newArrayList();

            // 设置门店基础信息
            setStoreBasicInfo(storeDTO, storeId, request);

            // 为每个商品规格组装数据
            for (CartCommodityDTO redisSpec : storeRedisSpecs) {
                CartCommodityRespDTO commodity = assembleCommodityData(redisSpec,
                        storeSpecMap, activitySpecMap, isVip, request);

                if (commodity != null) {
                    commodities.add(commodity);
                }
            }

            storeDTO.setCommodities(commodities);

            // 计算门店总价
            calculateStorePrices(storeDTO);

            return storeDTO;

        } catch (Exception e) {
            log.error("组装门店数据失败，门店：{}，用户：{}", storeId, request.getUserId(), e);
            return null;
        }
    }

    /**
     * 设置门店基础信息
     */
    private void setStoreBasicInfo(CartCommodityStoreDTO storeDTO, String storeId,
                                  ShoppingCartCommodityReqDTO request) {
        storeDTO.setStoreId(storeId);

        if (storeId.startsWith(LocalConst.CLOUD_FIXED_STORE)) {
            // 云仓门店
            storeDTO.setStoreName(LocalConst.CLOUD_FIXED_STORE_NAME);
            storeDTO.setSpCode(extractSpCodeFromStoreId(storeId));
        } else if (LocalConst.MERCHANT_B2C_FIXED_STORE.equals(storeId)) {
            // B2C门店
            storeDTO.setStoreName(LocalConst.MERCHANT_B2C_FIXED_STORE_NAME);
        } else {
            // 普通O2O门店
            StoreListResDTO storeInfo = findStoreInfo(storeId, request.getStoreList());
            if (storeInfo != null) {
                storeDTO.setStoreName(storeInfo.getName());
                storeDTO.setStoreAddress(storeInfo.getAddress());
                storeDTO.setStorePhone(storeInfo.getPhone());
            }
        }
    }

    /**
     * 组装单个商品数据
     */
    private CartCommodityRespDTO assembleCommodityData(CartCommodityDTO redisSpec,
                                                       Map<String, StoreSpec> storeSpecMap,
                                                       Map<String, ActivitySpecDTO> activitySpecMap,
                                                       boolean isVip,
                                                       ShoppingCartCommodityReqDTO request) {
        try {
            CartCommodityRespDTO commodity = new CartCommodityRespDTO();

            // 1. 复制Redis数据
            BeanUtils.copyProperties(redisSpec, commodity);

            // 2. 获取商品中台数据
            String storeSpecKey = redisSpec.getStoreId() + "_" + redisSpec.getSpecId();
            StoreSpec storeSpec = storeSpecMap.get(storeSpecKey);

            if (storeSpec == null) {
                log.warn("未找到商品规格信息，跳过该商品，规格ID：{}，门店ID：{}",
                        redisSpec.getSpecId(), redisSpec.getStoreId());
                return null;
            }

            // 3. 整合商品中台数据
            commodity.packCommodity(storeSpec);

            // 4. 处理VIP价格
            if (isVip && storeSpec.isUseVipPrice(isVip)) {
                commodity.setIsVip(YesOrNoType.YES.getCode());
                commodity.setOriginPrice(storeSpec.getPrice());
                commodity.setPrice(storeSpec.getVipPrice());
                commodity.setBeforePrice(storeSpec.getVipPrice());
                commodity.setGoodsSalesPrice(storeSpec.getVipPrice());
            }

            // 5. 处理活动信息
            String activityKey = redisSpec.getSpecId() + "_" + redisSpec.getStoreId();
            ActivitySpecDTO activitySpec = activitySpecMap.get(activityKey);
            if (activitySpec != null) {
                applyActivityInfo(commodity, activitySpec);
            }

            // 6. 设置其他信息
            commodity.setStoreName(getStoreName(redisSpec.getStoreId(), request));
            commodity.setDistributionDay(7); // 默认配送天数

            return commodity;

        } catch (Exception e) {
            log.error("组装商品数据失败，规格ID：{}，门店ID：{}",
                    redisSpec.getSpecId(), redisSpec.getStoreId(), e);
            return null;
        }
    }

    /**
     * 应用活动信息
     */
    private void applyActivityInfo(CartCommodityRespDTO commodity, ActivitySpecDTO activitySpec) {
        try {
            // 设置活动价格
            if (activitySpec.getActPrice() != null) {
                commodity.setPrice(activitySpec.getActPrice());
                commodity.setGoodsSalesPrice(activitySpec.getActPrice());
            }

            // 设置活动列表
            if (!CollectionUtils.isEmpty(activitySpec.getActivityList())) {
                List<ActivityDTO> commodityLevelActivities = Lists.newArrayList();
                List<ActivityDTO> orderLevelActivities = Lists.newArrayList();

                for (ActivityDTO activity : activitySpec.getActivityList()) {
                    if (isCommodityLevelActivity(activity)) {
                        commodityLevelActivities.add(activity);
                    } else {
                        orderLevelActivities.add(activity);
                    }
                }

                commodity.setCommodityLevelActivities(commodityLevelActivities);
                commodity.setOrderLevelActivities(orderLevelActivities);
            }

        } catch (Exception e) {
            log.error("应用活动信息失败，商品：{}", commodity.getSpecId(), e);
        }
    }

    /**
     * 判断是否为商品级活动
     */
    private boolean isCommodityLevelActivity(ActivityDTO activity) {
        // 这里根据活动类型判断是商品级还是订单级活动
        // 简化处理，可以根据实际业务逻辑调整
        return activity.getPmtType() != null &&
               (activity.getPmtType().equals(1) || activity.getPmtType().equals(2)); // 假设1,2为商品级活动
    }

    /**
     * 计算门店总价
     */
    private void calculateStorePrices(CartCommodityStoreDTO storeDTO) {
        if (CollectionUtils.isEmpty(storeDTO.getCommodities())) {
            storeDTO.setTotalPrice(BigDecimal.ZERO);
            storeDTO.setBeforePrice(BigDecimal.ZERO);
            storeDTO.setReducePrice(BigDecimal.ZERO);
            storeDTO.setChooseCommodityCount(0);
            return;
        }

        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal beforePrice = BigDecimal.ZERO;
        int chooseCommodityCount = 0;

        for (CartCommodityRespDTO commodity : storeDTO.getCommodities()) {
            if (commodity.getChoseFlag() != null && commodity.getChoseFlag() == 1) {
                BigDecimal commodityTotal = commodity.getPrice().multiply(new BigDecimal(commodity.getCount()));
                BigDecimal commodityBefore = commodity.getBeforePrice().multiply(new BigDecimal(commodity.getCount()));

                totalPrice = totalPrice.add(commodityTotal);
                beforePrice = beforePrice.add(commodityBefore);
                chooseCommodityCount += commodity.getCount();
            }
        }

        storeDTO.setTotalPrice(totalPrice);
        storeDTO.setBeforePrice(beforePrice);
        storeDTO.setReducePrice(beforePrice.subtract(totalPrice));
        storeDTO.setChooseCommodityCount(chooseCommodityCount);
    }

    /**
     * 获取会员信息
     */
    private boolean getMemberInfo(String userId) {
        try {
            return memberInfoService.getMemberBaseInfo(Long.valueOf(userId));
        } catch (Exception e) {
            log.error("获取会员信息失败，用户：{}", userId, e);
            return false;
        }
    }

    /**
     * 从门店ID中提取供应商编码
     */
    private String extractSpCodeFromStoreId(String storeId) {
        if (storeId != null && storeId.startsWith(LocalConst.CLOUD_FIXED_STORE)) {
            return storeId.substring(LocalConst.CLOUD_FIXED_STORE.length());
        }
        return null;
    }

    /**
     * 查找门店信息
     */
    private StoreListResDTO findStoreInfo(String storeId, List<StoreListResDTO> storeList) {
        if (CollectionUtils.isEmpty(storeList)) {
            return null;
        }

        return storeList.stream()
                .filter(store -> storeId.equals(store.getId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取门店名称
     */
    private String getStoreName(String storeId, ShoppingCartCommodityReqDTO request) {
        if (storeId.startsWith(LocalConst.CLOUD_FIXED_STORE)) {
            return LocalConst.CLOUD_FIXED_STORE_NAME;
        } else if (LocalConst.MERCHANT_B2C_FIXED_STORE.equals(storeId)) {
            return LocalConst.MERCHANT_B2C_FIXED_STORE_NAME;
        } else {
            StoreListResDTO storeInfo = findStoreInfo(storeId, request.getStoreList());
            return storeInfo != null ? storeInfo.getName() : "未知门店";
        }
    }

    /**
     * 创建空的门店DTO
     */
    private CartCommodityStoreDTO createEmptyStoreDTO(ShoppingCartCommodityReqDTO request) {
        // 这里应该调用原有的getCommodityStoreDTO方法
        // 简化实现
        return new CartCommodityStoreDTO();
    }
}
