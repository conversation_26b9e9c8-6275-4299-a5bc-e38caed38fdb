package cn.hydee.ydjia.merchantcustomer.service.v2.impl;

import cn.hydee.ydjia.merchantcustomer.dto.CurrentStoreInfoDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityStoreDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.enums.DrugType;
import cn.hydee.ydjia.merchantcustomer.service.MerchantSwitchService;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartDataAssembler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 购物车数据组装器实现
 * 
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Component
public class CartDataAssemblerImpl implements CartDataAssembler {
    
    @Autowired
    private MerchantSwitchService merchantSwitchService;
    
    /**
     * 处方药默认图片
     */
    private static final String PRESCRIPTION_DRUG_DEFAULT_PIC = "https://default-prescription-pic.jpg";
    
    @Override
    public CartCommodityRespCommonDTO assembleResponse(ShoppingCartProductHandleDTO productData,
                                                      List<ActivitySpecDTO> activitySpecs,
                                                      ShoppingCartCommodityReqDTO request) {
        try {
            log.info("开始组装购物车响应数据，用户：{}", request.getUserId());
            
            if (productData == null || CollectionUtils.isEmpty(productData.getRedisSpecs())) {
                log.warn("商品数据为空，返回空响应，用户：{}", request.getUserId());
                return createEmptyResponse(request);
            }
            
            CartCommodityRespCommonDTO response = new CartCommodityRespCommonDTO();
            
            // 1. 按门店分组并组装商品数据
            List<CartCommodityStoreDTO> storeCommodityList = assembleStoreCommodities(
                    productData, activitySpecs, request);
            
            response.setStoreCommodityList(storeCommodityList);
            
            // 2. 设置价格信息
            setPriceInfo(response);
            
            log.info("购物车响应数据组装完成，门店数量：{}，用户：{}", 
                    storeCommodityList.size(), request.getUserId());
            
            return response;
            
        } catch (Exception e) {
            log.error("组装购物车响应数据失败，用户：{}", request.getUserId(), e);
            throw new RuntimeException("数据组装失败", e);
        }
    }
    
    @Override
    public void handlePrescriptionDrugCompliance(CartCommodityRespCommonDTO response, String merCode) {
        try {
            if (!StringUtils.hasLength(merCode) || Objects.isNull(response) ||
                CollectionUtils.isEmpty(response.getStoreCommodityList())) {
                return;
            }
            
            // 查询处方药合规信息显示开关
            if (!merchantSwitchService.queryPrescriptionShowFlag(merCode)) {
                return;
            }
            
            log.info("开始处理处方药合规信息，商户：{}", merCode);
            
            response.getStoreCommodityList().forEach(store -> {
                if (CollectionUtils.isEmpty(store.getCommodities())) {
                    return;
                }
                
                store.getCommodities().forEach(commodity -> {
                    if (DrugType.PRESCRIPTION.getCode().equals(commodity.getDrugType())) {
                        // 设置处方药默认图片
                        commodity.setPicUrl(PRESCRIPTION_DRUG_DEFAULT_PIC);
                        commodity.setMainPic(commodity.getPicUrl());
                    }
                });
            });
            
            log.info("处方药合规信息处理完成，商户：{}", merCode);
            
        } catch (Exception e) {
            log.error("处理处方药合规信息失败，商户：{}", merCode, e);
            // 处方药合规处理失败不影响主流程
        }
    }
    
    @Override
    public CartCommodityRespCommonDTO createEmptyResponse(ShoppingCartCommodityReqDTO request) {
        try {
            CurrentStoreInfoDTO currentStoreInfo = request.getCurrentStoreInfo();
            if (currentStoreInfo == null) {
                return new CartCommodityRespCommonDTO();
            }
            
            CartCommodityRespCommonDTO response = new CartCommodityRespCommonDTO();
            CartCommodityStoreDTO storeDTO = createEmptyStoreDTO(request);
            
            BeanUtils.copyProperties(currentStoreInfo, storeDTO);
            
            List<CartCommodityStoreDTO> storeCommodityList = Lists.newArrayList();
            storeCommodityList.add(storeDTO);
            
            response.setStoreCommodityList(storeCommodityList);
            setPriceInfo(response);
            
            return response;
            
        } catch (Exception e) {
            log.error("创建空响应失败，用户：{}", request.getUserId(), e);
            return new CartCommodityRespCommonDTO();
        }
    }
    
    @Override
    public void setPriceInfo(CartCommodityRespCommonDTO response) {
        try {
            if (response == null || CollectionUtils.isEmpty(response.getStoreCommodityList())) {
                return;
            }
            
            // 调用原有的价格设置逻辑
            response.setPrice(response.getStoreCommodityList());
            
        } catch (Exception e) {
            log.error("设置价格信息失败", e);
            // 价格设置失败不影响主流程
        }
    }
    
    /**
     * 按门店分组并组装商品数据
     */
    private List<CartCommodityStoreDTO> assembleStoreCommodities(ShoppingCartProductHandleDTO productData,
                                                                List<ActivitySpecDTO> activitySpecs,
                                                                ShoppingCartCommodityReqDTO request) {
        try {
            List<CartCommodityStoreDTO> storeCommodityList = Lists.newArrayList();
            
            // 这里应该实现具体的门店商品组装逻辑
            // 包括：
            // 1. 按门店分组商品
            // 2. 组装商品详细信息
            // 3. 设置活动信息
            // 4. 处理库存和状态
            // 5. 计算价格和优惠
            
            // 简化实现，实际应该调用原有的packageRespCommonDTO逻辑
            log.info("门店商品组装完成，门店数量：{}", storeCommodityList.size());
            
            return storeCommodityList;
            
        } catch (Exception e) {
            log.error("组装门店商品数据失败", e);
            return Lists.newArrayList();
        }
    }
    
    /**
     * 创建空的门店DTO
     */
    private CartCommodityStoreDTO createEmptyStoreDTO(ShoppingCartCommodityReqDTO request) {
        // 这里应该调用原有的getCommodityStoreDTO方法
        // 简化实现
        return new CartCommodityStoreDTO();
    }
}
