package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.util.ExLogger;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityGetDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SingleStoreCartCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.activity.PredThriftRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.enums.CommodityOriginType;
import cn.hydee.ydjia.merchantcustomer.enums.IsvalidStatus;
import cn.hydee.ydjia.merchantcustomer.enums.PmtProductType;
import cn.hydee.ydjia.merchantcustomer.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.feign.dto.CommodityQueryAssembleParamDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.PriceDiscountParamDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreSpec;
import cn.hydee.ydjia.merchantcustomer.service.DiscountHandleService;
import cn.hydee.ydjia.merchantcustomer.service.MemberInfoService;
import cn.hydee.ydjia.merchantcustomer.service.MerchantSwitchService;
import cn.hydee.ydjia.merchantcustomer.service.ShoppingCartService;
import cn.hydee.ydjia.merchantcustomer.service.SingleStoreShoppingCartService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单门店购物车服务实现
 * 重构自多门店购物车，专门处理单门店场景，简化逻辑
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class SingleStoreShoppingCartServiceImpl implements SingleStoreShoppingCartService {

    @Autowired
    private ShoppingCartService shoppingCartService;

    @Autowired
    private DiscountHandleService discountHandleService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private MerchantSwitchService merchantSwitchService;

    @Override
    public SingleStoreCartCommodityRespDTO getSingleStoreCommodity(CartCommodityGetDTO dto) {
        Long startTime = System.currentTimeMillis();

        try {
            log.info("单门店购物车查询开始，参数：{}", JSON.toJSONString(dto));

            // 1. 参数验证
            validateSingleStoreRequest(dto);

            // 2. 获取Redis购物车数据
            List<CartCommodityDTO> redisSpecs = getFilteredRedisSpecs(dto);
            if (CollectionUtils.isEmpty(redisSpecs)) {
                log.info("指定门店无购物车商品，门店ID：{}", dto.getCurrentStoreId());
                return createEmptyResponse(dto);
            }

            // 3. 查询商品中台数据
            List<StoreSpec> storeSpecs = queryStoreSpecs(redisSpecs, dto.getMerCode());
            if (CollectionUtils.isEmpty(storeSpecs)) {
                log.warn("商品中台未返回有效数据，门店ID：{}", dto.getCurrentStoreId());
                return createEmptyResponse(dto);
            }

            // 4. 构建商品规格Map
            Map<String, StoreSpec> storeSpecMap = buildStoreSpecMap(storeSpecs);

            // 5. 获取会员信息\n            Boolean isVip = getMemberInfo(dto.getUserId());\n            \n            // 6. 查询活动信息\n            List<ActivitySpecDTO> activitySpecs = getActivitySpecs(redisSpecs, dto);\n            Map<String, ActivitySpecDTO> activitySpecMap = buildActivitySpecMap(activitySpecs);\n            \n            // 7. 组装商品数据\n            List<CartCommodityRespDTO> commodities = assembleCommodities(redisSpecs, storeSpecMap, \n                    activitySpecMap, isVip, dto);\n            \n            // 8. 构建响应数据\n            SingleStoreCartCommodityRespDTO response = buildResponse(commodities, dto);\n            \n            // 9. 处理Plus会员优惠\n            handlePlusThrift(response, activitySpecs, dto);\n            \n            long duration = System.currentTimeMillis() - startTime;\n            log.info(\"单门店购物车查询完成，门店ID：{}，商品数量：{}，耗时：{}ms\", \n                    dto.getCurrentStoreId(), commodities.size(), duration);\n            \n            return response;\n            \n        } catch (Exception e) {\n            long duration = System.currentTimeMillis() - startTime;\n            log.error(\"单门店购物车查询失败，门店ID：{}，耗时：{}ms\", dto.getCurrentStoreId(), duration, e);\n            ExLogger.logger().field(\"single_store_cart_error\").error(\"单门店购物车查询失败\", e);\n            throw new RuntimeException(\"购物车查询失败\", e);\n        }\n    }\n    \n    @Override\n    public void validateSingleStoreRequest(CartCommodityGetDTO dto) {\n        if (dto == null) {\n            throw new IllegalArgumentException(\"请求参数不能为空\");\n        }\n        \n        if (!StringUtils.hasText(dto.getMerCode())) {\n            throw new IllegalArgumentException(\"商户编码不能为空\");\n        }\n        \n        if (!StringUtils.hasText(dto.getUserId())) {\n            throw new IllegalArgumentException(\"用户ID不能为空\");\n        }\n        \n        if (!StringUtils.hasText(dto.getCurrentStoreId())) {\n            throw new IllegalArgumentException(\"门店ID不能为空\");\n        }\n    }\n    \n    /**\n     * 获取过滤后的Redis购物车数据\n     * 只获取指定门店的商品\n     */\n    private List<CartCommodityDTO> getFilteredRedisSpecs(CartCommodityGetDTO dto) {\n        try {\n            // 获取所有购物车商品\n            List<CartCommodityDTO> allRedisSpecs = shoppingCartService.getRedisCartListByKey(dto.getRedisKey());\n            \n            if (CollectionUtils.isEmpty(allRedisSpecs)) {\n                return Lists.newArrayList();\n            }\n            \n            // 过滤指定门店的商品\n            String targetStoreId = dto.getCurrentStoreId();\n            List<CartCommodityDTO> filteredSpecs = allRedisSpecs.stream()\n                    .filter(spec -> targetStoreId.equals(spec.getStoreId()))\n                    .filter(spec -> PmtProductType.NORMAL.getCode().equals(spec.getPmtProductType()))\n                    .collect(Collectors.toList());\n            \n            log.info(\"过滤门店商品完成，门店ID：{}，总商品数：{}，门店商品数：{}\", \n                    targetStoreId, allRedisSpecs.size(), filteredSpecs.size());\n            \n            return filteredSpecs;\n            \n        } catch (Exception e) {\n            log.error(\"获取Redis购物车数据失败，门店ID：{}\", dto.getCurrentStoreId(), e);\n            return Lists.newArrayList();\n        }\n    }\n    \n    /**\n     * 查询商品中台数据\n     */\n    private List<StoreSpec> queryStoreSpecs(List<CartCommodityDTO> redisSpecs, String merCode) {\n        try {\n            List<Long> specIds = redisSpecs.stream()\n                    .map(spec -> Long.valueOf(spec.getSpecId()))\n                    .distinct()\n                    .collect(Collectors.toList());\n            \n            List<String> storeIds = redisSpecs.stream()\n                    .map(CartCommodityDTO::getStoreId)\n                    .distinct()\n                    .collect(Collectors.toList());\n            \n            CommodityQueryAssembleParamDTO queryParam = CommodityQueryAssembleParamDTO.builder()\n                    .hasShare(true)\n                    .replaceCommodityNameFlag(false)\n                    .replacePrescriptionDrugPicFlag(false)\n                    .notSetRefPriceZeroFlag(false)\n                    .build();\n            \n            PageDTO<StoreSpec> pageResult = shoppingCartService.queryStoreSpecByClient(\n                    specIds, storeIds, merCode, queryParam);\n            \n            if (pageResult == null || CollectionUtils.isEmpty(pageResult.getData())) {\n                log.warn(\"商品中台查询无结果，规格数量：{}，门店数量：{}\", specIds.size(), storeIds.size());\n                return Lists.newArrayList();\n            }\n            \n            log.info(\"商品中台查询完成，返回商品数量：{}\", pageResult.getData().size());\n            return pageResult.getData();\n            \n        } catch (Exception e) {\n            log.error(\"查询商品中台数据失败\", e);\n            return Lists.newArrayList();\n        }\n    }\n    \n    /**\n     * 构建商品规格Map\n     */\n    private Map<String, StoreSpec> buildStoreSpecMap(List<StoreSpec> storeSpecs) {\n        if (CollectionUtils.isEmpty(storeSpecs)) {\n            return Maps.newHashMap();\n        }\n        \n        return storeSpecs.stream().collect(Collectors.toMap(\n                spec -> {\n                    if (CommodityOriginType.belongToSp(spec.getOrigin())) {\n                        // 云仓商品：CLOUD_STORE + spCode + \"_\" + specId\n                        return LocalConst.CLOUD_FIXED_STORE + spec.getSpCode() + \"_\" + spec.getSpecId();\n                    } else {\n                        // O2O商品：storeId + \"_\" + specId\n                        return spec.getStoreId() + \"_\" + spec.getSpecId();\n                    }\n                },\n                Function.identity(),\n                (existing, replacement) -> existing\n        ));\n    }\n    \n    /**\n     * 获取会员信息\n     */\n    private Boolean getMemberInfo(String userId) {\n        try {\n            return memberInfoService.getMemberBaseInfo(Long.valueOf(userId));\n        } catch (Exception e) {\n            log.error(\"获取会员信息失败，用户ID：{}\", userId, e);\n            return false;\n        }\n    }\n    \n    /**\n     * 获取活动信息\n     */\n    private List<ActivitySpecDTO> getActivitySpecs(List<CartCommodityDTO> redisSpecs, CartCommodityGetDTO dto) {\n        try {\n            // 这里应该调用活动中台获取活动信息\n            // 简化处理，返回空列表\n            log.info(\"获取活动信息，商品数量：{}\", redisSpecs.size());\n            return Lists.newArrayList();\n            \n        } catch (Exception e) {\n            log.error(\"获取活动信息失败\", e);\n            return Lists.newArrayList();\n        }\n    }\n    \n    /**\n     * 构建活动规格Map\n     */\n    private Map<String, ActivitySpecDTO> buildActivitySpecMap(List<ActivitySpecDTO> activitySpecs) {\n        if (CollectionUtils.isEmpty(activitySpecs)) {\n            return Maps.newHashMap();\n        }\n        \n        return activitySpecs.stream().collect(Collectors.toMap(\n                spec -> spec.getSpecId() + \"_\" + spec.getStoreId(),\n                Function.identity(),\n                (existing, replacement) -> existing\n        ));\n    }\n    \n    /**\n     * 组装商品数据\n     */\n    private List<CartCommodityRespDTO> assembleCommodities(List<CartCommodityDTO> redisSpecs,\n                                                           Map<String, StoreSpec> storeSpecMap,\n                                                           Map<String, ActivitySpecDTO> activitySpecMap,\n                                                           Boolean isVip,\n                                                           CartCommodityGetDTO dto) {\n        List<CartCommodityRespDTO> commodities = Lists.newArrayList();\n        \n        for (CartCommodityDTO redisSpec : redisSpecs) {\n            try {\n                CartCommodityRespDTO commodity = assembleSingleCommodity(redisSpec, storeSpecMap, \n                        activitySpecMap, isVip, dto);\n                \n                if (commodity != null) {\n                    commodities.add(commodity);\n                }\n                \n            } catch (Exception e) {\n                log.error(\"组装商品数据失败，规格ID：{}\", redisSpec.getSpecId(), e);\n                // 单个商品失败不影响其他商品\n            }\n        }\n        \n        log.info(\"商品数据组装完成，成功数量：{}/{}\", commodities.size(), redisSpecs.size());\n        return commodities;\n    }\n    \n    /**\n     * 组装单个商品数据\n     */\n    private CartCommodityRespDTO assembleSingleCommodity(CartCommodityDTO redisSpec,\n                                                         Map<String, StoreSpec> storeSpecMap,\n                                                         Map<String, ActivitySpecDTO> activitySpecMap,\n                                                         Boolean isVip,\n                                                         CartCommodityGetDTO dto) {\n        // 1. 创建商品响应对象\n        CartCommodityRespDTO commodity = new CartCommodityRespDTO();\n        BeanUtils.copyProperties(redisSpec, commodity);\n        \n        // 2. 获取商品中台数据\n        String storeSpecKey = redisSpec.getStoreId() + \"_\" + redisSpec.getSpecId();\n        StoreSpec storeSpec = storeSpecMap.get(storeSpecKey);\n        \n        if (storeSpec == null) {\n            log.warn(\"未找到商品规格信息，规格ID：{}，门店ID：{}\", redisSpec.getSpecId(), redisSpec.getStoreId());\n            return null;\n        }\n        \n        // 3. 整合商品中台数据\n        commodity.packCommodity(storeSpec);\n        \n        // 4. 处理VIP价格\n        if (isVip != null && isVip && storeSpec.isUseVipPrice(isVip)) {\n            commodity.setIsVip(YesOrNoType.YES.getCode());\n            commodity.setOriginPrice(storeSpec.getPrice());\n            commodity.setPrice(storeSpec.getVipPrice());\n            commodity.setBeforePrice(storeSpec.getVipPrice());\n            commodity.setGoodsSalesPrice(storeSpec.getVipPrice());\n        }\n        \n        // 5. 处理活动信息\n        String activityKey = redisSpec.getSpecId() + \"_\" + redisSpec.getStoreId();\n        ActivitySpecDTO activitySpec = activitySpecMap.get(activityKey);\n        if (activitySpec != null) {\n            applyActivityInfo(commodity, activitySpec);\n        }\n        \n        // 6. 设置门店名称\n        commodity.setStoreName(getStoreName(redisSpec.getStoreId(), dto));\n        \n        // 7. 设置配送天数\n        commodity.setDistributionDay(7); // 默认7天\n        \n        return commodity;\n    }\n    \n    /**\n     * 应用活动信息\n     */\n    private void applyActivityInfo(CartCommodityRespDTO commodity, ActivitySpecDTO activitySpec) {\n        try {\n            // 设置活动价格\n            if (activitySpec.getActPrice() != null) {\n                commodity.setPrice(activitySpec.getActPrice());\n                commodity.setGoodsSalesPrice(activitySpec.getActPrice());\n            }\n            \n            // 设置活动列表\n            if (!CollectionUtils.isEmpty(activitySpec.getActivityList())) {\n                commodity.setCommodityLevelActivities(activitySpec.getActivityList());\n            }\n            \n        } catch (Exception e) {\n            log.error(\"应用活动信息失败，商品：{}\", commodity.getSpecId(), e);\n        }\n    }\n    \n    /**\n     * 获取门店名称\n     */\n    private String getStoreName(String storeId, CartCommodityGetDTO dto) {\n        if (storeId.startsWith(LocalConst.CLOUD_FIXED_STORE)) {\n            return LocalConst.CLOUD_FIXED_STORE_NAME;\n        } else if (LocalConst.MERCHANT_B2C_FIXED_STORE.equals(storeId)) {\n            return LocalConst.MERCHANT_B2C_FIXED_STORE_NAME;\n        } else {\n            // 从当前门店信息中获取\n            return dto.getCurrentStoreName() != null ? dto.getCurrentStoreName() : \"门店\";\n        }\n    }\n    \n    /**\n     * 构建响应数据\n     */\n    private SingleStoreCartCommodityRespDTO buildResponse(List<CartCommodityRespDTO> commodities, \n                                                          CartCommodityGetDTO dto) {\n        SingleStoreCartCommodityRespDTO response = new SingleStoreCartCommodityRespDTO();\n        \n        // 设置门店基础信息\n        response.setStoreId(dto.getCurrentStoreId());\n        response.setStoreName(getStoreName(dto.getCurrentStoreId(), dto));\n        response.setMerCode(dto.getMerCode());\n        response.setIsValid(IsvalidStatus.EFFECTIVE.getCode());\n        response.setIsCurrentStore(YesOrNoType.YES.getCode());\n        \n        // 设置商品列表\n        response.setCommodities(commodities);\n        \n        // 计算价格信息\n        response.calculatePrices();\n        \n        // 设置配送信息\n        response.setIsDistribution(YesOrNoType.YES.getCode());\n        response.setDistributionDay(7);\n        \n        return response;\n    }\n    \n    /**\n     * 处理Plus会员优惠\n     */\n    private void handlePlusThrift(SingleStoreCartCommodityRespDTO response, \n                                 List<ActivitySpecDTO> activitySpecs, \n                                 CartCommodityGetDTO dto) {\n        try {\n            // 这里应该调用Plus会员优惠计算逻辑\n            // 简化处理，设置空对象\n            PredThriftRespDTO predThrift = new PredThriftRespDTO();\n            response.setPredThriftRespDTO(predThrift);\n            \n        } catch (Exception e) {\n            log.error(\"处理Plus会员优惠失败\", e);\n        }\n    }\n    \n    /**\n     * 创建空响应\n     */\n    private SingleStoreCartCommodityRespDTO createEmptyResponse(CartCommodityGetDTO dto) {\n        SingleStoreCartCommodityRespDTO response = new SingleStoreCartCommodityRespDTO();\n        \n        response.setStoreId(dto.getCurrentStoreId());\n        response.setStoreName(getStoreName(dto.getCurrentStoreId(), dto));\n        response.setMerCode(dto.getMerCode());\n        response.setIsValid(IsvalidStatus.EFFECTIVE.getCode());\n        response.setIsCurrentStore(YesOrNoType.YES.getCode());\n        response.setCommodities(Lists.newArrayList());\n        response.calculatePrices();\n        \n        return response;\n    }\n}"
