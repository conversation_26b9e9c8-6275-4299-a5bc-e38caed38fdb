package cn.hydee.ydjia.merchantcustomer.service;

import cn.hydee.ydjia.merchantcustomer.domain.ShopTransportarea;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

/**
 * (ShopTransportarea)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-31 14:17:49
 */
public interface ShopTransportareaService {

    /**
     * 通过ID查询单条数据
     *
     * @param guid 主键
     * @return 实例对象
     */
    ShopTransportarea queryById(String guid);

    /**
     * 分页查询
     *
     * @param shopTransportarea 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<ShopTransportarea> queryByPage(ShopTransportarea shopTransportarea, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param shopTransportarea 实例对象
     * @return 实例对象
     */
    ShopTransportarea insert(ShopTransportarea shopTransportarea);

    /**
     * 修改数据
     *
     * @param shopTransportarea 实例对象
     * @return 实例对象
     */
    ShopTransportarea update(ShopTransportarea shopTransportarea);

    /**
     * 通过主键删除数据
     *
     * @param guid 主键
     * @return 是否成功
     */
    boolean deleteById(String guid);

}
