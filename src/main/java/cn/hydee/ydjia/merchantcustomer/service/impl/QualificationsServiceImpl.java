package cn.hydee.ydjia.merchantcustomer.service.impl;

import cn.hydee.ydjia.merchantcustomer.dto.fegin.QualificationsDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.AreaResDTO;
import cn.hydee.ydjia.merchantcustomer.service.PlatformInnerServerService;
import cn.hydee.ydjia.merchantcustomer.service.QualificationsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class QualificationsServiceImpl implements QualificationsService {
    @Autowired
    private PlatformInnerServerService platformInnerServerService;

    public QualificationsDTO areaQualificationsInfo(String cityName) {
        AreaResDTO areaResDTO = platformInnerServerService.getRegionService().city(cityName);
        if(Objects.nonNull(areaResDTO)) {
            return platformInnerServerService.getManagerService().qualificationConfig(String.valueOf(areaResDTO.getParentId()));
        }
        return null;
    }
}
