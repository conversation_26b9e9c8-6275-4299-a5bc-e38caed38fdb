package cn.hydee.ydjia.merchantcustomer.interceptor;


import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.config.RequiredAuthConfig;
import cn.hydee.ydjia.merchantcustomer.enums.PlatformEnum;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MemberInfoDTO;
import cn.hydee.ydjia.merchantcustomer.util.HttpInterceptUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.*;

/**
 * 弱校验拦截器,如果能获取到userId则放入header中,不能则放行
 * 为兼容新老服务,对token校验也将分别处理
 */
@Slf4j
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "supports-auth-interceptor")
public class SupportsAuthInterceptor extends AbstractAuthInterceptorBase implements HandlerInterceptor {
    private List<String> pathPatterns;
    @Autowired
    private RedisTemplate redisTemplate2;
    @Autowired
    private RequiredAuthConfig requiredAuthConfig;
    @Value("${auth-interceptor.login-auth-supports:true}")
    private Boolean loginAuthSupports;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!loginAuthSupports) {
            return true;
        }
        Optional<String> oldAuthFlagOp = getTokenFromRequestToWeapp(request, "oldAuthFlag");
        if (oldAuthFlagOp.isPresent()) {
            String oldAuthFlag = oldAuthFlagOp.get();
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(oldAuthFlag, Boolean.FALSE.toString())) {
                return true;
            }
        }
        log.info("[Old-SupportsAuthInterceptor] RequestURI:" + request.getRequestURI());

        PlatformEnum platform = getUserPlatform(request);
        if (Objects.nonNull(platform) && MINI_PROGRAM.equals(platform.name())) {
            HttpInterceptUtil.setHeader(request, HEAD_USER_ACCESS_CHANNEL, MINI_PROGRAM);
        }

        Optional<String> optional = getTokenFromRequest(request, USER_KEY);
        log.info("optional:{}", optional);
        log.info("referer:{}", request.getHeader("referer"));
        if (!optional.isPresent()) return true;

        String tokenKey = optional.get();

        //前端页面调用接口，先查redis是否有用户token，有则放行接口
        //若用户已登陆/授权，将userId置入请求头
        if (isNewPlatform(request)) {
            processNewTokenAuth(request, tokenKey);
        } else {
            processOldTokenAuth(request, tokenKey);
        }

        return true;
    }

    private void processNewTokenAuth(HttpServletRequest request, String tokenKey) {
        String merCode = getMerCodeFromRequest(request).get();
        ResponseBase<MemberInfoDTO> loginResponse = memberManageClient.checkToken(merCode, tokenKey, false);
        if (loginResponse.checkSuccess() && loginResponse.getData() != null) {
            String userId = loginResponse.getData().getUserId().toString();
            HttpInterceptUtil.setHeader(request, HEAD_USER_ID_KEY, userId);
            PlatformEnum from = getUserAgent(request);
            String appId = multipleWeeChatMerConfiguration.isMultipleAppMerCode(merCode) && PlatformEnum.isWeeAppByEnumName(from.name()) ? getAppIdFromHeader(request) : null;

            HttpInterceptUtil.setHeader(request, HEAD_OPEN_ID_KEY, loginResponse.getData().getOpenIdBy(from.name(), appId));
            HttpInterceptUtil.setHeader(request, HEAD_MEMBER_CARD_KEY, loginResponse.getData().getMemberCard());
            HttpInterceptUtil.setHeader(request, HEAD_EMPLOYEE_FLAG, loginResponse.getData().getEmpFlag());
        }
    }


    private void processOldTokenAuth(HttpServletRequest request, String tokenKey) {
        String merCode = request.getHeader("merCode");
        String merCodeCache = getRedisUserInfo(LocalConst.MERCHANT_CODE, tokenKey);
        log.info("商户编号  merCode:{}; merCodeCache:{}", merCode, merCodeCache);
        //先取redis是否有用户登陆信息，需对应商户编号
        if (!StringUtils.isEmpty(merCode) && merCode.equals(merCodeCache)) {
            String userId = getRedisUserInfo(LocalConst.REDIS_USER_ID, tokenKey);
            HttpInterceptUtil.setHeader(request, LocalConst.HEAD_USER_ID_KEY, userId);
        }
    }

    /**
     * 取营销平台Redis 用户登录信息
     *
     * @param hkey
     * @param tokenKey
     * @return
     */
    private String getRedisUserInfo(String hkey, String tokenKey) {
        String token = (String) redisTemplate2.opsForHash().get(LocalConst.TOKEN_KEY, tokenKey);
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        String infoStr = (String) redisTemplate2.opsForHash().get(LocalConst.REDIS_SESSION_KEY, token);
        if (StringUtils.isEmpty(infoStr)) {
            return null;
        }
        return JSONObject.parseObject(infoStr).getString(hkey);
    }
}
