package cn.hydee.ydjia.merchantcustomer.interceptor;


import cn.hydee.ydjia.merchantcustomer.feign.AccountClient;
import cn.hydee.ydjia.merchantcustomer.feign.LianOuAuthClient;
import cn.hydee.ydjia.merchantcustomer.feign.PrescriptionApprovalClient;
import cn.hydee.ydjia.merchantcustomer.feign.service.MallRedisService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "lianou-auth-interceptor")
public class LianOUAuthInterceptor implements HandlerInterceptor {

    private List<String> pathPatterns;
    private List<String> excludePath;
    @Autowired
    private MallRedisService redisService;

    @Autowired
    @Lazy
    private LianOuAuthClient lianOuAuthClient;

    @Autowired
    @Lazy
    private AccountClient accountClient;

    @Autowired
    @Lazy
    private PrescriptionApprovalClient prescriptionApprovalClient;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return true;
    }

}
