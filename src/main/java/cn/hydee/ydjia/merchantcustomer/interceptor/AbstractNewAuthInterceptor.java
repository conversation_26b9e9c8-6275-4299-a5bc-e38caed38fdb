package cn.hydee.ydjia.merchantcustomer.interceptor;

import cn.hydee.ydjia.merchantcustomer.util.HttpInterceptUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.util.Assert;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Optional;

import static cn.hydee.ydjia.merchantcustomer.util.ServletUtils.getHeaderIgnoreCase;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/15 17:47
 */
@Slf4j
public abstract class AbstractNewAuthInterceptor extends AbstractAuthInterceptorBase implements HandlerInterceptor {
    private static final String DEV_FLAG = "1";

    /**
     * doInnerPreHandle
     *
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    public abstract boolean doInnerPreHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception;

    String marshallerFromUrl(HttpServletRequest request, String charset) throws UnsupportedEncodingException {
        String referer = request.getHeader("referer");
        if (StringUtils.isNotBlank(referer)) {
            if (StringUtils.isNotBlank(request.getQueryString())) {
                referer = referer + "&" + request.getQueryString();
            }
            return URLEncoder.encode(referer, charset);
        }
        return StringUtils.EMPTY;
    }

    public boolean devEnviron(boolean testUserFlag, HttpServletRequest request) {
        // dev/test dedicated channel
        if (testUserFlag && StringUtils.equalsAnyIgnoreCase(getHeaderIgnoreCase("devFlag"), DEV_FLAG)) {
            log.debug("[New-AuthInterceptor] devEnviron  ignore auth ");
            HttpInterceptUtil.setHeader(request, LocalConst.HEAD_USER_ID_KEY, getHeaderIgnoreCase("userId"));
            return true;
        }
        return false;
    }


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("[NewAuthInterceptor] URI:{} ", request.getRequestURI());

        Optional<String> merCode = getMerCodeFromRequest(request);
        Assert.isTrue(merCode.isPresent(), "Missing merCode");
        setMerCodeCache(merCode.get());
        boolean isNew = isNewPlatform(merCode.get());
        //ignore old merchant platform auth
        log.debug("[NewAuthInterceptor] merchant is new platform={}", isNew);
        if (!isNew) return true;

        return doInnerPreHandle(request, response, handler);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        clearContext();
    }


    private void clearContext() {
        try {
            MDC.clear();
        } catch (Exception ignore) {
        }
    }


    private void setMerCodeCache(String merCode) {
        MDC.put("merCode", merCode);
    }

}
