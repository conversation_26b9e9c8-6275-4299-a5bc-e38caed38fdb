package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.resp.PopupAdsResp;
import cn.hydee.ydjia.merchantcustomer.dto.resp.PopupCzzlResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = {"弹窗广告"}, value = "弹窗广告")
@RequestMapping("/${api.version}/popup-ads")
public class PopupAdsController extends AbstractController {

    @Autowired
    public PopupAdsResp popupAdsResps;
    @Autowired
    private PopupCzzlResp popupCzzlResp;

    @ApiOperation("获取地域配置")
    @GetMapping("area")
    public ResponseBase<PopupAdsResp> area() {
        return generateSuccess(popupAdsResps);
    }


    @ApiOperation(value = "成长阻力配置", tags = "YXDJ-1324")
    @GetMapping("/czzl")
    public ResponseBase<PopupCzzlResp> czzl() {
        return generateSuccess(popupCzzlResp);
    }
}
