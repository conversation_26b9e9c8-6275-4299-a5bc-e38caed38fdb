package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.starter.configuration.DisLockConfiguration;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.ydjia.merchantcustomer.config.SpringContextHolder;
import cn.hydee.ydjia.merchantcustomer.config.WeeChatParamsConfiguration;
import cn.hydee.ydjia.merchantcustomer.dto.EditMaternalInfoReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.EmployeeResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.WeeChatMaParseUserInfoDTO;
import cn.hydee.ydjia.merchantcustomer.dto.WeeChatParseCodeResultDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.collection.UserCollectionDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MaternalInformationDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MemberArticleRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MemberAttendanceSettingResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.collection.UserCollectionResp;
import cn.hydee.ydjia.merchantcustomer.dto.resp.member.MaternalInfoVO;
import cn.hydee.ydjia.merchantcustomer.dto.third.req.MemberStatusReqDTO;
import cn.hydee.ydjia.merchantcustomer.enums.ComOperateTypeEnum;
import cn.hydee.ydjia.merchantcustomer.enums.MemberStatusEnum;
import cn.hydee.ydjia.merchantcustomer.enums.PlatformEnum;
import cn.hydee.ydjia.merchantcustomer.enums.StatusChangeReasonEnum;
import cn.hydee.ydjia.merchantcustomer.feign.*;
import cn.hydee.ydjia.merchantcustomer.feign.honey.EmployeeRoleInfoResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MemberDTO;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MemberInfoDTO;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MemberInfoRequest;
import cn.hydee.ydjia.merchantcustomer.feign.mdmmiration.StoreClientAdapter;
import cn.hydee.ydjia.merchantcustomer.service.MemberInfoService;
import cn.hydee.ydjia.merchantcustomer.service.RedisService;
import cn.hydee.ydjia.merchantcustomer.service.UserCollectionService;
import cn.hydee.ydjia.merchantcustomer.util.AESUtils;
import cn.hydee.ydjia.merchantcustomer.util.ExceptionUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.ValidateUtils;
import com.yxt.lang.constants.ApiBizCodeEnum;
import com.yxt.lang.util.JsonUtils;
import com.yxt.middle.integral.api.integral.IntegralPointApi;
import com.yxt.middle.member.res.vip.MemberVipConfigInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.hydee.ydjia.merchantcustomer.controller.WeChatOpenManageController.printLogging;
import static cn.hydee.ydjia.merchantcustomer.dto.resp.MemberAttendanceSettingResDTO.DISABLE;
import static cn.hydee.ydjia.merchantcustomer.enums.MessageTemplateEnum.UPDATE_PHONE_VERIFICATION_CODE;
import static cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType.*;
import static cn.hydee.ydjia.merchantcustomer.feign.util.LocalConst.PRESCRIPTION_DRUG_COMPLIANCE_VERSION_PARAM;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.USER_KEY;
import static cn.hydee.ydjia.merchantcustomer.util.RedisKeyUtil.getMiniCacheKey;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/10 11:26
 */
@Slf4j
@RestController
@RequestMapping("/${api.version}/member")
@AllArgsConstructor
@Api(value = "会员信息接口管理器", description = "会员信息接口管理器")
public class MemberInfoController extends AbstractController {

    private final MemberInfoClient memberInfoClient;
    private final WeeChatParamsConfiguration paramsConfiguration;
    private final MemberCardClient memberCardClient;
    private final WeeChatMpClient weeChatMpClient;
    private final WeeChatMaClient weeChatMaClient;
    private final RedisService redisService;
    private static final String FROM_PAYMENT = "4";
    private final UserCollectionService userCollectionService;
    private final MemberRegisterClient memberRegisterClient;
    private final WebsiteMessageClient websiteMessageClient;
    private final ActivityCouponBusClient couponBusClient;
    private final MemberSignClient memberSignClient;
    private final MemberGradeClient memberGradeClient;
    private final MemberInfoService memberInfoService;
    private final MemberManageClient memberManageClient;
    private final StoreClientAdapter storeClientAdapter;
    private ThreadPoolTaskExecutor commQueryExecutor;

    @ApiOperation(value = "根据token查询会员基础信息", notes = "内购 - lwy-v1.1.4")
    @GetMapping("/queryByToken")
    public ResponseBase<MemberInfoDTO> checkToken(@RequestHeader(value = LocalConst.MER_CODE) String merCode,
                                                  @RequestHeader(value = USER_KEY, required = false) String token) {
        if (StringUtils.isBlank(token)) {
            return generateSuccess(null);
        }
        ResponseBase<MemberInfoDTO> responseBase = memberManageClient.checkToken(merCode, token, false);
        if (responseBase.checkSuccess() && responseBase.getData() != null) {
            MemberInfoDTO memberInfoDTO = responseBase.getData();
            memberInfoDTO.setUserIdString(memberInfoDTO.getUserId().toString());

            EmployeeResDTO employeeInfo =  memberInfoService.getEmpInfoByMobileCache(merCode,memberInfoDTO.getMemberPhone());
            if (Objects.nonNull(employeeInfo) && LocalConst.STATUS_ONE.equals(employeeInfo.getEmpStatus())){
                memberInfoDTO.setEmployeeFlag(true);
                memberInfoDTO.setEmpCode(employeeInfo.getEmpCode());
                memberInfoDTO.setSubOrgCode(employeeInfo.getSubOrgCode());
                memberInfoDTO.setEmpName(employeeInfo.getEmpName());
            } else {
                memberInfoDTO.clearEmpInfo();
            }

            return generateSuccess(memberInfoDTO);
        }
        return generateSuccess(null);
    }


    @GetMapping("/details/search/{merCode}")
    public ResponseBase<MemberDTO> details(@PathVariable("merCode") String merCode,
                                           @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                           @RequestHeader(USER_KEY) String tokenKey,
                                           @RequestHeader(value = LocalConst.HEAD_APPID_KEY, required = false) String appid) {
        ResponseBase<MemberDTO> responseBase = memberInfoClient.details(merCode, Long.parseLong(userId), appid);
        checkResult(responseBase);
        if (Objects.nonNull(responseBase.getData())) {
            MemberDTO memberDTO = responseBase.getData();
            buildMemberInfo(memberDTO, merCode, userId);
        } else {
            log.info("会员登出merCode:{}, token:{}", merCode, tokenKey);
            memberManageClient.logout(merCode, tokenKey, PlatformEnum.OFFIACCOUNT.name());
        }
        return responseBase;
    }

    @GetMapping("/details/encrypt/search/{merCode}")
    public ResponseBase<MemberDTO> encryptDetails(@PathVariable("merCode") String merCode,
                                                  @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                  @RequestHeader(USER_KEY) String tokenKey,
                                                  @RequestHeader(value = LocalConst.HEAD_APPID_KEY, required = false) String appid) {
        ResponseBase<MemberDTO> responseBase = memberInfoClient.details(merCode, Long.parseLong(userId), appid);
        checkResult(responseBase);
        if (Objects.nonNull(responseBase.getData())) {
            MemberDTO memberDTO = responseBase.getData();
            buildMemberInfo(memberDTO, merCode, userId);
            if (StringUtils.isNotBlank(memberDTO.getMemberPhone())) {
                memberDTO.setMemberPhone(AESUtils.encrypt(memberDTO.getMemberPhone()));
            }
            if (StringUtils.isNotBlank(memberDTO.getMemberIdcard())) {
                memberDTO.setMemberIdcard(AESUtils.encrypt(memberDTO.getMemberIdcard()));
            }
            if (StringUtils.isNotBlank(memberDTO.getMemberName())) {
                memberDTO.setMemberName(AESUtils.encrypt(memberDTO.getMemberName()));
            }
        } else {
            log.info("encryptDetails会员登出merCode:{}, token:{}", merCode, tokenKey);
            memberManageClient.logout(merCode, tokenKey, PlatformEnum.OFFIACCOUNT.name());
        }
        return responseBase;
    }

    @GetMapping("/details/getMemberBaseInfo")
    public ResponseBase<MemberDTO> getMemberBaseInfo(@RequestHeader(value = "merCode") String merCode,
                                                     @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                     @RequestHeader(USER_KEY) String tokenKey) {
        ResponseBase<MemberDTO> responseBase = memberInfoClient.getMemberBaseInfo(merCode, userId);
        checkResult(responseBase);
        if (Objects.nonNull(responseBase.getData())) {
            MemberDTO memberDTO = responseBase.getData();
            buildMemberInfo(memberDTO, merCode, userId);
        } else {
            log.info("getMemberBaseInfo会员登出merCode:{}, token:{}", merCode, tokenKey);
            memberManageClient.logout(merCode, tokenKey, PlatformEnum.OFFIACCOUNT.name());
        }
        return responseBase;
    }
    @ApiOperation(value = "用户信息查询", tags = {"2516-修改"})
    @GetMapping("/details/encrypt/getMemberBaseInfo")
    public ResponseBase<MemberDTO> getEncryptMemberBaseInfo(@RequestHeader(value = "merCode") String merCode,
                                                            @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                            @RequestHeader(USER_KEY) String tokenKey) {
        log.info("查询会员基础信息merCode:{}, userId:{}, tokenKey:{}", merCode, userId, tokenKey);
        ResponseBase<MemberDTO> responseBase = memberInfoClient.getMemberBaseInfo(merCode, userId);
        checkResult(responseBase);
        if (Objects.nonNull(responseBase.getData())) {
            MemberDTO memberDTO = responseBase.getData();
            buildMemberInfo(memberDTO, merCode, userId);
            if (StringUtils.isNotBlank(memberDTO.getMemberPhone())) {
                memberDTO.setMemberPhone(AESUtils.encrypt(memberDTO.getMemberPhone()));
            }
            if (StringUtils.isNotBlank(memberDTO.getMemberIdcard())) {
                memberDTO.setMemberIdcard(AESUtils.encrypt(memberDTO.getMemberIdcard()));
            }
            if (StringUtils.isNotBlank(memberDTO.getMemberName())) {
                memberDTO.setMemberName(AESUtils.encrypt(memberDTO.getMemberName()));
            }
            if(BooleanUtil.isTrue(memberDTO.getPaidMember())) {
                MemberVipConfigInfoVo memberVipConfigInfoVo = super.getVipConfigInfo(userId);
                memberDTO.setCycleType(memberVipConfigInfoVo.getCycleType());
            }
        } else {
            log.info("getEncryptMemberBaseInfo会员登出merCode:{}, token:{}", merCode, tokenKey);
            memberManageClient.logout(merCode, tokenKey, PlatformEnum.OFFIACCOUNT.name());
        }
        return responseBase;
    }

    @GetMapping("/details/getMemberOfflineIntegral")
    public ResponseBase<Integer> getMemberOfflineIntegral(@RequestHeader(value = "merCode") String merCode,
                                                          @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        try {
            ResponseBase<Integer> responseBase = memberInfoClient.getMemberOfflineIntegral(merCode, userId);
            checkResult(responseBase);
            return responseBase;
        } catch (Exception e) {
            log.warn("erp会员积分查询失败", e);
        }
        return generateSuccess(0);
    }


    private void buildMemberInfo(MemberDTO memberDTO, String merCode, String userId) {
        memberDTO.setUserId(null);
        if (Objects.isNull(memberDTO.getChangeamt())) {
            memberDTO.setChangeamt("0");
        }
        //会员消息数量
        WebsiteMessageQueryRequest request = new WebsiteMessageQueryRequest();
        request.setMerCode(merCode);
        request.setUserId(Long.valueOf(userId));
        request.setState(0);
        ResponseBase<Integer> response = websiteMessageClient.countUserMessage(request);
        memberDTO.setWebsiteMessageNum(response.getData());
        //优惠券数量
        memberDTO.setCouponNum(0L);
        ResponseBase<Integer> couponBase = couponBusClient.queryUserUnUseCouponNum(merCode, Long.valueOf(userId));
        if (couponBase.checkSuccess() && couponBase.getData() != null) {
            memberDTO.setCouponNum(couponBase.getData().longValue());
        }
        //会员签到规则
        ResponseBase<MemberAttendanceSettingResDTO> settingResponse = memberSignClient.queryAttendanceSetting(merCode);
        MemberAttendanceSettingResDTO settingResDTO;
        if (Objects.isNull(settingResDTO = settingResponse.getData()) || settingResDTO.getIsValid().equals(DISABLE)) {
            memberDTO.setMemberSignRule(0);
        } else {
            memberDTO.setMemberSignRule(1);
        }
        //是否存在erp卡种等级
        ResponseBase<Boolean> categoryResponse = memberGradeClient.isSyncErpGrade(merCode);
        if (categoryResponse.checkSuccess() && categoryResponse.getData()) {
            memberDTO.setIsErpCard(true);
        } else {
            memberDTO.setIsErpCard(false);
        }
        if (StringUtils.isNotBlank(memberDTO.getMemberPhone()) && memberDTO.getMemberPhone().length() > 11) {
            memberDTO.setMemberPhone(memberDTO.getMemberPhone().substring(0, 11));
        }
        // 加载员工信息
        EmployeeResDTO employeeInfo =  memberInfoService.getEmpInfoByMobileCache(merCode, memberDTO.getMemberPhone());
        if (Objects.nonNull(employeeInfo) && LocalConst.STATUS_ONE.equals(employeeInfo.getEmpStatus())){
            memberDTO.setEmployeeFlag(true);
        }
    }


    @PostMapping("/details/edit")
    @ApiOperation(value = "编辑会员信息", notes = "编辑会员信息")
    public ResponseBase edit(@RequestHeader(value = "merCode") String merCode,
                             @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                             @RequestBody MemberInfoRequest memberInfoRequest) {
        memberInfoRequest.setUserId(Long.valueOf(userId));
        memberInfoRequest.setMerCode(merCode);
        log.info("编辑会员信息  request={}", memberInfoRequest);
        return memberInfoClient.edit(memberInfoRequest);
    }

    @PostMapping("/details/encrypt/edit")
    @ApiOperation(value = "编辑会员信息", notes = "编辑会员信息")
    public ResponseBase encryptEdit(@RequestHeader(value = "merCode") String merCode,
                                    @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                    @RequestBody MemberInfoRequest memberInfoRequest) {
        memberInfoRequest.setUserId(Long.valueOf(userId));
        memberInfoRequest.setMerCode(merCode);
        if (StringUtils.isNotBlank(memberInfoRequest.getMemberPhone())) {
            memberInfoRequest.setMemberPhone(AESUtils.decrypt(memberInfoRequest.getMemberPhone()));
        }
        if (StringUtils.isNotBlank(memberInfoRequest.getMemberIdcard())) {
            memberInfoRequest.setMemberIdcard(AESUtils.decrypt(memberInfoRequest.getMemberIdcard()));
        }
        if (StringUtils.isNotBlank(memberInfoRequest.getMemberName())) {
            memberInfoRequest.setMemberName(AESUtils.decrypt(memberInfoRequest.getMemberName()));
        }
        return memberInfoClient.edit(memberInfoRequest);
    }


    @ApiOperation("点击支付消息跳转开卡地址")
    @ApiIgnore
    @GetMapping("/openCard")
    public void openCard(@RequestParam String merCode, HttpServletRequest httpServletRequest,
                         HttpServletResponse httpServletResponse) throws IOException {
        log.info("[openCardAfterPayment] BODY : {}", printLogging(httpServletRequest));
        ResponseBase<String> response = memberCardClient.getCardId(merCode);
        if (!response.checkSuccess()) {
            log.error("[openCardAfterPayment] not found merchant cardInfo merCode={} responseCode={} msg={}",
                    merCode, response.getCode(), response.getMsg());
            return;
        }

        ResponseBase<String> activateResponse = weeChatMpClient
                .getActivateUrl(response.getData(), merCode, FROM_PAYMENT);
        String redirect;
        if (StringUtils.isBlank(redirect = activateResponse.getData())) {
            redirect = paramsConfiguration.getUserCompleteAuthPageRedirectUrl() + merCode;
        }
        httpServletResponse.sendRedirect(redirect);
    }

    @PostMapping("/mini/parseMemberInfo")
    @ApiOperation(value = "解析会员信息", notes = "解析会员信息")
    public ResponseBase parseMemberInfo(@RequestBody WeeChatMiniParseReqDTO miniParseReqDTO,
                                        @RequestHeader(value = LocalConst.HEAD_APPID_KEY, required = false) String appid) {
        miniParseReqDTO.setAppid(appid);
        log.info("Parse memberInfo req={}", miniParseReqDTO);
        ResponseBase<WeeChatParseCodeResultDTO> responseBase = weeChatMaClient.login(miniParseReqDTO.getCode(), miniParseReqDTO.getAppid(), "1", miniParseReqDTO.getMerCode());
        if (!responseBase.checkSuccess() || responseBase.getData() == null) {
            log.error("Parse code error response={} {} {}", responseBase.getCode(), responseBase.getMsg(), miniParseReqDTO);
            throw WarnException.builder().code(WX_USER_INFO_PARSE_ERROR.getCode())
                    .message(WX_USER_INFO_PARSE_ERROR.getMsg()).build();
        }

        WeeChatParseCodeResultDTO codeResultDTO = responseBase.getData();

        log.debug("Parse code result={}", codeResultDTO);

        miniParseReqDTO.setSessionKey(codeResultDTO.getSessionKey());
        log.debug("Invoke parseUserInfo req={}", miniParseReqDTO);
        ResponseBase<WeeChatMaParseUserInfoDTO> parseUserInfoResponse = weeChatMaClient.parseUserInfo(miniParseReqDTO, miniParseReqDTO.getAppid(), "1");
        checkResult(parseUserInfoResponse);

        WeeChatMaParseUserInfoDTO userInfoDTO = parseUserInfoResponse.getData();
        redisService.setValueExpireMinute(getMiniCacheKey(miniParseReqDTO.getMerCode() + "_" + codeResultDTO.getOpenid()),
                userInfoDTO, 60);

        return ResponseBase.success();
    }

    @ApiOperation(value = "查询用户是否登录")
    @PostMapping(value = "/_queryIsLogin")
    public ResponseBase<Boolean> queryIsLogin(@RequestHeader(value = LocalConst.USER_ID, required = false) String userId) {
        return generateSuccess(!org.springframework.util.StringUtils.isEmpty(userId));
    }

    @ApiOperation(value = "修改会员经纬度", notes = "修改会员经纬度")
    @PostMapping("/updateMemberLatitude")
    public ResponseBase<Boolean> updateMemberLatitude(@RequestHeader(value = "merCode") String merCode,
                                                      @RequestHeader(value = "userId", required = false, defaultValue = "0") Long userId,
                                                      @RequestBody MemberLocationRequest request) {
        if (userId.equals(0L) || !request.validate()) {
            log.warn("[updateMemberLatitude] user has not login merCode={} {}", merCode, request);
            return ResponseBase.success();
        }
        commQueryExecutor.execute(() -> {
            SpringContextHolder.getBean(MemberInfoController.class).updateLatitude(merCode, userId, request);
        });
        return ResponseBase.success();
    }

    @DisLockConfiguration.DisLock(keyExpr = "'updateMemberLatitude:' + #userId",
            lockType = DisLockConfiguration.DisLock.LockType.OPTIMISTIC, expireTime = 3, timeUnit = TimeUnit.SECONDS)
    public void updateLatitude(String merCode, Long userId, MemberLocationRequest request) {
        memberInfoClient.updateMemberLatitude(merCode, userId, request.getLongitude(), request.getLatitude());
    }

    @ApiOperation(value = "查询用户收藏列表")
    @PostMapping(value = "/collection")
    public ResponseBase<PageDTO<UserCollectionResp>> queryCollectionList(@RequestBody UserCollectionDTO req,
                                                                         @Nullable @RequestHeader(PRESCRIPTION_DRUG_COMPLIANCE_VERSION_PARAM) String prescriptionDrugComplianceVersion,
                                                                         @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        req.setPrescriptionDrugComplianceVersion(prescriptionDrugComplianceVersion);
        req.setUserId(userId);
        return generateSuccess(userCollectionService.queryCollectionList(req));
    }

    @ApiOperation(value = "收藏商品")
    @PostMapping(value = "/_collect")
    public ResponseBase<Boolean> collectGoods(@RequestBody UserCollectionDTO req
            , @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        req.setUserId(userId);
        return generateSuccess(userCollectionService.collectOrCancel(req));
    }

    @ApiOperation(value = "清空失效商品")
    @PostMapping(value = "/_emptyDisabled")
    public ResponseBase<Boolean> emptyDisabled() {
        return null;
    }

    @ApiOperation(value = "批量取消收藏商品")
    @PostMapping(value = "/_batchCancel")
    public ResponseBase<Boolean> batchCancelGoods(@RequestBody UserCollectionDTO req
            , @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        return generateSuccess(userCollectionService.batchCancelGoods(req, userId));
    }

    @ApiOperation(value = "文章管理", notes = "文章管理")
    @GetMapping("/queryMemberArticle")
    public ResponseBase<List<MemberArticleRespDTO>> queryMemberArticle(@NonNull @RequestParam("merCode") String merCode) {

        return memberInfoClient.queryMemberArticle(merCode);
    }

    @ApiOperation(value = "文章详情", notes = "文章详情")
    @GetMapping("/queryArticleDetail")
    public ResponseBase<MemberArticleRespDTO> queryArticleDetail(@RequestParam String merCode, @RequestParam String articleCode) {

        return memberInfoClient.queryArticleDetail(merCode, articleCode);
    }

    @ApiOperation(value = "修改手机号码", notes = "修改手机号码")
    @PostMapping("/updateMemberPhone")
    public ResponseBase<Boolean> updateMemberPhone(@RequestBody MemberUpdateMoblieReqDTO memberUpdateMoblieReqDTO, @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        ResponseBase<String> responseBase = memberRegisterClient.getSmsCode(memberUpdateMoblieReqDTO.getMemberPhone(),
                UPDATE_PHONE_VERIFICATION_CODE.getCode());

        ValidateUtils.checkResult(responseBase);
        log.info("[updateMemberPhone] ==> smsCode={}  {}", responseBase.getData(), memberUpdateMoblieReqDTO);
        if (!StringUtils.equalsIgnoreCase(memberUpdateMoblieReqDTO.getSmsCode(), responseBase.getData())) {
            return ResponseBase.error(SMS_CODE_IS_ERROR.getCode(), SMS_CODE_IS_ERROR.getMsg());
        }

        MemberInfoRequest memberInfoRequest = new MemberInfoRequest();
        memberInfoRequest.setUserId(Long.valueOf(userId));
        memberInfoRequest.setMerCode(memberUpdateMoblieReqDTO.getMerCode());
        memberInfoRequest.setMemberPhone(memberUpdateMoblieReqDTO.getMemberPhone());
        ResponseBase<Integer> editResponse = memberInfoClient.edit(memberInfoRequest);
        if (!editResponse.checkSuccess()) {
            return ResponseBase.error(editResponse.getCode(), editResponse.getMsg());
        }
        return generateSuccess(true);
    }

    @ApiOperation(value = "修改手机号码", notes = "修改手机号码")
    @PostMapping("/encrypt/updateMemberPhone")
    public ResponseBase<Boolean> updateMemberEncryptPhone(@RequestBody MemberUpdateMoblieReqDTO memberUpdateMoblieReqDTO, @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        String memberPhone = AESUtils.decrypt(memberUpdateMoblieReqDTO.getMemberPhone());
        ResponseBase<String> responseBase = memberRegisterClient.getSmsCode(memberPhone,
                UPDATE_PHONE_VERIFICATION_CODE.getCode());
        ValidateUtils.checkResult(responseBase);
        log.info("[updateMemberPhone] ==> smsCode={}  {}", responseBase.getData(), memberUpdateMoblieReqDTO);
        if (!StringUtils.equalsIgnoreCase(memberUpdateMoblieReqDTO.getSmsCode(), responseBase.getData())) {
            return ResponseBase.error(SMS_CODE_IS_ERROR.getCode(), SMS_CODE_IS_ERROR.getMsg());
        }
        MemberInfoRequest memberInfoRequest = new MemberInfoRequest();
        memberInfoRequest.setUserId(Long.valueOf(userId));
        memberInfoRequest.setMerCode(memberUpdateMoblieReqDTO.getMerCode());
        memberInfoRequest.setMemberPhone(memberPhone);
        ResponseBase<Integer> editResponse = memberInfoClient.edit(memberInfoRequest);
        if (!editResponse.checkSuccess()) {
            return ResponseBase.error(editResponse.getCode(), editResponse.getMsg());
        }
        return generateSuccess(true);
    }


    @ApiOperation(value = "会员注销", notes = "会员注销")
    @PostMapping("/delMember")
    public ResponseBase<Boolean> delMember(@RequestHeader(value = "merCode") String merCode,
                                           @RequestHeader(value = "userId") String userId) {
        log.info("会员注销 delMember merCode = {} userId = {}", merCode, userId);
        ResponseBase<MemberDTO> responseBase = memberInfoClient.getMemberBaseInfo(merCode, userId);
        checkResult(responseBase);
        MemberDTO memberInfo = responseBase.getData();
        if (Objects.isNull(memberInfo) || MemberStatusEnum.EXPIRED.getCode().equals(memberInfo.getMemberStatus())) {
            return generateSuccess(true);
        }
        // 使用新接口注销
        MemberStatusReqDTO reqDTO = new MemberStatusReqDTO();
        reqDTO.setMerCode(merCode);
        reqDTO.setUserIdList(Collections.singletonList(userId));
        reqDTO.setMemberStatus(MemberStatusEnum.EXPIRED.getCode());
        // 15 自主注销
        reqDTO.setChangeStatusReason(String.valueOf(StatusChangeReasonEnum.REASON_EXPIRED_15.getKey()));
        reqDTO.setEmpCode(memberInfo.getMemberCard());
        reqDTO.setEmpName(Optional.ofNullable(memberInfo.getMemberName()).orElse("--"));
        reqDTO.setBizCode(ApiBizCodeEnum.WECHAT_APPLET.getCode());
        memberInfoClient.modMemberStatus(reqDTO);
        return generateSuccess(true);
    }

    @ApiOperation(value = "会员是否可以注销", notes = "会员是否可以注销")
    @PostMapping("/checkMemberDelete")
    public ResponseBase<Boolean> checkMemberDelete(@RequestHeader(value = "merCode") String merCode,
                                                   @RequestHeader(value = "userId") String userId) {
        log.info("会员是否可以注销 delMember merCode = {} userId = {}", merCode, userId);
        return memberInfoClient.checkMemberDelete(merCode, userId);
    }

    @ApiOperation(value = "商户定制拓客回传(登录未过期场景)")
    @PostMapping("/details/postToker")
    public ResponseBase<Boolean> postToker(@RequestHeader(value = LocalConst.MER_CODE) String merCode,
                                           @RequestHeader(value = LocalConst.USER_ID) Long userId,
                                           @RequestBody TokerReqDTO tokerReq) {
        return memberManageClient.postToker(merCode, userId, tokerReq);
    }


    @ApiOperation(value = "根据token查询员工基础信息", notes = "员工推广用")
    @PostMapping("/queryStaffByToken")
    public ResponseBase<EmployeeRoleInfoResDTO> checkStaffToken(@RequestBody EmpInfoReqDTO empInfoReqDTO,@RequestHeader(value = LocalConst.MER_CODE) String merCode,
                                                                @RequestHeader(value = USER_KEY, required = false) String token) {
        if (StringUtils.isBlank(token)) {
            return generateSuccess(null);
        }
        ResponseBase<MemberInfoDTO> responseBase = memberManageClient.checkToken(merCode, token, false);
        if (responseBase.checkSuccess() && responseBase.getData() != null) {
            EmployeeRoleInfoResDTO employeeRoleInfoResDTO = memberInfoService.queryInfoByEmpCode(empInfoReqDTO);
            employeeRoleInfoResDTO.setAvatarPath(responseBase.getData().getHeadUrl());
            return generateSuccess(employeeRoleInfoResDTO);
        }
        return generateSuccess(null);

    }

    @ApiOperation(value = "通过用户ID查询母婴信息", notes = "通过用户ID查询母婴信息")
    @GetMapping(value = "/queryMaternalInfo")
    public ResponseBase<MaternalInfoVO> queryMaternalInfo(@RequestHeader(LocalConst.HEAD_USER_ID_KEY) String hUserId, @ApiParam(required = true,value = "用户ID") @RequestParam(value = "userId") String userId){
        if (StrUtil.isEmpty(hUserId)){
            return generateError(ErrorType.PARA_ERROR);
        }
        ResponseBase<MaternalInformationDTO> responseBase = memberInfoClient.queryMaternalInfo(hUserId);
        checkResult(responseBase);
        MaternalInformationDTO maternalInformationDTO = responseBase.getData();
        if (Objects.isNull(maternalInformationDTO)){
            MaternalInfoVO maternalInfoVO = new MaternalInfoVO();
            maternalInfoVO.setUserId(Long.valueOf(hUserId));
            log.info("通过用户ID{}查询母婴信息信息为空",hUserId);
            return generateSuccess(maternalInfoVO);
        }
        //构建返回小前台的母婴信息
        MaternalInfoVO maternalInfoVO = builderMaternalVO(maternalInformationDTO);
        return generateSuccess(maternalInfoVO);

    }

    /**
     * 构建返回小前台的母婴信息
     * @param maternalInformationDTO
     * @return
     */
    private static MaternalInfoVO builderMaternalVO(MaternalInformationDTO maternalInformationDTO) {
        MaternalInfoVO maternalInfoVO = new MaternalInfoVO();
        List<MotherBabyInfoDTO> babyInfoDTOList = maternalInformationDTO.getMotherBabyInfo();
        if (CollectionUtils.isNotEmpty(babyInfoDTOList)){
            maternalInfoVO.setIsBorn(1);
            babyInfoDTOList.forEach(babyInfo ->{
                String babyBirthday = babyInfo.getBabyBirthday();
                if (StringUtils.isNotBlank(babyBirthday)){
                    babyInfo.setBabyBirthday(DateUtil.format(DateUtil.parseDate(babyBirthday, DateUtil.EN_YEAR_MONTH_DAY_FORMAT), DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                }
            });
        }

        Date dueDate = maternalInformationDTO.getDueDate();
        if (Objects.nonNull(dueDate)){
            maternalInfoVO.setIsBorn(0);
            maternalInfoVO.setDueDate(DateUtil.format(dueDate,DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
        }
        maternalInfoVO.setLikeMilkPowderBrand(maternalInformationDTO.getLikeMilkPowderBrand());
        maternalInfoVO.setRelationshipWithBaby(maternalInformationDTO.getRelationshipWithBaby());
        maternalInfoVO.setOpenStore(maternalInformationDTO.getOpenStore());
        maternalInfoVO.setUserId(maternalInformationDTO.getUserId());
        maternalInfoVO.setId(maternalInformationDTO.getId());
        maternalInfoVO.setMotherBabyInfo(babyInfoDTOList);
        return maternalInfoVO;
    }

    @ApiOperation(value = "编辑母婴信息", notes = "编辑母婴信息")
    @PostMapping(value = "/modAdditionalInfo")
    public ResponseBase<Boolean> modAdditionalInfo(@RequestBody @Valid EditMaternalInfoReqDTO reqDTO){
        log.info("通过用户ID编辑母婴信息入参{}", JsonUtils.toJson(reqDTO));

        // TODO 用户id是否通过 hear头取
        ModAdditionalInfoReqDTO modAdditionalInfoReqDTO = new ModAdditionalInfoReqDTO();
        modAdditionalInfoReqDTO.setUserId(reqDTO.getUserId());
        String dueDate = reqDTO.getDueDate();

        //预产日期最早只能选择当日
        if (StringUtils.isNotBlank(dueDate)
                && DateUtil.isLessThanToday(DateUtil.parseDate(dueDate, DateUtil.CN_YEAR_MONTH_DAY_FORMAT))
                && ComOperateTypeEnum.INSERT.getCode().equals(reqDTO.getOperationType())){
            return ResponseBase.error(ERROR_DUE_DATE.getCode(), ERROR_DUE_DATE.getMsg());
        }

        modAdditionalInfoReqDTO.setDueDate(dueDate);
        modAdditionalInfoReqDTO.setRelationshipWithBaby(reqDTO.getRelationshipWithBaby());
        modAdditionalInfoReqDTO.setLikeMilkPowderBrand(reqDTO.getLikeMilkPowderBrand());
        String openStore = reqDTO.getOpenStore();

        //新增-校验并填充门店信息 || 编辑-不校验门店信息
        if (ComOperateTypeEnum.INSERT.getCode().equals(reqDTO.getOperationType())){
            if (StringUtils.isBlank(openStore)){
                //默认将该用户的“首次建档门店”填充为“1000991101”
                modAdditionalInfoReqDTO.setOpenStore(LocalConst.VIRTUAL_ORG);
            }else {
                //校验门店代码是否存在
                Map<String,Object> map = new HashMap<>();
                map.put("or_code",openStore);
                map.put("mer_code",LocalConst.COMMON_MERCODE);
                ResponseBase<Boolean> checked = storeClientAdapter.checkOrganizationExists(map);
                if (Objects.isNull(checked.getData()) || !checked.getData()){
                    return ResponseBase.error(OPEN_STORE_CODE_NOT_EXIST.getCode(),OPEN_STORE_CODE_NOT_EXIST.getMsg());
                }
                modAdditionalInfoReqDTO.setOpenStore(openStore);
            }
        }

        //宝宝已出生才构建宝宝相关信息
        List<MotherBabyInfoDTO> babyInfoList = reqDTO.getBabyInfoList();
        if (CollectionUtils.isNotEmpty(babyInfoList)){
            for (MotherBabyInfoDTO babyInfoDTO : babyInfoList) {
                String babyBirthday = babyInfoDTO.getBabyBirthday();
                //生日期最晚只能选择当日
                if (StringUtils.isNotBlank(babyBirthday) && !DateUtil.isLessThanToday(DateUtil.parseDate(babyBirthday, DateUtil.CN_YEAR_MONTH_DAY_FORMAT))){
                    return ResponseBase.error(ERROR_BIRTHDAY_DATE.getCode(), ERROR_BIRTHDAY_DATE.getMsg());
                }
            }
        }
        modAdditionalInfoReqDTO.setMotherBabyInfo(babyInfoList);
        return memberInfoClient.modAdditionalInfo(modAdditionalInfoReqDTO);
    };

    @PostMapping("verify")
    public ResponseBase<Void> verify(@RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                    @Valid @RequestBody MemberVerifyReq memberVerifyReq) {
        memberInfoService.verifyIdCard(AESUtils.decrypt(memberVerifyReq.getVerifyName()), AESUtils.decrypt(memberVerifyReq.getIdCardNo()), userId);
        return generateSuccess(null);
    }

}
