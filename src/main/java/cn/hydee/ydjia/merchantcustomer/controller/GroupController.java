package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.GroupCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.GroupHotRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.GroupMemberListRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.OrderGroupRespDTO;
import cn.hydee.ydjia.merchantcustomer.enums.GroupStatus;
import cn.hydee.ydjia.merchantcustomer.enums.RegionStatus;
import cn.hydee.ydjia.merchantcustomer.feign.PromoteClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.ActiStoreSearchReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.ActiStoreSearchRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.PromotionType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ScheduleStatus;
import cn.hydee.ydjia.merchantcustomer.service.GroupService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.STATUS_ONE;

/**
 * <AUTHOR>
 * @date 2020/2/25 14:22
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/group")
@Api(value = "拼团活动管理", description = "拼团活动管理")
public class GroupController extends AbstractController {

    @Autowired
    private GroupService groupService;

    @Autowired
    private PromoteClient promoteClient;

    @ApiOperation(value = "进行中活动", notes = "根据门店id获取正在进行拼团的活动")
    @PostMapping("/_getGroupingList")
    public ResponseBase<List<ActiStoreSearchRespDTO>> getGroupingList(@RequestBody ActiStoreSearchReqDTO actiStoreSearchReqDTO,
                                                                      @RequestHeader(required = false) String userId,
                                                                      BindingResult result) {
        this.checkValid(result);
        actiStoreSearchReqDTO.setRegion(RegionStatus.ING.getCode());
        actiStoreSearchReqDTO.setType(PromotionType.GROUP.getCode());
        List<ActiStoreSearchRespDTO> list = groupService.getGroupList(actiStoreSearchReqDTO, userId);
        return generateSuccess(list);
    }

    @ApiOperation(value = "未开始活动", notes = "根据门店id获取未开始的拼团活动")
    @PostMapping("/_getGroupList")
    public ResponseBase<List<ActiStoreSearchRespDTO>> getGroupList(@RequestBody ActiStoreSearchReqDTO actiStoreSearchReqDTO,
                                                                   @RequestHeader(value = LocalConst.HEAD_USER_ID_KEY,
                                                                           required = false) String userId,
                                                                   BindingResult result) {
        this.checkValid(result);
        actiStoreSearchReqDTO.setRegion(RegionStatus.FUTURE.getCode());
        actiStoreSearchReqDTO.setType(PromotionType.GROUP.getCode());
        List<ActiStoreSearchRespDTO> list = groupService.getGroupList(actiStoreSearchReqDTO, userId);
        return generateSuccess(list);
    }

    @ApiOperation(value = "根据门店id获取正在进行的拼团活动的商品", notes = "前端已不再调用")
    @PostMapping("_getGroupProductList")
    public ResponseBase<List<GroupHotRespDTO>> getGroupProductListByStoreId(@RequestBody QueryGroupListReqDTO queryGroupListReqDTO,
                                                                            BindingResult result) {
        this.checkValid(result);
        queryGroupListReqDTO.setGroupStatus(GroupStatus.WAITED.getCode());
        queryGroupListReqDTO.setInfoStatus(ScheduleStatus.STARTED.getCode());
        List<GroupHotRespDTO> list = groupService.getGroupProductListByStoreId(queryGroupListReqDTO);
        return generateSuccess(list);
    }

    @ApiOperation(value = "获取门店是否有拼团活动", notes = "获取门店是否有拼团活动")
    @PostMapping("/_isExistGroup")
    public ResponseBase isExistGroup(@RequestBody ActiStoreSearchReqDTO actiStoreSearchReqDTO, BindingResult result) {
        this.checkValid(result);
        int flag = groupService.isExistGroup(actiStoreSearchReqDTO);
        return generateObjectSuccess(flag);
    }


    @ApiOperation(value = "获取团成员信息（分享页面）", notes = "入参：merCode、groupCode")
    @PostMapping("/_queryGroupMember")
    public ResponseBase<OrderGroupRespDTO> queryGroupMember(@RequestBody QueryGroupLeaderReqDTO queryGroupLeaderReqDTO,
                                                            @Nullable @RequestHeader(value = LocalConst.HEAD_USER_ID_KEY, required = false) String userId,
                                                            @Nullable @RequestHeader(value = LocalConst.HEAD_MEMBER_CARD_KEY, required = false) String memberCard,
                                                            @Nullable @RequestHeader(value = LocalConst.HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String empFlag,
                                                            BindingResult result) {
        this.checkValid(result);
        queryGroupLeaderReqDTO.setMemberId(userId);
        queryGroupLeaderReqDTO.setLoginUserDTO(this.getLoginUserDto(queryGroupLeaderReqDTO.getMerCode(), userId, memberCard, empFlag));
        queryGroupLeaderReqDTO.setQueryMarkingPriceFlag(true);
        List<OrderGroupRespDTO> list = groupService.queryGroupMember(queryGroupLeaderReqDTO);
        if (!CollectionUtils.isEmpty(list)) {
            return generateObjectSuccess(list.get(0));
        }
        return generateObjectSuccess(null);
    }

    @ApiOperation(value = "根据门店id获取正在进行拼团的活动-更多拼团", notes = "根据门店id获取正在进行拼团的活动-更多拼团")
    @PostMapping("/_getMoreGroups")
    public ResponseBase<List<GroupCommodityRespDTO>> getMoreGroups(@Valid @RequestBody ActiStoreSearchReqDTO actiStoreSearchReqDTO,
                                                                   BindingResult result) {
        this.checkValid(result);
        if (StringUtils.isEmpty(actiStoreSearchReqDTO.getSpecId())) {
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        actiStoreSearchReqDTO.setRegion(RegionStatus.ING.getCode());
        List<GroupCommodityRespDTO> list = groupService.getMoreGroupProductList(actiStoreSearchReqDTO);
        return generateSuccess(list);
    }

    @ApiOperation(value = "获取团信息-分享", notes = "获取团信息-分享")
    @PostMapping("/_queryGroupInfo")
    public ResponseBase<List<GroupMemberListRespDTO>> queryGroupInfo(@RequestBody QueryGroupInfoDTO queryGroupMemberReqDTO,
                                                                     BindingResult result) {
        this.checkValid(result);
        List<GroupMemberListRespDTO> list = groupService.queryGroupInfo(queryGroupMemberReqDTO);
        return generateObjectSuccess(list);
    }

    @ApiOperation(value = "判断参团占位", notes = "判断参团占位")
    @PostMapping("/checkAddGroup")
    public ResponseBase<String> checkAddGroup(@RequestBody GroupFollowCommitDTO dto,
                                              @RequestHeader(value = LocalConst.HEAD_USER_ID_KEY) Long userId) {
        dto.setUserId(userId);
        return promoteClient.checkAddGroup(dto);
    }

    @ApiOperation(value = "判断开团占位", notes = "判断开团占位")
    @PostMapping("/checkOpenGroup")
    public ResponseBase<String> checkOpenGroup(@RequestBody GroupLeaderCommitDTO dto,
                                               @RequestHeader(value = LocalConst.HEAD_USER_ID_KEY) Long userId) {
        dto.setUserId(userId);
        return promoteClient.checkOpenGroup(dto);
    }

    private LoginUserDTO getLoginUserDto(String merCode, String userId, String memberCard, String empFlag) {
        return LoginUserDTO.builder().merCode(merCode).userId(userId).memberCard(memberCard).empFlag(STATUS_ONE.toString().equals(empFlag)).build();
    }
}
