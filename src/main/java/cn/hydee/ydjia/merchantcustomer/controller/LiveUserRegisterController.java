package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.WeeChatMiniParseReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.LiveLoginRespDTO;
import cn.hydee.ydjia.merchantcustomer.enums.RegChannelEnum;
import cn.hydee.ydjia.merchantcustomer.feign.*;
import cn.hydee.ydjia.merchantcustomer.feign.honey.LiveUserInfoReqDTO;
import cn.hydee.ydjia.merchantcustomer.interceptor.LiveRequiredAuthInterceptor;
import cn.hydee.ydjia.merchantcustomer.service.MemberLoginHelper;
import cn.hydee.ydjia.merchantcustomer.service.RedisService;
import cn.hydee.ydjia.merchantcustomer.util.TBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

import static cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType.WX_USER_INFO_PARSE_ERROR;
import static cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType.WX_USER_PHONE_PARSE_ERROR;
import static cn.hydee.ydjia.merchantcustomer.util.RedisKeyUtil.getMiniCacheKey;
import static cn.hydee.ydjia.merchantcustomer.util.TypeConversionUtil.stringToInteger;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/10 11:23
 */
@Slf4j
@RestController
@RequestMapping("/${api.version}/liveUser")
@Api(value = "会员注册接口管理器", description = "会员注册接口管理器")
public class LiveUserRegisterController extends AbstractController {

    private final LiveUserRegisterClient memberRegisterClient;
    private final LiveUserManageClient memberManageClient;
    private final LiveRequiredAuthInterceptor config;
    private final MemberLoginHelper memberLoginHelper;
    private final WeeChatMaClient weeChatMaClient;
    private final RedisService redisService;
    @Value("${spring.profiles.active}")
    private String active;
    @Value("${auth-interceptor.testUserFlag}")
    private boolean testUserFlag;

    public LiveUserRegisterController(LiveUserRegisterClient memberRegisterClient,
                                      LiveUserManageClient memberManageClient,
                                      LiveRequiredAuthInterceptor config,
                                      MemberLoginHelper memberLoginHelper,
                                      WeeChatMaClient weeChatMaClient,
                                      RedisService redisService) {
        this.memberRegisterClient = memberRegisterClient;
        this.memberManageClient = memberManageClient;
        this.config = config;
        this.memberLoginHelper = memberLoginHelper;
        this.weeChatMaClient = weeChatMaClient;
        this.redisService = redisService;
    }

    @PostMapping("/mini/register")
    @ApiOperation(value = "小程序解析手机号码注册", notes = "小程序解析手机号码注册")
    public ResponseBase<LiveLoginRespDTO> registerByMini(@RequestBody WeeChatMiniParseReqDTO miniParseReqDTO, HttpServletResponse httpServletResponse) {
        log.info("[Live-Mini-register] request={}", miniParseReqDTO);
        LiveLoginRespDTO respDTO = new LiveLoginRespDTO();
        ResponseBase<WeeChatParseCodeResultDTO> responseBase = weeChatMaClient.login(miniParseReqDTO.getCode(), miniParseReqDTO.getAppid(), "2", miniParseReqDTO.getMerCode());
        if (!responseBase.checkSuccess() || responseBase.getData() == null) {
            log.error("Live-Parse code error response={} {} {}", responseBase.getCode(), responseBase.getMsg(), miniParseReqDTO);
            throw WarnException.builder().code(WX_USER_PHONE_PARSE_ERROR.getCode())
                    .message(WX_USER_PHONE_PARSE_ERROR.getMsg()).build();
        }

        WeeChatParseCodeResultDTO codeResultDTO = responseBase.getData();

        Object value = redisService.getObject(getMiniCacheKey(miniParseReqDTO.getMerCode() + "_" + codeResultDTO.getOpenid()));
        if (Objects.isNull(value)) {
            throw WarnException.builder().code(WX_USER_PHONE_PARSE_ERROR.getCode())
                    .message(WX_USER_PHONE_PARSE_ERROR.getMsg()).build();
        }
        WeeChatMaParseUserInfoDTO userInfoDTO = (WeeChatMaParseUserInfoDTO) value;
        log.debug("Live-Parse user info {}", userInfoDTO);

        miniParseReqDTO.setSessionKey(codeResultDTO.getSessionKey());

        log.debug("Live-Parse phone request={}", miniParseReqDTO);
        ResponseBase<WeeChatMaParsePhoneNumberDTO> parsePhoneResponse =
                weeChatMaClient.parsePhone(miniParseReqDTO, miniParseReqDTO.getAppid(), "2");
        checkResult(parsePhoneResponse);

        log.debug("Live-parse phone info {}", parsePhoneResponse.getData());
        LiveUserInfoReqDTO registerReqDTO = TBean.copy(LiveUserInfoReqDTO.class, userInfoDTO);
        registerReqDTO.setMerCode(miniParseReqDTO.getMerCode());
        registerReqDTO.setUserPhone(parsePhoneResponse.getData().getPurePhoneNumber());
        registerReqDTO.setHeadUrl(userInfoDTO.getAvatarUrl());
        registerReqDTO.setUserSex(stringToInteger(userInfoDTO.getGender()));
        registerReqDTO.setUnionid(userInfoDTO.getUnionId());
        registerReqDTO.setOpenid(userInfoDTO.getOpenId());
        registerReqDTO.setMerType(RegChannelEnum.WEAPP.getCode());
        log.info("Live-Mini program register request={}", registerReqDTO);
        ResponseBase<Long> registerResponseBase = memberRegisterClient.miniProgramRegister(registerReqDTO);
        checkResult(registerResponseBase);

        LiveUserLoginDTO memberLoginDTO = LiveUserLoginDTO.builder().userId(registerResponseBase.getData())
                .merCode(miniParseReqDTO.getMerCode()).merType(RegChannelEnum.WEAPP.getCode())
                .platformEnum(RegChannelEnum.WEAPP.name())
                .build();
        ResponseBase<LiveUserInfoResDTO> loginResponse = memberManageClient.login(memberLoginDTO);
        checkResult(loginResponse);
        memberLoginHelper.addCookie(loginResponse.getData(), httpServletResponse);
        respDTO.setToken(loginResponse.getData().getToken());
        respDTO.setNickName(loginResponse.getData().getNickName());
        return generateSuccess(respDTO);
    }

    @PostMapping("/mini/parseMemberInfo")
    @ApiOperation(value = "解析会员信息", notes = "解析会员信息")
    public ResponseBase parseMemberInfo(@RequestBody WeeChatMiniParseReqDTO miniParseReqDTO) {
        log.debug("Live-Parse memberInfo req={}", miniParseReqDTO);
        ResponseBase<WeeChatParseCodeResultDTO> responseBase = weeChatMaClient.login(miniParseReqDTO.getCode(), miniParseReqDTO.getAppid(), "2", miniParseReqDTO.getMerCode());
        if (!responseBase.checkSuccess() || responseBase.getData() == null) {
            log.error("Parse code error response={} {} {}", responseBase.getCode(), responseBase.getMsg(), miniParseReqDTO);
            throw WarnException.builder().code(WX_USER_INFO_PARSE_ERROR.getCode())
                    .message(WX_USER_INFO_PARSE_ERROR.getMsg()).build();
        }

        WeeChatParseCodeResultDTO codeResultDTO = responseBase.getData();

        log.debug("Live-Parse code result={}", codeResultDTO);

        miniParseReqDTO.setSessionKey(codeResultDTO.getSessionKey());
        log.debug("Live-Invoke parseUserInfo req={}", miniParseReqDTO);
        ResponseBase<WeeChatMaParseUserInfoDTO> parseUserInfoResponse = weeChatMaClient.parseUserInfo(miniParseReqDTO, miniParseReqDTO.getAppid(), "2");
        checkResult(parseUserInfoResponse);

        WeeChatMaParseUserInfoDTO userInfoDTO = parseUserInfoResponse.getData();
        redisService.setValueExpireMinute(getMiniCacheKey(miniParseReqDTO.getMerCode() + "_" + codeResultDTO.getOpenid()),
                userInfoDTO, 60);

        return ResponseBase.success();
    }


    private void checkResult(ResponseBase responseBase) {
        if (!responseBase.checkSuccess()) {
            throw WarnException.builder().code(responseBase.getCode()).
                    tipMessage(responseBase.getMsg()).build();
        }
    }


}
