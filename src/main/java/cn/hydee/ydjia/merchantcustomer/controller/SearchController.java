package cn.hydee.ydjia.merchantcustomer.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.config.B2CConfig;
import cn.hydee.ydjia.merchantcustomer.dto.CommoditySearchInfoResp;
import cn.hydee.ydjia.merchantcustomer.dto.EmployeeResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.fegin.manager.req.RecommendReq;
import cn.hydee.ydjia.merchantcustomer.dto.fegin.manager.resp.RecommendCacheResp;
import cn.hydee.ydjia.merchantcustomer.dto.fegin.manager.resp.RecommendResp;
import cn.hydee.ydjia.merchantcustomer.dto.req.CommoditySearchV2DTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.EmpInfoReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.SearchRecommendReq;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SPMerchantDetailInfoResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SearchRecommendResp;
import cn.hydee.ydjia.merchantcustomer.feign.MemberManageClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.SPMerchantQueryClient;
import cn.hydee.ydjia.merchantcustomer.feign.domain.YdjStore;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.CommonLoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CommoditySearchRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.sharedWarehouse.SharedStockQueryReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.sharedWarehouse.SharedStockQueryRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.honey.EmployeeRoleInfoResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MemberInfoDTO;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonCommodityClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.service.CommoditySreachService;
import cn.hydee.ydjia.merchantcustomer.service.MemberInfoService;
import cn.hydee.ydjia.merchantcustomer.service.PlatformInnerServerService;
import cn.hydee.ydjia.merchantcustomer.service.StoreService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.google.common.collect.Lists;
import com.yxt.middle.baseinfo.api.StoreInfoApi;
import com.yxt.middle.baseinfo.req.store.MultiStoreSimpleReqDTO;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import com.yxt.lang.dto.api.ResponseBase.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import static cn.hydee.ydjia.merchantcustomer.feign.util.LocalConst.PRESCRIPTION_DRUG_COMPLIANCE_VERSION_PARAM;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.*;


@Api(tags = "搜索")
@RestController
@RequestMapping("/${api.version}/search")
public class SearchController extends AbstractController {

    @Autowired
    private CommoditySreachService commoditySreachService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private PlatformInnerServerService platformInnerServerService;
    @Autowired
    private SPMerchantQueryClient spMerchantQueryClient;
    @Autowired
    private CommonCommodityClientWrapperService commonCommodityClientWrapperService;
    @Autowired
    private MemberManageClient memberManageClient;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private B2CConfig b2CConfig;
    @Lazy
    @Resource
    private StoreInfoApi storeInfoApi;

    @ApiOperation(value = "推广搜索", notes = "MATERIAL-729 -liweiyan")
    @PostMapping("/recommend")
    public ResponseBase<PageDTO<CommoditySearchInfoResp>> recommend(
            @Nullable @RequestHeader(PRESCRIPTION_DRUG_COMPLIANCE_VERSION_PARAM) String prescriptionDrugComplianceVersion,
            @RequestHeader(value = USER_KEY, required = false) String token,
            @RequestBody SearchRecommendReq searchRecommendReq) {
        PageDTO<CommoditySearchInfoResp> returnPage = new PageDTO<>(searchRecommendReq.getCurrentPage(), searchRecommendReq.getPageSize());
        if (StrUtil.isBlank(token)) {
            return generateSuccess(returnPage);
        }
        CommoditySearchV2DTO commoditySearchDTO = new CommoditySearchV2DTO();
        BeanUtils.copyProperties(searchRecommendReq, commoditySearchDTO);
        if (Objects.equals(searchRecommendReq.getSortField(), SORT_FIELD_PRICE)){
            commoditySearchDTO.setSortField(SORT_FIELD_PRICE);
            commoditySearchDTO.setAsc(searchRecommendReq.getAsc() == null || searchRecommendReq.getAsc());
        }
//        else {
//            commoditySearchDTO.setSortField(SORT_FILED_UPDATE_TIME);
//            commoditySearchDTO.setAsc(searchRecommendReq.getAsc() == null || searchRecommendReq.getAsc());
//        }

        List<RecommendCacheResp> recommendRes = platformInnerServerService.getManagerService().queryCurrentMonthPlanCommodity();
        if (CollectionUtils.isEmpty(recommendRes)) {
            return generateSuccess(returnPage);
        }
        //登录会员信息
        CommonLoginUserDTO loginUserDTO = getLoginUser(searchRecommendReq.getMerCode(), token);
        if (loginUserDTO == null) {
            return generateSuccess(returnPage);
        }
        commoditySearchDTO.setLoginUserDTO(loginUserDTO);
        commoditySearchDTO.setUserId(loginUserDTO.getUserId());
        if (searchRecommendReq.getHasIncome() != null){
            //过滤业绩
            recommendRes.removeIf(recommend -> {
                Double income = recommend.getIncomeStaff();
                if (loginUserDTO.isDz()) {
                    income = recommend.getIncomeManager();
                } else if (loginUserDTO.isDy()) {
                    income = recommend.getIncomeStoreStaff();
                }
                if (searchRecommendReq.getHasIncome()){//有业绩过滤业绩小于0的
                    return income == null || income <= 0;
                } else {//没有业绩, 过滤业绩大于0的
                    return income !=null && income > 0;
                }
            });
        }
        //按照业绩排序, 先内存排好
        if (Objects.equals(searchRecommendReq.getSortField(), SORT_FILED_INCOME)){
            recommendRes.sort((item1, item2) -> {
                int sort = item1.getIncomeStaff().compareTo(item2.getIncomeStaff());//默认取员工绩效比率排序
                if (loginUserDTO.isDz()) {
                    sort = item1.getIncomeManager().compareTo(item2.getIncomeManager());
                } else if (loginUserDTO.isDy()) {
                    sort = item1.getIncomeStoreStaff().compareTo(item2.getIncomeStoreStaff());
                }
                if (searchRecommendReq.getAsc() != null && !searchRecommendReq.getAsc()) {
                    //倒序
                    sort = -sort;
                }
                return sort;
            });
        }

        if (StrUtil.isNotEmpty(searchRecommendReq.getKeyword())){
            searchRecommendReq.setKeyword(searchRecommendReq.getKeyword().trim());
        } else {
            searchRecommendReq.setKeyword(null);
        }
        Set<String> selfErpCodes= new HashSet<>();//所有自营商品编码
        Set<String> spErpCodes= new HashSet<>();//所有云仓商品编码
        for (RecommendCacheResp recommend : recommendRes) {
            if (StrUtil.isNotBlank(recommend.getSpCode())){
                if (StrUtil.isNotEmpty(searchRecommendReq.getKeyword()) || spErpCodes.size() < b2CConfig.getCommodityCount()){//有关键字就带入所有编码查询，否则只取200个编码查询
                    spErpCodes.add(recommend.getErpCode());
                }
            } else {
                if (StrUtil.isNotEmpty(searchRecommendReq.getKeyword()) || selfErpCodes.size() < b2CConfig.getCommodityCount()){//有关键字就带入所有编码查询，否则只取200个编码查询
                    selfErpCodes.add(recommend.getErpCode());
                }
            }
        }
        commoditySearchDTO.setSelfErpCode(selfErpCodes);
        commoditySearchDTO.setSpErpCode(spErpCodes);
        if(StringUtils.isEmpty(commoditySearchDTO.getStoreId())) {
            String shipStoreId = storeService.shipStoreId(searchRecommendReq.getMerCode());
            commoditySearchDTO.setStoreId(shipStoreId);
        }
        commoditySearchDTO.setSearchType(2);
        commoditySearchDTO.setHasStock(null);
        commoditySearchDTO.setHasStockSort(null);
        commoditySearchDTO.setHasFlagShip(true);
        commoditySearchDTO.setPrescriptionDrugComplianceVersion(prescriptionDrugComplianceVersion);
        commoditySearchDTO.setCurrentPage(STATUS_ONE);
        commoditySearchDTO.setPageSize(b2CConfig.getCommodityCount());
        ResponseBase<PageDTO<CommoditySearchRespDTO>> responseBase = commoditySreachService.searchInCodes(commoditySearchDTO, super.getAPI_VERSION());
        List<CommoditySearchRespDTO> commoditySearchRespDTOList =  responseBase.getData().getData();
        Set<String> innerStoreIds = new HashSet<>();
        List<String> erpCodes = new ArrayList<>();
        Set<String> spCodes = new HashSet<>();
        List<Long> innerSpecIds = new ArrayList<>();
        commoditySearchRespDTOList.forEach(o -> {
            erpCodes.add(o.getErpCode());
            if (StringUtils.isEmpty(o.getSpCode())){
                innerSpecIds.add(Long.valueOf(o.getSpecId()));
                innerStoreIds.add(o.getStoreId());
            }else{
                spCodes.add(o.getSpCode());
            }
        });
        Map<String, RecommendResp> innerRecommendReqMap = new HashMap<>();
        Map<String, RecommendResp> spRecommendReqMap = new HashMap<>();
        Map<String, SPMerchantDetailInfoResDTO> spInfoMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(innerStoreIds)) {
            RecommendReq recommendReq = RecommendReq.builder()
                            .erpCodes(erpCodes)
                            .storeIds(Lists.newArrayList(innerStoreIds))
                        .build();

            List<RecommendResp> recommendResps = platformInnerServerService.getManagerService().recommendCommodityList(recommendReq);
            if(!CollectionUtils.isEmpty(recommendResps)) {
                innerRecommendReqMap =
                        recommendResps.stream().collect(Collectors.groupingBy(o -> String.join("-", o.getErpCode(), o.getStoreId()),
                                Collectors.collectingAndThen(Collectors.toList(), v -> v.get(0))));

            }


        }

        if(!CollectionUtils.isEmpty(spCodes)) {
            RecommendReq recommendReq = RecommendReq.builder()
                    .erpCodes(erpCodes)
                    .spCodes(Lists.newArrayList(spCodes))
                    .build();

            List<RecommendResp> recommendResps = platformInnerServerService.getManagerService().recommendCommodityList(recommendReq);

            if(!CollectionUtils.isEmpty(recommendResps)) {
                spRecommendReqMap =
                        recommendResps
                                .stream().collect(Collectors.groupingBy(o -> String.join("-", o.getErpCode(), o.getSpCode()),
                                        Collectors.collectingAndThen(Collectors.toList(), v -> v.get(0))));

            }

            spInfoMap = this.spInfoMap(Lists.newArrayList(spCodes));


        }

        // 查询共享库存
        Map<Long, SharedStockQueryRespDTO> sharedStockQueryRespDTOMap = this.queryResStock(
                    SharedStockQueryReqDTO.builder()
                        .merCode(searchRecommendReq.getMerCode())
                        .specIds(innerSpecIds)
                    .build());

        Map<String, RecommendResp> finalSpRecommendReqMap = spRecommendReqMap;
        Map<String, RecommendResp> finalInnerRecommendReqMap = innerRecommendReqMap;
        Map<String, SPMerchantDetailInfoResDTO> finalSpInfoMap = spInfoMap;
        // 得到所有门店编码
        List<String> storeIds = new ArrayList<>();
        List<CommoditySearchInfoResp> commodityInfoRespList = commoditySearchRespDTOList.stream()
                .map(o -> {
                    storeIds.add(o.getStoreId());
                    CommoditySearchInfoResp commodityInfoResp = new CommoditySearchInfoResp();
                    commodityInfoResp.setCommoditySearchResp(o);

                    RecommendResp recommendResp = null;
                    boolean isSp = !StringUtils.isEmpty(o.getSpCode());
                    String key = String.join("-", o.getErpCode(), isSp ? o.getSpCode() : o.getStoreId());
                    if(isSp) {
                        recommendResp = finalSpRecommendReqMap.get(key);
                        o.setSpName(finalSpInfoMap.get(o.getSpCode()).getMerName());
                    }else {
                        recommendResp = finalInnerRecommendReqMap.get(key);
                    }
                    if(Objects.nonNull(recommendResp)) {
                        SearchRecommendResp searchRecommendResp = new SearchRecommendResp();
                        BeanUtils.copyProperties(recommendResp, searchRecommendResp);
                        commodityInfoResp.setRecommendResp(searchRecommendResp);
                    }

                    SharedStockQueryRespDTO sharedStockQueryRespDTO = sharedStockQueryRespDTOMap.get(Long.valueOf(o.getSpecId()));
                    if (Objects.nonNull(sharedStockQueryRespDTO)) {
                        o.setSharedStock(sharedStockQueryRespDTO.getStock());
                    }
                    o.setStock(NumberUtil.add(o.getSharedStock(), o.getStock()).intValue());
                    return commodityInfoResp;
                }).collect(Collectors.toList());
        if (searchRecommendReq.getHasStock() != null && searchRecommendReq.getHasStock()){
            //过滤有库存
            commodityInfoRespList.removeIf(item -> item.getCommoditySearchResp().getStock() == null || item.getCommoditySearchResp().getStock() <= 0);
        }
        //按照比例排序
        if (Objects.equals(searchRecommendReq.getSortField(), SORT_FILED_INCOME)){
            commodityInfoRespList.sort((item1, item2) -> {
                int sort = item1.getRecommendResp().getIncomeStaff().compareTo(item2.getRecommendResp().getIncomeStaff());//默认取员工绩效比率排序
                if (loginUserDTO.isDz()) {
                    sort = item1.getRecommendResp().getIncomeManager().compareTo(item2.getRecommendResp().getIncomeManager());
                }else if (loginUserDTO.isDy()) {
                    sort = item1.getRecommendResp().getIncomeStoreStaff().compareTo(item2.getRecommendResp().getIncomeStoreStaff());
                }
                if (searchRecommendReq.getAsc() != null && !searchRecommendReq.getAsc()) {
                    //倒序
                    sort = -sort;
                }
                return sort;
            });
        }
        PageUtil.setOneAsFirstPageNo();
        int start =  Math.min(commodityInfoRespList.size(), PageUtil.getStart(searchRecommendReq.getCurrentPage(), searchRecommendReq.getPageSize()));
        int end = Math.min(commodityInfoRespList.size(), PageUtil.getEnd(searchRecommendReq.getCurrentPage(), searchRecommendReq.getPageSize()));
        // 处理门店名称问题 因Es查询的门店名称不是简称
        List<CommoditySearchInfoResp> commoditySearchInfoResps = commodityInfoRespList.subList(start, end);
        MultiStoreSimpleReqDTO req = new MultiStoreSimpleReqDTO();
        req.setMerCode(COMMON_MERCODE);
        req.setStoreId(storeIds);
        com.yxt.lang.dto.api.ResponseBase<List<StoreInfoDataResDTO>> storeInfoList = storeInfoApi.getStoreInfoList(req);
        Map<String, StoreInfoDataResDTO> idStoreNameMap;
        if (storeInfoList.checkSuccess() && CollUtil.isNotEmpty(storeInfoList.getData()))  {
            idStoreNameMap = storeInfoList.getData().stream().collect(Collectors.toMap(StoreInfoDataResDTO::getId, o -> o));
            for (CommoditySearchInfoResp commoditySearchInfoResp : commoditySearchInfoResps) {
                if (ObjectUtil.isNotNull(commoditySearchInfoResp.getCommoditySearchResp())){
                    if (idStoreNameMap.get(commoditySearchInfoResp.getCommoditySearchResp().getStoreId()) != null){
                        commoditySearchInfoResp.getCommoditySearchResp().setStoreName(idStoreNameMap.get(commoditySearchInfoResp.getCommoditySearchResp().getStoreId()).getShortName());
                    }
                }
            }
        } else {
            idStoreNameMap = Collections.emptyMap();
        }
        YdjStore centerStore = storeService.getCenterStore(COMMON_MERCODE);
        commoditySearchInfoResps.forEach(store ->{
            store.setCenter(centerStore != null && store.getCommoditySearchResp() != null
                    && idStoreNameMap.get(store.getCommoditySearchResp().getStoreId()) != null &&
                    Objects.equals(centerStore.getStCode(), idStoreNameMap.get(store.getCommoditySearchResp().getStoreId()).getStCode()));
        });
        returnPage.setData(commoditySearchInfoResps);

        returnPage.setTotalCount(commodityInfoRespList.size());
        int totalPage = PageUtil.totalPage(commodityInfoRespList.size(), searchRecommendReq.getPageSize());
        returnPage.setTotalPage(totalPage);
        return generateSuccess(returnPage);
    }


    private Map<String, SPMerchantDetailInfoResDTO> spInfoMap(List<String> spCodes) {
        ResponseBase<List<SPMerchantDetailInfoResDTO>> spMerchantListResponseBase = spMerchantQueryClient.getSPDetailByList(spCodes);
        if(!spMerchantListResponseBase.checkSuccess() || Objects.isNull(spMerchantListResponseBase.getData())) {
            return new HashMap<>();
        }
        return spMerchantListResponseBase.getData()
                .stream().collect(Collectors.groupingBy(o -> o.getMerCode(),
                        Collectors.collectingAndThen(Collectors.toList(), v -> v.get(0))));

    }

    public Map<Long, SharedStockQueryRespDTO> queryResStock(SharedStockQueryReqDTO sharedStockQueryReqDTO) {
        Map<Long, SharedStockQueryRespDTO> sharedStockQueryRespDTOMap = commonCommodityClientWrapperService.querySharedStock(sharedStockQueryReqDTO);
        if(Objects.nonNull(sharedStockQueryRespDTOMap)) {
            return sharedStockQueryRespDTOMap;
        }
        return new HashMap<>();
    }

    private CommonLoginUserDTO getLoginUser(String merCode, String token){
        //登录会员信息
        ResponseBase<MemberInfoDTO> memResponseBase = memberManageClient.checkToken(merCode, token, false);
        if (!memResponseBase.checkSuccess() || memResponseBase.getData() == null) {
            return null;
        }
        //查询员工信息
        EmployeeResDTO tmpEmployeeInfo =  memberInfoService.getEmpInfoByMobileCache(merCode, memResponseBase.getData().getMemberPhone());
        if (tmpEmployeeInfo == null) {
            return null;
        }
        EmployeeRoleInfoResDTO employeeInfo =  memberInfoService.queryInfoByEmpCode(new EmpInfoReqDTO(tmpEmployeeInfo.getEmpCode(), tmpEmployeeInfo.getMobile()));
        return CommonLoginUserDTO.builder().merCode(merCode)
                .userId(memResponseBase.getData().getUserId().toString()).memberCard(memResponseBase.getData().getMemberCard())
                .empFlag(STATUS_ONE.equals(tmpEmployeeInfo.getEmpStatus())).noQueryCouponPrice(false).noQueryPlusPrice(false)
                .isDz(employeeInfo.isDz()).isDy(employeeInfo.isDy()).build();
    }
}
