package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.req.CartCommodityGetDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.SingleStoreCartCommodityRespDTO;
import cn.hydee.ydjia.merchantcustomer.service.SingleStoreShoppingCartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 单门店购物车控制器
 * 提供单门店购物车相关接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/single-store/shopping-cart")
@Api(tags = "单门店购物车接口")
public class SingleStoreShoppingCartController {
    
    @Autowired
    private SingleStoreShoppingCartService singleStoreShoppingCartService;
    
    @ApiOperation(value = "获取单门店购物车商品", notes = "获取指定门店的购物车商品信息，简化多门店逻辑")
    @PostMapping("/commodity")
    public ResponseBase<SingleStoreCartCommodityRespDTO> getSingleStoreCommodity(
            @Validated @RequestBody CartCommodityGetDTO request) {
        
        try {
            log.info("单门店购物车查询开始，门店ID：{}", request.getCurrentStoreId());
            
            SingleStoreCartCommodityRespDTO result = singleStoreShoppingCartService.getSingleStoreCommodity(request);
            
            log.info("单门店购物车查询成功，门店ID：{}，商品数量：{}", 
                    request.getCurrentStoreId(), 
                    result.getCommodities() != null ? result.getCommodities().size() : 0);
            
            return ResponseBase.success(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("单门店购物车查询参数错误，门店ID：{}，错误：{}", 
                    request.getCurrentStoreId(), e.getMessage());
            return ResponseBase.fail("参数错误：" + e.getMessage());
            
        } catch (Exception e) {
            log.error("单门店购物车查询失败，门店ID：{}", request.getCurrentStoreId(), e);
            return ResponseBase.fail("系统错误，请稍后重试");
        }
    }
}
