package cn.hydee.ydjia.merchantcustomer.controller;


import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.feign.ActivityPerfectDataClient;
import cn.hydee.ydjia.merchantcustomer.feign.MemberInfoClient;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MemberInfoRequest;
import cn.hydee.ydjia.merchantcustomer.util.AESUtils;
import cn.hydee.ydjia.merchantcustomer.util.ExceptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.HEAD_USER_ID_KEY;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.MER_CODE;

/**
 * 完善资料有礼相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/28
 */
@RestController
@Api(tags = "完善资料有礼相关接口")
@RequestMapping("/${api.version}/activityPerfectDataGift")
public class ActivityPerfectDataController extends AbstractController {


    private final ActivityPerfectDataClient activityPerfectDataClient;

    private final MemberInfoClient memberInfoClient;

    public ActivityPerfectDataController(ActivityPerfectDataClient activityPerfectDataClient, MemberInfoClient memberInfoClient) {
        this.activityPerfectDataClient = activityPerfectDataClient;
        this.memberInfoClient = memberInfoClient;
    }


    @ApiOperation(value = "检查当前用户是否能参与完善有礼活动")
    @PostMapping("/checkMemberWhetherPerfectData")
    public ResponseBase<Object> checkMemberWhetherPerfectData(@RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                               @RequestHeader(MER_CODE) String merCode) {
        if(!activityPerfectDataClient.checkMemberWhetherPerfectData(Long.parseLong(userId), merCode).getData()){
            return generateSuccess(null);
        }
        return activityPerfectDataClient.getActivityPerfectDataDetail(merCode);
    }


    @ApiOperation(value = "完善有礼活动奖励校验", notes = "完善有礼活动奖励校验")
    @PostMapping("/checkCanSendPerfectDataGift")
    public ResponseBase<Boolean> checkCanSendPerfectDataGift(@RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                             @RequestHeader(MER_CODE) String merCode,
                                                             @RequestBody MemberInfoRequest memberInfoRequest) {
        memberInfoRequest.setUserId(Long.valueOf(userId));
        memberInfoRequest.setMerCode(merCode);

        // 对用户进行校验
        ResponseBase<Boolean> responseBase =  activityPerfectDataClient.checkCanSendPerfectDataGift(Long.valueOf(userId), merCode, memberInfoRequest);
        if (!responseBase.checkSuccess()) {
            throw ExceptionUtil.getWarnException(responseBase.getCode(), responseBase.getMsg());
        }
        memberInfoClient.edit(memberInfoRequest);
        return generateSuccess(true);
    }

    @ApiOperation(value = "完善有礼活动奖励校验", notes = "完善有礼活动奖励校验")
    @PostMapping("/encrypt/checkCanSendPerfectDataGift")
    public ResponseBase<Boolean> checkCanSendPerfectDataGiftEncrypt(@RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                             @RequestHeader(MER_CODE) String merCode,
                                                             @RequestBody MemberInfoRequest memberInfoRequest) {
        memberInfoRequest.setUserId(Long.valueOf(userId));
        memberInfoRequest.setMerCode(merCode);

        if (StringUtils.isNotBlank(memberInfoRequest.getMemberPhone())) {
            memberInfoRequest.setMemberPhone(AESUtils.decrypt(memberInfoRequest.getMemberPhone()));
        }

        if (StringUtils.isNotBlank(memberInfoRequest.getMemberIdcard())) {
            memberInfoRequest.setMemberIdcard(AESUtils.decrypt(memberInfoRequest.getMemberIdcard()));
        }

        if (StringUtils.isNotBlank(memberInfoRequest.getMemberName())) {
            memberInfoRequest.setMemberName(AESUtils.decrypt(memberInfoRequest.getMemberName()));
        }

        // 对用户进行校验
        ResponseBase<Boolean> responseBase =  activityPerfectDataClient.checkCanSendPerfectDataGift(Long.valueOf(userId), merCode, memberInfoRequest);
        if (!responseBase.checkSuccess()) {
            throw ExceptionUtil.getWarnException(responseBase.getCode(), responseBase.getMsg());
        }
        memberInfoClient.edit(memberInfoRequest);
        return generateSuccess(true);
    }


    @ApiOperation(value = "发送完善有礼活动奖励", notes = "发送完善有礼活动奖励")
    @PostMapping("/sendPerfectDataGift")
    public ResponseBase<Boolean> sendPerfectDataGift(@RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                     @RequestHeader(MER_CODE) String merCode) {
        return activityPerfectDataClient.sendPerfectDataGift(Long.parseLong(userId), merCode);
    }
}
