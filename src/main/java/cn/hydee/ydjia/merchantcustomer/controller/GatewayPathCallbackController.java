package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.config.PathCallbackConfig;
import cn.hydee.ydjia.merchantcustomer.dto.resp.PathCallbackRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Since: 2025/04/14 16:45
 * Author: qs
 */

@RestController
@RequestMapping(value = "/gatewayPathCallback")
@Api(value = "网关地址切换回退地址")
@Slf4j
public class GatewayPathCallbackController extends AbstractController {

    @Resource
    private PathCallbackConfig pathCallbackConfig;

    @ApiOperation(value = "获取所有服务域名")
    @GetMapping("/list")
    public ResponseBase<PathCallbackRespDTO> list() {
        PathCallbackRespDTO respDTO = new PathCallbackRespDTO();
        respDTO.setTtl(pathCallbackConfig.getTtl());
        respDTO.setRoutes(pathCallbackConfig.getRoutes());
        return generateSuccess(respDTO);
    }
}
