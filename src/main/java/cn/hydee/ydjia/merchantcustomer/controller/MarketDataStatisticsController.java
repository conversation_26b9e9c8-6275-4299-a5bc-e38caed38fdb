package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.req.MarketDataStatisticsVisitReportReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.MarketDataStatisticsClient;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * <AUTHOR>
 * @Date 2020/9/29 上午11:38
 */
@RestController
@Api(tags = "营销数据处理器")
@Slf4j
@RequestMapping("/${api.version}/marketData")
public class MarketDataStatisticsController extends AbstractController {

    @Autowired
    private MarketDataStatisticsClient marketDataStatisticsClient;

    @ApiOperation(value = "上报访问数据，1：领券中心，2：大转盘刮刮乐活动", notes = "")
    @PostMapping("/visitReport")
    public ResponseBase visitReport(@Valid @RequestBody MarketDataStatisticsVisitReportReqDTO dto, @Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        if (StringUtils.isEmpty(userId)) {
            return ResponseBase.success();
        }
        dto.setUserId(Long.parseLong(userId));
        return marketDataStatisticsClient.visitReport(dto);
    }


}
