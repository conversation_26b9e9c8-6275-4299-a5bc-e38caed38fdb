package cn.hydee.ydjia.merchantcustomer.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.BeanUtil;
import cn.hydee.ydjia.merchantcustomer.dto.StoreSalesListReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.fegin.manager.resp.RecommendCacheResp;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.dto.store.StoreAssembleReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.store.StoreAssembleResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.MemberInfoClient;
import cn.hydee.ydjia.merchantcustomer.feign.StoreFunctionClient;
import cn.hydee.ydjia.merchantcustomer.feign.YdjMerchantPlatformClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.domain.SysStoreFunction;
import cn.hydee.ydjia.merchantcustomer.feign.domain.YdjStore;
import cn.hydee.ydjia.merchantcustomer.feign.dto.MainResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.*;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonStoreClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.feign.util.LocationUtils;
import cn.hydee.ydjia.merchantcustomer.service.*;
import cn.hydee.ydjia.merchantcustomer.util.ConvertUtil;
import cn.hydee.ydjia.merchantcustomer.util.DateUtils;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 门店管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/04 15:57
 */
@RestController
@RequestMapping(value = "/${api.version}/store")
@Api(tags = "门店管理", description = "门店管理")
@Slf4j
public class StoreController extends AbstractController {

    @Autowired
    private StoreService storeService;
    @Autowired
    private StoreClient storeClient;

    @Autowired
    private YdjStoreService ydjStoreService;

    @Autowired
    private StoreFunctionClient storeFunctionClient;

    @Autowired
    private PlatformInnerServerService platformInnerServerService;
    @Autowired
    private PromoteService promoteService;

    @Autowired
    private MemberInfoClient memberInfoClient;

    @Autowired
    private LocalCacheService cacheService;

    @Autowired
    private StoreRuleNewService storeRuleNewService;

    @Autowired
    private YdjMerchantPlatformClient ydjMerchantPlatformClient;

    @Autowired
    private CommonStoreClientWrapperService commonStoreClientWrapperService;
    @Autowired
    StoreBuyService storeBuyService;
    @Autowired
    private RecommendService recommendService;

    @ApiOperation(
            value = "查中心店(旗舰店)",
            notes = "查中心店(旗舰店)",
            response = String.class,
            responseContainer = "Object")
    @PostMapping("/queryCenterStore")
    public ResponseBase<StoreListResDTO> queryCenterStore(@Valid @RequestBody StoreListReqDTO storeListReqDTO, BindingResult result) {
        this.checkValid(result);
        StoreListResDTO resDTO = ydjStoreService.queryCenterStore(storeListReqDTO, true);
        if (null != resDTO) {
            resDTO.setCenter(true);
        }
        return generateSuccess(resDTO);
    }

    @ApiOperation(value = "获取门店列表", notes = "获取门店列表")
    @PostMapping("/list")
    public ResponseBase<PageDTO<StoreListResDTO>> getStoreList(@Valid @RequestBody StoreListReqDTO storeListReqDTO,
                                                               @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                               @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                               BindingResult result) {
        checkValid(result);
        storeListReqDTO.setFilterMark(true);
        storeListReqDTO.setStClassList(Arrays.asList(OrgClassEnum.FRANCHISE_STORE.getCode(), OrgClassEnum.DIRECT_STORE.getCode()));
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            storeListReqDTO.setAppletAppId(appletAppId);
        }
        return storeService.getStoreList(storeListReqDTO);
    }

    @ApiOperation(value = "获取线上、线下门店列表", notes = "获取线上、线下门店列表")
    @PostMapping("/allStoreList")
    public ResponseBase<PageDTO<StoreListResDTO>> allStoreList(@Valid @RequestBody StoreListReqDTO storeListReqDTO,
                                                               @Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                               @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                               @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                               BindingResult result) {
        storeListReqDTO.setStClassList(Arrays.asList(OrgClassEnum.FRANCHISE_STORE.getCode(), OrgClassEnum.DIRECT_STORE.getCode()));
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            storeListReqDTO.setAppletAppId(appletAppId);
        }
        List<StoreListResDTO> allList = new ArrayList<>();
        //查询当前会员常用门店
        if (!StringUtils.isEmpty(userId) && storeListReqDTO.getCurrentPage() == 1) {
            StoreListResDTO storeListResDTO = ydjStoreService.getUsuallyStore(storeListReqDTO, userId);
            if (storeListResDTO != null) {
                if (StringUtils.isEmpty(storeListReqDTO.getStoreName()) || storeListResDTO.getStName().contains(storeListReqDTO.getStoreName())) {
                    allList.add(storeListResDTO);
                }
//                this.checkStoreRuleStatus(storeListReqDTO.getLongitude(), storeListReqDTO.getLatitude(), storeListResDTO);
                List<StoreListResDTO> storeList = Lists.newArrayList();
                storeList.add(storeListResDTO);
                storeService.checkStoreRuleStatus(storeListReqDTO.getMerCode(), storeListReqDTO.getLongitude(),
                        storeListReqDTO.getLatitude(), storeList);
            }
        }
        storeListReqDTO.setOnlineStatus(null);
        storeListReqDTO.setHasLongitudeLatitude(1);
        if (null != storeListReqDTO.getIsLongOpenStore() && storeListReqDTO.getIsLongOpenStore().intValue() == LocalConst.STATUS_ONE.intValue()) {
            storeListReqDTO.setOpenStartTime(LocalConst.CONST_START_TIME);
            storeListReqDTO.setOpenEndTime(LocalConst.CONST_END_TIME);
        }
        YdjStore centerStore = storeService.getCenterStore(storeListReqDTO.getMerCode());
        if (centerStore != null && StrUtil.isNotEmpty(storeListReqDTO.getStoreName())) {//搜索
            //转换旗舰店别名
            if (StrUtil.isNotEmpty(centerStore.getStName()) && centerStore.getStName().contains(storeListReqDTO.getStoreName())) {
                //查询旗舰店和其他门店
                storeListReqDTO.setOrStoreCode(centerStore.getStCode());
            }
        }
        ResponseBase<PageDTO<StoreListResDTO>> responseBase = storeService.getStoreList(storeListReqDTO);
        if (!responseBase.checkSuccess() || responseBase.getData() == null || CollectionUtils.isEmpty(responseBase.getData().getData())) {
            return responseBase;
        }
        PageDTO<StoreListResDTO> pageDTO = responseBase.getData();
        List<StoreListResDTO> storeList = pageDTO.getData();
        List<StoreListResDTO> list;
        //门店列表过滤当前门店
        if (!StringUtils.isEmpty(storeListReqDTO.getCurrentStoreId())) {
            list = storeList.stream().filter(store -> !store.getId().equals(storeListReqDTO.getCurrentStoreId())).collect(Collectors.toList());
        } else {
            //第一页查询，门店列表过滤常用门店
            if (!CollectionUtils.isEmpty(allList) && storeListReqDTO.getCurrentPage() == 1) {
                list = storeList.stream().filter(store -> !allList.stream().map(StoreListResDTO::getStCode).collect(Collectors.toList()).contains(store.getStCode())).collect(Collectors.toList());
            } else {
                list = storeList;
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            allList.addAll(list);
        }
        if (CollUtil.isNotEmpty(allList)) {
            allList.forEach(store -> store.setCenter(centerStore != null && Objects.equals(store.getStCode(), centerStore.getStCode())));
        }
        pageDTO.setData(allList);
        return responseBase;
    }

    @ApiOperation(value = "附近门店", tags = "YXDJ-1704", notes = "门店切换，升级版，只查询线上门店, 注意这里会返回云仓店，前端根据实际业务场景处理")
    @PostMapping("/nearby-store")
    public ResponseBase<PageDTO<StoreBaseInfoResp>> nearbyStore(@Valid @RequestBody NearbyStoreReq nearbyStoreReq) {
        return generateSuccess(storeService.pageStoreBaseInfo(nearbyStoreReq, YesOrNoType.YES));
    }

    @ApiOperation(value = "门店购药查询",  tags = "MATERIAL-1436", notes = "门店查询包含门店配送信息")
    @PostMapping(value = "/buy-medicine")
    public ResponseBase<StoreBuyResp> queryStoreHomePage(@Valid @RequestBody StoreBuyReq storeBuyReq, BindingResult bindingResult) {
        checkValid(bindingResult);
        return generateSuccess(storeBuyService.storeBuyInfo(storeBuyReq, super.getAPI_VERSION()));
    }

    @ApiOperation(value = "根据商品id获取门店列表", tags = {"MATERIAL-1936"})
    @PostMapping("/saleList")
    public ResponseBase<List<StoreListResDTO>> getStoreSaleList(@Valid @RequestBody StoreSalesListReqDTO storeListReqDTO,
                                                                @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                                @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                                BindingResult result) {
        checkValid(result);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            storeListReqDTO.setAppletAppId(appletAppId);
        }
        YdjStore centerStore = storeService.getCenterStore(storeListReqDTO.getMerCode());
        if (centerStore != null && StrUtil.isNotEmpty(storeListReqDTO.getStoreName())) {//搜索
            //转换旗舰店别名
            if (StrUtil.isNotEmpty(centerStore.getStName()) && centerStore.getStName().contains(storeListReqDTO.getStoreName())) {
                //查询旗舰店和其他门店
                storeListReqDTO.setOrStoreCode(centerStore.getStCode());
            }
        }
        List<StoreListResDTO> storeSaleList = storeService.getStoreSaleList(storeListReqDTO);
        if (CollUtil.isNotEmpty(storeSaleList)) {
            //旗舰店标记
            storeSaleList.forEach(store -> store.setCenter(centerStore != null && Objects.equals(store.getStCode(), centerStore.getStCode())));
        }
        if (storeListReqDTO.getCurrentSpecId() != null && CollUtil.isNotEmpty(storeSaleList)) {
            List<RecommendCacheResp> recommend = recommendService.getRecommend(Lists.newArrayList(storeListReqDTO.getCurrentSpecId()), storeSaleList.stream().map(MainResDTO::getId).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(recommend)) {
                Map<String, RecommendCacheResp> recommendMap = recommend.stream().collect(Collectors.toMap(RecommendCacheResp::getStoreId, Function.identity()));
                //设置B2C商品标识
                storeSaleList.forEach(store -> store.setB2cGoods(recommendMap.containsKey(store.getId())));
            }
        }
        return generateSuccess(storeSaleList);
    }

    @ApiOperation(value = "获取门店详情", notes = "获取门店详情")
    @GetMapping("/{storeId}")
    public ResponseBase<StoreResDTO> getStore(@PathVariable String storeId) {
        if (StringUtils.isEmpty(storeId)) {
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        return storeService.getStore(storeId);
    }

    @ApiOperation(value = "B2C首页查门店数据", notes = "B2C首页查门店数据", tags = {"MATERIAL-1936-v2"})
    @PostMapping("/queryB2CPageSet")
    public ResponseBase<StoreListResDTO> queryB2CPageSet(@Valid @RequestBody StoreListReqDTO storeListReqDTO,
                                                         @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                         @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                         BindingResult result) throws InstantiationException, IllegalAccessException {
        this.checkValid(result);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            storeListReqDTO.setAppletAppId(appletAppId);
        }
        List<YdjStore> list;
        StoreSaleListReqDTO storeSaleListReqDTO = new StoreSaleListReqDTO();
        storeSaleListReqDTO.setIsForFirstPage(String.valueOf(YesOrNoType.YES.getCode()));
        if (storeListReqDTO.getPromotionType() != null) {
            //查有活动的门店列表
            QueryPromoteStoresDTO queryPromoteStoresDTO = new QueryPromoteStoresDTO();
            BeanUtils.copyProperties(storeListReqDTO, queryPromoteStoresDTO);
            List<String> storeList = promoteService.getStoreListByActivity(queryPromoteStoresDTO);
            if (CollectionUtils.isEmpty(storeList)) {
                return generateSuccess(null);
            }
            storeListReqDTO.setList(storeList);
            PageDTO<StoreListResDTO> storeListResDTOPageDTO = commonStoreClientWrapperService.queryStoreListV3(storeListReqDTO);

            list = BeanUtil.copyList(storeListResDTOPageDTO.getData(), YdjStore.class);
//            storeSaleListReqDTO.setIsForFirstPage("0");
        } else {
            list = ydjStoreService.queryYdjCenterStore(storeListReqDTO.getMerCode(), true);
        }
        if (CollectionUtils.isEmpty(list)) {
            return generateSuccess(null);
        }
        //如果经纬度为空，则查中心店
        if (StringUtils.isEmpty(storeListReqDTO.getLatitude()) || StringUtils.isEmpty(storeListReqDTO.getLongitude())) {

            StoreListResDTO storeListResDTO = ydjStoreService.queryCenterStore(storeListReqDTO, true);
            if (storeListResDTO != null) {
                storeListResDTO.setCenter(true);
            }
            return generateSuccess(storeListResDTO);
        }
        BeanUtils.copyProperties(storeListReqDTO, storeSaleListReqDTO);
        List<StoreResDTO> storeResDTOList = commonStoreClientWrapperService.queryStoreSaleList(storeSaleListReqDTO);
        if (CollectionUtils.isEmpty(storeResDTOList)) {
            //storeListReqDTO.setList(null);
            StoreListResDTO storeListResDTO = ydjStoreService.queryCenterStore(storeListReqDTO, true);
            if (storeListResDTO != null) {
                storeListResDTO.setCenter(true);
            }
            return generateSuccess(storeListResDTO);
        } else {
            StoreListResDTO storeListResDTO = new StoreListResDTO();
            BeanUtils.copyProperties(storeResDTOList.get(0), storeListResDTO);
            if (null != storeListResDTO && list.get(0).getStCode().equals(storeListResDTO.getStCode())) {
                storeListResDTO.setCenter(true);
            }
            return generateSuccess(storeListResDTO);
        }
    }

    @ApiOperation(value = "获取门店详情-带距离及配送时间段", notes = "获取门店详情-带距离及配送时间段")
    @PostMapping("/getStoreById")
    public ResponseBase<StoreListResDTO> getStoreById(@Valid @RequestBody QueryStoreById queryStoreById,
                                                      @RequestHeader(required = false) String userId,
                                                      @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                      @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                      BindingResult result) {
        checkValid(result);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            queryStoreById.setAppletAppId(appletAppId);
        }
        return generateSuccess(getStoreById(queryStoreById, userId));
    }

    private StoreListResDTO getStoreById(QueryStoreById queryStoreById, String userId) {
        StoreListResDTO storeListResDTO;
        if (StringUtils.isEmpty(queryStoreById.getLatitude()) || StringUtils.isEmpty(queryStoreById.getLongitude())) {
            StoreResDTO storeResDTO = platformInnerServerService.getStoreClientService().queryStore(queryStoreById.getStoreId());
            storeListResDTO = new StoreListResDTO();
            BeanUtils.copyProperties(storeResDTO, storeListResDTO);
            storeListResDTO.setShortName(storeResDTO.getShortName());
        } else {
            StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
            BeanUtils.copyProperties(queryStoreById, storeListReqDTO);
            storeListReqDTO.setStoreId(queryStoreById.getStoreId());
            storeListReqDTO.setPageSize(1);
            PageDTO<StoreListResDTO> pageDTO = commonStoreClientWrapperService.pageStoreV3(storeListReqDTO);
            if (pageDTO == null || CollectionUtils.isEmpty(pageDTO.getData())) {
                throw WarnException.builder().code(ErrorType.STORE_IS_NULL.getCode()).
                        tipMessage(ErrorType.STORE_ONLINE_OFF.getMsg()).build();
            }
            storeListResDTO = pageDTO.getData().get(0);
        }
        if (null != storeListResDTO) {
            YdjStore ydjStore = storeService.getCenterStore(queryStoreById.getMerCode());
            if (ydjStore != null && ydjStore.getStCode().equals(storeListResDTO.getStCode())) {
                if (ydjStore.getCenterStore().equals(IsCenterStore.IS_CENTER_SOTRE.getCode())) {
                    storeListResDTO.setCenter(true);
                }
            }
            List<String> storeIds = new ArrayList<>();
            storeIds.add(storeListResDTO.getId());

            //查询门店资质图片
            List<StoreCerResDTO>  storeCerResDTOS = platformInnerServerService.getStoreClientService().storeCerList(StoreCerQueryReqDTO
                    .builder().merCode(storeListResDTO.getMerCode()).stCode(storeListResDTO.getStCode()).build());
            if(!CollectionUtils.isEmpty(storeCerResDTOS)) {
                List<String> pics = storeCerResDTOS.stream().map(StoreCerResDTO::getPic).collect(Collectors.toList());
                storeListResDTO.setStoreCerList(pics);
            }
            List<SysStoreFunction> funList = platformInnerServerService.getStoreFunctionService().getByStoreIds(storeListResDTO.getId());
             storeListResDTO.setFunList(funList);
            // 门店是否支持医保
            if (cacheService.queryMedicalPaymentConfig(queryStoreById.getMerCode(), queryStoreById.getStoreId())) {
                storeListResDTO.setSupportMedicarePay(YesOrNoType.YES.getCode());
            }
            this.checkStoreRuleFilterStatus(queryStoreById.getLongitude(), queryStoreById.getLatitude(),
                    storeListResDTO);
        }
        return storeListResDTO;
    }

    @ApiOperation(value = "获取线上、线下门店详情-带距离及配送时间段", notes = "获取线上、线下门店详情-带距离及配送时间段")
    @PostMapping("/getAllStoreById")
    public ResponseBase<StoreListResDTO> getAllStoreById(@Valid @RequestBody QueryStoreById queryStoreById,
                                                         @RequestHeader(required = false) String userId,
                                                         @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                         @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId) {
        queryStoreById.setOnlineStatus(null);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            queryStoreById.setAppletAppId(appletAppId);
        }
        return generateSuccess(getStoreById(queryStoreById, userId));
    }

    @ApiOperation(value = "切换门店-获取门店列表", notes = "切换门店-获取门店列表")
    @PostMapping("/changeStoreList")
    public ResponseBase<List<ChangeStoreListDTO>> changeStoreList(@Valid @RequestBody QueryStoresReqDTO queryStoresReqDTO,
                                                                  @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                                  @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                                  BindingResult result) throws InstantiationException, IllegalAccessException {
        //查有活动的门店列表
        QueryPromoteStoresDTO queryPromoteStoresDTO = new QueryPromoteStoresDTO();
        BeanUtils.copyProperties(queryStoresReqDTO, queryPromoteStoresDTO);

        long startTime = System.currentTimeMillis();
        List<String> storeList = promoteService.getStoreListByActivity(queryPromoteStoresDTO);
        long endTime = System.currentTimeMillis();

        log.info("getStoreListByActivity查询时长:" + (endTime - startTime));
        if (CollectionUtils.isEmpty(storeList)) {
            return generateSuccess(null);
        }
        ChangeStoreResDTO changeStoreResDTO = new ChangeStoreResDTO();
        List<ChangeStoreBaseDTO> changeStoreBaseDTOS;
        if (StringUtils.isEmpty(queryStoresReqDTO.getLatitude()) || StringUtils.isEmpty(queryStoresReqDTO.getLongitude())) {
            //通过门店ID列表查门店信息
            QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
            queryStoreDTO.setMerCode(queryStoresReqDTO.getMerCode());
            queryStoreDTO.setOnlineStatus(YesOrNoType.YES.getCode());
            queryStoreDTO.setStatus(YesOrNoType.YES.getCode());
            queryStoreDTO.setExcelFlag(true);
            queryStoreDTO.setList(storeList);
            if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
                queryStoreDTO.setAppletAppId(appletAppId);
            }
            PageDTO<StoreResDTO> storeResDTOPageDTO = commonStoreClientWrapperService.queryStoreByCondition(queryStoreDTO);
            if (Objects.isNull(storeResDTOPageDTO)) {
                return generateSuccess(null);
            }
            changeStoreBaseDTOS = BeanUtil.copyList(storeResDTOPageDTO.getData(), ChangeStoreBaseDTO.class);
        } else {
            StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
            BeanUtils.copyProperties(queryStoresReqDTO, storeListReqDTO);
            storeListReqDTO.setPageFlag(1);
            storeListReqDTO.setList(storeList);
            if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
                storeListReqDTO.setAppletAppId(appletAppId);
            }
            PageDTO<StoreListResDTO> pageDTO = commonStoreClientWrapperService.queryStoreList(storeListReqDTO);
            if (Objects.isNull(pageDTO) || CollectionUtils.isEmpty(pageDTO.getData())) {
                return generateSuccess(null);
            }
            changeStoreBaseDTOS = BeanUtil.copyList(pageDTO.getData(), ChangeStoreBaseDTO.class);
        }

        if (CollectionUtils.isEmpty(changeStoreBaseDTOS)) {
            return generateSuccess(null);
        }
        //附近门店
        if (!StringUtils.isEmpty(changeStoreBaseDTOS.get(0).getDistance())) {
            log.info("附近的门店" + changeStoreBaseDTOS.get(0).getId());
            BeanUtils.copyProperties(changeStoreBaseDTOS.get(0), changeStoreResDTO);
            changeStoreResDTO.setStoreId(changeStoreBaseDTOS.get(0).getId());
        }
        //去掉市为空的数据
        changeStoreBaseDTOS = changeStoreBaseDTOS.stream().filter(o -> !StringUtils.isEmpty(o.getCity())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(changeStoreBaseDTOS)) {
            return generateSuccess(null);
        }
        Map<String, List<ChangeStoreBaseDTO>> subStoreMap = new HashMap<>();
        List<ChangeStoreListDTO> changeStoreListDTOS = new ArrayList<>();
        changeStoreBaseDTOS.forEach(store -> {
            List<ChangeStoreBaseDTO> tempList = subStoreMap.computeIfAbsent(store.getCity(),
                    k -> new ArrayList<>());
            tempList.add(store);
        });
        //获取城市列表
        List<String> citys = changeStoreBaseDTOS.stream().map((ChangeStoreBaseDTO::getCity)).collect(Collectors.toList());
        citys = citys.stream().distinct().collect(Collectors.toList());
        ChangeStoreListDTO changeStoreListDTO;
        for (String city : citys) {
            changeStoreListDTO = new ChangeStoreListDTO();
            changeStoreListDTO.setCity(city);
            changeStoreListDTO.setStoreResDTOS(BeanUtil.copyList((List<ChangeStoreBaseDTO>) subStoreMap.get(city), ChangeStoreBaseDTO.class));
            changeStoreListDTOS.add(changeStoreListDTO);
        }
        changeStoreResDTO.setStoreLis(changeStoreListDTOS);
        return generateSuccess(changeStoreListDTOS);
    }

    @Deprecated
    @ApiOperation(value = "小程序-获取商户门店列表", notes = "小程序-获取商户门店列表")
    @PostMapping("/appletList")
    public ResponseBase<PageDTO<StoreListResDTO>> getStoreListForApplet(@Valid @RequestBody StoreListForAppletReqDTO storeListForAppletReqDTO, BindingResult result) {
        checkValid(result);
        StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
        BeanUtils.copyProperties(storeListForAppletReqDTO, storeListReqDTO);
        storeListReqDTO.setIsGroupMerCode(YesOrNoType.YES.getCode());
//        storeListReqDTO.setMerCode("666666");
        log.info("====小程序-获取商户门店列表storeListReqDTO:{}", JSON.toJSONString(storeListReqDTO));
        return generateSuccess(commonStoreClientWrapperService.pageStoreV3(storeListReqDTO));
    }

    @ApiOperation(value = "获取用户常用门店", notes = "获取用户常用门店")
    @PostMapping("/getUsuallyStore")
    public ResponseBase<StoreResDTO> getUsuallyStore(@RequestBody GetUsuallyStoreReqDTO getUsuallyStoreReqDTO,
                                                     @RequestHeader(required = false) String userId,
                                                     @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                     @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                     BindingResult result) {
        checkValid(result);
        if (StringUtils.isEmpty(userId)) {
            return ResponseBase.success();
        }
        if (!LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            appletAppId = null;
        }
        StoreResDTO storeResDTO = storeService.getAppletSceneUsualStore(getUsuallyStoreReqDTO.getMerCode(), userId, appletAppId);
        if (storeResDTO == null) {
            return ResponseBase.success();
        }

        String latitude = getUsuallyStoreReqDTO.getLatitude();
        String longitude = getUsuallyStoreReqDTO.getLongitude();
        boolean flag = !StringUtils.isEmpty(latitude) && !StringUtils.isEmpty(longitude)
                && !StringUtils.isEmpty(storeResDTO.getLatitude()) && !StringUtils.isEmpty(storeResDTO.getLongitude());
        if (flag) {
            Double latitude1 = Double.valueOf(latitude);
            Double longitude1 = Double.valueOf(longitude);
            Double latitude2 = Double.valueOf(storeResDTO.getLatitude());
            Double longitude2 = Double.valueOf(storeResDTO.getLongitude());
            storeResDTO.setDistance(LocationUtils.getDistance(latitude1, longitude1, latitude2, longitude2));
        }
        StoreListResDTO storeListResDTO = new StoreListResDTO();
        BeanUtils.copyProperties(storeResDTO, storeListResDTO);
        List<StoreListResDTO> storeList = Lists.newArrayList();
        storeList.add(storeListResDTO);
        storeService.checkStoreRuleStatus(getUsuallyStoreReqDTO.getMerCode(), getUsuallyStoreReqDTO.getLongitude(),
                getUsuallyStoreReqDTO.getLatitude(), storeList);
        storeResDTO.setOverDeliveryStatus(storeListResDTO.getOverDeliveryStatus());
        storeResDTO.setClosedStatus(storeListResDTO.getClosedStatus());
        storeResDTO.setOpenStartTimeString(storeListResDTO.getOpenTime());
        storeResDTO.setOpenEndTimeString(storeListResDTO.getEndTime());
        return generateObjectSuccess(storeResDTO);

    }

    @ApiOperation(value = "设置常用门店", notes = "设置常用门店")
    @PostMapping("/setUsuallyStore")
    public ResponseBase<Integer> setUsuallyStore(@RequestBody SetUsuallyStoreReqDTO setUsuallyStoreReqDTO, @RequestHeader String userId, BindingResult result) {
        checkValid(result);
        if (YesOrNoType.NO.getCode().equals(setUsuallyStoreReqDTO.getIsClose())) {
            setUsuallyStoreReqDTO.setStoreId("");
        }
        ResponseBase<Integer> base = memberInfoClient.updateMemberStore(setUsuallyStoreReqDTO.getMerCode(), setUsuallyStoreReqDTO.getStoreId(), userId);
        return base;
    }

    @ApiOperation(value = "获取用户购买过的门店", notes = "获取用户购买过的门店")
    @PostMapping("/getUserBuyStore")
    public ResponseBase<PageDTO<StoreListResDTO>> getUserBuyStore(@RequestBody UserBuyStoreReqDTO userBuyStoreReqDTO,
                                                                  @RequestHeader(required = false) String userId,
                                                                  @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                                  @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                                  BindingResult result) {
        checkValid(result);
        if (StringUtils.isEmpty(userId)) {
            return ResponseBase.success();
        }
        userBuyStoreReqDTO.setMemberId(userId);
        userBuyStoreReqDTO.setIsGroupStoreId(true);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            userBuyStoreReqDTO.setAppletAppId(appletAppId);
        }
        return storeService.getUserBuyStore(userBuyStoreReqDTO, false);

    }

    @ApiOperation(value = "获取用户购买过的线上、线下门店", notes = "获取用户购买过的线上、线下门店")
    @PostMapping("/getUserBuyAllStore")
    public ResponseBase<PageDTO<StoreListResDTO>> getUserBuyAllStore(@RequestBody UserBuyStoreReqDTO userBuyStoreReqDTO,
                                                                     @RequestHeader(required = false) String userId,
                                                                     @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                                     @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                                     BindingResult result) {
        checkValid(result);
        if (StringUtils.isEmpty(userId)) {
            return ResponseBase.success();
        }
        userBuyStoreReqDTO.setMemberId(userId);
        userBuyStoreReqDTO.setIsGroupStoreId(true);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            userBuyStoreReqDTO.setAppletAppId(appletAppId);
        }
        return storeService.getUserBuyStore(userBuyStoreReqDTO, true);

    }

    @ApiOperation(value = "根据条件查询门店信息", notes = "根据条件查询门店信息")
    @PostMapping("/getStoreListBy")
    public ResponseBase<PageDTO<StoreResDTO>> getStoreListBy(@RequestBody @Valid QueryStoreDTO queryStoreDTO,
                                                             @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                             @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                             BindingResult result) {
        checkValid(result);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            queryStoreDTO.setAppletAppId(appletAppId);
        }
        return generateSuccess(commonStoreClientWrapperService.queryStoreByCondition(queryStoreDTO));
    }

    @Deprecated
    @ApiOperation(value = "根据定位状态及登录状态查询门店信息")
    @PostMapping(value = "/_queryStoreByCondition")
    public ResponseBase<HomePageStoreRespDto> queryStoreByCondition(@Valid @RequestBody StoreListReqDTO req,
                                                                    @RequestHeader(required = false) String userId,
                                                                    @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                                    @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                                    BindingResult result) {
        checkValid(result);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            req.setAppletAppId(appletAppId);
        }
        return generateSuccess(ydjStoreService.queryStoreByCondition(req, userId));
    }

    @ApiOperation(value = "获取门店二维码", notes = "获取门店二维码")
    @PostMapping("/getQrCode")
    public ResponseBase<String> getQrCode(@RequestBody @Valid QrCodeReqDTO reqDTO, BindingResult result) {
        checkValid(result);
        //门店推荐
        String type = "2";
        return ydjMerchantPlatformClient.getQrCode(type, reqDTO);
    }

    @ApiOperation(value = "进店规则-门店查询")
    @PostMapping(value = "/_queryAssembledStore")
    public ResponseBase<StoreAssembleResDTO> queryAssembledStore(@Valid @RequestBody StoreAssembleReqDTO req,
                                                                 @RequestHeader(required = false) String userId,
                                                                 @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                                 @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                                 BindingResult result) {
        checkValid(result);
        req.setUserId(userId);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            req.setAppletAppId(appletAppId);
        }
        return generateSuccess(ydjStoreService.queryAssembledStore(req));
    }

    @ApiOperation(value = "活动门店列表", notes = "活动页切换门店")
    @PostMapping("/getActivityStoreList")
    public ResponseBase<PageDTO<StoreListResDTO>> getActivityStoreList(@Valid @RequestBody QueryStoresReqDTO queryStoresReqDTO,
                                                                       @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                                       @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                                       BindingResult result) {
        checkValid(result);
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            queryStoresReqDTO.setAppletAppId(appletAppId);
        }
        return generateSuccess(storeService.getActivityStoreList(queryStoresReqDTO));
    }

    private void checkStoreRuleFilterStatus(String longitude, String latitude, StoreListResDTO storeListResDTO) {
        if (storeListResDTO == null) {
            return;
        }
        this.checkStoreClosedStatus(storeListResDTO);
        List<StoreListResDTO> storeList = Lists.newArrayList();
        storeList.add(storeListResDTO);
        storeService.checkStoreRuleStatus(storeListResDTO.getMerCode(), longitude, latitude, storeList);
        if (StatusEnums.STOP_USE.getCode().equals(storeListResDTO.getOnlineStatus())) {
            storeListResDTO.setFilterStatus(true);
            return;
        }
        storeListResDTO.setFilterStatus(false);
        if (!storeListResDTO.getOverDeliveryStatus() && !storeListResDTO.getClosedStatus()) {
            return;
        }
        StoreRuleResDTO detail = storeRuleNewService.getDetail(storeListResDTO.getMerCode());
        boolean beyondDeliveryStatus =
                detail != null && StatusEnums.ENABLING.getCode().equals(detail.getBeyondDeliveryStatus());
        boolean nonOpenTimeStatus =
                detail != null && StatusEnums.ENABLING.getCode().equals(detail.getNonOpenTimeStatus());
        boolean filterStatus = (beyondDeliveryStatus && storeListResDTO.getOverDeliveryStatus())
                || (nonOpenTimeStatus && storeListResDTO.getClosedStatus());
        if (!filterStatus) {
            // 再判断自提
            this.checkOverSelfDistanceLimitStatus(longitude, latitude, storeListResDTO, detail);
            filterStatus = storeListResDTO.getOverSelfDistanceLimitStatus();
        }
        storeListResDTO.setFilterStatus(filterStatus);
    }

    private void checkOverSelfDistanceLimitStatus(String longitude, String latitude, StoreListResDTO storeListResDTO,
                                                  StoreRuleResDTO detail) {
        storeListResDTO.setOverSelfDistanceLimitStatus(false);
        if (detail == null) {
            return;
        }
        if (StringUtils.isEmpty(longitude) || StringUtils.isEmpty(latitude)) {
            return;
        }
        boolean selfStatus = StatusEnums.ENABLING.getCode().equals(storeListResDTO.getIsself());
        if (!selfStatus) {
            return;
        }
        if (StatusEnums.STOP_USE.getCode().equals(detail.getBeyondSelfDistanceStatus())) {
            return;
        }
        Integer selfDistanceLimit = detail.getSelfDistanceLimit();
        String distance = storeListResDTO.getDistance();
        if (StringUtils.isEmpty(distance) || selfDistanceLimit == null
                || BigDecimal.valueOf(Double.valueOf(distance)).compareTo(BigDecimal.valueOf(selfDistanceLimit)) <= 0) {
            return;
        }
        storeListResDTO.setOverSelfDistanceLimitStatus(true);
    }

    private void checkStoreClosedStatus(StoreListResDTO store) {
        store.setClosedStatus(false);
        Integer openStartTime = store.getOpenStartTime() == null ? LocalConst.CONST_START_TIME
                : store.getOpenStartTime();
        Integer openEndTime = store.getOpenEndTime() == null ? LocalConst.CONST_END_TIME
                : store.getOpenEndTime();
        Integer openStatus = store.getOpenStatus();
        store.setOpenTime(ConvertUtil.getHourString(openStartTime, false));
        store.setEndTime(ConvertUtil.getHourString(openEndTime, true));
        long time = DateUtils.getLocalTimeValue(LocalTime.now());
        if (openStartTime > time || openEndTime < time) {
            store.setClosedStatus(true);
        }
    }

    /**
     * 设置常用门店标记
     *
     * @param merCode
     * @param userId
     * @param appletAppId
     * @param storeListResDTO
     * <AUTHOR>
     * @date 2023/4/14
     */
    private void setUsualStoreMark(String merCode, String userId, String appletAppId, StoreListResDTO storeListResDTO) {
        storeListResDTO.setIsUsuallyStore(YesOrNoType.NO.getCode());
        if (StringUtils.isEmpty(merCode) || StringUtils.isEmpty(userId)) {
            return;
        }
        StoreResDTO storeResDTO = storeService.getAppletSceneUsualStore(merCode, userId, appletAppId);
        if (Objects.isNull(storeResDTO)) {
            return;
        }
        if (storeListResDTO.getId().equals(storeResDTO.getId())) {
            // 当前门店即常用门店
            storeListResDTO.setIsUsuallyStore(YesOrNoType.YES.getCode());
            return;
        }
        storeListResDTO.setUsualStoreId(storeResDTO.getId());
        storeListResDTO.setUsualStoreName(storeResDTO.getStName());
    }
}
