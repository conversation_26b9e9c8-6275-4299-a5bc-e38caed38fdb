package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.resp.ActivityDeliveryPrizeResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.ActivityDeliveryClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 配送有礼相关接口
 *
 * <AUTHOR>
 * @date 2020/10/30 下午5:01
 */
@RestController
@Api(tags = "配送有礼相关接口")
@RequestMapping("/${api.version}/activityDelivery")
@AllArgsConstructor
public class ActivityDeliveryController extends AbstractController {

    @Autowired
    private ActivityDeliveryClient activityDeliveryClient;

    /**
     * 查询配送有礼活动奖励信息
     *
     * @param merCode 商户编码
     * @param storeId 门店id
     */
    @ApiOperation(value = "查询配送有礼活动奖励信息")
    @GetMapping("/getActivityDeliveryPrizeList")
    public ResponseBase<List<ActivityDeliveryPrizeResDTO>> getActivityDeliveryPrizeList(@RequestParam(value = "merCode") String merCode, @RequestParam(value = "storeId") String storeId) {
        return activityDeliveryClient.getActivityDeliveryPrizeList(merCode, storeId);
    }

}
