package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.poster.dto.PosterReqDTO;
import cn.hydee.ydjia.merchantcustomer.service.PosterService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.MER_CODE;

/**
 * <AUTHOR>
 * @date 2020/2/25 14:22
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/poster")
@Api(value = "海报分享管理", description = "海报分享管理")
public class PosterController extends AbstractController {

    @Autowired
    private PosterService posterService;

    /**
     * 前端拼接地址：
     * dev是https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com
     * 线上是https://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com
     */
    @ApiOperation(value = "生成海报", notes = "生成海报")
    @PostMapping("createPoster")
    public ResponseBase<String> createPoster(@RequestHeader(MER_CODE) String merCode,
                                             @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                             @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                             @RequestBody PosterReqDTO dto) throws Exception {
        log.info("/poster/createPoster merCode = {}, param = {}", merCode, JSON.toJSONString(dto));
        if (!StringUtils.hasLength(merCode) && !StringUtils.hasLength(dto.getMerCode())) {
            return ResponseBase.success();
        }
        if (!StringUtils.hasLength(dto.getMerCode())) {
            dto.setMerCode(merCode);
        }
        // 设置小程序appId
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            dto.setAppletAppId(appletAppId);
        }
        String posterUrl = posterService.createPoster(dto);
        return generateObjectSuccess(posterUrl);
    }
}
