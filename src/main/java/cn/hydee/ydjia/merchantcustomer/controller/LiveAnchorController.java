package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.configuration.DisLockConfiguration;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.LiveSponsorOrCloseReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivityRedpackAddReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LiveCreateRedPackReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LiveReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.enums.LiveLotteryStatusEnum;
import cn.hydee.ydjia.merchantcustomer.feign.RedPackClient;
import cn.hydee.ydjia.merchantcustomer.feign.WeeChatMaClient;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.feign.LiveClient;
import cn.hydee.ydjia.merchantcustomer.service.ActivityLiveChatService;
import cn.hydee.ydjia.merchantcustomer.service.LiveService;
import cn.hydee.ydjia.merchantcustomer.service.RedisService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.ValidateUtils;
import cn.hydee.ydjia.merchantcustomer.util.ModelConvertUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;

import java.util.List;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.*;

/**
 * <AUTHOR>
 * @date 2020/5/18 16:10
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/live-anchor")
@Api(value = "主播端直播间接口", tags = "主播端直播间接口")
public class LiveAnchorController extends AbstractController {

    @Autowired
    private ActivityLiveChatService liveChatService;

    @Autowired
    private LiveService liveService;

    @Autowired
    RedisService redisService;

    @Autowired
    private LiveClient liveClient;

    @Autowired
    private RedPackClient redPackClient;

    @Autowired
    private WeeChatMaClient weeChatMaClient;


    @ApiOperation(value = "进入直播间，获取直播间基础配置信息", notes = "获取直播间基础配置信息，比如商品，" +
            "直播地址等")
    @PostMapping("/info")
    public ResponseBase<LiveInfoRespDTO> getLiveInfo(@Nullable @RequestHeader(LocalConst.HEAD_LIVE_USER_ID_KEY) String userId,
                                                     @Valid @RequestBody LiveReqDTO dto, BindingResult result) {
        checkValid(result);
        dto.setUserId(userId);
        LiveInfoRespDTO liveInfo = liveChatService.getLiveAnchorInfo(dto);
        return generateObjectSuccess(liveInfo);
    }

    @ApiOperation(value = "发起直播", notes = "发起直播，在beginTime之前30分钟内才能发起")
    @PostMapping("sponsor")
    @DisLockConfiguration.DisLock(keyExpr = "'sponsor:' + #reqDTO.id",
            lockType = DisLockConfiguration.DisLock.LockType.OPTIMISTIC)
    public ResponseBase<Boolean> sponsor(@Nullable @RequestHeader(LocalConst.HEAD_LIVE_USER_ID_KEY) String userId,
                                                          @Nullable @RequestHeader(LocalConst.HEAD_LIVE_OPEN_ID_KEY) String openId,
                                                          @RequestHeader(value = MER_CODE, required = false) String merCode,
                                                          @RequestBody LiveSponsorOrCloseReqDTO reqDTO) {
        log.info("发起直播，req={}", reqDTO);

        return liveService.sponsor(merCode,reqDTO.getId(), userId, openId);
    }

    @ApiOperation(value = "关闭直播", notes = "关闭直播")
    @PostMapping("close")
    public ResponseBase<Boolean> close(@RequestBody LiveSponsorOrCloseReqDTO reqDTO) {
        log.info("关闭直播，req={}", reqDTO);


        return liveService.close(reqDTO.getId());
    }


    @ApiOperation(value = "关闭或者开启海报", notes = "关闭或者开启海报")
    @GetMapping("setPosterStatus")
    public ResponseBase<Integer> setPosterStatus(@RequestParam Long liveId, @ApiParam(value = "海报状态：0，关闭；1：开启")@RequestParam Integer status) {
        if (StringUtils.isEmpty(liveId)) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }
        redisService.setValue(REDIS_LIVE_POSTER_STATUS_KEY + liveId, status);
        return generateObjectSuccess(status);
    }


    @ApiOperation(value = "获取抽奖活动列表", notes = "获取抽奖活动列表")
    @GetMapping("getActivityList")
    public ResponseBase<List<ActivityDetailRespDTO>> getActivityList(@RequestParam Long liveId) {

        return generateObjectSuccess(liveService.getActivityList(liveId));
    }

    @ApiOperation(value = "关闭或者开启抽奖活动", notes = "关闭或者开启抽奖活动")
    @GetMapping("setActivityStatus")
    public ResponseBase<Integer> setActivityStatus(@RequestParam Long liveId,@RequestParam Integer activityId, @ApiParam(value = "活动状态：0，关闭；1：开启")@RequestParam Integer status) {
        if (StringUtils.isEmpty(liveId)) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }
        redisService.setValue(REDIS_LIVE_ACTIVITY_STATUS_KEY + liveId + "_" + activityId, status);
        if(status.equals(LiveLotteryStatusEnum.OPEN.getCode())){
            //多场抽奖活动，开启一场关闭其他场次
            ResponseBase<List<ActivityDetailRespDTO>> responseBase = liveClient.getActivityById(liveId);
            ValidateUtils.checkResult(responseBase);
            List<ActivityDetailRespDTO> list = responseBase.getData();
            if(!CollectionUtils.isEmpty(list)){
                list.stream().filter(activity -> !activity.getId().equals(activityId)).forEach(activity ->{
                    redisService.setValue(REDIS_LIVE_ACTIVITY_STATUS_KEY + liveId + "_" + activity.getId(), LiveLotteryStatusEnum.CLOSE.getCode());
                });
            }
        }

        return generateObjectSuccess(status);
    }

    @ApiOperation(value = "IM登录过期，重新生成userSig", notes = "IM登录过期，重新生成userSig")
    @PostMapping("/getUserSig")
    public ResponseBase<String> getUserSig(@Nullable @RequestHeader(LocalConst.HEAD_LIVE_USER_ID_KEY) String userId) {
        return liveChatService.getUserSig(userId);
    }

    @ApiOperation(value = "获取主播端商品列表", notes = "获取主播端商品列表")
    @GetMapping("/getCommodities")
    public ResponseBase<List<LiveCommodityRespDTO>> getCommodities(@RequestParam Long liveId) {

        return liveClient.getCommodityList(liveId);
    }

    @ApiOperation(value = "创建红包", notes = "创建红包")
    @PostMapping("createRedPack")
    public ResponseBase<Integer> createRedPack(@RequestHeader(MER_CODE) String merCode,
                                                                   @RequestBody LiveCreateRedPackReqDTO reqDTO) {
        ActivityRedpackAddReqDTO req = ModelConvertUtils.convert(reqDTO,ActivityRedpackAddReqDTO.class);
        req.setMerCode(merCode);
        req.setSource(1);
        req.setCover(1);
        req.setBusinessId(reqDTO.getLiveId().intValue());
        return redPackClient.creatActivity(req);
    }

    @ApiOperation(value = "判断是否可以发红包", notes = "判断是否可以发红包")
    @GetMapping("checkSelfApp")
    public ResponseBase<Boolean> checkSelfApp(@RequestHeader(MER_CODE) String merCode) {
        ResponseBase<String> responseBase = weeChatMaClient.getSelfMinAppid(merCode);
        ValidateUtils.checkResult(responseBase);
        String appId = responseBase.getData();
        if(StringUtils.isEmpty(appId)){
            return generateObjectSuccess(false);
        }
        return generateObjectSuccess(true);
    }

    @ApiOperation(value = "获取红包配置", notes = "获取红包配置")
    @GetMapping("getRedpackConfig")
    public ResponseBase<RedpackConfigResDTO> getRedpackConfig() {

        return redPackClient.getRedpackConfig();
    }


}
