package cn.hydee.ydjia.merchantcustomer.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.feign.honey.PharmacistEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 药师信息
 *
 * <AUTHOR>
 * @date 2019-06-06 09:12:32
 */
@RestController
@Api(value = "药师controller", produces = "application/json", tags = {"药师接口"})
@RequestMapping("/${api.version}/pharmacist")
public class PharmacistController extends AbstractController {
	

	@GetMapping("getPharmacistEntity")
    @ApiOperation("查询当前公司，有效+注册通过的药师")
    public ResponseBase<PharmacistEntity> getPharmacistEntity(@RequestParam Long pharmacistId) {
		ResponseBase<PharmacistEntity> response = ResponseBase.success();
        return response;
    }

}
