package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hippo4j.message.api.NotifyConfigBuilder;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.domain.ShopSearchGroup;
import cn.hydee.ydjia.merchantcustomer.domain.ShopSearchGroupGoods;
import cn.hydee.ydjia.merchantcustomer.dto.ShopSearchGroupDTO;
import cn.hydee.ydjia.merchantcustomer.dto.ShopSearchGroupGoodsDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.OwnCommoditySearchDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.ShopRecommendReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.SpCommoditySearchDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.CommonLoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CommoditySearchRespDTO;
import cn.hydee.ydjia.merchantcustomer.service.CommoditySreachService;
import cn.hydee.ydjia.merchantcustomer.service.ShopSearchGroupGoodsService;
import cn.hydee.ydjia.merchantcustomer.service.ShopSearchGroupService;
import cn.hydee.ydjia.merchantcustomer.util.BeanCopyUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.internal.constraintvalidators.hv.NotBlankValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.*;

/**
 * 搜索推荐商品分组
 */
@RestController
@Validated
@RequestMapping(value = "/${api.version}/search-recommend")
@Api(value = "搜索推荐商品", tags = "搜索推荐商品")
@Slf4j
public class SearchRecommendController extends AbstractController {

    @Autowired
    private ShopSearchGroupService shopSearchGroupService;
    @Autowired
    private ShopSearchGroupGoodsService shopSearchGroupGoodsService;
    @Autowired
    private CommoditySreachService commoditySreachService;

    @ApiOperation(value = "获取推荐商品分组")
    @PostMapping("/list")
    public ResponseBase<List<ShopSearchGroupDTO>> list(@Nullable @RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                       @Nullable @RequestHeader(HEAD_MEMBER_CARD_KEY) String memberCard,
                                                       @Nullable @RequestHeader(value = HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String empFlag,
                                                       @RequestBody ShopRecommendReqDTO req){
        CommonLoginUserDTO loginUserDto = this.getLoginUserDto(LocalConst.COMMON_MERCODE, userId, memberCard, empFlag);
        List<ShopSearchGroup> topGroup = shopSearchGroupService.getTopValidGroup(3);
        List<Long> groupIds = topGroup.stream().map(ShopSearchGroup::getId).collect(Collectors.toList());
        List<ShopSearchGroupGoods> groupGoodsList = shopSearchGroupGoodsService.listByGroupId(groupIds);
        List<String> selfGoodsCodes = new ArrayList<>();
        List<String> spGoodsCodes = new ArrayList<>();
        for (ShopSearchGroup group : topGroup) {
            groupGoodsList.stream()
                    .filter(item -> Objects.equals(group.getId(), item.getGroupId()))
                    .forEach(item -> {
                        if (StringUtils.isEmpty(item.getSpCode())) {
                            selfGoodsCodes.add(item.getErpCode());
                        } else {
                            spGoodsCodes.add(item.getErpCode());
                        }
                    });
        }
        List<CommoditySearchRespDTO> allGoods = new ArrayList<>();
        if (!selfGoodsCodes.isEmpty()){
            //自营商品
            List<String> erpCodes = selfGoodsCodes.stream().distinct().collect(Collectors.toList());
            erpCodes = erpCodes.subList(0, Math.min(5000, erpCodes.size()));
            OwnCommoditySearchDTO dto = new OwnCommoditySearchDTO();
            dto.setStoreId(req.getStoreId());
            dto.setErpCodes(erpCodes);
            dto.setMerCode(LocalConst.COMMON_MERCODE);
            dto.setHasStock(true);
            dto.setJustBaseInfo(false);
            dto.setLoginUserDTO(loginUserDto);
            dto.setPageSize(erpCodes.size());
            dto.setCurrentPage(1);
            List<CommoditySearchRespDTO> selfGoods = commoditySreachService.searchByCodes(dto);
            allGoods.addAll(selfGoods);
        }

        if (!spGoodsCodes.isEmpty()) {
            //云仓商品
            SpCommoditySearchDTO spCommoditySearchDTO = new SpCommoditySearchDTO();
            spCommoditySearchDTO.setMerCode(LocalConst.COMMON_MERCODE);
            spCommoditySearchDTO.setCurrentPage(1);
            spCommoditySearchDTO.setPageSize(spGoodsCodes.size());
            spCommoditySearchDTO.setLoginUserDTO(loginUserDto);
            spCommoditySearchDTO.setErpCodes(new HashSet<>(spGoodsCodes));
            List<CommoditySearchRespDTO> spGoods = commoditySreachService.searchSpGoods(spCommoditySearchDTO);
            allGoods.addAll(spGoods);
        }
        List<ShopSearchGroupDTO> groupDTOS = BeanCopyUtil.copyListProperties(topGroup, ShopSearchGroupDTO::new);
        Map<Long, List<ShopSearchGroupGoods>> groupGoods = groupGoodsList.stream().collect(Collectors.groupingBy(ShopSearchGroupGoods::getGroupId));
        Iterator<ShopSearchGroupDTO> iterator = groupDTOS.iterator();
        while (iterator.hasNext()) {
            ShopSearchGroupDTO groupDTO = iterator.next();
            List<ShopSearchGroupGoods> goods = groupGoods.get(groupDTO.getId());
            if (CollectionUtils.isEmpty(goods)) {
                //没有商品不展示到前端
                iterator.remove();
                continue;
            }
            List<ShopSearchGroupGoodsDTO> goodsDTOS = BeanCopyUtil.copyListProperties(goods, ShopSearchGroupGoodsDTO::new);
            goodsDTOS.sort(Comparator.comparing(ShopSearchGroupGoodsDTO::getSort));
            Iterator<ShopSearchGroupGoodsDTO> ssgIterator = goodsDTOS.iterator();
            while (ssgIterator.hasNext()) {
                ShopSearchGroupGoodsDTO goodsDTO = ssgIterator.next();
                Optional<CommoditySearchRespDTO> commodity = allGoods.stream()
                        .filter(item -> Objects.equals(item.getErpCode(), goodsDTO.getErpCode()) && Objects.equals(item.getSpCode(), goodsDTO.getSpCode()))
                        .findFirst();
                if (commodity.isPresent()){
                    goodsDTO.setGoods(commodity.get());
                } else {
                    ssgIterator.remove();
                }
            }
            if (goodsDTOS.isEmpty()) {
                //没有商品不展示到前端
                iterator.remove();
                continue;
            }
            groupDTO.setItemList(goodsDTOS.subList(0, Math.min(6, goodsDTOS.size())));
        }
        groupDTOS = groupDTOS.subList(0, Math.min(3, groupDTOS.size()));
        return generateSuccess(groupDTOS);
    }

    @ApiOperation(value = "更新点击量")
    @GetMapping("/increaseClick")
    public ResponseBase<Boolean> increaseClick(@ApiParam(value = "推荐条目ID", required = true) @NotNull @RequestParam Long itemId) {
        this.shopSearchGroupGoodsService.incrClickSum(itemId);
        return generateSuccess(true);
    }

    private CommonLoginUserDTO getLoginUserDto(String merCode, String userId, String memberCard, String empFlag) {
        return CommonLoginUserDTO.builder().merCode(merCode).userId(userId).memberCard(memberCard).empFlag(STATUS_ONE.toString().equals(empFlag)).noQueryCouponPrice(false).noQueryPlusPrice(false).build();
    }
}
