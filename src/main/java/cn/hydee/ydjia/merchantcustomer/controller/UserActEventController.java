package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.domain.UserActEventDO;
import cn.hydee.ydjia.merchantcustomer.dto.UserActczzlEventDTO;
import cn.hydee.ydjia.merchantcustomer.dto.UserActczzlInfoDTO;
import cn.hydee.ydjia.merchantcustomer.service.UserActEventService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.LockUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@RestController
@Api(tags = "用户活动事件")
@RequestMapping(value = "/${api.version}/act-event")
@Slf4j
public class UserActEventController extends AbstractController {

    @Autowired
    private UserActEventService userActEventService;

    @GetMapping("/czzl-info")
    @ApiOperation(value = "成长助力信息", tags = "YXDJ-1324")
    public ResponseBase<UserActczzlInfoDTO> czzlInfo(@RequestHeader(value = LocalConst.HEAD_USER_ID_KEY, required = false) Long userId) {
        return generateSuccess(this.actczzl(userId));
    }

    @PostMapping("czzl-event")
    @ApiOperation(value = "成长助力", tags = "YXDJ-1324")
    public ResponseBase<UserActczzlEventDTO> czzlEvent(@RequestHeader(value = LocalConst.HEAD_USER_ID_KEY) Long userId) {
        UserActczzlEventDTO userActczzlDTO = new UserActczzlEventDTO();
        Integer position = LockUtils.tryLock(String.valueOf(userId), () -> userActEventService.ygczZl(userId), 2L);
        userActczzlDTO.setPosition(position);

        int total = userActEventService.calYgczZlTotal();
        userActczzlDTO.setTotal(total);
        return generateSuccess(userActczzlDTO);
    }

    private UserActczzlInfoDTO  actczzl(Long userId) {
        UserActczzlInfoDTO userActczzlDTO = new UserActczzlInfoDTO();
        if(Objects.nonNull(userId)) {
            UserActEventDO userActEvent = userActEventService.userYgczZl(userId);
            if(Objects.nonNull(userActEvent)) {
                userActczzlDTO.setPosition(userActEvent.getPosition());
            }
        }
        int total = userActEventService.calYgczZlTotal();
        userActczzlDTO.setTotal(total);
        return userActczzlDTO;
    }
}
