package cn.hydee.ydjia.merchantcustomer.controller;


import cn.hydee.starter.configuration.DisLockConfiguration;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.EmployeeResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.QueryEmpDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.EmpDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MemberVipInvitePosterResp;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MemberVipOpenRecordRespDTO;
import cn.hydee.ydjia.merchantcustomer.enums.PosterType;
import cn.hydee.ydjia.merchantcustomer.feign.EmployeeClient;
import cn.hydee.ydjia.merchantcustomer.feign.MemberVipClient;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.poster.dto.PosterReqDTO;
import cn.hydee.ydjia.merchantcustomer.service.MultipleOrderStoreService;
import cn.hydee.ydjia.merchantcustomer.service.PosterService;
import cn.hydee.ydjia.merchantcustomer.feign.mdmmiration.EmployeeClientAdapter;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.HEAD_USER_ID_KEY;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.MER_CODE;


/**
 * @version 1.0
 * @Author: lisp
 * @Description: 付费会员支付管理
 * @Date: 2022/04/11
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/memberVipOpen")
@Api(value = "付费会员支付管理", description = "付费会员支付管理控制器")
public class MemberVipOpenController extends AbstractController {

    @Autowired
    private MemberVipClient memberVipClient;
    @Autowired
    private PosterService posterService;
    @Autowired
    private EmployeeClient employeeClient;
    @Autowired
    private EmployeeClientAdapter employeeClientAdapter;
    @Autowired
    private MultipleOrderStoreService multipleOrderStoreService;


    @Deprecated
    @ApiOperation(value = "开通付费会员", notes = "开通付费会员")
//    @PostMapping("/pay")
    @DisLockConfiguration.DisLock(keyExpr = "'memberVipNewPay:' + #userId",
            lockType = DisLockConfiguration.DisLock.LockType.OPTIMISTIC, expireTime = 3, timeUnit = TimeUnit.SECONDS)
    public ResponseBase<String> pay(@RequestHeader(LocalConst.MER_CODE) String merCode,
                                    @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                    @RequestHeader(value = LocalConst.HEAD_APPID_KEY, required = false) String appid,
                                    @Valid @RequestBody MemberVipOpenAddReqDTO reqDTO) {
        log.info("memberVipNewPay. merCode: {}, userId: {}, reqDTO: {} ", merCode, userId, reqDTO);

        reqDTO.setFromEmpName(null);
        reqDTO.setCollectionStoreId(null);

        boolean findEmp = false;
        String empId = null;
        if (StringUtils.hasText(reqDTO.getFromEmpCode())) {
            QueryEmpDTO queryEmpDTO = QueryEmpDTO.builder().merCode(merCode).userId(reqDTO.getFromEmpCode())
                    .status(1).build();
            ResponseBase<PageDTO<EmployeeResDTO>> employeeRes = employeeClientAdapter.queryEmpByCondition(queryEmpDTO);
            if (employeeRes.checkSuccess() && employeeRes.getData() != null
                    && !CollectionUtils.isEmpty(employeeRes.getData().getData())) {
                EmployeeResDTO employeeData = employeeRes.getData().getData().get(0);
                reqDTO.setFromPhone(employeeData.getMobile());
                reqDTO.setFromEmpName(employeeData.getEmpName());
                empId = employeeData.getId();
                findEmp = true;
            } else {
                log.info("memberVipPay. 邀请员工编码未找到对应员工. merCode: {}, userId: {}, reqDTO: {} ", merCode, userId, reqDTO);
                reqDTO.setFromEmpCode(null);
            }
        }

        if (!findEmp && StringUtils.hasText(reqDTO.getFromPhone())) {
            ResponseBase<EmployeeResDTO> employeeRes = employeeClientAdapter.locateEmpByMobile(merCode, reqDTO.getFromPhone());
            EmployeeResDTO employeeData;
            if (employeeRes.checkSuccess() && (employeeData = employeeRes.getData()) != null
                    && employeeData.getEmpStatus() != null && employeeData.getEmpStatus() == 1) {
                reqDTO.setFromEmpCode(employeeData.getEmpCode());
                reqDTO.setFromEmpName(employeeData.getEmpName());
                empId = employeeData.getId();
            } else {
                log.info("memberVipPay. 邀请手机号未找到对应员工. merCode: {}, userId: {}, reqDTO: {} ", merCode, userId, reqDTO);
            }
        }

        if (StringUtils.hasText(empId)) {
            ResponseBase<List<EmpDTO>> employeeData = employeeClient.listEmpByIds(merCode, Lists.newArrayList(empId));
            if (employeeData.checkSuccess() && !CollectionUtils.isEmpty(employeeData.getData())
                    && employeeData.getData().get(0).getSysStore() != null) {
                reqDTO.setCollectionStoreId(employeeData.getData().get(0).getSysStore().getId());
            } else {
                log.info("员工信息不存在:{}", JSON.toJSONString(employeeData));
                reqDTO.setCollectionStoreId(multipleOrderStoreService.getOrderStoreId(merCode, appid, null));
            }
        } else {
            reqDTO.setCollectionStoreId(multipleOrderStoreService.getOrderStoreId(merCode, appid, null));
        }

        reqDTO.setMerCode(merCode);
        reqDTO.setUserId(Long.valueOf(userId));

        return memberVipClient.pay(reqDTO);
    }

    @ApiOperation(value = "查询开通记录", notes = "查询开通记录")
    @GetMapping("/detail")
    public ResponseBase<MemberVipOpenRecordRespDTO> detail(@RequestParam(value = "busId") String busId) {
        return memberVipClient.detail(busId);
    }

    @ApiOperation(value = "获取用户开通状态", notes = "获取用户开通状态")
    @PostMapping("/getMemberVipOpenStatus")
    public ResponseBase<Boolean> getMemberVipOpenStatus(@Valid @RequestBody MemberVipInfoReq reqDTO,
                                                        @RequestHeader(MER_CODE) String merCode,
                                                        @RequestHeader(HEAD_USER_ID_KEY) String userId) {
        reqDTO.setMerCode(merCode);
        reqDTO.setUserId(userId);
        return memberVipClient.getMemberVipOpenStatus(reqDTO);
    }

    @ApiOperation(value = "付费会员开通记录", notes = "付费会员开通记录")
    @PostMapping("/queryOpenRecordList")
    public ResponseBase<PageDTO<MemberVipOpenRecordRespDTO>> queryOpenRecordList(@RequestBody MemberVipOpenRecordReqDTO reqDTO,
                                                                                 @RequestHeader(MER_CODE) String merCode,
                                                                                 @RequestHeader(HEAD_USER_ID_KEY) String userId) {
        reqDTO.setMerCode(merCode);
        reqDTO.setUserId(userId);
        return memberVipClient.queryOpenRecordList(reqDTO);
    }


    @ApiOperation(value = "生成邀请海报", notes = "生成邀请海报")
    @PostMapping("createInvitePoster")
    public ResponseBase<String> createInvitePoster(@RequestHeader(MER_CODE) String merCode,
                                                   @Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                   @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                   @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                   @Valid @RequestBody MemberVipInvitePosterReqDTO reqDTO) {
        if (StringUtils.isEmpty(userId)) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }
        reqDTO.setMerCode(merCode);
        reqDTO.setUserId(Long.valueOf(userId));

        ResponseBase<MemberVipInvitePosterResp> responseBase = memberVipClient.queryMemberVipPosterInfo(reqDTO);
        if (!responseBase.checkSuccess() || responseBase.getData() == null) {
            throw WarnException.builder().code(ErrorType.MEMBER_VIP_CREATE_POSTER_ERROR.getCode())
                    .tipMessage(ErrorType.MEMBER_VIP_CREATE_POSTER_ERROR.getMsg()).build();
        }
        MemberVipInvitePosterResp posterResp = responseBase.getData();
        PosterReqDTO dto = new PosterReqDTO();
        dto.setMerCode(reqDTO.getMerCode());
        dto.setScene(reqDTO.getScene());
        dto.setQrUrl(reqDTO.getAppletUrl());
        dto.setQrType(2);
        dto.setPosterType(PosterType.MEMBER_VIP_INVITE.getCode());
        dto.setCommodityName(posterResp.getName());
        dto.setCount(Objects.isNull(posterResp.getCouponNum()) ? 0 : posterResp.getCouponNum());
        dto.setUserImgUrl(StringUtils.isEmpty(posterResp.getMemberLogoUrl()) ? LocalConst.LIVE_DEFAULT_LOGO_URL : posterResp.getMemberLogoUrl());
        dto.setUserName(StringUtils.isEmpty(posterResp.getMemberName()) ? LocalConst.LIVE_DEFAULT_NICK_NAME : posterResp.getMemberName());
        dto.setPrice(posterResp.getPrice().toString());
        dto.setLinePrice(Objects.isNull(posterResp.getScribingPrice()) ? "" : posterResp.getScribingPrice().toString());
        dto.setDiscount(posterResp.getMinDiscount());
        dto.setEstimateSaveAmount(posterResp.getEstimateSaveAmount());
        dto.setCycleType(posterResp.getCycleType());
        // 设置小程序appId
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            dto.setAppletAppId(appletAppId);
        }
        String url = null;
        try {
            url = posterService.createPoster(dto);
        } catch (Exception e) {
            log.info("memberVip createInvitePoster error", e);
        }
        return generateSuccess(url);
    }


}
