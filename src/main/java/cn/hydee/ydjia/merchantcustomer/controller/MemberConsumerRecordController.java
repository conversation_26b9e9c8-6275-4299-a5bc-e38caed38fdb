package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.req.MemberRecordsReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MemberRecordsRspDto;
import cn.hydee.ydjia.merchantcustomer.feign.BusinessOrderClient;
import cn.hydee.ydjia.merchantcustomer.service.MemberInfoService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.text.DecimalFormat;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.HEAD_USER_ID_KEY;

/**
 * 会员消费记录控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/18
 */
@RestController
@Api(tags = "会员消费记录控制器")
@RequestMapping("/${api.version}/memberConsumerRecord")
public class MemberConsumerRecordController {

    @Autowired
    private BusinessOrderClient businessOrderClient;

    @Autowired
    private MemberInfoService memberInfoService;

    @ApiOperation(value = "获取会员消费记录列表数据(中药明细隐藏只展示为“中药”及所有中药总数)")
    @PostMapping("/getMemberConsumerRecords")
    public ResponseBase<Page<MemberRecordsRspDto>> getMemberConsumerRecords(
            @RequestHeader(HEAD_USER_ID_KEY) String userId,
            @Valid @RequestBody MemberRecordsReqDTO reqDto) {
        reqDto.setMemberNo(memberInfoService.foundMemberCardByUserId(reqDto.getMerCode(), userId));
        //订单状态(空数组查所有)，5:待审方，10:待接单，20：待拣货，30：待配送，40：配送中，100：已完成，102：已取消，101：已关闭
        reqDto.setStates(new Integer[]{5, 10, 20, 30, 40, 100, 101});

        ResponseBase<Page<MemberRecordsRspDto>> results = businessOrderClient.getMemberConsumerRecords(reqDto);
        if (results.checkSuccess()) {
            handleChinaMedicine(results.getData().getRecords());
        }
        return results;
    }

    /**
     * 隐藏及合并中药明细
     *
     * @param memberRecordsList 消费记录数据列表
     */
    private void handleChinaMedicine(List<MemberRecordsRspDto> memberRecordsList) {
        if (CollectionUtils.isEmpty(memberRecordsList)) {
            return;
        }

        DecimalFormat df = new DecimalFormat("0.####");
        Predicate<MemberRecordsRspDto.CommodityInfoRspDto> predicate = commodityInfo -> commodityInfo != null
                && commodityInfo.getChinaMedicineFlag() != null
                && commodityInfo.getChinaMedicineFlag() == 1;

        memberRecordsList.forEach(o -> {
            if (StringUtils.isEmpty(o.getCommodityInfo())) {
                return;
            }

            if (o.getCommodityInfo().stream().noneMatch(predicate)) {
                o.getCommodityInfo().forEach(c -> c.setCommodityCountFmt(df.format(c.getCommodityCount())));
                return;
            }

            List<MemberRecordsRspDto.CommodityInfoRspDto> commodityInfoList = o.getCommodityInfo().stream()
                    .filter(predicate.negate()).collect(Collectors.toList());
            double chinaMedicineCount = o.getCommodityInfo().stream()
                    .filter(predicate).mapToDouble(MemberRecordsRspDto.CommodityInfoRspDto::getCommodityCount)
                    .summaryStatistics().getSum();

            MemberRecordsRspDto.CommodityInfoRspDto commodityInfo = new MemberRecordsRspDto.CommodityInfoRspDto();
            commodityInfo.setCommodityName("中药");
            commodityInfo.setCommodityCount(chinaMedicineCount);
            commodityInfo.setChinaMedicineFlag(1);
            commodityInfoList.add(commodityInfo);

            commodityInfoList.forEach(c -> c.setCommodityCountFmt(df.format(c.getCommodityCount())));

            o.setCommodityInfo(commodityInfoList);
        });
    }

}
