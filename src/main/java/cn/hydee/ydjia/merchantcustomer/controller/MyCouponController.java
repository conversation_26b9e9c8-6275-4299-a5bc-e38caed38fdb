package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.dto.CouponDetailDTO;
import cn.hydee.ydjia.merchantcustomer.dto.MerchantBaseInfoDTO;
import cn.hydee.ydjia.merchantcustomer.dto.activity.NovoRequestDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.CouponOfPersonQueryReq;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CouponBaseExtendDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CouponQueryRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.ActivityCouponBusClient;
import cn.hydee.ydjia.merchantcustomer.feign.MerchantInfoClient;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.service.ActivityCouponService;
import cn.hydee.ydjia.merchantcustomer.service.impl.CouponNewServiceImpl;
import cn.hydee.ydjia.merchantcustomer.util.TBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.util.DateUtil.getTimeDifference;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.*;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/27 11:10
 */
@RestController
@RequestMapping(value = "/${api.version}/myCoupon")
@Api(value = "我的优惠券", description = "我的优惠券")
@Slf4j
@AllArgsConstructor
public class MyCouponController extends AbstractController {
    private final ActivityCouponBusClient couponBusClient;
    private final MerchantInfoClient merchantInfoClient;
    @ApiOperation(value = "我的优惠券")
    @PostMapping("/list")
    public ResponseBase<PageDTO<CouponQueryRespDTO>> myCouponList(@RequestBody @Valid CouponOfPersonQueryReq request,
                                                                  @RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                                  @RequestHeader(HEAD_MEMBER_CARD_KEY) String memberCard,
                                                                  @RequestHeader(MER_CODE) String merCode,
                                                                  @RequestHeader(value = HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String employeeFlag) {
        PageDTO<CouponQueryRespDTO> pageDTO = new PageDTO<>();
        request.setUserId(Long.valueOf(userId));
        request.setUserCard(memberCard);
        request.setMerCode(merCode);
        ResponseBase<PageDTO<CouponDetailDTO>> responseBase = couponBusClient.myCouponListNew(request);
        checkResult(responseBase);
        PageDTO<CouponDetailDTO> page = responseBase.getData();
        if (page == null || CollectionUtils.isEmpty(page.getData())) {
            return generateObjectSuccess(pageDTO);
        }

        pageDTO.setTotalCount(page.getTotalCount());
        pageDTO.setTotalPage(page.getTotalPage());

        List<CouponQueryRespDTO> queryResList = page.getData()
                .parallelStream()
                .map(couponDetailDTO -> {
                    CouponQueryRespDTO respDTO = TBean.copy(couponDetailDTO, CouponQueryRespDTO::new, (s, t) -> {
                        if (Objects.isNull(t.getActivityId())) {
                            t.setActivityId(0);
                        }
                    });
                    respDTO.setId(String.valueOf(couponDetailDTO.getId()));
                    if (STATUS_ONE.toString().equals(employeeFlag)) {
                        respDTO.setEnable(STATUS_ONE.equals(respDTO.getEmpRule()));
                    }
                    try {
                        respDTO.setRemainDay(getTimeDifference(LocalDateTime.now(), couponDetailDTO.getValidEndTime()));
                    } catch (Exception ignore) {
                    }
                    return respDTO;
                }).collect(Collectors.toList());

        pageDTO.setData(queryResList);
        return generateObjectSuccess(pageDTO);
    }

    @ApiOperation(value = "优惠券详情")
    @PostMapping("/details")
    public ResponseBase<CouponQueryRespDTO> myCouponDetails(@RequestBody @Valid CouponOfPersonQueryReq request,
                                                            @RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                            @RequestHeader(HEAD_MEMBER_CARD_KEY) String memberCard) {
        request.setUserCard(memberCard);
        request.setUserId(Long.valueOf(userId));
        request.setPageSize(999);
        ResponseBase<PageDTO<CouponDetailDTO>> responseBase = couponBusClient.myCouponListNew(request);
        checkResult(responseBase);
        PageDTO<CouponDetailDTO> page = responseBase.getData();
        if (page == null || CollectionUtils.isEmpty(page.getData())) {
            return ResponseBase.success();
        }
        List<CouponDetailDTO> validCoupon = page.getData().stream().filter(detail -> {
            LocalDateTime now = LocalDateTime.now();
            return now.isAfter(detail.getValidBeginTime()) && now.isBefore(detail.getValidEndTime());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validCoupon)) {
            return ResponseBase.error(ErrorType.COUPON_NOT_STARTED.getCode(), ErrorType.COUPON_NOT_STARTED.getMsg());
        } else {
            CouponDetailDTO couponDetailDTO = validCoupon.get(0);
            CouponQueryRespDTO couponQueryRespDTO = TBean.copy(couponDetailDTO, CouponQueryRespDTO::new, (s, t) -> {
                if (Objects.isNull(t.getActivityId())) {
                    t.setActivityId(0);
                }
                t.setId(String.valueOf(couponDetailDTO.getId()));
                t.setMerName(s.getMerchantName());
            });

            ResponseBase<MerchantBaseInfoDTO> merchantResponse =
                    merchantInfoClient.queryMerchantByCode(request.getMerCode());

            if (merchantResponse.checkSuccess() && merchantResponse.getData() != null) {
                couponQueryRespDTO.setMerName(StringUtils.firstNonBlank(couponDetailDTO.getMerchantName(), merchantResponse.getData().getMerName()));
                couponQueryRespDTO.setMerLogo(merchantResponse.getData().getMerLogo());
            }

            return generateObjectSuccess(couponQueryRespDTO);
        }
    }


}
