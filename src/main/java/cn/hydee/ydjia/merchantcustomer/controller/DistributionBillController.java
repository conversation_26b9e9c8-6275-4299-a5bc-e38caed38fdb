package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.feign.client.DistributionBillClient;
import cn.hydee.ydjia.merchantcustomer.feign.dto.distribution.UserBillDetailReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.distribution.UserBillDetailRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.distribution.UserBillTotalRespDTO;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微商城分销账单管理器
 * <AUTHOR>
 * @date 2022-07-13 9:40
 * @since 1.0
 */
@Api(value = "微商城分销账单管理器", description = "微商城分销账单管理器")
@RestController
@RequestMapping(value = "/${api.version}/distribution-bill")
public class DistributionBillController extends AbstractController {

    @Autowired
    private DistributionBillClient distributionBillClient;

    @ApiOperation(value = "查询用户账单汇总", notes = "查询用户账单汇总")
    @PostMapping("/queryTotal")
    public ResponseBase<UserBillTotalRespDTO> queryTotal(@RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
            @RequestHeader(LocalConst.MER_CODE) String merCode) {
        return distributionBillClient.queryTotal(merCode, Long.parseLong(userId));
    }

    @ApiOperation(value = "查询用户账单列表", notes = "查询用户账单列表")
    @PostMapping("/queryList")
    public ResponseBase<PageDTO<UserBillDetailRespDTO>> queryList(@RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
            @RequestHeader(LocalConst.MER_CODE) String merCode, @Valid @RequestBody UserBillDetailReqDTO reqDTO,
            BindingResult result) {
        checkValid(result);
        return distributionBillClient
                .queryList(merCode, Long.parseLong(userId), reqDTO.getCurrentPage(), reqDTO.getPageSize());
    }
}
