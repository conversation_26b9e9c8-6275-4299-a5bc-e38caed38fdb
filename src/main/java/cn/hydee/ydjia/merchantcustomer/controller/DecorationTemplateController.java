package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.feign.dto.DecorationTemplateDetailDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.service.DecorationTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022年08月30日
 */
@Slf4j
@Api(value = "装修模板控制器", tags = {"装修模板控制器"})
@RestController
@RequestMapping(value = "/${api.version}/decorationTemplate")
public class DecorationTemplateController extends AbstractController {

    @Autowired
    private DecorationTemplateService decorationTemplateService;

    @ApiOperation(value = "预览", notes = "装修模板预览")
    @GetMapping(value = "/preview/{id}")
    public ResponseBase<DecorationTemplateDetailDTO> preview(@RequestHeader String merCode, @PathVariable String id) {
        this.checkDimensionId(id);
        return generateObjectSuccess(decorationTemplateService.preview(merCode, Long.valueOf(id)));
    }

    private void checkDimensionId(String id) {
        if (StringUtils.isEmpty(id)) {
            throw WarnException.builder().code(ErrorType.PARA_ERROR.getCode()).
                    tipMessage(ErrorType.PARA_ERROR.getMsg()).build();
        }
        try {
            Long.valueOf(id);
        } catch (NumberFormatException e) {
            log.warn("{}", id);
            throw WarnException.builder().code(ErrorType.DATA_TYPE_CONVERT_ERROR.getCode()).
                    tipMessage(ErrorType.DATA_TYPE_CONVERT_ERROR.getMsg()).build();
        }
    }
}
