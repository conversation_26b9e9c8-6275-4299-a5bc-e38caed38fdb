package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hutool.core.util.StrUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.config.ActivityType;
import cn.hydee.ydjia.merchantcustomer.dto.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.req.coupon.CouponCenterScanRequest;
import cn.hydee.ydjia.merchantcustomer.dto.req.store.CouponAvailableStoreRequest;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CouponBaseExtendDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CouponCenterListDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CouponDetailSimpleResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.ReceiveCouponResultDTO;
import cn.hydee.ydjia.merchantcustomer.enums.*;
import cn.hydee.ydjia.merchantcustomer.feign.ActivityCouponBusClient;
import cn.hydee.ydjia.merchantcustomer.feign.MarketClient;
import cn.hydee.ydjia.merchantcustomer.feign.OrderPaymentLogClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.CommodityClient;
import cn.hydee.ydjia.merchantcustomer.feign.client.StoreClient;
import cn.hydee.ydjia.merchantcustomer.feign.domain.YdjStore;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.CommonActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.CommonLoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.AssembleSearchDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.StoreListReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CouponProductDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CouponProductTypeDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.feign.enums.OrgClassEnum;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonCommodityClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonStoreClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.service.*;
import cn.hydee.ydjia.merchantcustomer.util.BeanCopyUtil;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.ServletUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hydee.starter.dto.ErrorType.OPERATOR_ERROR;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.*;
import static cn.hydee.ydjia.merchantcustomer.util.TypeConversionUtil.stringToLong;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/27 14:44
 */
@RestController
@RequestMapping(value = "/${api.version}/coupon")
@Api(value = "领券中心", description = "领券中心")
@Slf4j
@AllArgsConstructor
public class ActivityCouponController extends AbstractController {
    private final ActivityCouponBusClient couponBusClient;
    private final OrderPaymentLogClient paymentClient;
    private final CommodityClient commodityClient;
    private final StoreClient storeClient;
    private final MultipleOrderStoreService multipleOrderStoreService;
    private final CommonCommodityClientWrapperService commonCommodityClientWrapperService;
    private final CommoditySreachService commoditySreachService;
    private final RedisService redisService;
    private final ActivityCouponService activityCouponService;
    private final MarketClient marketClient;
    private CommonStoreClientWrapperService commonStoreClientWrapperService;

    @Autowired
    private StoreService storeService;

    public static final String COUPON_TEMPLATE_CODE = "TA001";
    private static final Integer NOT_STARTED = 2;

    @ApiOperation(value = "优惠券列表")
    @PostMapping("/list")
    public ResponseBase<PageDTO<ActivityCouponDTO>> couponList(@RequestBody @Valid CouponQueryReq request,
                                                               @RequestHeader(HEAD_USER_ID_KEY) @Nullable String userId,
                                                               @RequestHeader(value = HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String employeeFlag) {
        request.setActivityTemplateCode(COUPON_TEMPLATE_CODE);
        request.setUserId(StringUtils.isEmpty(userId) ? null : stringToLong(userId));
        request.setIsShow(1);
        request.setEmpFlag(STATUS_ONE.toString().equals(employeeFlag));
        if (request.getQueryType() == null) {
            request.setQueryType(2);
        }
        PageDTO<ActivityCouponDTO> pageDTO = new PageDTO<>(request.getCurrentPage(), request.getPageSize());
        List<ActivityCouponDTO> activityCouponList;
        if (ActivityType.CASH.equals(request.getActivityType())) {
            ResponseBase<PageDTO<ActivityCouponDTO>> responseBase = couponBusClient.listActivityCoupon(request);
            checkResult(responseBase);
            pageDTO.setTotalPage(responseBase.getData().getTotalPage());
            pageDTO.setTotalCount(responseBase.getData().getTotalCount());
            activityCouponList = responseBase.getData().getData();
        } else {
            CouponCenterQueryRequest queryRequest = BeanCopyUtil.copy(request, CouponCenterQueryRequest::new);
            ResponseBase<PageDTO<ActivityCouponDTO>> responseBase = couponBusClient.queryCouponCenterListNew(queryRequest);
            checkResult(responseBase);
            activityCouponList = responseBase.getData().getData();
            pageDTO.setTotalPage(responseBase.getData().getTotalPage());
            pageDTO.setTotalCount(responseBase.getData().getTotalCount());
        }
        if (CollectionUtils.isEmpty(activityCouponList)) {
            return generateSuccess(pageDTO);
        }
        pageDTO.setData(activityCouponList);

        activityCouponList.forEach(couponDTO -> {
            if (couponDTO.getActivityState().equals(NOT_STARTED)) {
                long begin = couponDTO.getBeginTime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
                long current = System.currentTimeMillis();
                couponDTO.setDifference((begin - current) / 1000);
            }
        });
        return generateSuccess(pageDTO);
    }

    @ApiOperation(value = "领取优惠券")
    @PostMapping("/receive")
    public ResponseBase<ReceiveCouponResultDTO> get(@RequestBody @Valid ActivityReceiveCouponReqDto request,
                            @RequestHeader(HEAD_USER_ID_KEY) String userId,
                            @RequestHeader(value = HEAD_APPID_KEY, required = false) String appid,
                            @RequestHeader(HEAD_MEMBER_CARD_KEY) String memberCard,
                            @RequestHeader(value = HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String employeeFlag) {
        log.info("领取优惠券请求参数：{}", JSONObject.toJSONString(request));
        request.setUserId(Long.valueOf(userId));
        request.setUserCard(memberCard);
        request.setPlatformEnum(ServletUtils.getPlatformEnum().orElse(null));
        request.setEmpFlag(STATUS_ONE.toString().equals(employeeFlag));
        request.setStoreId(multipleOrderStoreService.getOrderStoreId(request.getMerCode(), appid, request.getStoreId()));

        ReceiveCouponResultDTO receiveCouponResultDTO;
        if (request.isNovoOrder()){
            // 特殊优惠券领取逻辑，目前只有诺和
             receiveCouponResultDTO = activityCouponService.specialCouponReceive(request);
        }else {
             receiveCouponResultDTO = activityCouponService.normalCouponReceive(request);
        }
        if (receiveCouponResultDTO == null){
            ResponseBase<ReceiveCouponResultDTO> error = generateError(OPERATOR_ERROR);
            error.setMsg("当前活动您暂不可参与");
            return error;
        }
        return generateSuccess(receiveCouponResultDTO);
    }
    @ApiOperation(value = "支付现金优惠券")
    @PostMapping("/pay")
    public ResponseBase<Map<String, String>> get(@RequestBody PayDTO request,
                                                 @RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                 @RequestHeader(value = HEAD_OPEN_ID_KEY, required = false) String openId,
                                                 @RequestHeader(name = "x-forwarded-for") String remoteIp,
                                                 @RequestHeader(name = "User-Agent") String userAgent) {
        request.setUserId(userId);
        request.setOpenId(openId);
        return paymentClient.payCoupon(remoteIp, userAgent, request);
    }

    @ApiOperation(value = "查询优惠券订单详情")
    @PostMapping("/queryCouponOrderDetail")
    public ResponseBase<CouponOrderDTO> queryCouponOrderDetail(@RequestHeader(value = "merCode") String merCode,
                                                               @RequestParam(value = "orderNo") String orderNo) {
        return couponBusClient.buyActivityCashCouponDetail(merCode, orderNo);
    }

    @ApiOperation(value = "消费时可选用的优惠券列表")
    @PostMapping("/consumeCouponList")
    public ResponseBase<List<CouponDetailSimpleResDTO>> consumeCouponList(
            @RequestBody @Valid ConsumerCouponListReqDTO request,
            @RequestHeader(HEAD_USER_ID_KEY) String userId,
            @RequestHeader(HEAD_MEMBER_CARD_KEY) String memberCard,
            @RequestHeader(value = HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String employeeFlag) {
        request.setUserId(Long.valueOf(userId));
        request.setMemberCard(memberCard);
        request.setEmpFlag(STATUS_ONE.toString().equals(employeeFlag));
        return couponBusClient.consumeCouponList(request);
    }

    @ApiOperation(value = "商品对应可领取的优惠券列表", notes = "如果paymentList不为空调用此接口,将不做分页处理")
    @PostMapping("/listActivityDetailProduct")
    public ResponseBase<List<ActivityGetCouponCenterDTO>> listActivityDetailProduct(
            @Valid @RequestBody ActivityGetCouponProductReqDTO activityGetCouponProductReqDto,
            @RequestHeader(value = HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String employeeFlag) {
        activityGetCouponProductReqDto.setActivityTemplateCode(COUPON_TEMPLATE_CODE);
        activityGetCouponProductReqDto.setEmpFlag(STATUS_ONE.toString().equals(employeeFlag));
        return couponBusClient.listActivityDetailProduct(activityGetCouponProductReqDto);
    }

    @ApiOperation(value = "购买优惠券大礼包", notes = "购买优惠券大礼包")
    @PostMapping("/buyCouponGift")
    public ResponseBase<String> buyCouponGift(@RequestBody ActivityBuyCouponGiftRequest request,
                                              @RequestHeader(USER_ID) String userId,
                                              @RequestHeader(value = HEAD_APPID_KEY, required = false) String appid,
                                              @RequestHeader(MER_CODE) String merCode) {
        request.setMerCode(merCode);
        request.setUserId(Long.valueOf(userId));
        request.setPlatformEnum(ServletUtils.getPlatformEnum().orElse(null));
        request.setStoreId(multipleOrderStoreService.getOrderStoreId(request.getMerCode(), appid, request.getStoreId()));
        ResponseBase<CouponOrderDTO> responseBase = couponBusClient.buy(request);
        checkResult(responseBase);
        return generateSuccess(responseBase.getData().getOrderNo());
    }

    @PostMapping("/queryOrderRecord")
    @ApiOperation(value = "优惠券订单记录")
    public ResponseBase<PageDTO<CouponOrderDTO>> queryOrderRecord(@RequestBody CouponOrderQueryRequest request,
                                                                  @RequestHeader(USER_ID) String userId,
                                                                  @RequestHeader(MER_CODE) String merCode) {
        request.setUserId(Long.valueOf(userId));
        request.setMerCode(merCode);
        return couponBusClient.queryOrderRecord(request);
    }

    @PostMapping("/couponGiftDetail")
    @ApiOperation(value = "优惠券礼包详情")
    public ResponseBase<ActivityCouponList> detail(@RequestBody BaseRequest request) {
        return couponBusClient.detail(request);
    }

    @PostMapping("/searchAvailableCommodityList")
    @ApiOperation(value = "优惠券可用商品列表")
    public ResponseBase<CouponBaseInfoDTO> searchAvailableCommodityList(@RequestHeader(MER_CODE) String merCode,
                                                                        @RequestHeader(value = LocalConst.HEAD_USER_ID_KEY, required = false) String userId,
                                                                        @RequestHeader(value = LocalConst.HEAD_MEMBER_CARD_KEY, required = false) String memberCard,
                                                                        @RequestHeader(value = LocalConst.HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String empFlag,
                                                                        @RequestBody CouponAvailableCommodityRequest request) {
        CouponBaseExtendDTO couponBaseExtendDTO = null;
        String searchCouponKey = String.format("customer:coupon:searchCouponKey:%s-%s", merCode, request.getCouponId());
        if (commoditySreachService.useCouponCache()) {
            // 查询优惠券信息
            String searchCouponVal = null;
            try {
                searchCouponVal = redisService.get(searchCouponKey);
                if (StringUtils.isNotBlank(searchCouponVal)) {
                    couponBaseExtendDTO = JSONObject.parseObject(searchCouponVal, CouponBaseExtendDTO.class);
                    log.info("使用缓存处理优惠券扩展信息成功，key:{}", searchCouponKey);
                }
            } catch (Exception e) {
                log.warn("使用缓存处理优惠券扩展信息失败，val:{}", searchCouponVal, e);
            }
        }

        if (null == couponBaseExtendDTO) {
            ResponseBase<CouponBaseExtendDTO> responseBase = couponBusClient.getCouponExtendDetail(request.getCouponId());
            checkResult(responseBase);
            if (responseBase.getData() == null) {
                log.warn("getCouponExtendDetail failed merCode={} {}", merCode, request);
                return ResponseBase.error(ErrorType.COUPONS_NOT_FOUND.getCode(), ErrorType.COUPONS_NOT_FOUND.getMsg());
            }
            couponBaseExtendDTO = responseBase.getData();
            redisService.setValueExpire(searchCouponKey, JSON.toJSONString(couponBaseExtendDTO), 10, TimeUnit.MINUTES);
        }

        CouponBaseInfoDTO dto = new CouponBaseInfoDTO();
        dto.setCType(couponBaseExtendDTO.getCType());
        dto.setDenomination(couponBaseExtendDTO.getDenomination());
        dto.setUseRule(couponBaseExtendDTO.getUseRule());
        dto.setSceneRule(couponBaseExtendDTO.getSceneRule());
        dto.setHasCommodity(0);

        // 云仓商品现金卷场景标记
        boolean cloudMoneyCouponFlag = CouponType.CASH_NEW.getCode().equals(couponBaseExtendDTO.getCType()) && CouponSceneRule.ONLINE_CLOUD.getCode().equals(couponBaseExtendDTO.getSceneRule());
        if (cloudMoneyCouponFlag) {
            CommoditySearchDTO commoditySearchDTO = new CommoditySearchDTO();
            BeanUtils.copyProperties(request, commoditySearchDTO);
            commoditySearchDTO.setMerCode(merCode);
            ProductRuleEnum productRuleEnum = ProductRuleEnum.getEnumByCode(couponBaseExtendDTO.getProductRule());
            if (productRuleEnum != null) {
                switch (productRuleEnum) {
                    case PARTLY_AVAILABLE:
                        if (!CollectionUtils.isEmpty(couponBaseExtendDTO.getListCouponProductEntity())) {
                            commoditySearchDTO.setErpCodes(couponBaseExtendDTO.getListCouponProductEntity().parallelStream().map(CouponProductDTO::getProCode)
                                    .collect(Collectors.toList()));
                        }
                        break;

                    case PARTLY_NOT_AVAILABLE:
                        if (!CollectionUtils.isEmpty(couponBaseExtendDTO.getListCouponProductEntity())) {
                            commoditySearchDTO.setUnErpCodes(couponBaseExtendDTO.getListCouponProductEntity().parallelStream().map(CouponProductDTO::getProCode)
                                    .collect(Collectors.toList()));
                        }
                        break;
                    default:
                }
            }

            // 1、转化云仓商品；2、组装查询优惠价参数
            List<CommoditySearchRespDTO> spCommoditySearchRespDTOS = commoditySreachService.getSpCommoditySearchRespDTOS(commoditySearchDTO);
            List<CommonActivitySpecDTO> spCommonActivitySpecDTOS = commoditySreachService.assembleCommonSpDiscountPriceQueryParam(spCommoditySearchRespDTOS);

            ResponseBase<PageDTO<CommoditySearchRespDTO>> re = ResponseBase.success();
            PageDTO<CommoditySearchRespDTO> pageDTO = new PageDTO<>();
            pageDTO.setTotalCount(0);
            pageDTO.setData(Lists.newArrayList());
            re.setData(pageDTO);

            commoditySreachService.handleOnlySpSpecs(re, spCommoditySearchRespDTOS, commoditySearchDTO);
            // 处理优惠价信息
            commoditySearchDTO.setLoginUserDTO(CommonLoginUserDTO.builder().merCode(merCode).userId(userId).memberCard(memberCard).empFlag(STATUS_ONE.toString().equals(empFlag)).noQueryCouponPrice(false).noQueryPlusPrice(false).build());
            commoditySearchDTO.setUserId(userId);
            commoditySreachService.handleCommonSpSpecsPriceRes(commoditySreachService.handleCommonSpSpecsPriceRequest(commoditySearchDTO.getLoginUserDTO(), spCommonActivitySpecDTOS), pageDTO.getData());

            dto.setCommoditySearchRespDTOList(re.getData());
            return generateSuccess(dto);
        }
        // 下面都是自营商品业务
        if (ShopRuleEnum.PART.getCode().equals(couponBaseExtendDTO.getShopRule()) && StringUtils.isNotBlank(request.getStoreId())) {
            boolean match = couponBaseExtendDTO.getListCouponStoreEntity().stream()
                    .anyMatch(couponStoreDTO -> StringUtils.equals(request.getStoreId(), couponStoreDTO.getStoreId()));

            if (!match) {
                PageDTO<CommoditySearchRespDTO> pageDTO = new PageDTO<>();
                pageDTO.setData(Lists.newLinkedList());
                dto.setCommoditySearchRespDTOList(pageDTO);
                return generateSuccess(dto);
            }
        }

        ProductRuleEnum combineProductRuleEnum = ProductRuleEnum.getEnumByCode(couponBaseExtendDTO.getCombineProductRule());
        ProductRuleEnum productRuleEnum = ProductRuleEnum.getEnumByCode(couponBaseExtendDTO.getProductRule());

        // 根据 storeId 查询 stCode
        ResponseBase<String> stCodeResponseBase = storeClient.queryCodeById(request.getStoreId());
        checkResult(stCodeResponseBase);

        // 获取商品信息的入参
        CommoditySearchDTO searchDTO = new CommoditySearchDTO();
        searchDTO.setMerCode(merCode);
        searchDTO.setStoreId(request.getStoreId());
        searchDTO.setKeyword(request.getKeyword());
        searchDTO.setSortField(request.getSortField());
        searchDTO.setAsc(request.getAsc());
        searchDTO.setBrandIds(request.getBrandIds());
        searchDTO.setFPrice(request.getFPrice());
        searchDTO.setTPrice(request.getTPrice());
        searchDTO.setPageSize(request.getPageSize());
        searchDTO.setCurrentPage(request.getCurrentPage());
        searchDTO.setCouponId(couponBaseExtendDTO.getId());
        searchDTO.setCouponType(couponBaseExtendDTO.getCType());
        searchDTO.setStCode(stCodeResponseBase.getData());
        searchDTO.setSpCurrentPage(request.getSpCurrentPage());

        if (productRuleEnum != null) {
            switch (productRuleEnum) {
                case PARTLY_AVAILABLE:
                    if (!CollectionUtils.isEmpty(couponBaseExtendDTO.getListCouponProductEntity())) {
                        searchDTO.addErpCodes(couponBaseExtendDTO.getListCouponProductEntity().parallelStream().map(CouponProductDTO::getProCode)
                                .collect(Collectors.toList()));
                    }
                    if (!CollectionUtils.isEmpty(couponBaseExtendDTO.getListCouponProductTypeEntity())) {
                        searchDTO.setThreeCategoryIds(couponBaseExtendDTO.getListCouponProductTypeEntity().parallelStream().map(CouponProductTypeDTO::getTypeCode)
                                .collect(Collectors.toList()));
                        searchDTO.setHasShould(true);
                    }
                    break;

                case PARTLY_NOT_AVAILABLE:
                    if (!CollectionUtils.isEmpty(couponBaseExtendDTO.getListCouponProductEntity())) {
                        searchDTO.addUnErpCodes(couponBaseExtendDTO.getListCouponProductEntity().parallelStream().map(CouponProductDTO::getProCode)
                                .collect(Collectors.toList()));
                    }
                    if (!CollectionUtils.isEmpty(couponBaseExtendDTO.getListCouponProductTypeEntity())) {
                        searchDTO.setUnThreeCategoryIds(couponBaseExtendDTO.getListCouponProductTypeEntity().parallelStream().map(CouponProductTypeDTO::getTypeCode)
                                .collect(Collectors.toList()));
                    }
                    break;
                default:
            }
        }
        // 黑名单商品过滤
        if(Boolean.TRUE.equals(couponBaseExtendDTO.getHasProductBlacklist())){
            searchDTO.addUnErpCodes(
                    couponBaseExtendDTO.getBlackCouponProductList().parallelStream().map(CouponProductDTO::getProCode)
                    .collect(Collectors.toList())
            );
            searchDTO.addProductBlacklist(
                    couponBaseExtendDTO.getBlackCouponProductList().parallelStream().map(couponProductDTO -> {
                                if(StringUtils.isEmpty(couponProductDTO.getSpCode())){
                                    return couponProductDTO.getProCode();
                                }else{
                                    return couponProductDTO.getProCode()+"_"+couponProductDTO.getSpCode();
                                }
                            }).collect(Collectors.toList())
            );
        }

        // 组合商品编码
        List<String> combineAvailableErpCodes = null;
        if (!CollectionUtils.isEmpty(couponBaseExtendDTO.getListCouponCombineProductEntity())) {
            combineAvailableErpCodes = couponBaseExtendDTO.getListCouponCombineProductEntity().stream()
                    .map(CouponProductDTO::getProCode).collect(Collectors.toList());
        }

        if (combineProductRuleEnum != null) {
            switch (combineProductRuleEnum) {
                case ALL:
                    if (ProductRuleEnum.PARTLY_AVAILABLE.equals(productRuleEnum)) {
                        ResponseBase<PageDTO<AssembleSearchRespDTO>> assemblePage = getAssembleListLimit800(merCode);
                        Optional.ofNullable(assemblePage.getData())
                                .flatMap(page -> Optional.ofNullable(page.getData()))
                                .ifPresent(assembleCommodityList ->
                                        searchDTO.addErpCodes(assembleCommodityList.parallelStream().map(AssembleSearchRespDTO::getErpCode).collect(Collectors.toList())));
                    }
                    break;

                case PARTLY_AVAILABLE:
                    if (ProductRuleEnum.PARTLY_NOT_AVAILABLE.equals(productRuleEnum) || ProductRuleEnum.ALL.equals(productRuleEnum)) {
                        List<String> availableErpCodes = combineAvailableErpCodes;
                        ResponseBase<PageDTO<AssembleSearchRespDTO>> assemblePage = getAssembleListLimit800(merCode);
                        Optional.ofNullable(assemblePage.getData())
                                .flatMap(page -> Optional.ofNullable(page.getData()))
                                .ifPresent(assembleCommodityList ->
                                        searchDTO.addUnErpCodes(assembleCommodityList.parallelStream()
                                                .map(AssembleSearchRespDTO::getErpCode)
                                                .filter(s -> {
                                                    assert availableErpCodes != null;
                                                    return !availableErpCodes.contains(s);
                                                }).collect(Collectors.toList())));

                    } else if (ProductRuleEnum.PARTLY_AVAILABLE.equals(productRuleEnum)) {
                        searchDTO.addErpCodes(couponBaseExtendDTO.getListCouponCombineProductEntity().parallelStream()
                                .map(CouponProductDTO::getProCode).collect(Collectors.toList()));
                    } else if (ProductRuleEnum.ALL_NOT_AVAILABLE.equals(productRuleEnum) && !CollectionUtils.isEmpty(combineAvailableErpCodes)) {
                        searchDTO.addErpCodes(combineAvailableErpCodes);
                    }
                    break;

                case ALL_NOT_AVAILABLE:
                    if (ProductRuleEnum.PARTLY_NOT_AVAILABLE.equals(productRuleEnum)) {
                        ResponseBase<PageDTO<AssembleSearchRespDTO>> assemblePage = getAssembleListLimit800(merCode);
                        Optional.ofNullable(assemblePage.getData())
                                .flatMap(page -> Optional.ofNullable(page.getData()))
                                .ifPresent(assembleCommodityList ->
                                        searchDTO.addUnErpCodes(assembleCommodityList.parallelStream().map(AssembleSearchRespDTO::getErpCode).collect(Collectors.toList())));
                    }
                    searchDTO.setCommodityTypeList(Lists.newArrayList(CommodityTypeEnum.NORMAL.getCode()));
                    break;
                default:
            }
        }

        return generateSuccess(commoditySreachService.searchCommodityAndCheck(dto, searchDTO, combineProductRuleEnum));
    }

    private ResponseBase<PageDTO<AssembleSearchRespDTO>> getAssembleListLimit800(String merCode) {
//        String searchAssembleKey = String.format("customer:coupon:searchAssembleKey:%s", merCode);
//        List<AssembleSearchRespDTO> result = null;
//        if (commoditySreachService.useCouponCache()) {
//            String searchAssembleVal = null;
//            try {
//                searchAssembleVal = redisService.get(searchAssembleKey);
//                if (StringUtils.isNotBlank(searchAssembleVal)) {
//                    result = JSONObject.parseArray(searchAssembleVal, AssembleSearchRespDTO.class);
//                    log.info("使用缓存处理Assemble信息成功，key:{}", searchAssembleKey);
//                }
//            } catch (Exception e) {
//                log.warn("使用缓存处理Assemble信息失败，val:{}", searchAssembleVal, e);
//            }
//        }
//
//        if (null == result) {
            AssembleSearchDTO assembleSearchDTO = new AssembleSearchDTO();
            assembleSearchDTO.setMerCode(merCode);
            assembleSearchDTO.setCommodityType("2");
            assembleSearchDTO.setPageSize(800);
        //            if (assemblePage.checkSuccess() && null != assemblePage.getData() && !CollectionUtils.isEmpty(assemblePage.getData().getData())) {
//                redisService.setValueExpire(searchAssembleKey, JSON.toJSONString(assemblePage.getData().getData()), 10, TimeUnit.MINUTES);
//            }
            return commodityClient.getAssemblePage(assembleSearchDTO);
//        }

//        PageDTO<AssembleSearchRespDTO> pageDTO = new PageDTO<>();
//        pageDTO.setData(result);
//        pageDTO.setCurrentPage(1);
//        pageDTO.setPageSize(800);
//        ResponseBase<PageDTO<AssembleSearchRespDTO>> responseBase = ResponseBase.success();
//        responseBase.setData(pageDTO);
//        return responseBase;
    }

    @PostMapping("/searchAvailableStoreList")
    @ApiOperation(value = "优惠券可用门店列表")
    public ResponseBase<PageDTO<StoreListResDTO>> searchAvailableStoreList(@RequestHeader(MER_CODE) String merCode,
                                                                           @RequestBody CouponAvailableStoreRequest request) {

        ResponseBase<List<String>> responseBase = couponBusClient.getCouponDetail(merCode, request.getCouponId());
        checkResult(responseBase);
        List<String> couponStoreBlacklist = null;
        /*
            responseBase.getData()为空的情况:
            1.真全部门店并且没有门店黑名单,查询门店黑名单为空,不影响后续处理
            2.真全部门店并且有门店黑名单,查询门店黑名单不为空,要在结果集排掉黑名单门店
         */
        if (CollectionUtils.isEmpty(responseBase.getData())) {
            ResponseBase<List<String>> storeBlacklistResp = couponBusClient.getCouponBlacklist(request.getCouponId());
            checkResult(responseBase);
            couponStoreBlacklist = storeBlacklistResp.getData();
        }
        StoreListReqDTO dto = new StoreListReqDTO();
        // 优惠券对应门店集合
        dto.setMerCode(merCode);
        dto.setList(responseBase.getData());
        // 门店名称
        dto.setStoreName(request.getStoreName());
        if (!StringUtils.isEmpty(request.getLongitude()) && !StringUtils.isEmpty(request.getLatitude())) {
            dto.setLongitude(request.getLongitude());
            dto.setLatitude(request.getLatitude());
        } else {
            dto.setLongitude(null);
            dto.setLatitude(null);
        }

        if (request.getOnlineStatus() != null && request.getOnlineStatus().equals(STATUS_ONE)) {
            //线上适用的优惠券只能查线上门店
            dto.setOnlineStatus(request.getOnlineStatus());
        } else {
            //勾选下线，线上+线下门店可适用
            dto.setOnlineStatus(null);
        }
        dto.setPageSize(request.getPageSize());
        dto.setCurrentPage(request.getCurrentPage());
        // 优惠券可用门店 排除仓库
        dto.setStClassList(Arrays.asList(OrgClassEnum.COMPANY.getCode(), OrgClassEnum.CONSTITUENT_COMPANY.getCode(), OrgClassEnum.DATA_CENTER.getCode(),
                OrgClassEnum.DEPARTMENT.getCode(), OrgClassEnum.FRANCHISE_STORE.getCode(), OrgClassEnum.DIRECT_STORE.getCode()));
        YdjStore centerStore = storeService.getCenterStore(merCode);
        if (centerStore != null && StrUtil.isNotEmpty(dto.getStoreName())) {//搜索
            //转换旗舰店别名
            if (StrUtil.isNotEmpty(centerStore.getStName()) && centerStore.getStName().contains(dto.getStoreName())) {
                //查询旗舰店和其他门店
                dto.setOrStoreCode(centerStore.getStCode());
            }
        }
        PageDTO<StoreListResDTO> pageDTO = commonStoreClientWrapperService.queryStoreListV3(dto);
        if (pageDTO == null || pageDTO.getData() == null) {
            return generateSuccess(new PageDTO<>());
        }
        // 根据门店距离排序
        pageDTO.getData().sort(Comparator.comparing(StoreListResDTO::getDistance));
        // 临时处理:先把门店黑名单排掉,后期优化补足一页数据
        if (!CollectionUtils.isEmpty(couponStoreBlacklist)) {
            List<String> finalCouponStoreBlacklist = couponStoreBlacklist;
            List<StoreListResDTO> collect = pageDTO.getData().stream().filter(store -> !finalCouponStoreBlacklist.contains(store.getId())).collect(Collectors.toList());
            pageDTO.setData(collect);
            return generateSuccess(pageDTO);
        }
        //处理旗舰店标识
        for(StoreListResDTO store : pageDTO.getData()){
            store.setCenter(centerStore != null && Objects.equals(centerStore.getStCode(), store.getStCode()));
        }
        return generateSuccess(pageDTO);
    }

    @ApiOperation(value = "查询该门店是否是优惠券可用门店")
    @GetMapping("/checkStore")
    public ResponseBase<Boolean> checkStoreSupport(@RequestParam(value = "couponId", required = false) String couponId,
                                                   @RequestParam(value = "storeId", required = false) String storeId,
                                                   @RequestHeader("merCode") String merCode) {
        if (StringUtils.isAnyBlank(couponId, storeId) || !NumberUtils.isCreatable(couponId)) {
            return generateSuccess(false);
        }

        return couponBusClient.checkStoreSupport(Integer.valueOf(couponId), storeId, 1, merCode);
    }

    @ApiOperation(value = "查询领券中心初始数据")
    @GetMapping("/getCouponCenterInitList")
    public ResponseBase<CouponCenterListDTO> getCouponCenterInitList(@RequestParam("merCode") String merCode,
                                                                     @RequestHeader(value = HEAD_USER_ID_KEY, required = false) @Nullable String userId,
                                                                     @RequestHeader(value = HEAD_EMPLOYEE_FLAG, required = false, defaultValue = "0") String employeeFlag) {
        return couponBusClient.getCouponCenterInitList(merCode, StringUtils.isEmpty(userId) ? null : stringToLong(userId), STATUS_ONE.toString().equals(employeeFlag));
    }

    @ApiOperation("领券中心推广--扫码领券")
    @PostMapping("/queryCouponCenterSingle")
    public ResponseBase<ActivityCouponDTO> queryCouponCenterSingle(@Valid @RequestBody CouponCenterScanRequest request,
                                                                   @RequestHeader(value = HEAD_USER_ID_KEY, required = false) @Nullable String userId) {
        request.setUserId(userId == null ? null : Long.valueOf(userId));
        return couponBusClient.queryCouponCenterSingle(request);
    }

    @ApiOperation(value = "优惠券退款申请")
    @PostMapping("/applyRefund")
    public ResponseBase<Boolean> applyRefund(@RequestBody CouponRefundQueryRequest request,
                                             @RequestHeader(USER_ID) String userId,
                                             @RequestHeader(MER_CODE) String merCode) {
        request.setUserId(Long.valueOf(userId));
        request.setMerCode(merCode);
        return couponBusClient.applyRefund(request);
    }

    @ApiOperation(value = "优惠券退款详情")
    @PostMapping("/refundDetail")
    public ResponseBase<CouponRefundDTO> refundDetail(@RequestBody CouponRefundDetailReqDTO request,
                                                      @RequestHeader(USER_ID) String userId,
                                                      @RequestHeader(MER_CODE) String merCode) {
        request.setUserId(Long.valueOf(userId));
        request.setMerCode(merCode);
        return couponBusClient.refundDetail(request);
    }

}
