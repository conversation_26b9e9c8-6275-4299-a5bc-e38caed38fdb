package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.feign.ShortUrlClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/11/09 20:14
 */
@Slf4j
@RestController
@RequestMapping("/sl")
@RequiredArgsConstructor
public class ShortUrlController {
    private final ShortUrlClient shortUrlClient;

    @GetMapping("/{shortUrl}")
    public void redirect(@PathVariable("shortUrl") String shortUrl, HttpServletResponse response) {
        try {
            ResponseBase<String> responseBase = shortUrlClient.queryOriginUrl(shortUrl);
            checkResult(responseBase);
            response.sendRedirect(responseBase.getData());
        } catch (Exception ex) {
            log.warn("[shortUrl_redirect] failed shortUrl={}", shortUrl, ex);
        }
    }
}
