package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.configuration.DisLockConfiguration;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.config.SpringContextHolder;
import cn.hydee.ydjia.merchantcustomer.dto.ActivityDetailExtendDTO;
import cn.hydee.ydjia.merchantcustomer.dto.CommonReqIntegerDTO;
import cn.hydee.ydjia.merchantcustomer.dto.LiveRecommendQueryReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.enums.LiveLotteryStatusEnum;
import cn.hydee.ydjia.merchantcustomer.enums.LivePosterStatusEnum;
import cn.hydee.ydjia.merchantcustomer.feign.ActivityManagerClient;
import cn.hydee.ydjia.merchantcustomer.feign.RedPackClient;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CommoditySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.LiveCouponRespDto;
import cn.hydee.ydjia.merchantcustomer.dto.resp.LiveInfoRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.LiveSimpleRespDTO;
import cn.hydee.ydjia.merchantcustomer.enums.LiveViewerType;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MemberInfoDTO;
import cn.hydee.ydjia.merchantcustomer.service.ActivityLiveChatService;
import cn.hydee.ydjia.merchantcustomer.service.LiveService;
import cn.hydee.ydjia.merchantcustomer.service.MemberLoginHelper;
import cn.hydee.ydjia.merchantcustomer.service.RedisService;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.util.ModelConvertUtils;
import cn.hydee.ydjia.merchantcustomer.util.RedisKeyUtil;
import cn.hydee.ydjia.merchantcustomer.util.ValidateUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType.ACTIVITY_IS_NULL;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.*;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * <AUTHOR>
 * @date 2020/5/18 16:10
 */
@Slf4j
@RestController
@RequestMapping(value = "/${api.version}/live-chat")
@Api(value = "直播间接口", tags = "直播间接口")
public class LiveChatController extends AbstractController {

    @Autowired
    private ActivityLiveChatService liveChatService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ActivityManagerClient activityManagerClient;

    @Autowired
    private MemberLoginHelper memberLoginHelper;

    @Autowired
    private LiveService liveService;

    @Autowired
    private RedPackClient redPackClient;

    private final static Integer ACTIVITY_FROM_ACTIVITY = 3;


    @ApiOperation(value = "进入直播间，获取直播间基础配置信息", notes = "获取直播间基础配置信息，比如商品，" +
            "直播地址，优惠券等")
    @PostMapping("/info")
    public ResponseBase<LiveInfoRespDTO> getLiveInfo(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                     @Nullable @RequestHeader(LocalConst.HEAD_OPEN_ID_KEY) String openId,
                                                     @RequestHeader(MER_CODE) String merCode,
                                                     @Valid @RequestBody LiveReqDTO dto, BindingResult result) {
        checkValid(result);
        dto.setUserId(userId);
        dto.setOpenId(openId);
        dto.setMerCode(merCode);
        LiveInfoRespDTO liveInfo = liveChatService.getLiveInfo(dto);
        return generateObjectSuccess(liveInfo);
    }

    @GetMapping("/crossDomain/login")
    @ApiOperation(value = "会员h5跨域登录", notes = "会员h5跨域登录")
    public ResponseBase login(@RequestParam String token,
                              HttpServletResponse httpServletResponse) {
        String tokenStr = redisService.get(RedisKeyUtil.getCrossAdminLoginKey(token));
        if (StringUtils.isEmpty(tokenStr)) {
            throw WarnException.builder().code(ErrorType.WEB_SERVICE_AUTH_ERROR.getCode()).
                    tipMessage(ErrorType.WEB_SERVICE_AUTH_ERROR.getMsg()).build();
        }
        MemberInfoDTO memberInfoDTO = JSON.parseObject(tokenStr, MemberInfoDTO.class);
        memberLoginHelper.addCookieCrossDomain(memberInfoDTO, httpServletResponse);

        return ResponseBase.success();
    }

    @ApiOperation(value = "厂家分享直播获取直播间配置信息", notes = "厂家分享直播获取直播间配置信息")
    @PostMapping("/factoryInfo")
    public ResponseBase<LiveInfoRespDTO> getFactoryLiveInfo(@Valid @RequestBody LiveReqDTO dto, BindingResult result) {
        checkValid(result);
        LiveInfoRespDTO liveInfo = liveChatService.getFactoryLiveInfo(dto);
        return generateObjectSuccess(liveInfo);
    }

    @ApiOperation(value = "C端用户领取优惠券", notes = "C端用户领取优惠券")
    @PostMapping("/getCoupon")
    public ResponseBase<Boolean> getCoupon(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId, @Valid @RequestBody LiveCouponReqDTO req, BindingResult result) {
        checkValid(result);
        if (StringUtils.isEmpty(userId)) {
            throw WarnException.builder().code(ErrorType.AUTH_ERROR.getCode()).
                    tipMessage(ErrorType.AUTH_ERROR.getMsg()).build();
        }
        req.setUserId(userId);
        return liveChatService.getCoupon(req);
    }

    @ApiOperation(value = "C端用户领取抽奖活动", notes = "C端用户领取抽奖活动")
    @GetMapping("/getLottery")
    public ResponseBase<Boolean> getLottery(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                            @RequestHeader(MER_CODE) String merCode,
                                            @RequestParam Long liveId, @RequestParam Integer activityId) {

        return liveChatService.getLottery(merCode,userId,liveId,activityId);
    }

    @ApiOperation(value = "IM登录过期，重新生成userSig", notes = "IM登录过期，重新生成userSig")
    @PostMapping("/getUserSig")
    public ResponseBase<String> getUserSig(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId) {
        return liveChatService.getUserSig(userId);
    }

    @ApiOperation(value = "获取直播间优惠券列表", notes = "获取直播间优惠券列表")
    @PostMapping("/getCouponList")
    public ResponseBase<List<LiveCouponRespDto>> getCouponList(@Valid @RequestBody LiveCouponListReqDTO req, BindingResult result) {
        checkValid(result);
        List<LiveCouponRespDto> couponList = liveChatService.getCouponList(req);
        return generateObjectSuccess(couponList);
    }


    @ApiOperation(value = "获取直播间商品列表", notes = "获取直播间商品列表")
    @PostMapping("/getCommodityList")
    public ResponseBase<List<CommoditySpecDTO>> getCommodityList(@Valid @RequestBody LiveCommodityReqDTO req, BindingResult result) {
        checkValid(result);
        List<CommoditySpecDTO> commodityList = liveChatService.getCommodityList(req);
        return generateObjectSuccess(commodityList);
    }

    @ApiOperation(value = "查询推荐的直播活动列表",
            notes = "查询推荐的直播活动列表（简要信息）")
    @PostMapping("getRecommendList")
    public ResponseBase<PageDTO<LiveSimpleRespDTO>> getRecommendList(@RequestBody LiveRecommendQueryReqDTO reqDTO) {

        return liveChatService.getRecommendList(reqDTO);
    }

    @ApiOperation(value = "厂家客户端用户点赞接口", notes = "厂家客户端用户点赞接口")
    @GetMapping("addFactoryLikeNum")
    public ResponseBase<Long> addFactoryLikeNum(@RequestParam Long liveId, @RequestParam Long num) {
        if (StringUtils.isEmpty(liveId)) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }
        Long totalNum = redisService.increment(REDIS_LIVE_USER_LIKE_KEY + liveId, num);
        SpringContextHolder.getBean(LiveChatController.class).sendMessage(liveId);
        return generateObjectSuccess(totalNum);
    }

    @ApiOperation(value = "C端用户点赞接口", notes = "C端用户点赞接口")
    @GetMapping("addUserLikeNum")
    @DisLockConfiguration.DisLock(keyExpr = "'addUserLikeNum:' + #liveId +':' + #userId",
            lockType = DisLockConfiguration.DisLock.LockType.OPTIMISTIC)
    public ResponseBase<Long> addUserLikeNum(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                             @RequestParam Long liveId, @RequestParam Long num) {
        if (StringUtils.isEmpty(userId)) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }
        //更新点赞次数
        LiveLikeCountReqDTO reqDTO = new LiveLikeCountReqDTO();
        reqDTO.setLiveId(liveId);
        reqDTO.setUserId(Long.parseLong(userId));
        reqDTO.setNum(num);
        ResponseBase<Boolean> responseBase = liveChatService.updateLikeCount(reqDTO);
        Long totalNum = 0L;
        if (responseBase.checkSuccess() && responseBase.getData()) {
            totalNum = redisService.increment(REDIS_LIVE_USER_LIKE_KEY + liveId, num);
        }
        SpringContextHolder.getBean(LiveChatController.class).sendMessage(liveId);

        return generateObjectSuccess(totalNum);
    }

    @ApiOperation(value = "获取C端用户点赞数接口", notes = "获取C端用户点赞数接口")
    @GetMapping("getTotalUserLikeNum")
    public ResponseBase<Long> getTotalUserLikeNum(@RequestParam Long liveId) {
        if (StringUtils.isEmpty(liveId)) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }
        String num = redisService.get(REDIS_LIVE_USER_LIKE_KEY + liveId);
        Long totalNum = StringUtils.isEmpty(num) ? 0 : Long.parseLong(num);
        return generateObjectSuccess(totalNum);
    }

    @ApiOperation(value = "获取海报状态", notes = "获取海报状态")
    @GetMapping("getPosterStatus")
    public ResponseBase<Integer> getPosterStatus(@RequestParam Long liveId) {
        String num = redisService.get(REDIS_LIVE_POSTER_STATUS_KEY + liveId);
        Integer status = StringUtils.isEmpty(num) ? LivePosterStatusEnum.OPEN.getCode() : Integer.valueOf(num);
        return generateObjectSuccess(status);
    }

    @ApiOperation(value = "获取活动状态", notes = "获取活动状态")
    @GetMapping("getActivityStatus")
    public ResponseBase<Integer> getActivityStatus(@RequestParam Long liveId,@RequestParam Integer activityId) {
        String num = redisService.get(REDIS_LIVE_ACTIVITY_STATUS_KEY + liveId + "_" + activityId);
        Integer status = StringUtils.isEmpty(num) ? LiveLotteryStatusEnum.CLOSE.getCode() : Integer.valueOf(num);
        return generateObjectSuccess(status);
    }

    @ApiOperation(value = "获取活动详情", notes = "获取活动详情")
    @GetMapping("getActivityDetail")
    public ResponseBase<LotteryActivityDetailRespDTO> getActivityDetail(@RequestHeader(MER_CODE) String merCode,@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                                        @RequestParam Long liveId,@RequestParam Integer activityId) {
        String num = redisService.get(REDIS_LIVE_ACTIVITY_STATUS_KEY + liveId + "_" + activityId);
        Integer activeStatus = StringUtils.isEmpty(num) ? LiveLotteryStatusEnum.CLOSE.getCode() : Integer.valueOf(num);
        CommonReqIntegerDTO request = new CommonReqIntegerDTO();
        request.setId(activityId);
        ResponseBase<ActivityDetailExtendDTO> responseBase = activityManagerClient.normalActivityDetail(request);
        checkResult(responseBase);
        ActivityDetailExtendDTO activityDetail = responseBase.getData();
        if (Objects.isNull(activityDetail)) {
            log.warn("[Activity] Not found activity {}", activityId);
            throw WarnException.builder().code(ACTIVITY_IS_NULL.getCode()).message(ACTIVITY_IS_NULL.getMsg()).build();
        }
        ResponseBase<Integer> activityResponse = activityManagerClient.getUseNum(merCode, activityId, Long.valueOf(userId));
        checkResult(activityResponse);
        if (activityResponse.getData() != null && activityResponse.getData() >= 0) {
            activityDetail.setAvailableNum(activityResponse.getData());
        } else {
            if (ACTIVITY_FROM_ACTIVITY.equals(activityDetail.getJoinRule())) {
                activityDetail.setAvailableNum(0);
            } else {
                activityDetail.setAvailableNum(activityDetail.getCountRule());
            }
        }
        LotteryActivityDetailRespDTO respDTO = new LotteryActivityDetailRespDTO();
        respDTO.setActiveStatus(activeStatus);
        respDTO.setActivityDetail(activityDetail);
        //商户公众号名称
        String officialAccountName = redisService.get(REDIS_LIVE_MERCHANT_OFFICIAL_ACCOUNT_KEY + liveId);
        if(!StringUtils.isEmpty(officialAccountName)){
            respDTO.setOfficialAccountName(officialAccountName);
        }
        return generateObjectSuccess(respDTO);
    }

    @ApiOperation(value = "直播开播提醒",
            notes = "直播开播提醒")
    @PostMapping("remindLiveStart")
    public ResponseBase<Boolean> remindLiveStart(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                 @Nullable @RequestHeader(LocalConst.HEAD_OPEN_ID_KEY) String openId,
                                                 @RequestHeader(value = LocalConst.HEAD_APPID_KEY, required = false) String appId,
                                                 @RequestBody LiveStartRemindReqDTO reqDTO) {
        if(!StringUtils.isEmpty(userId)) {
            reqDTO.setUserId(Long.valueOf(userId));
        }
        reqDTO.setOpenId(openId);
        reqDTO.setAppid(appId);
        return liveChatService.remindLiveStart(reqDTO);
    }

    @ApiOperation(value = "查询正在直播活动观看人数",
            notes = "查询正在直播活动观看人数")
    @GetMapping("getLiveViewers")
    public ResponseBase<Long> getLiveViewers(@RequestParam Long liveId) {

        String num = redisService.get(REDIS_LIVE_VIEWER_KEY + liveId);
        Long totalNum = StringUtils.isEmpty(num) ? 0 : Long.parseLong(num);
        return generateObjectSuccess(totalNum);
    }


    @ApiOperation(value = "统计直播观看人数",
            notes = "统计直播观看人数")
    @GetMapping("countLiveViewers")
    public ResponseBase<Long> countLiveViewers(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                               @RequestParam Long liveId, @RequestParam Integer type) {

        if (StringUtils.isEmpty(liveId) || StringUtils.isEmpty(type) ) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }

        if(type.equals(LiveViewerType.ENTER.getCode())){
            //进入直播，自增1
            redisService.increment(REDIS_LIVE_VIEWER_KEY + liveId, 1);
            //更新观看人次
            LiveFrequencyReqDTO reqDTO = new LiveFrequencyReqDTO();
            reqDTO.setLiveId(liveId);
            reqDTO.setUserId(Long.valueOf(userId));
            liveChatService.updateFrequency(reqDTO);
        } else {
            //退出直播，自减1
            redisService.decrement(REDIS_LIVE_VIEWER_KEY + liveId, 1);
        }
        String num = redisService.get(REDIS_LIVE_VIEWER_KEY + liveId);
        Long totalNum = StringUtils.isEmpty(num) ? 0 : Long.parseLong(num);
        //发送socket消息
        sendMessage(liveId);
        return generateObjectSuccess(totalNum);
    }

    @ApiOperation(value = "保存分享记录", notes = "保存分享记录")
    @PostMapping("saveShareRecord")
    public ResponseBase<Boolean> saveShareRecord(@Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                 @RequestBody LiveSaveShareRecordReqDTO reqDTO) {

        reqDTO.setUserId(Long.valueOf(userId));
        return liveChatService.saveShareRecord(reqDTO);
    }


    @ApiOperation(value = "分享排行榜", notes = "分享排行榜")
    @GetMapping("shareRecordRank")
    public ResponseBase<List<LiveSharerRecordRankRespDTO>> shareRecordRank(@RequestHeader(MER_CODE) String merCode,
                                                                           @RequestParam Long liveId) {

        return liveChatService.shareRecordRank(merCode,liveId);
    }

    @ApiOperation(value = "点赞排行榜", notes = "点赞排行榜")
    @GetMapping("liveLikeRank")
    public ResponseBase<List<LiveViewerRespDTO>> liveLikeRank(@RequestParam Long liveId) {

        return liveChatService.liveLikeRank(liveId);
    }

    @ApiOperation(value = "生成分享海报", notes = "生成分享海报")
    @PostMapping("createSharePoster")
    public ResponseBase<String> createSharePoster(@RequestHeader(MER_CODE) String merCode,
                                                  @Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                  @RequestHeader(value = LocalConst.HEAD_USER_ACCESS_CHANNEL, required = false) String userAccessChannel,
                                                  @RequestHeader(value = LocalConst.HEAD_APP_ID_KEY, required = false) String appletAppId,
                                                  @Valid @RequestBody LiveSharePosterReqDTO reqDTO) {
        if (StringUtils.isEmpty(userId)) {
            throw WarnException.builder().code(ErrorType.ID_IS_NULL.getCode()).
                    tipMessage(ErrorType.ID_IS_NULL.getMsg()).build();
        }
        reqDTO.setMerCode(merCode);
        reqDTO.setUserId(Long.valueOf(userId));
        // 设置小程序appId
        if (LocalConst.MINI_PROGRAM.equalsIgnoreCase(userAccessChannel)) {
            reqDTO.setAppletAppId(appletAppId);
        }
        return generateObjectSuccess(liveChatService.createSharePoster(reqDTO));
    }

    @ApiOperation(value = "编辑邀请人", notes = "编辑邀请人")
    @PostMapping("updateInviteName")
    public ResponseBase<Boolean> updateInviteName(@Valid @RequestBody LiveInviteNameReqDTO reqDTO) {

        return liveChatService.updateInviteName(reqDTO);
    }

    @ApiOperation(value = "实名认证",
            notes = "实名认证")
    @PostMapping("idCardCheck")
    public ResponseBase<LiveIdCardCheckRespDTO> idCardCheck(@RequestHeader(MER_CODE) String merCode,
                                                            @Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                            @Valid @RequestBody LiveIdCardCheckReqDTO reqDTO) {
        return generateObjectSuccess(liveChatService.idCardCheck(reqDTO, merCode, userId));
    }

    @ApiOperation(value = "领取红包汇总", notes = "领取红包汇总")
    @PostMapping("getRedPackRecord")
    public ResponseBase<List<ActivityRedpackReceiveRecordResDTO>> getRedPackRecord(@RequestHeader(MER_CODE) String merCode,
                                                                                   @Valid @RequestBody LiveRedPackRecordReqDTO reqDTO) {
        ActivityRedpackReceiveRecordReqDTO req = ModelConvertUtils.convert(reqDTO,ActivityRedpackReceiveRecordReqDTO.class);
        req.setMerCode(merCode);
        req.setSource(1);
        req.setBusinessId(reqDTO.getLiveId().intValue());
        return redPackClient.getRedpackReceiveRecordList(req);
    }

    @ApiOperation(value = "领取红包记录详情", notes = "领取红包记录详情")
    @PostMapping("getRedPackRecordDetail")
    public ResponseBase<Page<ActivityRedpackReceiveRecordDetailResDTO>> getRedPackRecordDetail(@Valid @RequestBody LiveRedpackRecordDetailReqDTO reqDTO) {

        ActivityRedpackReceiveRecordDetailReqDTO req = ModelConvertUtils.convert(reqDTO,ActivityRedpackReceiveRecordDetailReqDTO.class);
        ResponseBase<Page<ActivityRedpackReceiveRecordDetailResDTO>>  responseBase = redPackClient.getRedpackReceiveRecordDetailList(req);
        ValidateUtils.checkResult(responseBase);
        Page<ActivityRedpackReceiveRecordDetailResDTO> page = responseBase.getData();
        List<ActivityRedpackReceiveRecordDetailResDTO> list = page.getRecords();
        if(!CollectionUtils.isEmpty(list)){
            List<Long> userIds = list.stream().map(ActivityRedpackReceiveRecordDetailResDTO::getUserId).collect(Collectors.toList());
            ResponseBase<List<LiveViewerRespDTO>> response = liveChatService.getLiveViewerInfo(reqDTO.getLiveId(), userIds);
            List<LiveViewerRespDTO> liveViewers = response.getData();
            if(!CollectionUtils.isEmpty(liveViewers)){
                for(ActivityRedpackReceiveRecordDetailResDTO record: list){
                    Optional<LiveViewerRespDTO> respDTO = liveViewers.stream().filter(viewer -> viewer.getUserId().equals(record.getUserId().toString())).findFirst();
                    if(respDTO.isPresent()){
                        LiveViewerRespDTO liveViewer = respDTO.get();
                        record.setNickName(liveViewer.getNickName());
                        record.setLogoUrl(liveViewer.getLogoUrl());
                    }
                    if(StringUtils.isEmpty(record.getNickName())){
                        record.setNickName(LocalConst.LIVE_DEFAULT_NICK_NAME);
                    }
                    if(StringUtils.isEmpty(record.getLogoUrl())){
                        record.setLogoUrl("");
                    }
                }
            }
        }
        return responseBase;
    }

    @ApiOperation(value = "获取粉丝团二维码", notes = "获取粉丝团二维码")
    @GetMapping("getFanBasePicUrl")
    public ResponseBase<String> getFanBasePicUrl(@RequestHeader(MER_CODE) String merCode,@RequestParam Long liveId) {
        LiveInfoRespDTO liveInfoRespDTO = liveChatService.initLiveInfo(merCode,liveId);
        if(liveInfoRespDTO == null || StringUtils.isEmpty(liveInfoRespDTO.getFanBasePicUrl())){
            generateObjectSuccess(null);
        }
        return generateObjectSuccess(liveInfoRespDTO.getFanBasePicUrl());
    }

    @ApiOperation(value = "用户是否可以领取红包", notes = "用户是否可以领取红包")
    @GetMapping("isCanReceive")
    public ResponseBase<Boolean> isCanReceive(@RequestHeader(MER_CODE) String merCode,
                                              @Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,@RequestParam Integer configId) {
        return redPackClient.isCanReceive(merCode,configId,Long.valueOf(userId));
    }

    @ApiOperation(value = "领取红包", notes = "领取红包")
    @PostMapping("receiveRedPack")
    public ResponseBase<ActivityRedpackReceiveResDTO> receiveRedPack(@RequestHeader(MER_CODE) String merCode,
                                                                     @Nullable @RequestHeader(LocalConst.HEAD_USER_ID_KEY) String userId,
                                                                     @Nullable @RequestHeader(LocalConst.HEAD_OPEN_ID_KEY) String openId,
                                                                     @Valid @RequestBody ReceiveRedpackDTO reqDTO) {
        reqDTO.setMerCode(merCode);
        reqDTO.setUserId(Long.valueOf(userId));
        reqDTO.setReOpenid(openId);
        return redPackClient.receiveRedpack(reqDTO);
    }

    @ApiOperation(value = "发送直播状态消息", notes = "发送直播状态消息")
    @GetMapping("/sendWebSocketMsg")
    ResponseBase<Boolean> sendWebSocketMsg(@RequestParam Long liveId) {
        log.info("直播回调通知发送socket消息：{}", liveId);
        ResponseBase<List<LiveViewerRespDTO>> responseBase = liveService.connectViewerList(liveId);
        sendMessage(liveId, responseBase.getData());
        return generateObjectSuccess(true);
    }

    public void sendMessage(Long liveId) {
        sendMessage(liveId, null);
    }

    public void sendMessage(Long liveId, List<LiveViewerRespDTO> data) {
        LiveSendMessageDTO message = liveChatService.getMessage(liveId);
        message.setTerminal(3);
        message.setMsgDirection(2);
        message.setCommand("update");
        if (Objects.nonNull(data)) {
            message.setLinkUsers(data);
        }
        message.setLiveId(liveId.toString());
        liveService.sendClusterMessage(JSON.toJSONString(message));
    }
}
