package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.req.ActivityCommentSubmitReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.ActivityCommentSettingResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.ActivityCommentClient;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 评价有礼控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/27
 */
@RestController
@Api(tags = "评价有礼控制器")
@RequestMapping("/${api.version}/activityComment")
public class ActivityCommentController {

    @Autowired
    private ActivityCommentClient activityCommentClient;

    @ApiOperation(value = "获取评价记录是否已评价", notes = "commentRecordId：评价记录id")
    @GetMapping("/isComment")
    public ResponseBase<Boolean> isComment(@RequestHeader(LocalConst.MER_CODE) String merCode,
                                           @RequestParam(value = "commentRecordId") Long commentRecordId) {
        return activityCommentClient.isComment(merCode, commentRecordId);
    }

    @ApiOperation(value = "获取评价有礼活动状态", notes = "merCode：商户编码")
    @GetMapping("/getActivityStatus")
    public ResponseBase<Integer> getActivityStatus(@RequestHeader(LocalConst.MER_CODE) String merCode) {
        return activityCommentClient.getActivityStatus(merCode);
    }

    @ApiOperation(value = "获取评价界面所需数据", notes = "commentRecordId：评价记录id")
    @GetMapping("/getCommentPageInfo")
    public ResponseBase<List<ActivityCommentSettingResDTO>> getCommentPageInfo(
            @RequestHeader(LocalConst.MER_CODE) String merCode,
            @RequestParam(value = "commentRecordId") Long commentRecordId) {
        return activityCommentClient.getCommentPageInfo(merCode, commentRecordId);
    }

    @ApiOperation(value = "评价数据提交")
    @PostMapping("/commentSubmit")
    public ResponseBase<Object> commentSubmit(@RequestHeader(LocalConst.MER_CODE) String merCode,
                                              @Valid @RequestBody ActivityCommentSubmitReqDTO reqDto) {
        reqDto.setMerCode(merCode);
        return activityCommentClient.commentSubmit(reqDto);
    }


}
