package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MultiAddCartDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.HoneyShareDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.HoneyStoreSpecDTO;
import cn.hydee.ydjia.merchantcustomer.service.HoneyShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.HEAD_USER_ID_KEY;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.MER_CODE;

/**
 * 店员助手分享商品
 *
 * <AUTHOR>
 * @date 2020-07-06
 */
@Slf4j
@RestController
@Api(value = "店员助手分享商品C端控制器", produces = "application/json", tags = {"店员助手分享商品C端接口"})
@RequestMapping("/${api.version}/honey-share")
public class HoneyShareController extends AbstractController {

    private HoneyShareService honeyShareService;

    @Autowired
    public HoneyShareController(HoneyShareService honeyShareService) {
        this.honeyShareService = honeyShareService;
    }

    /**
     * 分享清单种下架和无货的商品，可以切换门店选择
     * 返回展示列表，
     * 切换门店选择的商品需要临时存储
     * 故增加redis保存打开过的分享列表
     *
     * @param userId
     * @param merCode
     * @param shareCode
     * @return
     */
    @ApiOperation("顾客获取助手分享的商品列表")
    @GetMapping("/list")
    public ResponseBase<HoneyShareDTO> getHoneyShareList(@RequestHeader(HEAD_USER_ID_KEY) @Nullable String userId,
                                                         @RequestHeader(MER_CODE) @Nullable String merCode,
                                                         @RequestParam String shareCode) {
        log.info("=====打开店员助手分享商品，merCode：{}，userId：{}，分享码：{}", merCode, userId, shareCode);
        return generateObjectSuccess(honeyShareService.getHoneyShareList(merCode, shareCode, userId));
    }

    @ApiOperation("店员获取助手分享的商品列表")
    @GetMapping("/listByEmp")
    public ResponseBase<HoneyShareDTO> getHoneyShareListForEmp(@RequestHeader(MER_CODE) @Nullable String merCode,
                                                               @RequestParam String shareCode) {
        return generateObjectSuccess(honeyShareService.getHoneyShareList(merCode, shareCode, null));
    }

    @ApiOperation(value = "获取助手分享商品-当前门店无货商品选择其他门店",notes = "不作24小时缓存，直接返回true")
    @PostMapping("/chooseByStore")
    public ResponseBase<Boolean> chooseByStore(@RequestHeader(HEAD_USER_ID_KEY) @Nullable String userId,
                                               @RequestHeader(MER_CODE) @Nullable String merCode,
                                               @Valid @RequestBody HoneyStoreSpecDTO honeyStoreSpecDTO, BindingResult result) {
        return generateObjectSuccess(true);
        //this.checkValid(result);
        //honeyStoreSpecDTO.setUserId(userId);
        //honeyStoreSpecDTO.setMerCode(merCode);
        //return generateObjectSuccess(honeyShareService.chooseByStore(honeyStoreSpecDTO));
    }

    @ApiOperation("获取助手分享商品-批量加入购物车")
    @PostMapping("/addToCart")
    public ResponseBase<Boolean> addToCart(@RequestHeader(HEAD_USER_ID_KEY) @Nullable String userId,
                                           @Valid @RequestBody MultiAddCartDTO multiAddCartDTO,
                                           BindingResult result) {
        this.checkValid(result);
        return generateObjectSuccess(honeyShareService.addToCart(multiAddCartDTO.getDtos(), userId));
    }

}
