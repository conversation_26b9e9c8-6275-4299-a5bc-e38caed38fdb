package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.domain.YdjMerchantSetting;
import cn.hydee.ydjia.merchantcustomer.dto.req.QueryOnlineIntegralReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.OnlineIntegralRespDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.member.IntegralExpireDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.member.IntegralRuleDTO;
import cn.hydee.ydjia.merchantcustomer.feign.MemberInfoClient;
import cn.hydee.ydjia.merchantcustomer.feign.MemberIntegralClient;
import cn.hydee.ydjia.merchantcustomer.service.MemberIntegralService;
import cn.hydee.ydjia.merchantcustomer.service.YdjMerchantSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

import static cn.hydee.ydjia.merchantcustomer.service.YdjMerchantSettingService.EXCHANGE_PROPORTION;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.HEAD_USER_ID_KEY;
import static cn.hydee.ydjia.merchantcustomer.util.ValidateUtils.checkResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/05/13 10:33
 */
@Slf4j
@RestController
@RequestMapping("/${api.version}/memberIntegral")
@Api(value = "会员积分管理", description = "会员积分管理")
@AllArgsConstructor
public class MemberIntegralController extends AbstractController {

    private final MemberInfoClient memberInfoClient;
    private final YdjMerchantSettingService merchantSettingService;
    private final MemberIntegralClient memberIntegralClient;
    private final MemberIntegralService memberIntegralService;

    @ApiOperation(value = "积分明细列表", notes = "积分明细列表")
    @PostMapping("/list")
    public ResponseBase<PageDTO<OnlineIntegralRespDTO>> onlineIntegralList(@RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                                           @RequestBody QueryOnlineIntegralReqDTO request) {
        request.setUserId(userId);
        return memberInfoClient.onlineIntegralList(request);
    }


    @ApiOperation(value = "线下积分兑换成心币", notes = "线下积分兑换成心币")
    @PostMapping("/exchange/{merCode}/{integral}")
    public ResponseBase<Integer> exchangeOfflineIntegral(@PathVariable String merCode,
                                                         @RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                         @PathVariable Integer integral) {
//        if (integral == 0) {
//            throw WarnException.builder().code(INTEGRAL_IS_NOT_POSITIVE_NUMBER.getCode())
//                    .message(INTEGRAL_IS_NOT_POSITIVE_NUMBER.getMsg())
//                    .build();
//        }
        ResponseBase<String> responseBase = proportionNew(merCode);
        checkResult(responseBase);
        return memberInfoClient.exchangeOfflineIntegral(merCode, Long.valueOf(userId), integral, responseBase.getData());
    }

    @ApiOperation(value = "积分兑换比例")
    @GetMapping("/proportion/{merCode}")
    public ResponseBase<Integer> proportion(@PathVariable String merCode) {
        List<YdjMerchantSetting> settings = merchantSettingService.queryByMerCodeAndKey(merCode, EXCHANGE_PROPORTION);
        if (CollectionUtils.isEmpty(settings)) {
            return generateObjectSuccess(1);
        }
        // 获取积分兑换比例字段
        String sysValue = settings.get(0).getSysValue();
        Integer rate = 1;
        // 若积分兑换比例中包含'1:'和':'字符，则需要截取与计算给前端数字类型做兼容
        if (sysValue.contains(":")) {
            Integer offline = Integer.valueOf(sysValue.split(":")[0]);
            Integer online = Integer.valueOf(sysValue.split(":")[1]);
            rate = online / offline;
        } else {
            rate = Integer.valueOf(sysValue);
        }
        return generateObjectSuccess(rate);
    }

    @ApiOperation(value = "积分兑换比例(新)")
    @GetMapping("/proportionNew/{merCode}")
    public ResponseBase<String> proportionNew(@PathVariable String merCode) {
        // 积分兑换比例修改为字符串比例形式，如"5:2"
        List<YdjMerchantSetting> settings = merchantSettingService.queryByMerCodeAndKey(merCode, EXCHANGE_PROPORTION);
        if (CollectionUtils.isEmpty(settings)) {
            // 默认为"1:1"
            return generateObjectSuccess("1:1");
        }
        // 获取积分兑换比例字段
        String sysValue = settings.get(0).getSysValue();
        // 若积分兑换比例中不包含':'字符，则是老数据，需要转换为'1:100'的形式
        if (!sysValue.contains(":")) {
            sysValue = "1:" + sysValue;
        }
        return generateObjectSuccess(sysValue);
    }

    @ApiOperation(value = "查询线下会员积分", notes = "查询线下会员积分")
    @GetMapping("/queryOfflineIntegral/{merCode}")
    public ResponseBase<BigDecimal> queryOfflineIntegral(@RequestHeader(HEAD_USER_ID_KEY) String userId, @PathVariable String merCode) {
        try {
            return memberIntegralClient.queryOfflineIntegral(Long.valueOf(userId), merCode);
        } catch (Exception e) {
            log.warn("查询会员线下积分异常", e);
        }
        return generateObjectSuccess(BigDecimal.ZERO);
    }

    @ApiOperation(value = "查询商户会员心币积分", notes = "查询商户会员心币积分")
    @GetMapping("/queryOnlineIntegral/{merCode}")
    public ResponseBase<Integer> queryOnlineIntegral(@RequestHeader(value = HEAD_USER_ID_KEY, required = false) @Nullable String userId, @PathVariable String merCode) {
        if (StringUtils.isEmpty(userId)) {
            // 和前端约定, 返回null表示当前会员未登录
            return generateObjectSuccess(null);
        }
        try {
            return generateObjectSuccess(memberIntegralService.selectIntegral(Long.valueOf(userId)));
        } catch (Exception e) {
            log.warn("查询会员积分异常", e);
        }
        return generateObjectSuccess(0);
    }

    @ApiOperation(value = "查询会员即将过期的心币")
    @PostMapping("/queryExpireIntegralInfo")
    public ResponseBase<IntegralExpireDTO> queryExpireIntegralInfo(@RequestHeader("merCode") String merCode,
                                                                   @RequestHeader("userId") Long userId) {
        return memberIntegralClient.queryExpireIntegralInfo(merCode, userId);
    }

    @ApiOperation(value = "获取心币清零规则详情")
    @GetMapping("/getIntegralManagerDetail")
    public ResponseBase<IntegralRuleDTO> getIntegralManagerDetail(@RequestHeader(value = "merCode") String merCode) {
        return generateSuccess(memberIntegralService.queryIntegralRule(merCode));
    }

}
