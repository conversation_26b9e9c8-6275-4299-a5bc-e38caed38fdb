package cn.hydee.ydjia.merchantcustomer.controller;

import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.req.QJGiftCardBindReq;
import cn.hydee.ydjia.merchantcustomer.dto.req.QJGiftCardPageReq;
import cn.hydee.ydjia.merchantcustomer.dto.resp.MemberGiftCardInfoRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.MemberManageClient;
import cn.hydee.ydjia.merchantcustomer.service.MemberInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.HEAD_USER_ID_KEY;
import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.MER_CODE;

@Slf4j
@Api(value = "千金礼品卡控制器", tags = {"千金礼品卡控制器"})
@RestController
@RequestMapping(value = "/${api.version}/qianjin")
public class QianjinGiftCardController extends AbstractController {

    @Autowired
    private MemberManageClient memberManageClient;

    @Autowired
    private MemberInfoService memberInfoService;

    @ApiOperation(value = "查询会员礼品卡列表", notes = "查询会员礼品卡列表（千金专用）")
    @PostMapping("/queryMemberGiftCardPage")
    public ResponseBase<PageDTO<MemberGiftCardInfoRespDTO>> queryMemberGiftCardPage(@RequestHeader(MER_CODE) String merCode,
                                                                                    @RequestHeader(HEAD_USER_ID_KEY) String userId,
                                                                                    @Valid @RequestBody QJGiftCardPageReq req) {
        String memberCard = memberInfoService.foundMemberCardByUserId(merCode, userId);
        if (StringUtils.isBlank(memberCard)) {
            return ResponseBase.error("-1", "会员不存在");
        }
        req.setPageIndex(req.getCurrentPage());
        req.setMerCode(merCode);
        req.setMemCardNo(memberCard);
        return memberManageClient.queryMemberGiftCardPage(req);
    }

    @ApiOperation(value = "查询会员礼品卡可用总额", notes = "查询会员礼品卡可用总额（千金专用）")
    @GetMapping("/queryMemberGiftCardTotalAmount")
    public ResponseBase<BigDecimal> queryMemberGiftCardTotalAmount(@RequestHeader(MER_CODE) String merCode,
                                                                   @RequestHeader(HEAD_USER_ID_KEY) String userId) {
        String memberCard = memberInfoService.foundMemberCardByUserId(merCode, userId);
        if (StringUtils.isBlank(memberCard)) {
            return ResponseBase.error("-1", "会员不存在");
        }
        return memberManageClient.queryMemberGiftCardTotalAmount(merCode, memberCard);
    }

    @ApiOperation(value = "绑定礼品卡", notes = "绑定礼品卡（千金专用）")
    @PostMapping("/bindGiftCard")
    public ResponseBase<Void> bindGiftCard(@RequestHeader(MER_CODE) String merCode,
                                              @RequestHeader(HEAD_USER_ID_KEY) String userId,
                                              @Valid @RequestBody QJGiftCardBindReq req) {
        String memberCard = memberInfoService.foundMemberCardByUserId(merCode, userId);
        if (StringUtils.isBlank(memberCard)) {
            return ResponseBase.error("-1", "会员不存在");
        }
        req.setMerCode(merCode);
        req.setMemCardNo(memberCard);
        return memberManageClient.bindGiftCard(req);
    }

}
