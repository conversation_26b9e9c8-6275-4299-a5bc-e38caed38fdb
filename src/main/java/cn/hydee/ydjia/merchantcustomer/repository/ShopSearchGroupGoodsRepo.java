package cn.hydee.ydjia.merchantcustomer.repository;

import cn.hydee.ydjia.merchantcustomer.domain.ShopSearchGroupGoods;
import cn.hydee.ydjia.merchantcustomer.dto.ShopSearchGroupGoodsDetailDTO;
import cn.hydee.ydjia.merchantcustomer.feign.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ShopSearchGroupGoodsRepo extends BaseMapper<ShopSearchGroupGoods> {

    /**
     * 查询有效的商品推荐条数
     * @return
     */
    @DS(LocalConst.DB_MANAGER_SLAVE)
    List<ShopSearchGroupGoodsDetailDTO> getValidGoodsDetail();
}
