package cn.hydee.ydjia.merchantcustomer.repository;

import cn.hydee.ydjia.merchantcustomer.domain.LoydCrdCard;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * (LoydCrdCard)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-01 14:47:11
 */
public interface LoydCrdCardDao {

    /**
     * 查询指定行数据
     *
     * @param loydCrdCard 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<LoydCrdCard> queryAllByLimit(LoydCrdCard loydCrdCard, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param loydCrdCard 查询条件
     * @return 总行数
     */
    long count(LoydCrdCard loydCrdCard);

    /**
     * 新增数据
     *
     * @param loydCrdCard 实例对象
     * @return 影响行数
     */
    int insert(LoydCrdCard loydCrdCard);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<LoydCrdCard> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<LoydCrdCard> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<LoydCrdCard> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<LoydCrdCard> entities);

    /**
     * 修改数据
     *
     * @param loydCrdCard 实例对象
     * @return 影响行数
     */
    int update(LoydCrdCard loydCrdCard);


}

