package cn.hydee.ydjia.merchantcustomer.repository;

import cn.hydee.ydjia.merchantcustomer.dto.req.LinkSearchParamDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.LinkSearchReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.YdjInternalLinkDTO;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商户链接配置DAO接口
 *
 * <AUTHOR>
 * @create 2020-11-15
 */
@Repository
public interface YdjInternalLinkRepo {

    /**
     * 通过商家编码查内部链接
     *
     * @return List<YdjInternalLinkDTO>
     */
    @DS(LocalConst.DB_MANAGER_SLAVE)
    List<YdjInternalLinkDTO> queryAll();

    /**
     * 根据条件查询链接
     *
     * @param list
     * @return
     */
    @DS(LocalConst.DB_MANAGER_SLAVE)
    List<YdjInternalLinkDTO> searchInternalLink(@Param("list") List<LinkSearchParamDTO> list);
}
