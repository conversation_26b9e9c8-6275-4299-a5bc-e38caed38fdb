package cn.hydee.ydjia.merchantcustomer.repository;

import cn.hydee.ydjia.merchantcustomer.domain.ShopTransportarea;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * (ShopTransportarea)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-31 14:17:46
 */
public interface ShopTransportareaDao {

    /**
     * 通过ID查询单条数据
     *
     * @param guid 主键
     * @return 实例对象
     */
    @DS(LocalConst.DB_YXT_SHOP)
    ShopTransportarea queryById(String guid);

    /**
     * 查询指定行数据
     *
     * @param shopTransportarea 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    @DS(LocalConst.DB_YXT_SHOP)
    List<ShopTransportarea> queryAllByLimit(ShopTransportarea shopTransportarea, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param shopTransportarea 查询条件
     * @return 总行数
     */
    @DS(LocalConst.DB_YXT_SHOP)
    long count(ShopTransportarea shopTransportarea);

    /**
     * 新增数据
     *
     * @param shopTransportarea 实例对象
     * @return 影响行数
     */
    @DS(LocalConst.DB_YXT_SHOP)
    int insert(ShopTransportarea shopTransportarea);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ShopTransportarea> 实例对象列表
     * @return 影响行数
     */
    @DS(LocalConst.DB_YXT_SHOP)
    int insertBatch(@Param("entities") List<ShopTransportarea> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ShopTransportarea> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    @DS(LocalConst.DB_YXT_SHOP)
    int insertOrUpdateBatch(@Param("entities") List<ShopTransportarea> entities);

    /**
     * 修改数据
     *
     * @param shopTransportarea 实例对象
     * @return 影响行数
     */
    @DS(LocalConst.DB_YXT_SHOP)
    int update(ShopTransportarea shopTransportarea);

    /**
     * 通过主键删除数据
     *
     * @param guid 主键
     * @return 影响行数
     */
    @DS(LocalConst.DB_YXT_SHOP)
    int deleteById(String guid);

}

