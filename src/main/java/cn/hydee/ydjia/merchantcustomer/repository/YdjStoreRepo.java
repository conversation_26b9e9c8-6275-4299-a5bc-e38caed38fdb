package cn.hydee.ydjia.merchantcustomer.repository;

import cn.hydee.ydjia.merchantcustomer.feign.domain.YdjStore;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface YdjStoreRepo extends BaseMapper<YdjStore> {
    /**
     * 删除中心店
     * @param merCode
     * @param userId
     * @return
     */
    @DS(LocalConst.DB_MANAGER_MASTER)
    @Update("update ydj_store set isvalid=0 ,modify_name=#{userId} where mer_code=#{merCode} and center_store=1 ")
   int deleteCenterStore(@Param("merCode") String merCode, @Param("userId") String userId);

    /**
     * 查中心店
     * @param merCode
     * @return YdjStore
     */
    @DS(LocalConst.DB_MANAGER_SLAVE)
    YdjStore queryCenterStore(@Param("merCode") String merCode);
}