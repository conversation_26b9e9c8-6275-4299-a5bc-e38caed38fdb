package cn.hydee.ydjia.merchantcustomer.util;

import cn.hydee.ydjia.merchantcustomer.enums.IBaseEnum;
import cn.hydee.ydjia.merchantcustomer.enums.ShopStoreTypeEnum;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.List;

public class EnumsUtil {

    public static <T extends Enum<T>> T getEnum(Class<T> clazz, Integer val) {
        if(Objects.isNull(val)) {
            return null;
        }
        Enum[] enums =  (Enum[])clazz.getEnumConstants();
        if(Arrays.stream(clazz.getInterfaces()).collect(Collectors.toList()).contains(IBaseEnum.class)) {
            List<Enum> enus = Arrays.stream(enums)
                    .filter(enu -> {
                        return ((IBaseEnum) enu).getValue().equals(val);
                    }).collect(Collectors.toList());
            if(enus.size() == 1) {
                return (T)enus.get(0);
            }
        }
        return null;
    }

    public static <T extends Enum<T>> T getEnumDefalut(Class<T> clazz, Integer val, T d) {
        T e = getEnum(clazz, val);
        if(Objects.nonNull(e)) {
            return e;
        }
        return d;
    }


    public static void main(String[] args) throws Exception{
        ShopStoreTypeEnum type = getEnum(ShopStoreTypeEnum.class, 1);
        System.out.println(type.getDesc());
    }
}
