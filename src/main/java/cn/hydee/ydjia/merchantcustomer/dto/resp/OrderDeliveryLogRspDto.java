package cn.hydee.ydjia.merchantcustomer.dto.resp;

import cn.hydee.ydjia.merchantcustomer.feign.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/10 20:06
 */
@Data
public class OrderDeliveryLogRspDto extends BaseDTO {
    @ApiModelProperty(value = "记录ID")
    private String id;
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ApiModelProperty(value = "配送员名称")
    private String riderName;
    @ApiModelProperty(value = "配送员手机")
    private String riderPhone;
    @ApiModelProperty(value = "状态，包括1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常，20门店员工配送")
    private Integer state;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "详细配送方式名称")
    private String deliveryPlatName;

}
