package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel("商品信息")
public class CommodityConReq {

    @NotNull(message = "商品ID不可为空")
    @ApiModelProperty("商品ID")
    private Long commodityId;

    @NotNull(message = "商品规格不可为空")
    @ApiModelProperty("商品规格")
    private Long specId;

    @ApiModelProperty("商品类型，1-普通商品，2-组合商品")
    private Integer commodityType;

    @NotNull(message = "所需库存数量不能小于1")
    @ApiModelProperty("最小库存数")
    private Integer minStock;
}
