package cn.hydee.ydjia.merchantcustomer.dto;

import cn.hydee.ydjia.merchantcustomer.dto.req.LoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ClientTypeEnum;
import cn.hydee.ydjia.merchantcustomer.feign.enums.StatusEnums;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/9/3 17:38
 */
@Data
public class SwitchStoresReq {


    @ApiModelProperty(value = "门店ID")
    @NotNull(message = "门店ID不可为空")
    private String storeId;


    @NotBlank(message = "商户编码不能为空")
    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty(value = "经度")
    @NotBlank(message = "经度不能为空")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @NotBlank(message = "纬度不能为空")
    private String latitude;

    @ApiModelProperty(value = "是否立即购买")
    @NotNull(message = "是否立即购买")
    private Boolean whetherToBuyNow;

    @Valid
    @Size(max = 200, min = 1, message = "商品数量不能超过200个")
    @ApiModelProperty("销售商品")
    public List<SaleCommodity> saleCommodityList;

    @ApiModelProperty(value = "订单类型:N.正常订单 G.拼团订单 I.积分兑换订单 A.预约订单 F.分销订单 C.云仓订单")
    private String orderType;

    @Getter
    @Setter
    public static class SaleCommodity {
        @NotNull(message = "商品ID不可为空")
        @ApiModelProperty("商品ID")
        private String commodityId;

        @NotNull(message = "商品规格不可为空")
        @ApiModelProperty("商品规格")
        private Long specId;

        @ApiModelProperty("商品类型，1-普通商品，2-组合商品")
        private Integer commodityType;

        @NotNull(message = "数量不可为空")
        @ApiModelProperty(value = "数量")
        private Integer count;

        @ApiModelProperty(value = "是否选中(1：是；0：否)")
        private Integer choseFlag = StatusEnums.ENABLING.getCode();

        @ApiModelProperty(value = "药品类型(0：甲类OTC，1：处方药，2：乙类OTC，3：非处方药)")
        private Integer drugType;

    }


    @ApiModelProperty(value = "登录会员信息", hidden = true)
    private LoginUserDTO loginUser;


    @ApiModelProperty(hidden = true)
    private String userId;

    @ApiModelProperty(hidden = true, value = "客户端来源")
    private String clientType;

    @ApiModelProperty(value = "处方药合规小程序版本，header中前端传参，版本号", hidden = true)
    private String prescriptionDrugComplianceVersion;

    @ApiModelProperty(value = "小程序appId", hidden = true)
    private String appletAppId;

    @JsonIgnore
    public String getRedisKey() {
        //非大屏请求
        if (!ClientTypeEnum.ANDROID.getName().equals(clientType)) {
            return LocalConst.REDIS_CART + merCode + LocalConst.SPLITER_UNDERLINE + userId;
        }
        return LocalConst.REDIS_CART_LARGE_SCREEN + merCode + LocalConst.SPLITER_UNDERLINE + userId;
    }
}
