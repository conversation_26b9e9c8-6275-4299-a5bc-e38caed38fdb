package cn.hydee.ydjia.merchantcustomer.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/3 15:21
 */
@Data
public class MiniPayDTO {
    @ApiModelProperty("小程序APPID")
    private String appId;
    @ApiModelProperty("时间戳")
    private String timeStamp;
    @ApiModelProperty("随机字符串")
    private String nonceStr;
    @ApiModelProperty("预支付id字符串")
    @JsonProperty("package")
    private String packageValue;
    @ApiModelProperty("签名类型")
    private String signType;
    @ApiModelProperty("支付签名")
    private String paySign;
    @ApiModelProperty("请求返回，SUCCESS-成功，FAIL-失败")
    private String returnCode;
}
