package cn.hydee.ydjia.merchantcustomer.dto.req;

import cn.hutool.db.sql.Order;
import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CustomerSearchBase extends PageBase {
    @ApiModelProperty("搜索关键词")
    private String keyword;
    @ApiModelProperty("门店ID， 不传会默认旗舰店ID")
    private String storeId;
    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "纬度")
    private String latitude;
    @ApiModelProperty(value = "商户")
    private String merCode;
    @ApiModelProperty(value = "排序字段：price-价格排序; income-业绩比例排序")
    private String sortField;
    @ApiModelProperty(value = "正序/倒序，默认 true 正序")
    private Boolean asc = true;
    @ApiModelProperty(value = "仅有业绩商品: 全部不传此参数，true表示仅看有业绩商品")
    private Boolean hasIncome = null;
    @ApiModelProperty(value = "仅有库存商品: 全部不传此参数，true表示仅看有库存商品")
    private Boolean hasStock = null;
}
