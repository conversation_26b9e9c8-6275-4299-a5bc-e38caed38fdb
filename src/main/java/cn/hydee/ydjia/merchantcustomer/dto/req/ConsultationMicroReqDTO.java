package cn.hydee.ydjia.merchantcustomer.dto.req;

import cn.hydee.ydjia.merchantcustomer.dto.resp.ConsultationMicroDrugInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 微问诊请求跳转参数dto
 * 用来问诊记录初始化
 *
 * <AUTHOR>
 */
@Data
public class ConsultationMicroReqDTO {

    @ApiModelProperty(value = "商家编码")
    @NotBlank(message = "商家编码不能为空")
    private String merCode;

    @ApiModelProperty(value = "问诊所在门店ID")
    private String storeId;

    @ApiModelProperty(value = "问诊模式：1-[模式一]：传递药品咨询开方模式 2-[模式二]：不传递药品随机咨询模式")
    @NotNull(message = "问诊模式不能为空")
    private Integer mode;

    @ApiModelProperty(value = "问诊药品列表，模式一：立即购买需传入，购物车结算由后端获取，订单提交后补录由后端获取")
    private List<ConsultationMicroDrugInfo> drugList;

    @ApiModelProperty(value = "会员唯一标示",hidden = true)
    private String memberId;

    @ApiModelProperty(value = "问诊所在门店名称",hidden = true)
    private String storeName;

    @ApiModelProperty(value = "订单ID")
    private String orderId;
}
