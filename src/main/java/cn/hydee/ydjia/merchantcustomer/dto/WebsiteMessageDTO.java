package cn.hydee.ydjia.merchantcustomer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2021/01/08 15:29
 */
@Data
public class WebsiteMessageDTO {
    private Long id;
    private Long messageId;
    private Long userId;
    private String merCode;
    private String title;
    private String content;
    /**
     * 0:全体发送 1:指定单个会员
     */
    private Integer type;
    @ApiModelProperty("是否已读 0:未读 1:已读")
    private Integer state;
    private Integer iconType;
    private String redirectUrl;
    private LocalDateTime createTime;
}
