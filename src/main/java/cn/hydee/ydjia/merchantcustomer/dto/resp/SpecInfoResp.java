package cn.hydee.ydjia.merchantcustomer.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SpecInfoResp {

    @ApiModelProperty("门店ID")
    private String storeId;

    @ApiModelProperty("商品信息")
    private Long commodityId;

    @ApiModelProperty("规格信息")
    private Long specId;

    @ApiModelProperty("库存")
    private Integer stock;


}
