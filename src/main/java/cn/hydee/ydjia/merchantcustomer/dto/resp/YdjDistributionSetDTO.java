package cn.hydee.ydjia.merchantcustomer.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data

public class YdjDistributionSetDTO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "起送价")
    private BigDecimal initialDeliveryPrice;
    @ApiModelProperty(value = "是否有浮动运费0-无，1-超出距离加收 2-阶梯规则")
    private Integer isfloatingFreight;
    @ApiModelProperty(value = "浮动超出公里数")
    private BigDecimal floatOverKilometers;
    @ApiModelProperty(value = "浮动每公里加收金额")
    private BigDecimal cashOnDelivery;
    @ApiModelProperty(value = "是否免运配送0不1免")
    private Integer isfreeShipping;
    @ApiModelProperty(value = "配送费")
    private BigDecimal distributionFee;
    @ApiModelProperty(value = "免运门槛")
    private BigDecimal freeEntryThreshold;
    @ApiModelProperty(value = "配送时间")
    private String deliveryTime;
    @ApiModelProperty(value = "浮动费用阶梯配置")
    private List<FloatLadderConfig> floatLadderConfigList;
}