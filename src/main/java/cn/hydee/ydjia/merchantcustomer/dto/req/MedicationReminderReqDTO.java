package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: HuangYiB<PERSON>
 * @time: 2021/4/27 14:30
 */

@Data
public class MedicationReminderReqDTO {

    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty("唯一标示编码集合")
    private List<String> uniqueCodes;

    @ApiModelProperty(value = "会员ID")
    private Long memberId;

    @ApiModelProperty("0:否 1:是 是否用药指导")
    private Integer medGuideFlag;

    @ApiModelProperty(value = "渠道")
    private Integer channel;

    @ApiModelProperty(value = "订单编号")
    private String orderId;

    @ApiModelProperty(value = "用药提醒集合")
    private List<MedicationReminderNotifiedReqDTO> list;

    @Data
    public static class MedicationReminderNotifiedReqDTO {
        @ApiModelProperty("商品编码")
        private String uniqueCode;

        @ApiModelProperty("商品名称(冗余)")
        private String proName;

        @ApiModelProperty("用药剂量")
        private String dosage;

        @ApiModelProperty("用药剂量单位")
        private String unit;

        @ApiModelProperty("用药频次")
        private String frequency;

        @ApiModelProperty("服用周期类型 1:天/2:周/3:月")
        private Integer cycleType;

        @ApiModelProperty("服用周期")
        private Integer cycle;

        @ApiModelProperty("0:开启,1：关闭,2：过期")
        private Integer status;

        @ApiModelProperty(value = "商品主图地址")
        private String mainPic;

        @ApiModelProperty("提醒时间点集合")
        private List<String> remindHourList;
    }
}
