package cn.hydee.ydjia.merchantcustomer.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <Description>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/26 18:38
 */
@Data
public class PrescriptionOrderDetailDTO {
    @ApiModelProperty(value = "订单ID")
    private Long orderId;
    @ApiModelProperty(value = "明细ID")
    private Long id;
    @ApiModelProperty(value = "商品编码")
    private Long commodityId;
    @ApiModelProperty(value = "商品个数")
    private Integer commodityNumber;
    @ApiModelProperty(value = "规格值")
    private String skuValue;
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    private Long specId;
}
