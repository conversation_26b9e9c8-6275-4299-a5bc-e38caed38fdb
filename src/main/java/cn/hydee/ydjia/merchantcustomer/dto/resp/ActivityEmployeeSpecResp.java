package cn.hydee.ydjia.merchantcustomer.dto.resp;

import cn.hydee.ydjia.merchantcustomer.enums.NewShopRuleEnum;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreSpec;
import cn.hydee.ydjia.merchantcustomer.feign.enums.YesOrNoType;
import cn.hydee.ydjia.merchantcustomer.util.EnumsUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("内购商城列表")
public class ActivityEmployeeSpecResp {

    @ApiModelProperty("活动信息")
    private ActivityEmployeeSpecSetResp activityEmployeeSpecSetResp;

    @ApiModelProperty("产品信息")
    private StoreSpec storeSpec;

    @ApiModelProperty("旗舰店ID, 旗舰店参与才会有值")
    private String shipStoreId;

    // 推荐置顶商品排序
    @ApiModelProperty("顶部推荐商品置顶排序（存在补齐数据，所以有可能是局部顺序）")
    private Integer topRecommendSort;



    public ActivityEmployeeSpecResp buildDeliveryType() {
        List<Integer> deliveryTypeList = new ArrayList<>();
        if(YesOrNoType.NO.getCode().equals(this.activityEmployeeSpecSetResp.getCommSource())) {
            // 自营
            if(Objects.isNull(this.shipStoreId)) {
                // 旗舰店没有参与
                deliveryTypeList.add(YesOrNoType.YES.getCode());
            } else if(YesOrNoType.NO.getCode().equals(this.activityEmployeeSpecSetResp.getWareHouseSelfType())) {
                // 旗舰店参与开启
                deliveryTypeList.add(YesOrNoType.YES.getCode());
                deliveryTypeList.add(YesOrNoType.NO.getCode());
            }
            if (YesOrNoType.YES.getCode().equals(this.activityEmployeeSpecSetResp.getWareHouseSelfType()) && Objects.nonNull(this.shipStoreId)){//并且关闭了自提开关,并且有旗舰店
                NewShopRuleEnum shopRuleEnum = EnumsUtil.getEnum(NewShopRuleEnum.class, activityEmployeeSpecSetResp.getSelectType());
                if (shopRuleEnum == NewShopRuleEnum.PART  && activityEmployeeSpecSetResp.getStoreIdList().size() == 1) {//指定门店仅包含旗舰店
                    deliveryTypeList.add(YesOrNoType.NO.getCode());
                } else {
                    deliveryTypeList.add(YesOrNoType.YES.getCode());
                    deliveryTypeList.add(YesOrNoType.NO.getCode());
                }
            }

        } else {
            // 云仓
            deliveryTypeList.add(YesOrNoType.NO.getCode());
        }
        this.activityEmployeeSpecSetResp.setDeliveryType(deliveryTypeList);
        return this;
    }

}
