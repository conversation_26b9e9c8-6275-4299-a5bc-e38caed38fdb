package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON>iB<PERSON>
 * @time: 2021/5/12 15:26
 */

@Data
public class MedicationReminderSetReqDTO {
    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty("提醒药品表主键集合")
    private List<String> notifiedIds;

    @ApiModelProperty(value = "会员ID")
    private Long memberId;
}
