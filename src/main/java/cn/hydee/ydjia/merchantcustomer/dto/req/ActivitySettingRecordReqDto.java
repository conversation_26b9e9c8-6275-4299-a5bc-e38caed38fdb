package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @version: V1.0
 * @author: JackLi
 * @className: ActivitySettingRecordReqDto
 * @packageName: cn.hydee.middle.market.dto.req
 * @description: 分享记录入参实体类
 * @data: 2020/8/11 9:26
 **/
@Data
public class ActivitySettingRecordReqDto {
    /**
     * 我的分享记录id
     */
    @ApiModelProperty(value = "发起方唯一标识", notes = "发起方唯一标识")
    @NotNull
    private Integer shareId;

    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码", notes = "")
    private String merCode;

    /**
     * 参与的分享有礼活动ID
     */
    @ApiModelProperty(value = "参与的分享有礼活动ID", notes = "")
    @NotNull
    private Integer activityId;
    /**
     * 获取奖励的userId
     */
    @ApiModelProperty(value = "获取奖励的userId", notes = "")
    private Long userId;

    /**
     * 分享绑定的商品ID/活动id
     **/
    @ApiModelProperty(value = "商品ID/活动id", notes = "")
    @NotNull
    private String busId;
    /**
     * 获取奖励的usercard
     */
    @ApiModelProperty(value = "获取奖励的usercard", notes = "")
    private String userCard;
}
