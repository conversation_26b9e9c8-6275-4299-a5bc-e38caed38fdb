package cn.hydee.ydjia.merchantcustomer.dto.resp;

import cn.hydee.ydjia.merchantcustomer.domain.ReturnQuestDetail;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreResDTO;
import cn.hydee.ydjia.merchantcustomer.feign.honey.MedicalPrescriptionApplyDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/14 14:39
 */
@Data
public class QueryOrderRespDTO {

    @ApiModelProperty(value = "订单id")
    private String id;
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "门店id")
    private String storeId;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "订单编号")
    private String serialNumber;
    @ApiModelProperty(value = "会员唯一标示")
    private String memberId;
    @ApiModelProperty(value = "订单类型")
    private String orderType;
    @ApiModelProperty(value = "客户端渠道")
    private String sourceChannel;
    @ApiModelProperty(value = "来源媒体：会员卡号，员工编码，门店编码")
    private String sourceMedia;
    @ApiModelProperty(value = "流量来源入口（用于流量分析）")
    private String fromChannel;
    @ApiModelProperty(value = "订单状态 2.待付款 4.待发货 6.待收货 8.待退货 10.待退款 12.已完成 20.已取消 30.退款完成 ")
    private Integer orderStatus;
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    @ApiModelProperty(value = "支付方式(0.在线支付 1.货到付款)")
    private Integer payMode;
    @ApiModelProperty(value = "支付类型")
    private Integer payType;
    @ApiModelProperty(value = "支付状态")
    private Integer payStatus;
    @ApiModelProperty(value = "支付时间最后付款时间")
    private Date payTime;
    @ApiModelProperty(value = "商品总重")
    private Integer totalGoodsNumber;
    @ApiModelProperty(value = "订单商品总额")
    private BigDecimal totalOrderAmount;
    @ApiModelProperty(value = "订单支付心币数量")
    private Integer totalActualHb;
    @ApiModelProperty(value = "积分抵扣")
    private BigDecimal integralDeduction;
    @ApiModelProperty(value = "优惠券抵扣")
    private BigDecimal couponDeduction;
    @ApiModelProperty(value = "活动优惠金额")
    private BigDecimal activityDiscountAmont;
    @ApiModelProperty(value = "其他优惠金额")
    private BigDecimal otherDiscountAmont;
    @ApiModelProperty(value = "原运费金额")
    private BigDecimal originalFreightAmount;
    @ApiModelProperty(value = "实际运费金额")
    private BigDecimal actualFreightAmount;
    @ApiModelProperty(value = "订单总金额商品总额-积分抵扣-优惠券抵扣-活动优惠-其它优惠+实际运费")
    private BigDecimal totalActualOrderAmount;
    @ApiModelProperty(value = "实际支付总金额")
    private BigDecimal actuallyPaid;
    @ApiModelProperty(value = "待支付金额")
    private BigDecimal amountTobepaid;
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "是否需要发票标识(1-需要 0-不需要)")
    private Integer isInvoice;
    @ApiModelProperty(value = "订单留言")
    private String orderMessage;
    @ApiModelProperty(value = "订单备注")
    private String orderRemark;
    @ApiModelProperty(value = "是否锁定")
    private Integer isLocked;
    @ApiModelProperty(value = "锁定人")
    private String lockedName;
    @ApiModelProperty(value = "锁定时间")
    private Date lockedTime;
    @ApiModelProperty(value = "取消人")
    private String cancelName;
    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;
    @ApiModelProperty(value = "取消原因描述")
    private String cancelReason;
    @ApiModelProperty(value = "处方单标识")
    private Integer prescriptionSheetMark;
    @ApiModelProperty(value = "是否新品")
    private Integer isNew;
    @ApiModelProperty(value = "是否新客户")
    private Integer isNewCustomer;
    @ApiModelProperty(value = "是否推送ERP")
    private Integer isPushErp;
    @ApiModelProperty(value = "推送ERP时间")
    private Date pushErpTime;
    @ApiModelProperty(value = "是否页面下单")
    private Integer isPageOrder;
    @ApiModelProperty(value = "")
    private Integer systemCheck;
    @ApiModelProperty(value = "是否跨境订单0否1是")
    private Integer isBorderOrder;
    @ApiModelProperty(value = "订单明细列表")
    private List<OrderDetailRespDTO> detailList;
    @ApiModelProperty(value = "物流类型 0普通快递1配送上门2门店自提")
    private Integer deliveryType;
    @ApiModelProperty(value = "门店自提提货码")
    private Integer deliveryCode;
    @ApiModelProperty(value = "需求单审批状态0审批（未提交小蜜)1审批中2审批通过3审批拒绝")
    private  Integer prescriptionStatus;
    @ApiModelProperty(value = "售后申请单id")
    private String returnQuestId;
    @ApiModelProperty(value = "售后申请单对象")
    private ReturnQuestDetail returnQuest;
    @ApiModelProperty(value = "成团状态(0.成团中 1.已成团 2.成团失败)")
    private Integer isAgroup = 0;
    @ApiModelProperty(value = "团编码")
    private String  groupCode;
    @ApiModelProperty(value = "参团还是开团open:开团,follow参团")
    private String groupOperateType;
    @ApiModelProperty(value = "门店信息")
    private StoreResDTO storeResDTO;
    @ApiModelProperty(value = "团员信息")
    private OrderGroupRespDTO orderGroupRespDTO;
    @ApiModelProperty(value = "预约单：提货状态 0-调货中 1-待自提 2-自提完成")
    private Integer deliveryStatus;
    @ApiModelProperty(value = "供应商编码")
    private String spCode;
    @ApiModelProperty(value = "是否B2C订单")
    private Integer isB2cOrder;
    @ApiModelProperty(value = "是否需要补录处方申请")
    private Boolean additionPrescription;
    @ApiModelProperty(value = "补录处方申请剩余时间（毫秒）")
    private String additionPrescriptionLeftTime;
    @ApiModelProperty(value = "需求单审批信息")
    private MedicalPrescriptionApplyDTO medicalPrescriptionApplyDTO;

    @ApiModelProperty(value = "已付金额")
    private BigDecimal paidFee;

    @ApiModelProperty(value = "处方单审方后支付，0：否，1：是")
    private Integer payAfterAudit;

    @ApiModelProperty(value = "会员余额支付金额")
    private BigDecimal payBalanceAmount; 
    
    @ApiModelProperty(value = "预售订单类型，0：非预售单，1：定金预售，2：全款预售")
    private Integer presaleOrderType;
    
    @ApiModelProperty(value = "订单定金（订单明细定金总和）")
    private BigDecimal totalDepositPayAmount;

    @ApiModelProperty(value = "订单尾款(订单明细尾款总和+运费)")
    private BigDecimal totalFinalPaymentAmount;

    @ApiModelProperty(value = "预售信息")
    private OrderPresaleResDTO orderPresaleResDTO;
    
}
