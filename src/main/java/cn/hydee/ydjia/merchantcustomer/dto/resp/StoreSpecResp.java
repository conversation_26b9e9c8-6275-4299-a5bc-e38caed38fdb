package cn.hydee.ydjia.merchantcustomer.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@SuperBuilder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class StoreSpecResp {

    @ApiModelProperty("门店ID")
    private String storeId;

    @ApiModelProperty("门店商品信息")
    private List<SpecInfoResp> specInfoRespList;

    @ApiModelProperty("是否是B2C门店， 只有为true 时才是B2C")
    private Boolean isB2C;
}
