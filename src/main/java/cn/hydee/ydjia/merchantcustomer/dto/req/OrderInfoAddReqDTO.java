package cn.hydee.ydjia.merchantcustomer.dto.req;


import cn.hydee.ydjia.merchantcustomer.domain.OrderDetail;
import cn.hydee.ydjia.merchantcustomer.dto.OrderInfoExtraDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.PrescriptionApprovalDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 新增订单信息，用于提交订单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/13 16:16
 */
@Data
public class OrderInfoAddReqDTO {
 @ApiModelProperty(value = "商家编码")
 @NotEmpty(message = "商家编码不能为空")
 private String merCode;
 @ApiModelProperty(value = "订单信息不能")
 @NotEmpty(message = "订单信息不能为空")
 private  OrderInfoDTO orderInfoDTO;
 @ApiModelProperty(value = "订单明细信息")
 @NotEmpty(message = "订单明细信息不能为空")
 private List<OrderDetailDTO> orderDetailDTOS;
 @ApiModelProperty(value = "优惠券信息")
 private List<OrderCouponDTO> orderCouponDTOS;
 @ApiModelProperty(value = "活动信息")
 private List<OrderMarketActivityDTO> orderMarketActivityDTOS;

 @ApiModelProperty(value = "收货地址信息")
 private OrderDeliveryAddressDTO orderDeliveryAddressDTO;

 @ApiModelProperty(value = "订单扩展信息：服务商收款参数等")
 private OrderInfoExtraDTO orderInfoExtraDTO;

 }
