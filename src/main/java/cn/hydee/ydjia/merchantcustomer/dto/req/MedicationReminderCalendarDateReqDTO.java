package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @description:
 * @author: HuangYiBo
 * @time: 2021/4/27 14:45
 */

@Data
public class MedicationReminderCalendarDateReqDTO {

    @ApiModelProperty("商户编码")
    private String merCode;

    @ApiModelProperty(value = "会员ID")
    private Long memberId;

    @ApiModelProperty("日期")
    private String startTime;

    @ApiModelProperty("日期")
    private String endTime;
}
