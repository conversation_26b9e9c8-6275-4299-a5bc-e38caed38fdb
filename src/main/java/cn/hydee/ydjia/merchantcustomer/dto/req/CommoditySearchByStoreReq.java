package cn.hydee.ydjia.merchantcustomer.dto.req;

import cn.hydee.starter.dto.PageBase;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.CommonLoginUserDTO;
import cn.hydee.ydjia.merchantcustomer.feign.enums.YesOrNoType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description C端商品搜索
 * @date 2019/10/15 14:31
 */
@Data
public class CommoditySearchByStoreReq extends PageBase implements Serializable {
    private static final long serialVersionUID = 2710033460105652949L;
    @ApiModelProperty(value = "搜索词")
    private String keyword;
    @ApiModelProperty(value = "一级分组Id集合")
    private List<String> fTypeIds;
    @ApiModelProperty(value = "二级分组Id集合")
    private List<String> sTypeIds;
    @NotBlank(message = "门店不能为空")
    @ApiModelProperty(value = "门店")
    private String storeId;
    @NotBlank(message = "商户编码不能为空")
    private String merCode;
    @ApiModelProperty(value = "排序字段：price-价格排序; newTime-门店商品创建时间；distance-门店按距离；updateTime-门店商品更新时间；sales-销量")
    private String sortField;
    @ApiModelProperty(value = "正序/倒序，默认 true 正序")
    private Boolean asc = true;
    @ApiModelProperty(value = "活动类型", notes = "活动类型，promoteType")
    private String pmtTypeCode;
    @JsonIgnore
    @ApiModelProperty(value = "是否扩大范围搜索", notes = "当无法找到商品时扩大范围查询， 默认为false, 即不扩大搜索")
    private Boolean extendSearchFlag = false;

    @ApiModelProperty(value = "是否加载购物车商品数量", notes = "加载购物车商品数量")
    private Boolean loadCartNumFlag = false;
}
