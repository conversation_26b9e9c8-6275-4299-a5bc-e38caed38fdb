package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class EmpCustomerBatchQueryDTO {

    @NotEmpty(message = "商户编码不能为空")
    @ApiModelProperty("商户编码")
    private String merCode;

    @NotEmpty(message = "员工编码不能为空")
    @ApiModelProperty("员工编码")
    private List<String> empCodes;

    @NotEmpty(message = "会员UnionId不能为空")
    @ApiModelProperty("会员UnionId")
    private String memberUnionId;
}
