package cn.hydee.ydjia.merchantcustomer.dto;
import cn.hydee.ydjia.merchantcustomer.feign.domain.AssembleCommodityRelate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 组合商品创建对象
 *
 * <AUTHOR>
 * @date 2019/11/7
 */
@Data
public class AssembleCommodityDTO {

    @ApiModelProperty(value = "商品ID")
    private String id;

    @ApiModelProperty(value = "SKU规格ID")
    private String specId;

    @ApiModelProperty(value = "商家编码")
    @NotBlank(message = "商家编码不能为空")
    private String merCode;

    @ApiModelProperty(value = "商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String name;

    @ApiModelProperty(value = "一级分类")
    private String firstTypeId;

    @ApiModelProperty(value = "二级分类")
    private String secondTypeId;

    @ApiModelProperty(value = "三级分类")
    private String typeId;

    @ApiModelProperty(value = "第三级分组ID")
    @NotEmpty(message = "商品分组不能为空")
    private List<String> groupIds;

    @ApiModelProperty(value = "关键字")

    private String keyWord;

    @ApiModelProperty(value = "商品图片")
    @NotBlank(message = "组合商品图片不能为空")
    private String image;

    @ApiModelProperty(value = "商品重量")
    private Integer weight;

    @ApiModelProperty(value = "商品重量单位")
    private String unit;

    @ApiModelProperty(value = "商品详情")
    private String detail;

    @ApiModelProperty(value = "商品价格")
    @NotNull(message = "商品价格不能为空")
    private BigDecimal price;

    @ApiModelProperty(value = "商品参考价格")
    @NotNull(message = "商品参考价格不能为空")
    private BigDecimal mPrice;

    @ApiModelProperty(value = "限制数量")
    private Integer limitNum;

    @ApiModelProperty(value = "子商品信息")
    private List<AssembleCommodityRelate> childCommodities;

    @ApiModelProperty(value = "用户名")
    private String userName;
}
