package cn.hydee.ydjia.merchantcustomer.dto.resp.payment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021-04-28
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class MicroPayResDTO implements Serializable {

    private static final long serialVersionUID = 993495441918637426L;

    @JsonProperty(value = "return_code")
    private String returnCode;
    @JsonProperty(value = "return_msg")
    private String returnMsg;
    @JsonProperty(value = "result_code")
    private String resultCode;
    @JsonProperty(value = "err_code")
    private String errCode;
    @JsonProperty(value = "err_code_des")
    private String errCodeDes;
    @JsonProperty(value = "appid")
    private String appId;
    @JsonProperty(value = "mch_id")
    private String mchId;
    @JsonProperty(value = "device_info")
    private String deviceInfo;
    @JsonProperty(value = "nonce_str")
    private String nonceStr;
    @JsonProperty(value = "sign")
    private String sign;
    @JsonProperty(value = "openid")
    private String openId;
    @JsonProperty(value = "is_subscribe")
    private String isSubscribe;
    @JsonProperty(value = "trade_type")
    private String tradeType;
    @JsonProperty(value = "total_fee")
    private String totalFee;
    @JsonProperty(value = "transaction_id")
    private String transactionId;
    @JsonProperty(value = "out_trade_no")
    private String outTradeNo;
    @JsonProperty(value = "time_end")
    private String paymentTime;

    private String status;
}
