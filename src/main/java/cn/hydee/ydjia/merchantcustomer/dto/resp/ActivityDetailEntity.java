package cn.hydee.ydjia.merchantcustomer.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-14 15:42:50
 */
@Data
public class ActivityDetailEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动id")
    private Integer id;

    /**
     * 活动模板编码
     */
    @ApiModelProperty(value = "活动模板编码")
    private String activityTemplateCode;

    /**
     * 活动类型（0：其他类型，1：免费，2：积分，3：现金）
     */
    @ApiModelProperty(value = "活动类型（0：其他类型，1：免费，2：积分，3：现金）")
    private Integer activityType;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityDetailName;

    /**
     * 活动说明
     */
    @ApiModelProperty(value = "活动说明")
    private String activityNote;
    /**
     * 活动起始时间
     */
    @ApiModelProperty(value = "活动起始时间")
    private LocalDateTime beginTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime endTime;


    @ApiModelProperty(value = "活动营销类型,0-手动营销 1-自动营销")
    private Integer markingType;

    /**
     * 商户编号
     */
    private String merCode;

    @ApiModelProperty(value = "使用场景（1：线上，2：线下）")
    private Integer sceneRule;//使用场景（1：线上，2：线下）
    @ApiModelProperty(value = "门店规则（1：全部，2：部分）")
    private Integer shopRule;//门店规则（1：全部，2：部分）
    @ApiModelProperty(value = "商品规则（1：全部，2：部分可用，3：部分不可用）")
    private Integer productRule;//商品规则（1：全部，2：部分可用，3：部分不可用）
    @ApiModelProperty(value = "金额限制（0：无门槛）")
    private BigDecimal useRule;//金额限制（0：无门槛）
    @ApiModelProperty(value = "发放模式（1：订单支付后，2：订单完成后）")
    private Integer sendRule;//发放模式（1：订单支付后，2：订单完成后）
    @ApiModelProperty(value = "是否有效（0：否，1：是）")
    private Integer isValid;//是否有效（0：否，1：是）

    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    private String updateName;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 退回权益 1生效 2作废
     */
    @ApiModelProperty(value = "退款后是否回收权益（1：回收权益，2：不回收权益）")
    private Integer returnRule;

    /**
     * 失效状态 0 失效  1有效  默认0
     */
    @ApiModelProperty(value = "失效状态 0 失效  1有效  默认0")
    private Integer status;

    /**
     * 每个人限领礼包数量
     */
    @ApiModelProperty(value = "优惠券礼包每个人限领数量")
    private Integer everyoneLimitSum;

    /**
     * 每人次数限制（0：无限制）
     */
    @ApiModelProperty(value = "每人次数限制（0：无限制）")
    private Integer countRule;

    /**
     * 消耗积分
     */
    @ApiModelProperty(value = "消耗积分")
    private Integer integralRule;

    /**
     * 底部文案
     */
    @ApiModelProperty(value = "底部文案")
    private String bottomNote;

    @ApiModelProperty(value = "封面")
    private String cover;

    @ApiModelProperty(value = "参与模式（0：渠道，1：免费，2：积分，3：活动）")
    private Integer joinRule;

    @ApiModelProperty(value = "次数限制类型（1：每人，2：每天）")
    private Integer countType;

    @ApiModelProperty(value = "使用类型（1：商品参与，2：订单参与）")
    private Integer useType;

    @ApiModelProperty(value = "是否分享 0否 1是")
    private Integer isShare;
    @ApiModelProperty(value = "是否在领券中心展示0否1是")
    private Integer isShow;

    @ApiModelProperty("是否可退款：1可退，0不可退")
    private Integer refundRule;

    @ApiModelProperty(value = "老卡绑定会员是否享受新人礼包 0否 1是")
    private Integer isOldPartake;

    @ApiModelProperty(value = "数量")
    private Integer giftNum;
    @ApiModelProperty(value = "配送类型1门店自提，2快递配送，多个用逗号隔开(TA003,TA004时传)")
    private String deliveryType;
    @ApiModelProperty(value = "奖品领取截至时间(TA003,TA004时传)")
    private LocalDateTime prizeDeadline;

    @ApiModelProperty(value = "支付有礼特价商品是否不可用：0可用，1不可用。使用场景scene_rule为0或2即包含线下时生效")
    private Integer specialGoodsNot;

    @ApiModelProperty(value = "支付有礼促销商品是否不可用：0可用，1不可用。使用场景scene_rule为0或2即包含线下时生效")
    private Integer promotionalGoodsNot;

    @ApiModelProperty(value = "支付有礼会员价商品是否不可用：0可用，1不可用。使用场景scene_rule为0或2即包含线下时生效")
    private Integer memberPriceGoodsNot;

    @ApiModelProperty(value = "操作0:新增1:修改")
    private int process;

    @ApiModelProperty(value = "活动类型名")
    private String activityTypeName;

    @ApiModelProperty(value = "是否周期性活动：0否，1是(TA003,TA004时传)")
    private Integer isCycle;

    @ApiModelProperty(value = "阶梯id")
    private Integer levelId;

    @ApiModelProperty(value = "权益奖品id")
    private Integer payId;

    @ApiModelProperty(value = "绑定礼品的时间")
    private LocalDateTime bindGiftTime;

    @ApiModelProperty(value = "关联其他业务ID")
    private String relationId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "置顶 0 不置顶  1 置顶")
    private  Integer isTop;

    @ApiModelProperty(value = "系统自动活动(不参与校验), 0-否 1-是")
    private Integer systemAutoFlag;

    @ApiModelProperty(value = "活动子类型")
    private Integer smsSecondBizType;

    @ApiModelProperty(value = "用券订单是否参与活动（1：参与，2：不参与）")
    private Integer useCouponCanJoin;

    public boolean isAdd() {
        return process == 0;
    }

    public boolean isUpdate() {
        return process == 1;
    }
}
