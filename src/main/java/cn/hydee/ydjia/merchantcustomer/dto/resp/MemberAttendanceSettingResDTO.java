package cn.hydee.ydjia.merchantcustomer.dto.resp;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2020/6/15
 * @Param
 * @return
 */
@Data
public class MemberAttendanceSettingResDTO {

    /**
     * ID
     */
    private Integer id;

    /**
     * 纯数字6位数编码
     */
    private String merCode;

    /**
     * 签到模式1：固定模式,2：激励模式
     */
    private Byte modelType;

    /**
     * 每人每天签到赠送心币
     */
    private Integer fixedValue;

    /**
     * 连续签到，在前一天赠送数量的基础上多赠送心币数量
     */
    private Integer incrValue;

    /**
     * 连续签到X天
     */
    private Integer incrLimitRuleValue;

    /**
     * 第X天的赠送额度继续赠送
     */
    private Integer incrLimitValue;

    /**
     * 规则说明
     */
    private String remark;

    /**
     * 是否有效（0：否，1：是）
     */
    private Byte isValid;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String updateName;

    public final static Byte DISABLE = 0;
}