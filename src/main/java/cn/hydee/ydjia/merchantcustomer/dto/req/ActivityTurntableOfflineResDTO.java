package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
*<AUTHOR>
*@date 2024/5/15 10:17
*/
@Data
public class ActivityTurntableOfflineResDTO implements Serializable {

    @ApiModelProperty(value = "门店编码")
    private String storeCode;
    @ApiModelProperty(value = "用户手机号")
    private String userPhone;
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;
    @ApiModelProperty(value = "操作人名称")
    private String operatorName;
    @ApiModelProperty(value = "购物订单号")
    private String bizNo;
    @ApiModelProperty(value = "购物订单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty(value = "发放时间")
    private String lotteryTime;

    public String getLotteryTime() {
        if (lotteryTime != null) {
            lotteryTime = lotteryTime.replace("T", " ");
        }
        return lotteryTime;
    }
}
