package cn.hydee.ydjia.merchantcustomer.dto.req;

import cn.hydee.ydjia.merchantcustomer.enums.PlatformEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2021/05/21 14:30
 */
@Data
public class MaterialGroupQueryRequest {
    private String appid;
    @ApiModelProperty(value = "recommendGroup为true时需传")
    private List<Integer> labelList;
    @ApiModelProperty(value = "素材类型MaterialTypeEnum")
    private String materialType;
    private String merCode;
    @ApiModelProperty(value = "是否返回默认分组,true:返回默认分组Id false:不返回默认分组Id  默认分组ID为0")
    private boolean defaultGroup;
    @ApiModelProperty(value = "是否返回推荐分组(根据标签匹配素材标签) true:返回推荐分组Id false:不返回推荐分组Id 推荐分组ID为-1")
    private boolean recommendGroup;
    @ApiModelProperty(hidden = true)
    private PlatformEnum platformEnum;
}
