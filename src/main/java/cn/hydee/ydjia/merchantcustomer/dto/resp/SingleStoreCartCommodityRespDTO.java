package cn.hydee.ydjia.merchantcustomer.dto.resp;

import cn.hydee.ydjia.merchantcustomer.dto.resp.activity.PredThriftRespDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 单门店购物车商品响应DTO
 * 简化的单门店购物车响应格式，去除多门店相关的复杂逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class SingleStoreCartCommodityRespDTO {
    
    /**
     * 门店ID
     */
    private String storeId;
    
    /**
     * 门店名称
     */
    private String storeName;
    
    /**
     * 门店地址
     */
    private String storeAddress;
    
    /**
     * 门店电话
     */
    private String storePhone;
    
    /**
     * 门店距离（米）
     */
    private Double distance;
    
    /**
     * 是否当前门店
     */
    private Integer isCurrentStore;
    
    /**
     * 商户编码
     */
    private String merCode;
    
    /**
     * 是否有效门店
     */
    private Integer isValid;
    
    /**
     * 供应商编码（云仓门店使用）
     */
    private String spCode;
    
    /**
     * 供应商名称（云仓门店使用）
     */
    private String spName;
    
    /**
     * 中心门店ID
     */
    private String centerStoreId;
    
    /**
     * 商品列表
     */
    private List<CartCommodityRespDTO> commodities;
    
    /**
     * 选中商品数量
     */
    private Integer chooseCommodityCount;
    
    /**
     * 商品总价（选中商品）
     */
    private BigDecimal totalPrice;
    
    /**
     * 优惠前总价（选中商品）
     */
    private BigDecimal beforePrice;
    
    /**
     * 优惠金额
     */
    private BigDecimal reducePrice;
    
    /**
     * 是否支持配送
     */
    private Integer isDistribution;
    
    /**
     * 起送价格
     */
    private BigDecimal initialDeliveryPrice;
    
    /**
     * 配送天数
     */
    private Integer distributionDay;
    
    /**
     * Plus会员预计优惠信息
     */
    private PredThriftRespDTO predThriftRespDTO;
    
    /**
     * 设置价格信息
     * 根据商品列表计算总价、优惠前价格、优惠金额等
     */
    public void calculatePrices() {
        if (commodities == null || commodities.isEmpty()) {
            this.totalPrice = BigDecimal.ZERO;
            this.beforePrice = BigDecimal.ZERO;
            this.reducePrice = BigDecimal.ZERO;
            this.chooseCommodityCount = 0;
            return;
        }
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal beforeAmount = BigDecimal.ZERO;
        int chooseCount = 0;
        
        for (CartCommodityRespDTO commodity : commodities) {
            if (commodity.getChoseFlag() != null && commodity.getChoseFlag() == 1) {
                BigDecimal commodityTotal = commodity.getPrice().multiply(new BigDecimal(commodity.getCount()));
                BigDecimal commodityBefore = commodity.getBeforePrice().multiply(new BigDecimal(commodity.getCount()));
                
                totalAmount = totalAmount.add(commodityTotal);
                beforeAmount = beforeAmount.add(commodityBefore);
                chooseCount += commodity.getCount();
            }
        }
        
        this.totalPrice = totalAmount;
        this.beforePrice = beforeAmount;
        this.reducePrice = beforeAmount.subtract(totalAmount);
        this.chooseCommodityCount = chooseCount;
    }
}
