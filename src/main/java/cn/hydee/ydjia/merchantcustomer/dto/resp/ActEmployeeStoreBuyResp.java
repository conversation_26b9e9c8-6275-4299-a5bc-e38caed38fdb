package cn.hydee.ydjia.merchantcustomer.dto.resp;

import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.CommodityType;
import cn.hydee.ydjia.merchantcustomer.obc.dto.resp.PromotionTypeResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Getter
@Setter
public class ActEmployeeStoreBuyResp {

    @ApiModelProperty(value = "分类信息")
    private List<CommodityType> commodityTypeList;

    @ApiModelProperty(value = "门店信息")
    private StoreBuyInfoResp storeBuyInfoResp;

}
