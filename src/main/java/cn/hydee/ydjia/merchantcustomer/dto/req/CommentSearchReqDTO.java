package cn.hydee.ydjia.merchantcustomer.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020/06/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommentSearchReqDTO extends PageBase {
    @ApiModelProperty("商家编码")
    private String merCode;
    @ApiModelProperty("评论的会员ID")
    private Long userId;
    @ApiModelProperty("评论的会员昵称")
    private String nickName;
    @ApiModelProperty("评论的商品ID")
    private Long commodityId;
    @ApiModelProperty("评论来源，1-用户评论，2-商户导入")
    private Integer origin;
}
