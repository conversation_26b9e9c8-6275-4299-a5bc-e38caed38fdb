package cn.hydee.ydjia.merchantcustomer.dto.activity;

import cn.hydee.ydjia.merchantcustomer.dto.marketform.FormResDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 惊喜赠品活动 Response DTO
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
public class ActivitySurpriseGiftResDTO {

    @ApiModelProperty(value = "活动ID，不为空表示编辑")
    private Integer id;

    /* ========== 基础信息 ========== */

    @ApiModelProperty(value = "活动名称")
    private String activityDetailName;

    @ApiModelProperty(value = "品牌方")
    private String brandName;

    @ApiModelProperty(value = "活动规则")
    private String activityNote;

    /* ========== 奖励资源 ========== */

    @ApiModelProperty(value = "活动权益ID")
    private Integer payId;

    @ApiModelProperty(value = "赠品名称")
    private String giftName;

    @ApiModelProperty(value = "赠品图片")
    private String giftImg;

    @ApiModelProperty(value = "赠品数量")
    private Integer giftNum;

    @ApiModelProperty(value = "库存预警")
    private Integer inventoryAlert;

    /* ========== 限制条件 ========== */

    @ApiModelProperty("动态表单")
    private List<FormResDTO> formResList;

    /* ========== 列表参数 ========== */

    @ApiModelProperty(value = "已领取")
    private Integer receivedCount;

    @ApiModelProperty(value = "排序")
    private Integer sortNum;

    @JsonIgnore
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusDesc;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "修改时间")
    private String updateTime;

    @ApiModelProperty(value = "员工编号")
    private String empCode;

}
