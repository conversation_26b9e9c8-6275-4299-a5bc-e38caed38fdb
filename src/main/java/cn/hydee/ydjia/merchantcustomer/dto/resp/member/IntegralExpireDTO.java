package cn.hydee.ydjia.merchantcustomer.dto.resp.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/2 14:32
 */
@Data
public class IntegralExpireDTO {

    @ApiModelProperty(value = "过期心币数量")
    private Integer expireIntegralNum;
    @ApiModelProperty(value = "过期时间")
    private String expireTime;
    @ApiModelProperty(value = "心币余额")
    private Integer onlineIntegral;


}
