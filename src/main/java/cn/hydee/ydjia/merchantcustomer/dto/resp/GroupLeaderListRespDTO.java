package cn.hydee.ydjia.merchantcustomer.dto.resp;

import cn.hydee.ydjia.merchantcustomer.feign.domain.PmtGroupLeader;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商详页获取拼团信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/03/08 20:33
 */
@Data
public class GroupLeaderListRespDTO extends PmtGroupLeader {

    @ApiModelProperty(value = "用户是否参团1是，0未")
    private Integer userTag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentTime;

    @ApiModelProperty(value = "当前时间与活动时间时间戳")
    private Long timeStamp;
}
