package cn.hydee.ydjia.merchantcustomer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/04/07 14:07
 */
@Data
public class MerchantBaseInfoDTO {
    private String id;
    private String merCode;
    private String merName;
    private String merLogo;
    private Integer merType;
    private Integer erpType;
    private Integer areaId;
    private String area;
    @ApiModelProperty("省份")
    private String province;
    @ApiModelProperty("城市")
    private String city;
    @ApiModelProperty("详细地址")
    private String address;
    @ApiModelProperty("负责人名称")
    private String principalName;
    @ApiModelProperty("负责人邮箱")
    private String principalMail;
    @ApiModelProperty("负责人电话")
    private String principalMobile;
    @ApiModelProperty("商家状态")
    private Integer merStatus;
    @ApiModelProperty("商家介绍")
    private String merRemark;
    @ApiModelProperty("客服电话")
    private String customerMobile;
    @ApiModelProperty("经度")
    private String longitude;
    @ApiModelProperty("纬度")
    private String latitude;
    @ApiModelProperty("所属事业部")
    private String bedept;
    @ApiModelProperty("企业名称")
    private String companyName;
    @ApiModelProperty("上线门店")
    private Integer onlineStore;
    @ApiModelProperty("上线终端")
    private Integer onlineClient;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("公众号原始id")
    private String publicNoId;
    @ApiModelProperty("公众号昵称")
    private String publicNoNick;
    private String erpUrl;
    private String createName;
    private String modifyName;
}
