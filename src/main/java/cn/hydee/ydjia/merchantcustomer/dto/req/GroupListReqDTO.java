package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <Description>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/03/08 20:38
 */
@Data
public class GroupListReqDTO {
    @ApiModelProperty(value = "商家编码")
    @NotEmpty(message = "商家编码不能为空")
    private String merCode;
    @ApiModelProperty(value = "用户ID",hidden = true)
    private String userId;
    @ApiModelProperty(value = "活动ID")
    @NotEmpty(message = "活动ID不能为空")
    private String activityId;
    @ApiModelProperty(value = "门店ID")
    private String storeId;
    @NotEmpty(message = "规格ID不能为空")
    @ApiModelProperty(value = "规格ID")
    private String specId;
}
