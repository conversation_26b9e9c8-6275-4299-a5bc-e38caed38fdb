package cn.hydee.ydjia.merchantcustomer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/27 10:14
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CouponProductDetailDTO {
    @ApiModelProperty(value = "商品编码", notes = "")
    @NotNull
    private String productCode;

    @ApiModelProperty(value = "原商品金额", notes = "")
    @NotNull
    private BigDecimal payment;

    @ApiModelProperty(value = "原商品金额(减去促销优惠价)")
    @NotNull
    private BigDecimal originPayment;

    @ApiModelProperty(value = "商品类型编码", notes = "")
    private String typeCode;

    @ApiModelProperty(value = "商品类型:0：普通商品，3:组合商品")
    private Integer gainType;

    /**
     * 是否使用优惠券
     */
    private Boolean useCoupon = true;

    @ApiModelProperty(value = "优惠券核销金额")
    private BigDecimal saleAmount;
    private BigDecimal actualAmount;
    /**
     * 该商品使用的优惠券详情
     */
    private List<CouponDetail> couponDetails;

    @ApiModelProperty(value = "商品来源，1-普通商品，2-云集市")
    private Integer commodityOrigin;
    @ApiModelProperty(value = "分账标识，是否可以分账：true-是，false或null-否")
    private Boolean collectionMark;
    @ApiModelProperty(value = "商品数量")
    private Integer commodityCount;

    @ApiModelProperty(value = "是否应用付费会员价", notes = "应用了付费会员价的商品不能用券")
    private Boolean applyPaidMemberPrice;

    @Data
    public static class CouponDetail {
        private String couponCode;
        private BigDecimal saleAmount;
        private Integer cType;
    }

}
