package cn.hydee.ydjia.merchantcustomer.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2020/05/28 20:15
 */
@Data
@ToString(callSuper = true)
public class SaleOrReturnCouponRequest extends WebServiceBase {
    private String couponCode;
    private String userCard;
    private String orderNo;
    @ApiModelProperty("订单金额")
    private BigDecimal payment;
    @ApiModelProperty("营业员编码")
    private String saleCode;
    @ApiModelProperty("营业员名称")
    private String saleName;
    @ApiModelProperty("门店编码")
    private String storeCode;
    private String storeName;
    @ApiModelProperty(value = "1代表线下核销 2代表线下退回")
    private Integer couponType;
    @ApiModelProperty(value = "优惠券核销金额", notes = "")
    private BigDecimal saleAmount;

}
