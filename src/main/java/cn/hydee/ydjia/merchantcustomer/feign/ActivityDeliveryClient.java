package cn.hydee.ydjia.merchantcustomer.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.resp.ActivityDeliveryPrizeResDTO;
import cn.hydee.ydjia.merchantcustomer.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 配送有礼feign接口
 *
 * <AUTHOR>
 * @date 2020/11/3 上午9:24
 */
@FeignClient(LocalConst.MARKET_APP_NAME)
public interface ActivityDeliveryClient {

    /**
     * 查询配送有礼活动配置
     *
     * @param merCode 商户编码
     */
    @GetMapping("/${api.version}/activityDelivery/getActivityDetailList")
    ResponseBase getActivityDetailList(@RequestParam(value = "merCode") String merCode);

    /**
     * 查询配送有礼活动配置
     *
     * @param merCode 商户编码
     */
    @GetMapping("/${api.version}/activityDelivery/getActivityDeliveryPrizeList")
    ResponseBase<List<ActivityDeliveryPrizeResDTO>> getActivityDeliveryPrizeList(@RequestParam(value = "merCode") String merCode, @RequestParam(value = "storeId") String storeId);
}
