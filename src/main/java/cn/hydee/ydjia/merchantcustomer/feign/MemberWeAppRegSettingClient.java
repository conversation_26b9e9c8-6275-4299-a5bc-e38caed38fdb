package cn.hydee.ydjia.merchantcustomer.feign;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.MemberWeappRegSettingDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import static cn.hydee.ydjia.merchantcustomer.util.LocalConst.MEMBER_SERVICE;

/**
 * <AUTHOR>
 * @Description //TODO
 * @Date 2020/10/18
 * @Param
 * @return
 */
@FeignClient(name = MEMBER_SERVICE)
public interface MemberWeAppRegSettingClient {

    /**
     * @return cn.hydee.starter.dto.ResponseBase<cn.hydee.ydjia.merchantmanager.dto.MemberCardDTO>
     * <AUTHOR>
     * @Description //TODO
     * @Date 2020/10/18
     * @Param [merCode]
     */
    @GetMapping("/${api.version}/weappRegSetting/queryWeappRegSetting")
    ResponseBase<MemberWeappRegSettingDTO> queryWeappRegSetting(@RequestParam("merCode") String merCode);
}
