package cn.hydee.ydjia.merchantcustomer.config;

import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.ivs.v2.IvsClient;
import com.huaweicloud.sdk.ivs.v2.region.IvsRegion;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <p>Title: 华为云配置</p>
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "huawei-cloud")
public class HuaWeiCloudConfiguration {

    /**
     * 华为云AK
     */
    private String accessKeyId;
    /**
     * 华为云SK
     */
    private String secretAccessKey;
    /**
     * 企业项目ID
     */
    private String projectId;

    /**
     * 初始化客户端
     * @return 华为云客户端
     */
    @Bean
    public IvsClient getClient() {
        return IvsClient.newBuilder()
                .withCredential(this.getCredential())
                .withRegion(IvsRegion.valueOf("cn-north-4"))
                .build();
    }

    /**
     * 创建AK、SK认证凭据
     */
    private ICredential getCredential() {
        return new BasicCredentials()
                .withAk(accessKeyId)
                .withSk(secretAccessKey)
                .withProjectId(projectId);
    }

}
