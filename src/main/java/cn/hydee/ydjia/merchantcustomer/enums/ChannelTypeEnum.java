package cn.hydee.ydjia.merchantcustomer.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

@Getter
public enum ChannelTypeEnum {

//    /**
//     * 自主开方。
//     */
//    OWNER("2000", "owner", "自主开方"),
//
//    /**
//     * 自有处方。
//     */
//    SELF("3000", "self", "自有处方"),

    /**
     * 微问诊。
     */
    MICRO_WZ("1401", "microWz", "微问诊"),

    /**
     * 莲藕。
     */
    LOTUS("1402", "lotus", "莲藕"),

    /**
     * 平安。
     */
    PINGAN("1403", "pingAn", "平安"),

    /**
     * 药店云。
     */
    PINGAN_YDY("1405", "pingAnYdy", "药店云"),

    /**
     * 桃子健康。
     */
    PEACH("1406", "peach", "桃子健康");


    /**
     * 编码。
     */
    private String code;

    /**
     * 英文名。
     */
    private String enName;

    /**
     * 描述。
     */
    private String desc;

    ChannelTypeEnum(String code, String enName, String desc) {
        this.code = code;
        this.enName = enName;
        this.desc = desc;
    }

    /**
     * 该渠道是否为三方开方平台
     * @return
     */
    public static boolean isThird(String channelEnName) {
        if (!StringUtils.hasLength(channelEnName)) {
            return false;
        }
        for (ChannelTypeEnum value : ChannelTypeEnum.values()) {
            if (value.enName.equals(channelEnName)) {
                return true;
            }
        }
        return false;
    }
}
