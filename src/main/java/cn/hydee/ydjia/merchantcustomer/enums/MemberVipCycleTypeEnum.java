package cn.hydee.ydjia.merchantcustomer.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 付费会员卡周期类型
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MemberVipCycleTypeEnum {

    /**
     * 付费会员卡周期枚举
     */
    MONTH(1, "月"),
    SEASON(2, "季"),
    YEAR(3, "年");

    /**
     * 周期举值
     */
    private final Integer code;

    /**
     * 周期值描述
     */
    private final String msg;

    public static String getCode(Integer code) {

        return Arrays.stream(MemberVipCycleTypeEnum.values()).filter(cycle -> cycle.getCode().equals(code)).map(MemberVipCycleTypeEnum::getMsg).findFirst().orElse("");
    }
}
