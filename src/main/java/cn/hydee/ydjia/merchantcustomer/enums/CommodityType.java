package cn.hydee.ydjia.merchantcustomer.enums;

/**
 * 商品类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/14 14:58
 */
public enum CommodityType {

    NORMAL(1, "普通商品"),
    Combined_commodity(2, "组合商品"),
    GIFT_COMMODITY(3, "赠品"),
    ;

    private Integer code;
    private String msg;

    CommodityType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
