package cn.hydee.ydjia.merchantcustomer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.concurrent.TimeUnit;

@AllArgsConstructor
@Getter
public enum CacheEnum {

    _store_rule("cache:store:rule:{}", "门店规则", 30, TimeUnit.MINUTES),
    _mercode_config("cache:mercode:appconfig:{}:{}", "集团化套餐设置", 30, TimeUnit.MINUTES),
    _center_store("cache:ship:store:{}", "商户中心店", 30, TimeUnit.MINUTES),
    _store_info("cache:store:info:{}", "门店信息", 30, TimeUnit.MINUTES),
    _store_info_v3("cache:store:infov3:{}", "门店信息", 30, TimeUnit.MINUTES),
    _store_info_code("cache:store:info:code:{}", "门店信息", 30, TimeUnit.MINUTES),
    _store_cer_info("cache:store:cer:{}", "门店资质", 30, TimeUnit.MINUTES),
    _store_fun("cache:store:fun:{}", "门店服务信息", 30, TimeUnit.MINUTES),
    _commodity_config_comment("cache:commodity:config:comment:{}", "商品评论配置", 30, TimeUnit.MINUTES),
    _commodity_intro("cache:commodity:intro:{}", "商品评论配置", 30, TimeUnit.MINUTES),
    _mall_config("cache:mall:config:{}:{}:{}", "小城首页配置", 30, TimeUnit.MINUTES),
    _survey_config("cache:survey:config:{}", "问卷缓存", 30, TimeUnit.MINUTES),

    ;
    /**
     * 模板key
     */
    private String templateKey;
    /**
     * 描述
     */
    private String desc;
    /**
     * 有效期
     */
    private int ttl;
    /**
     * 有效期单位
     */
    private TimeUnit timeUnit;

}
