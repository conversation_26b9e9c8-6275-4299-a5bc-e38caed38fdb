package cn.hydee.ydjia.merchantcustomer.enums;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2022/11/1
 */
public enum CouponDeliveryTypeEnum {

    COUPON_DELIVERY(1, "普通快递", 0),
    COUPON_DISTRIBUTION(2, "配送上门", 1),
    COUPON_STORE_SELF(3, "门店自提", 2),
    ;

    private Integer code;
    private String msg;
    private Integer deliveryType;

    CouponDeliveryTypeEnum(Integer code, String msg, Integer deliveryType) {
        this.code = code;
        this.msg = msg;
        this.deliveryType = deliveryType;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public static CouponDeliveryTypeEnum getItemByDeliveryType(Integer deliveryType) {
        for (CouponDeliveryTypeEnum item : values()) {
            if (item.getDeliveryType().equals(deliveryType)) {
                return item;
            }
        }
        return null;
    }

}
