package cn.hydee.ydjia.merchantcustomer.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName UserSwich.java
 * @Description
 * @createTime 2020年10月28日 14:06:00
 */
@Data
public class UserSwitch {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 状态 0-未启用 1-启用
     */
    private Integer status;
    /**
     * 类型 1-分销协议
     */
    private Integer type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
