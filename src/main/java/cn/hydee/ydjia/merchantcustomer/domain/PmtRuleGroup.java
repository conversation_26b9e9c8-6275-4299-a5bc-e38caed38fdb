package cn.hydee.ydjia.merchantcustomer.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 拼团规则表
 * </p>
 * <AUTHOR>
 * @since 2020-04-11
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PmtRuleGroup {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "成团有效时间")
    private Integer effectiveTime;

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "商品规格ID")
    private Long specId;

    @ApiModelProperty(value = "商品拼团价")
    private BigDecimal activityPrice;

    @ApiModelProperty(value = "商品活动库存")
    private Integer productActivityCount;

    @ApiModelProperty(value = "参团限制次数")
    private Integer addLimitTimes;

    @ApiModelProperty(value = "参团限制次数")
    private Integer openLimitTimes;

    @ApiModelProperty(value = "是否包邮(0不包邮,1包邮)")
    private Integer isFreeshipping;

    @ApiModelProperty(value = "单次限购份数")
    private Integer limitCount;

    @ApiModelProperty(value = "成团人数")
    private Integer activityNumber;

    @ApiModelProperty(value = "商品排序")
    private Integer sortNumber;

    @ApiModelProperty(value = "用来记录活动库存总量，不随库存扣除而变化")
    private Integer productActivityAllcount;

    @ApiModelProperty(value = "末次修改时间")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "修改人")
    private String modifyName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "是否删除")
    @TableLogic(value = "1", delval = "0")
    private Integer isvalid;


}
