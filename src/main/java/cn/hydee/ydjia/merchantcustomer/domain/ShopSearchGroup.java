package cn.hydee.ydjia.merchantcustomer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "shop_search_group")
public class ShopSearchGroup {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 分组名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 1使用、0停用
     */
    @TableField(value = "use_flag")
    private Integer useFlag;
    /**
     * 自定义有效开始时间
     */
    @TableField(value = "time_start")
    private Date timeStart;
    /**
     * 自定义有效结束时间
     */
    @TableField(value = "time_end")
    private Date timeEnd;
    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;
    @TableField(value = "create_time")
    private Date createTime;
    @TableField(value = "create_name")
    private String createName;
    @TableField(value = "modify_time")
    private Date modifyTime;
    @TableField(value = "modify_name")
    private String modifyName;
}
