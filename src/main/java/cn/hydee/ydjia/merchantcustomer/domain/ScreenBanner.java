package cn.hydee.ydjia.merchantcustomer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大屏开屏banner
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/28
 */
@Data
@TableName("screen_banner")
public class ScreenBanner implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * banner图地址
     */
    private String imgUrl;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private Date modifyTime;

}
