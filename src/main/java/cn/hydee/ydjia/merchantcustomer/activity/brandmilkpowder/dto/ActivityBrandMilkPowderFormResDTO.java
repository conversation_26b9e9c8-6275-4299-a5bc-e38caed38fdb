package cn.hydee.ydjia.merchantcustomer.activity.brandmilkpowder.dto;

import cn.hydee.ydjia.merchantcustomer.dto.ActivityPayResDTO;
import cn.hydee.ydjia.merchantcustomer.dto.marketform.FormResDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Desc:
 * 母婴活动-品牌奶粉活动详情(C端)响应DTO
 *
 * <AUTHOR>
 * @since 2024/5/31 14:00
 */
@Data
public class ActivityBrandMilkPowderFormResDTO implements Serializable {

    private static final long serialVersionUID = -8973195019264544587L;

    /** 奶粉品牌 */
    @ApiModelProperty("奶粉品牌名称")
    private String brandName;

    /** 活动要求 */
    @ApiModelProperty("活动要求")
    private String activityNote;

    /** 活动状态 */
    @ApiModelProperty("活动状态 14 启用 15 禁用")
    private Integer status;

    /** 奖励资源 */
    @ApiModelProperty("奖励资源")
    private ActivityPayResDTO activityPayDTO;

    /** 动态表单列表 */
    @ApiModelProperty("动态表单列表")
    private List<FormResDTO> formDTOList;

}
