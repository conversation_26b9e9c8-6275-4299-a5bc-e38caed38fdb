package cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon;

import java.io.Serializable;
import java.util.Map;



/**
 * 优惠券校验商品信息
 * <AUTHOR>
 *
 */
public class IdprefixCheckGoodsInfo implements Serializable{
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 6681472191482678115L;

	private String idprefix;//优惠券码
	
	private Map<String,InterCEnum> checkGoodsInfo;//校验的每个商品编码信息，key为商品编码，value为对应的校验结果信息

	public String getIdprefix() {
		return idprefix;
	}

	public void setIdprefix(String idprefix) {
		this.idprefix = idprefix;
	}

	public Map<String, InterCEnum> getCheckGoodsInfo() {
		return checkGoodsInfo;
	}

	public void setCheckGoodsInfo(Map<String, InterCEnum> checkGoodsInfo) {
		this.checkGoodsInfo = checkGoodsInfo;
	}

	@Override
	public String toString() {
		return "IdprefixCheckGoodsInfo [idprefix=" + idprefix + ", checkGoodsInfo=" + checkGoodsInfo + "]";
	}
	
	
	
	

}
