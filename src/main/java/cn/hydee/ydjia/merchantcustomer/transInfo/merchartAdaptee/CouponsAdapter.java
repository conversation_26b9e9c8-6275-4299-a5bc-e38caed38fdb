package cn.hydee.ydjia.merchantcustomer.transInfo.merchartAdaptee;

import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.feign.enums.ErrorType;
import cn.hydee.ydjia.merchantcustomer.transInfo.dto.Message;
import cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon.*;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CouponsAdapter extends BaseMarketingHttp {

    /**
     * 获取优惠券规则接口
     * <AUTHOR>
     * @date 14:47 2019/12/16
     * @param merCode 商家编码
     * @param userId 会员ID
     * @param storeCodes 门店编码
     * @param goodsCodes 商品编码
     **/
    public List<MerCouponNew> getCouponsRuleInfo(String merCode, String userId,
                                                 List<String> storeCodes,
                                                 List<String> goodsCodes) {
        CouponsRuleReq req = new CouponsRuleReq(icConfig.getTokenKey());
        req.setGoodsCodes(goodsCodes);
        req.setStoreCodes(storeCodes);
        req.setUserId(userId);
        req.setMerCode(merCode);
        String url = marketUrl(icConfig.getCouponsRuleInfo());
        return messagePostRevert(url,req,new TypeReference<Message<List<MerCouponNew>>>() {});
    }

    /**
     * 生成优惠券规则
     * <AUTHOR>
     * @date 14:47 2019/12/16
     * @param merCode 商家编码
     * @param userId 会员ID
     * @param couponId 优惠券规则id
     * @param cbDesc 业务描述
     **/
    public boolean couponsTake(String merCode, String userId,
                                      String couponId, String cbDesc) {
        CouponTakeReq req = new CouponTakeReq(icConfig.getTokenKey());
        req.setCbDesc(cbDesc);
        req.setCouponId(couponId);
        req.setMerCode(merCode);
        req.setUserId(userId);
        String url = marketUrl(icConfig.getCouponsTake());
        Message<CouponTakeResp> result = postToReference(url, req, null,
                new TypeReference<Message<CouponTakeResp>>() {});
        boolean res = result != null && result.isSuccess();
        if (res && result.getData() != null) {
            res = result.getData().isSuccess();
        } else {
            String msg = result == null ? cn.hydee.starter.dto.ErrorType.SERVICE_TEMP_ERROR.getMsg():result.getMsg();
            throw WarnException.builder().code(ErrorType.COUPONS_TAKE_ERROR.getCode()).
                    tipMessage(msg).build();
        }
        return res;
    }

    /**
     * 查会员已领取且可使用的优惠券码 （备注：返回券码和规则）
     * <AUTHOR>
     * @date 14:47 2019/12/16
     * @param merCode 商家编码
     * @param userId 会员ID
     * @param storeCodes 门店编码
     * @param goodsInfos 商品编码,与价格信息，如果价格不为数字会报错
     **/
    public List<CouponIdprefix> couponsTakeInfo(String merCode, String userId,
                                                List<String> storeCodes,
                                                Map<String,String> goodsInfos) {
        CouponTakeInfoReq req = new CouponTakeInfoReq(icConfig.getTokenKey());
        req.setMerCode(merCode);
        req.setUserId(userId);
        req.setStoreCodes(storeCodes);
        req.setGoodsInfos(goodsInfos);
        String url = marketUrl(icConfig.getCouponsTakeInfo());
        return messagePostRevert(url,req,new TypeReference<Message<List<CouponIdprefix>>>() {});
    }

    /**
     * 批量优惠券码查单个优惠券规则信息
     * <AUTHOR>
     * @date 14:47 2019/12/16
     * @param merCode 商家编码
     * @param idPrefixs 优惠券券码集合
     **/
    public List<CouponIdprefix> couponsRuleByIds(String merCode,
                                                 List<String> idPrefixs) {
        CouponsRuleIdsReq req = new CouponsRuleIdsReq(icConfig.getTokenKey());
        req.setIdPrefixs(idPrefixs);
        req.setMerCode(merCode);
        String url = marketUrl(icConfig.getCouponsRuleByIDS());
        return messagePostRevert(url,req,new TypeReference<Message<List<CouponIdprefix>>>() {});
    }

    /**
     * 使用优惠券
     * <AUTHOR>
     * @date 14:47 2019/12/16
     * @param merCode 商家编码
     * @param idPrefix 优惠券码
     **/
    public boolean couponsUsed(String merCode,List<String> idPrefix) {
        return couponsUseAndReturn(merCode,idPrefix,2);
    }

    /**
     * 退回优惠券
     * <AUTHOR>
     * @date 14:47 2019/12/16
     * @param merCode 商家编码
     * @param idPrefix 优惠券码
     **/
    public boolean couponsReturn(String merCode,List<String> idPrefix) {
        return couponsUseAndReturn(merCode,idPrefix,1);
    }

    private boolean couponsUseAndReturn(String merCode,List<String> idPrefix,int status) {
        CouponsUseReq req = new CouponsUseReq(icConfig.getTokenKey());
        req.setIdPrefix(idPrefix);
        req.setMerCode(merCode);
        req.setStatus(status);
        String url = marketUrl(icConfig.getCouponsStatus());
        Message<List<CouponUseResp>> result = postToReference(url, req,null,new TypeReference<Message<List<CouponUseResp>>>() {});
        boolean res = result != null && result.isSuccess();
        if (res && !CollectionUtils.isEmpty(result.getData())) {
            for (CouponUseResp resp : result.getData()) {
                res = resp.isSuccess();
                if (!res) {
                    break;
                }
            }
        }
        if (!res || CollectionUtils.isEmpty(result.getData())) {
            String msg = result == null ? cn.hydee.starter.dto.ErrorType.SERVICE_TEMP_ERROR.getMsg():result.getMsg();
            String code = cn.hydee.starter.dto.ErrorType.OPERATOR_ERROR.getCode();
            if (status == 1) {
                code = ErrorType.COUPONS_RETURN_ERROR.getCode();
            } else if(status == 2) {
                code = ErrorType.COUPONS_USER_ERROR.getCode();
            }
            throw WarnException.builder().code(code).tipMessage(msg).build();
        }
        return true;
    }

    /**
     * 商品是否符合优惠券规则校验
     */
    public List<IdprefixCheckGoodsInfo> couponsCheck(String merCode,
                                                     List<String> idPrefixs,
                                                     Map<String,String> goodsPrice) {
        CouponCheckReq req = new CouponCheckReq(icConfig.getTokenKey());
        req.setMerCode(merCode);
        req.setIdPrefixList(idPrefixs);
        req.setGoodsInfos(goodsPrice);
        String url = marketUrl(icConfig.getCouponsCheck());
        return messagePostRevert(url,req,new TypeReference<Message<List<IdprefixCheckGoodsInfo>>>() {});
    }
}
