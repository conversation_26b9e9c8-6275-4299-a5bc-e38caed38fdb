package cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon;

import cn.hydee.ydjia.merchantcustomer.transInfo.dto.BaseMarketReq;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 使用优惠券
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/16 14:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CouponsUseReq extends BaseMarketReq {
    @JsonProperty("mercode")
    private String merCode;
    @JsonProperty("idprefixs")
    private List<String> idPrefix;
    private int status;

    public CouponsUseReq(String tokenKey) {
        super(tokenKey);
    }
}
