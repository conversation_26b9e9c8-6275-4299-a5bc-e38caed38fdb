package cn.hydee.ydjia.merchantcustomer.transInfo.dto.coupon;

import cn.hydee.ydjia.merchantcustomer.transInfo.dto.BaseMarketReq;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 商品是否符合优惠券规则校验
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/16 14:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CouponCheckReq extends BaseMarketReq {
    @JsonProperty("mercode")
    private String merCode;
    @JsonProperty("idprefixs")
    private List<String> idPrefixList;
    private Map<String,String> goodsInfos;

    public CouponCheckReq(String tokenKey) {
        super(tokenKey);
    }
}
