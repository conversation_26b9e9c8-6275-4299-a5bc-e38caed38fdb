package cn.hydee.ydjia.merchantcustomer.transInfo.dto.honey;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 处方单状态
 * @mg
 * **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class PreSaleOrderStatus {
    /**
     * 处方单号
     * **/
    private String  cfNo;
    /**
     * 0-未审核，1-已审核，2-已确认，3-已作废，4-已完成,5-待提交
     * **/
    private int     presStatus;
    /**
     * 处方编号
     * **/
    private String  presNo;
    /**
     * 带token的七牛云图片地址
     * **/
    private String  presPic;
    /**
     * 姓名
     * **/
    private String  buyer;
    /**
     * 年龄
     * **/
    private String  buyerAge;
    /**
     * 性别-男，女，未知
     * **/
    private String  buyerSex;
    /**
     * 身份证号
     * **/
    private String  idcard;
    /**
     * 管理员
     * **/
    private String  auditorName;
    /**
     * 专项库：处方不合理，作废
     * **/
    private String  auditorSuggestion;
    /**
     * 外部处方单号
     * **/
    private String  outPresNo;
    /**
     * 医院医生-开处方单的医生
     * **/
    private String  maker;

    public String getCfNo() {
        return cfNo;
    }

    public void setCfNo(String cfNo) {
        this.cfNo = cfNo;
    }

    public int getPresStatus() {
        return presStatus;
    }

    public void setPresStatus(int presStatus) {
        this.presStatus = presStatus;
    }

    public String getPresNo() {
        return presNo;
    }

    public void setPresNo(String presNo) {
        this.presNo = presNo;
    }

    public String getPresPic() {
        return presPic;
    }

    public void setPresPic(String presPic) {
        this.presPic = presPic;
    }

    public String getBuyer() {
        return buyer;
    }

    public void setBuyer(String buyer) {
        this.buyer = buyer;
    }

    public String getBuyerAge() {
        return buyerAge;
    }

    public void setBuyerAge(String buyerAge) {
        this.buyerAge = buyerAge;
    }

    public String getBuyerSex() {
        return buyerSex;
    }

    public void setBuyerSex(String buyerSex) {
        this.buyerSex = buyerSex;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getAuditorSuggestion() {
        return auditorSuggestion;
    }

    public void setAuditorSuggestion(String auditorSuggestion) {
        this.auditorSuggestion = auditorSuggestion;
    }

    public String getOutPresNo() {
        return outPresNo;
    }

    public void setOutPresNo(String outPresNo) {
        this.outPresNo = outPresNo;
    }

    public String getMaker() {
        return maker;
    }

    public void setMaker(String maker) {
        this.maker = maker;
    }

    @Override
    public String toString() {
        return "PreSaleOrderStatus{" +
                "cfNo='" + cfNo + '\'' +
                ", presStatus=" + presStatus +
                ", presNo='" + presNo + '\'' +
                ", presPic='" + presPic + '\'' +
                ", buyer='" + buyer + '\'' +
                ", buyerAge='" + buyerAge + '\'' +
                ", buyerSex='" + buyerSex + '\'' +
                ", idcard='" + idcard + '\'' +
                ", auditorName='" + auditorName + '\'' +
                ", auditorSuggestion='" + auditorSuggestion + '\'' +
                ", outPresNo='" + outPresNo + '\'' +
                ", maker='" + maker + '\'' +
                '}';
    }
}
