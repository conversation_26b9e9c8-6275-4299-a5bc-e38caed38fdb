package cn.hydee.ydjia.merchantcustomer.controller.v2;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.service.v2.ShoppingCartHandleServiceV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 购物车V2控制器
 * 提供重构后的购物车相关接口
 * 
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v2/shopping-cart")
@Api(tags = "购物车V2接口")
public class ShoppingCartV2Controller {
    
    @Autowired
    private ShoppingCartHandleServiceV2 shoppingCartHandleServiceV2;
    
    @ApiOperation(value = "获取购物车商品信息V2", notes = "重构后的购物车商品处理接口，提供更好的性能和可维护性")
    @PostMapping("/commodities")
    public ResponseBase<CartCommodityRespCommonDTO> getCartCommoditiesV2(
            @Validated @RequestBody ShoppingCartCommodityReqDTO request) {
        
        try {
            log.info("购物车V2接口调用开始，用户：{}", request.getUserId());
            
            CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
            
            log.info("购物车V2接口调用成功，用户：{}", request.getUserId());
            
            return ResponseBase.success(result);
            
        } catch (IllegalArgumentException e) {
            log.warn("购物车V2接口参数错误，用户：{}，错误：{}", request.getUserId(), e.getMessage());
            return ResponseBase.fail("参数错误：" + e.getMessage());
            
        } catch (Exception e) {
            log.error("购物车V2接口调用失败，用户：{}", request.getUserId(), e);
            return ResponseBase.fail("系统错误，请稍后重试");
        }
    }
}
