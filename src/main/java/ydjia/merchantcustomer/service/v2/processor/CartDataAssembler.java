package cn.hydee.ydjia.merchantcustomer.service.v2.processor;

import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.CartCommodityRespCommonDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;

import java.util.List;

/**
 * 购物车数据组装器
 * 负责将处理后的数据组装成最终的响应格式
 * 
 * <AUTHOR>
 * @version 2.0
 */
public interface CartDataAssembler {
    
    /**
     * 组装购物车响应数据
     * 
     * 功能包括：
     * 1. 按门店分组商品
     * 2. 组装商品详细信息
     * 3. 设置活动信息
     * 4. 计算总价和优惠
     * 5. 处理特殊标记和状态
     * 
     * @param productData 商品数据
     * @param activitySpecs 活动规格列表
     * @param request 请求参数
     * @return 组装后的响应数据
     */
    CartCommodityRespCommonDTO assembleResponse(ShoppingCartProductHandleDTO productData,
                                               List<ActivitySpecDTO> activitySpecs,
                                               ShoppingCartCommodityReqDTO request);
    
    /**
     * 处理处方药合规信息
     * 
     * @param response 响应数据
     * @param merCode 商户编码
     */
    void handlePrescriptionDrugCompliance(CartCommodityRespCommonDTO response, String merCode);
    
    /**
     * 创建空响应（当无有效商品时）
     * 
     * @param request 请求参数
     * @return 空响应数据
     */
    CartCommodityRespCommonDTO createEmptyResponse(ShoppingCartCommodityReqDTO request);
    
    /**
     * 设置价格信息
     * 
     * @param response 响应数据
     */
    void setPriceInfo(CartCommodityRespCommonDTO response);
}
