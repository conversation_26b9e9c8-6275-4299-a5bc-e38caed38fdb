package cn.hydee.ydjia.merchantcustomer.service.v2.processor;

import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.PriceDiscountParamDTO;

import java.util.List;

/**
 * 购物车价格计算器
 * 负责各种价格的计算和处理
 * 
 * <AUTHOR>
 * @version 2.0
 */
public interface CartPriceCalculator {
    
    /**
     * 创建价格优惠参数
     * 
     * @param request 请求参数
     * @return 价格优惠参数
     */
    PriceDiscountParamDTO createPriceDiscountParam(ShoppingCartCommodityReqDTO request);
    
    /**
     * 计算商品最终价格
     * 
     * 考虑因素：
     * 1. 商品原价
     * 2. 会员价格
     * 3. 促销活动价格
     * 4. 优惠券优惠
     * 5. 会员权益优惠
     * 
     * @param productData 商品数据
     * @param activitySpecs 活动规格列表
     * @param request 请求参数
     */
    void calculateFinalPrices(ShoppingCartProductHandleDTO productData,
                             List<ActivitySpecDTO> activitySpecs,
                             ShoppingCartCommodityReqDTO request);
    
    /**
     * 处理VIP价格
     * 
     * @param productData 商品数据
     * @param isVip 是否VIP用户
     */
    void handleVipPricing(ShoppingCartProductHandleDTO productData, boolean isVip);
    
    /**
     * 计算预计节省金额
     * 
     * @param activitySpecs 活动规格列表
     * @param request 请求参数
     */
    void calculateEstimatedSavings(List<ActivitySpecDTO> activitySpecs,
                                  ShoppingCartCommodityReqDTO request);
}
