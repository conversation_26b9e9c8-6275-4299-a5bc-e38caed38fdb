package cn.hydee.ydjia.merchantcustomer.service.v2.impl;

import cn.hydee.ydjia.merchantcustomer.dto.req.ActivitySpecDTO;
import cn.hydee.ydjia.merchantcustomer.dto.req.shoppingcart.ShoppingCartCommodityReqDTO;
import cn.hydee.ydjia.merchantcustomer.dto.resp.shoppingcart.ShoppingCartProductHandleDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.activity.PriceDiscountParamDTO;
import cn.hydee.ydjia.merchantcustomer.service.MemberInfoService;
import cn.hydee.ydjia.merchantcustomer.service.v2.processor.CartPriceCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 购物车价格计算器实现
 * 
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
@Component
public class CartPriceCalculatorImpl implements CartPriceCalculator {
    
    @Autowired
    private MemberInfoService memberInfoService;
    
    @Override
    public PriceDiscountParamDTO createPriceDiscountParam(ShoppingCartCommodityReqDTO request) {
        try {
            log.info("创建价格优惠参数，用户：{}", request.getUserId());
            
            PriceDiscountParamDTO priceDiscountParam = new PriceDiscountParamDTO();
            
            if (request.getLoginUser() != null) {
                // 复制登录用户信息
                BeanUtils.copyProperties(request.getLoginUser(), priceDiscountParam);
                priceDiscountParam.setUserId(request.getLoginUser().getUserId());
            } else {
                // 设置基础信息
                priceDiscountParam.setUserId(request.getUserId());
                priceDiscountParam.setMerCode(request.getMerCode());
            }
            
            // 设置其他必要参数
            priceDiscountParam.setAppletAppId(request.getAppletAppId());
            
            log.info("价格优惠参数创建完成，用户：{}", request.getUserId());
            
            return priceDiscountParam;
            
        } catch (Exception e) {
            log.error("创建价格优惠参数失败，用户：{}", request.getUserId(), e);
            throw new RuntimeException("创建价格参数失败", e);
        }
    }
    
    @Override
    public void calculateFinalPrices(ShoppingCartProductHandleDTO productData,
                                    List<ActivitySpecDTO> activitySpecs,
                                    ShoppingCartCommodityReqDTO request) {
        try {
            log.info("开始计算商品最终价格，用户：{}", request.getUserId());
            
            if (productData == null || CollectionUtils.isEmpty(productData.getRedisSpecs())) {
                log.warn("商品数据为空，跳过价格计算，用户：{}", request.getUserId());
                return;
            }
            
            // 1. 获取会员信息
            boolean isVip = getMemberInfo(request.getUserId());
            
            // 2. 处理VIP价格
            handleVipPricing(productData, isVip);
            
            // 3. 应用活动价格
            applyActivityPrices(productData, activitySpecs);
            
            // 4. 计算预计节省金额
            calculateEstimatedSavings(activitySpecs, request);
            
            log.info("商品最终价格计算完成，用户：{}", request.getUserId());
            
        } catch (Exception e) {
            log.error("计算商品最终价格失败，用户：{}", request.getUserId(), e);
            throw new RuntimeException("价格计算失败", e);
        }
    }
    
    @Override
    public void handleVipPricing(ShoppingCartProductHandleDTO productData, boolean isVip) {
        try {
            if (!isVip || productData == null || CollectionUtils.isEmpty(productData.getStoreSpecs())) {
                return;
            }
            
            log.info("开始处理VIP价格，商品数量：{}", productData.getStoreSpecs().size());
            
            // 遍历商品规格，应用VIP价格
            productData.getStoreSpecs().forEach(storeSpec -> {
                if (storeSpec.getVipPrice() != null && 
                    storeSpec.getVipPrice().compareTo(storeSpec.getPrice()) < 0) {
                    // 应用VIP价格
                    storeSpec.setOriginalPrice(storeSpec.getPrice());
                    storeSpec.setPrice(storeSpec.getVipPrice());
                    log.debug("应用VIP价格，商品：{}，原价：{}，VIP价：{}", 
                            storeSpec.getSpecId(), storeSpec.getOriginalPrice(), storeSpec.getVipPrice());
                }
            });
            
            log.info("VIP价格处理完成");
            
        } catch (Exception e) {
            log.error("处理VIP价格失败", e);
            // VIP价格处理失败不影响主流程
        }
    }
    
    @Override
    public void calculateEstimatedSavings(List<ActivitySpecDTO> activitySpecs,
                                         ShoppingCartCommodityReqDTO request) {
        try {
            if (CollectionUtils.isEmpty(activitySpecs)) {
                return;
            }
            
            log.info("开始计算预计节省金额，用户：{}", request.getUserId());
            
            // 这里应该实现具体的节省金额计算逻辑
            // 包括：
            // 1. 促销活动节省
            // 2. 会员优惠节省
            // 3. 优惠券节省
            // 4. Plus会员节省
            
            log.info("预计节省金额计算完成，用户：{}", request.getUserId());
            
        } catch (Exception e) {
            log.error("计算预计节省金额失败，用户：{}", request.getUserId(), e);
            // 节省金额计算失败不影响主流程
        }
    }
    
    /**
     * 获取会员信息
     */
    private boolean getMemberInfo(String userId) {
        try {
            return memberInfoService.getMemberBaseInfo(Long.valueOf(userId));
        } catch (Exception e) {
            log.error("获取会员信息失败，用户：{}", userId, e);
            return false;
        }
    }
    
    /**
     * 应用活动价格
     */
    private void applyActivityPrices(ShoppingCartProductHandleDTO productData,
                                    List<ActivitySpecDTO> activitySpecs) {
        try {
            if (CollectionUtils.isEmpty(activitySpecs)) {
                return;
            }
            
            log.info("开始应用活动价格，活动数量：{}", activitySpecs.size());
            
            // 这里应该实现具体的活动价格应用逻辑
            // 包括：
            // 1. 特惠价格
            // 2. 满减优惠
            // 3. 加价购价格
            // 4. 限时优惠价格
            
            log.info("活动价格应用完成");
            
        } catch (Exception e) {
            log.error("应用活动价格失败", e);
            // 活动价格应用失败不影响主流程
        }
    }
}
