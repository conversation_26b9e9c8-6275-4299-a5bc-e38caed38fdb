# 购物车服务V2重构 - 详细实现指南

## 📋 目录

1. [重构概述](#重构概述)
2. [架构设计](#架构设计)
3. [核心组件详解](#核心组件详解)
4. [实现细节](#实现细节)
5. [使用指南](#使用指南)
6. [测试策略](#测试策略)
7. [部署指南](#部署指南)
8. [性能优化](#性能优化)
9. [故障排查](#故障排查)

## 🎯 重构概述

### 重构目标
- **提高代码可读性**: 将100+行的大方法拆分为职责单一的小方法
- **增强可维护性**: 通过分层架构和模块化设计降低耦合度
- **改善可测试性**: 每个组件都可以独立测试
- **提升性能**: 优化处理流程，减少不必要的计算
- **增强错误处理**: 提供更好的异常处理和日志记录

### 重构前后对比

| 方面 | V1版本 | V2版本 |
|------|--------|--------|
| 方法长度 | 100+行 | 10-30行/方法 |
| 职责分离 | 单一大方法 | 4个专门处理器 |
| 错误处理 | 基础异常捕获 | 分层异常处理 |
| 可测试性 | 难以单元测试 | 每个组件可独立测试 |
| 可扩展性 | 修改风险高 | 易于添加新功能 |

## 🏗️ 架构设计

### 分层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Controller Layer                         │
│                ShoppingCartV2Controller                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    Service Layer                            │
│              ShoppingCartHandleServiceV2                    │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Processor Layer                            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ CartCommodity   │ │ CartActivity    │ │ CartPrice       │ │
│  │ Processor       │ │ Handler         │ │ Calculator      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐                                         │
│  │ CartData        │                                         │
│  │ Assembler       │                                         │
│  └─────────────────┘                                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Data Access Layer                           │
│     Redis, 商品中台, 活动中台, 会员服务                        │
└─────────────────────────────────────────────────────────────┘
```

### 数据流图

```
Request → Validation → Product Processing → Activity Processing 
    ↓
Price Calculation → Data Assembly → Response
```

## 🔧 核心组件详解

### 1. CartCommodityProcessor (商品处理器)

**职责**: 处理商品基础信息的获取、验证和预处理

**核心方法**:
- `processProductInfo()`: 主处理方法
- `cleanInvalidProducts()`: 清理无效商品
- `getProductDetails()`: 获取商品详细信息
- `queryO2OStoreSpecs()`: 查询O2O商品规格
- `querySpStoreSpecs()`: 查询云仓商品规格

**处理流程**:
```java
1. 清理无效商品 (下架、失效门店等)
2. 过滤普通商品 (排除赠品、换购商品)
3. 处理集团化数据隔离 (如果需要)
4. 分类商品 (O2O vs 云仓)
5. 调用商品中台获取详情
6. 构建商品规格Map
7. 设置OBC和B2C配置
```

**关键实现**:
```java
// 分类商品规格ID和门店ID
for (CartCommodityDTO spec : redisSpecs) {
    if (spec.getStoreId().startsWith(LocalConst.CLOUD_FIXED_STORE)) {
        // 云仓商品
        spSpecIds.add(Long.valueOf(spec.getSpecId()));
        spCodes.add(spec.getSpCode());
    } else {
        // O2O商品
        o2oSpecIds.add(Long.valueOf(spec.getSpecId()));
        o2oStoreIds.add(spec.getStoreId());
    }
}
```

### 2. CartActivityHandler (活动处理器)

**职责**: 处理各种促销活动和优惠计算

**核心方法**:
- `getPromotionActivities()`: 获取促销活动
- `handleMemberActivities()`: 处理会员活动
- `handleCouponDiscount()`: 处理优惠券
- `handleGiftAndRepurchaseProducts()`: 处理赠品和换购

**处理流程**:
```java
1. 调用活动中台获取促销活动
2. 处理Plus会员优惠
3. 处理会员日活动
4. 处理会员权益优惠
5. 处理优惠券优惠
6. 处理赠品和换购商品
```

### 3. CartPriceCalculator (价格计算器)

**职责**: 负责各种价格的计算和处理

**核心方法**:
- `createPriceDiscountParam()`: 创建价格优惠参数
- `calculateFinalPrices()`: 计算最终价格
- `handleVipPricing()`: 处理VIP价格
- `calculateEstimatedSavings()`: 计算预计节省

**VIP价格处理示例**:
```java
productData.getStoreSpecs().forEach(storeSpec -> {
    if (storeSpec.getVipPrice() != null && 
        storeSpec.getVipPrice().compareTo(storeSpec.getPrice()) < 0) {
        // 应用VIP价格
        storeSpec.setOriginalPrice(storeSpec.getPrice());
        storeSpec.setPrice(storeSpec.getVipPrice());
    }
});
```

### 4. CartDataAssembler (数据组装器)

**职责**: 将处理后的数据组装成最终的响应格式

**核心方法**:
- `assembleResponse()`: 组装主响应
- `assembleStoreCommodities()`: 按门店分组组装
- `assembleCommodityData()`: 组装单个商品
- `handlePrescriptionDrugCompliance()`: 处理处方药合规

**门店分组逻辑**:
```java
// 按门店分组Redis商品
Map<String, List<CartCommodityDTO>> storeGroupedSpecs = 
    redisSpecs.stream().collect(Collectors.groupingBy(CartCommodityDTO::getStoreId));

// 为每个门店组装商品数据
for (Map.Entry<String, List<CartCommodityDTO>> entry : storeGroupedSpecs.entrySet()) {
    String storeId = entry.getKey();
    List<CartCommodityDTO> storeRedisSpecs = entry.getValue();
    
    CartCommodityStoreDTO storeDTO = assembleStoreData(storeId, storeRedisSpecs, ...);
    storeCommodityList.add(storeDTO);
}
```

## 💻 实现细节

### 错误处理策略

**分层错误处理**:
```java
// 主服务层 - 捕获所有异常
try {
    // 业务逻辑
} catch (IllegalArgumentException e) {
    log.warn("参数错误: {}", e.getMessage());
    throw e;
} catch (Exception e) {
    log.error("系统错误", e);
    throw new RuntimeException("购物车处理失败", e);
}

// 处理器层 - 捕获特定异常
try {
    // 具体处理逻辑
} catch (Exception e) {
    log.error("商品处理失败", e);
    // 返回默认值或空结果，不中断主流程
    return Lists.newArrayList();
}
```

### 日志记录规范

**日志级别使用**:
- `INFO`: 关键业务流程节点
- `WARN`: 业务异常但不影响主流程
- `ERROR`: 系统错误和异常
- `DEBUG`: 详细调试信息

**日志格式示例**:
```java
log.info("购物车商品算价V2开始，用户：{}，商品数量：{}", 
         request.getUserId(), request.getCartRedisSpecs().size());

log.warn("未找到商品规格信息，跳过该商品，规格ID：{}，门店ID：{}", 
         specId, storeId);

log.error("获取商品详细信息失败，用户：{}", request.getUserId(), e);
```

### 性能优化点

**1. 批量查询优化**:
```java
// 批量查询商品规格，而不是逐个查询
PageDTO<StoreSpec> storeSpecs = shoppingCartHandleService.queryStoreSpecByClient(
    specIds, storeIds, merCode, queryParam);
```

**2. Map缓存优化**:
```java
// 构建Map避免重复查找
Map<String, StoreSpec> storeSpecMap = buildStoreSpecMap(allStoreSpecs);
Map<String, ActivitySpecDTO> activitySpecMap = buildActivitySpecMap(activitySpecs);
```

**3. 流式处理**:
```java
// 使用Stream API进行高效的数据处理
return redisSpecs.stream()
    .filter(spec -> PmtProductType.NORMAL.getCode().equals(spec.getPmtProductType()))
    .collect(Collectors.toList());
```

## 📖 使用指南

### 基础使用

```java
@Autowired
private ShoppingCartHandleServiceV2 shoppingCartHandleServiceV2;

public CartCommodityRespCommonDTO processCart(ShoppingCartCommodityReqDTO request) {
    try {
        return shoppingCartHandleServiceV2.handleCartCommodityV2(request);
    } catch (IllegalArgumentException e) {
        // 处理参数错误
        log.warn("参数错误: {}", e.getMessage());
        return createErrorResponse("参数错误");
    } catch (Exception e) {
        // 处理系统错误
        log.error("系统错误", e);
        return createErrorResponse("系统繁忙，请稍后重试");
    }
}
```

### 控制器集成

```java
@PostMapping("/api/v2/shopping-cart/commodities")
public ResponseBase<CartCommodityRespCommonDTO> getCartCommoditiesV2(
        @Validated @RequestBody ShoppingCartCommodityReqDTO request) {
    
    CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
    return ResponseBase.success(result);
}
```

### 请求参数构建

```java
ShoppingCartCommodityReqDTO request = new ShoppingCartCommodityReqDTO();
request.setMerCode("MERCHANT_001");
request.setUserId("123456");
request.setIfCartOperate(true);

// 设置登录用户
LoginUserDTO loginUser = LoginUserDTO.builder()
    .merCode("MERCHANT_001")
    .userId("123456")
    .build();
request.setLoginUser(loginUser);

// 设置购物车商品
List<CartCommodityDTO> cartSpecs = Lists.newArrayList();
// ... 添加商品
request.setCartRedisSpecs(cartSpecs);
```

## 🧪 测试策略

### 单元测试

**测试每个处理器**:
```java
@Test
void testCartCommodityProcessor() {
    // Given
    ShoppingCartCommodityReqDTO request = createTestRequest();
    
    // When
    ShoppingCartProductHandleDTO result = commodityProcessor.processProductInfo(request);
    
    // Then
    assertNotNull(result);
    assertFalse(CollectionUtils.isEmpty(result.getStoreSpecs()));
}
```

### 集成测试

**测试完整流程**:
```java
@Test
void testCompleteCartProcess() {
    // Given
    ShoppingCartCommodityReqDTO request = createCompleteRequest();
    
    // When
    CartCommodityRespCommonDTO result = shoppingCartHandleServiceV2.handleCartCommodityV2(request);
    
    // Then
    assertNotNull(result);
    assertNotNull(result.getStoreCommodityList());
    assertTrue(result.getTotalPrice().compareTo(BigDecimal.ZERO) >= 0);
}
```

### 性能测试

```java
@Test
void testPerformance() {
    int testCount = 1000;
    long startTime = System.currentTimeMillis();
    
    for (int i = 0; i < testCount; i++) {
        ShoppingCartCommodityReqDTO request = createTestRequest();
        shoppingCartHandleServiceV2.handleCartCommodityV2(request);
    }
    
    long endTime = System.currentTimeMillis();
    long avgTime = (endTime - startTime) / testCount;
    
    assertTrue(avgTime < 100); // 平均响应时间应小于100ms
}
```

## 🚀 部署指南

### 配置要求

**Spring配置**:
```java
@Configuration
@ComponentScan(basePackages = {
    "cn.hydee.ydjia.merchantcustomer.service.v2.impl",
    "cn.hydee.ydjia.merchantcustomer.service.v2.processor"
})
public class CartServiceV2Configuration {
}
```

**依赖注入检查**:
```bash
# 检查所有必要的Bean是否正确注入
curl -X GET http://localhost:8080/actuator/beans | grep -i cart
```

### 灰度发布策略

**1. 功能开关控制**:
```java
@Value("${cart.v2.enabled:false}")
private boolean cartV2Enabled;

public CartCommodityRespCommonDTO processCart(ShoppingCartCommodityReqDTO request) {
    if (cartV2Enabled) {
        return shoppingCartHandleServiceV2.handleCartCommodityV2(request);
    } else {
        return shoppingCartHandleService.handleCartCommodityRespCommonDTO(request);
    }
}
```

**2. 用户白名单**:
```java
@Value("${cart.v2.whitelist:}")
private List<String> v2Whitelist;

public boolean shouldUseV2(String userId) {
    return v2Whitelist.contains(userId) || 
           userId.hashCode() % 100 < rolloutPercentage;
}
```

### 监控配置

**关键指标监控**:
```java
// 响应时间监控
@Timed(name = "cart.v2.process.time", description = "Cart V2 processing time")
public CartCommodityRespCommonDTO handleCartCommodityV2(ShoppingCartCommodityReqDTO request) {
    // ...
}

// 成功率监控
@Counted(name = "cart.v2.process.total", description = "Cart V2 total requests")
@Counted(name = "cart.v2.process.errors", description = "Cart V2 error requests", 
         condition = "#result == null")
```

## ⚡ 性能优化

### 缓存策略

**商品信息缓存**:
```java
@Cacheable(value = "storeSpecs", key = "#specIds + '_' + #storeIds")
public PageDTO<StoreSpec> queryStoreSpecByClient(List<Long> specIds, List<String> storeIds) {
    // 商品中台调用
}
```

**会员信息缓存**:
```java
@Cacheable(value = "memberInfo", key = "#userId", unless = "#result == null")
public boolean getMemberBaseInfo(Long userId) {
    // 会员服务调用
}
```

### 异步处理

**非关键路径异步化**:
```java
@Async
public CompletableFuture<Void> updateCartStatistics(String userId, CartCommodityRespCommonDTO response) {
    // 异步更新统计信息
    return CompletableFuture.completedFuture(null);
}
```

### 数据库优化

**批量操作**:
```java
// 批量更新Redis
Map<String, CartCommodityDTO> batchUpdates = Maps.newHashMap();
// ... 收集更新数据
redisService.batchUpdate(batchUpdates);
```

## 🔍 故障排查

### 常见问题

**1. 商品信息缺失**
```
症状: 返回的商品列表为空或部分商品缺失
排查: 检查商品中台调用日志，验证规格ID和门店ID的正确性
解决: 确保传入的规格ID和门店ID有效，检查商品上下架状态
```

**2. 价格计算错误**
```
症状: 商品价格显示异常或VIP价格未生效
排查: 检查会员信息获取和VIP价格应用逻辑
解决: 验证会员状态，检查VIP价格配置
```

**3. 活动信息丢失**
```
症状: 促销活动未显示或活动价格未生效
排查: 检查活动中台调用和活动信息应用逻辑
解决: 验证活动有效期和参与条件
```

### 日志分析

**关键日志搜索**:
```bash
# 查看用户购物车处理日志
grep "购物车商品算价V2" application.log | grep "userId:123456"

# 查看错误日志
grep "ERROR" application.log | grep "cart"

# 查看性能日志
grep "耗时" application.log | grep "cart"
```

### 性能分析

**慢查询分析**:
```java
// 添加性能监控
long startTime = System.currentTimeMillis();
try {
    // 业务逻辑
} finally {
    long duration = System.currentTimeMillis() - startTime;
    if (duration > 1000) { // 超过1秒记录慢查询
        log.warn("慢查询检测: 方法={}, 耗时={}ms", methodName, duration);
    }
}
```

## 📈 监控指标

### 业务指标
- 购物车处理成功率
- 平均响应时间
- 商品查询成功率
- 活动应用成功率

### 技术指标
- JVM内存使用率
- GC频率和耗时
- 数据库连接池状态
- Redis连接状态

### 告警规则
- 响应时间 > 2秒
- 错误率 > 5%
- 内存使用率 > 80%
- 数据库连接数 > 80%

---

## 📞 支持与反馈

如果在使用过程中遇到问题，请：

1. 查看本文档的故障排查部分
2. 检查相关日志文件
3. 联系开发团队获取支持

**重构团队**: 购物车V2重构小组  
**文档版本**: 2.0  
**最后更新**: 2024年
