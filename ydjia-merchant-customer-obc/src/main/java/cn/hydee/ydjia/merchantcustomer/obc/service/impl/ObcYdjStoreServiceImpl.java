package cn.hydee.ydjia.merchantcustomer.obc.service.impl;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantcustomer.feign.client.*;
import cn.hydee.ydjia.merchantcustomer.feign.domain.HomepageDimension;
import cn.hydee.ydjia.merchantcustomer.feign.domain.YdjStore;
import cn.hydee.ydjia.merchantcustomer.feign.dto.medical.MedicalInsuranceBatchReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.medical.MedicalInsurancePaymentConfig;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.*;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.*;
import cn.hydee.ydjia.merchantcustomer.feign.enums.*;
import cn.hydee.ydjia.merchantcustomer.feign.prescription.MessageData;
import cn.hydee.ydjia.merchantcustomer.feign.service.MallMerchantConfigService;
import cn.hydee.ydjia.merchantcustomer.feign.service.MallRedisService;
import cn.hydee.ydjia.merchantcustomer.feign.service.StoreRuleService;
import cn.hydee.ydjia.merchantcustomer.feign.service.wrapper.CommonStoreClientWrapperService;
import cn.hydee.ydjia.merchantcustomer.feign.util.LocalConst;
import cn.hydee.ydjia.merchantcustomer.feign.util.LocationUtils;
import cn.hydee.ydjia.merchantcustomer.obc.dto.req.ObcStoreHomePageReqDto;
import cn.hydee.ydjia.merchantcustomer.obc.dto.resp.ObcStoreHomePageDto;
import cn.hydee.ydjia.merchantcustomer.obc.repository.ObcYdjStoreRepo;
import cn.hydee.ydjia.merchantcustomer.obc.service.ObcCommodityTypeService;
import cn.hydee.ydjia.merchantcustomer.obc.service.ObcPromoteService;
import cn.hydee.ydjia.merchantcustomer.obc.service.ObcYdjStoreService;
import cn.hydee.ydjia.merchantcustomer.util.ConvertUtil;
import cn.hydee.ydjia.merchantcustomer.util.DateUtils;
import cn.hydee.ydjia.merchantcustomer.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.awt.geom.Point2D;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 微商城门店服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/21 11:20
 */
@Service
@Slf4j
public class ObcYdjStoreServiceImpl implements ObcYdjStoreService {
    @Autowired
    private ObcYdjStoreRepo ydjStoreRepo;
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private ObcPromoteService promoteService;
    @Autowired
    private OrderInfoClient orderInfoClient;
    @Autowired
    private MallMerchantConfigService mallMerchantConfigService;
    @Autowired
    private MallRedisService redisService;
    @Autowired
    private CommonStoreClientWrapperService commonStoreClientWrapperService;

    /**
     * 查中心店数据true需要计算经纬度，false不需要计算经纬度
     *
     * @param merCode c
     * @param flag            c
     * @return c
     */
    @Override
    public StoreListResDTO queryCenterStore(String merCode, Boolean flag) {
        String cacheKey = "mall:query_center_store:merCode:" + merCode + ":" + flag;
        StoreListResDTO storeListRes = (StoreListResDTO) redisService.getObject(cacheKey);
        if (storeListRes != null) {
            return storeListRes;
        }
        List<YdjStore> list = this.queryYdjCenterStore(merCode);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
        storeListReqDTO.setMerCode(merCode);
        StoreListResDTO storeListResDTO;
        if (flag) {
            storeListReqDTO.setStCode(list.get(0).getStCode());
            PageDTO<StoreListResDTO> pageDTO = commonStoreClientWrapperService.pageStoreV3(storeListReqDTO);
            storeListResDTO = Objects.isNull(pageDTO) || CollectionUtils.isEmpty(pageDTO.getData()) ? null : pageDTO.getData().get(0);
        } else {
            QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
            queryStoreDTO.setMerCode(storeListReqDTO.getMerCode());
            queryStoreDTO.setStCode(list.get(0).getStCode());
            queryStoreDTO.setExcelFlag(true);
            ResponseBase<PageDTO<StoreResDTO>> res = storeClient.queryStoreByCondition(queryStoreDTO);
            if (res == null || !res.checkSuccess() || CollectionUtils.isEmpty(res.getData().getData())) {
                log.warn("{}", JSON.toJSONString(res));
                return null;
            }
            storeListResDTO = new StoreListResDTO();
            storeListResDTO.setMerCode(storeListReqDTO.getMerCode());
            storeListResDTO.setId(res.getData().getData().get(0).getId());
        }
        if (storeListResDTO != null) {
            redisService.setValueExpire(cacheKey, storeListResDTO, 15, TimeUnit.MINUTES);
        }
        return storeListResDTO;
    }

    @DS(LocalConst.DB_MANAGER_SLAVE)
    @Override
    public List<YdjStore> queryYdjCenterStore(String merCode) {
        if (mallMerchantConfigService.checkMerchantCollectivizationPackageMark(merCode)) {
            // 集团化套餐开启
            return null;
        }
        QueryWrapper<YdjStore> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(YdjStore::getMerCode, merCode);
        queryWrapper.lambda().eq(YdjStore::getCenterStore, IsCenterStore.IS_CENTER_SOTRE.getCode());
        queryWrapper.lambda().eq(YdjStore::getIsvalid, IsvalidStatus.EFFECTIVE.getCode());
        List<YdjStore> list = ydjStoreRepo.selectList(queryWrapper);
        return list;
    }

    @Override
    public List<ActiPriceSearchRespDTO> queryObcStoreAct(ObcStoreHomePageReqDto req) {
        //1、查询门店活动信息
        ActiStoreSearchReqDTO actiStoreSearchReqDTO = new ActiStoreSearchReqDTO();
        BeanUtils.copyProperties(req, actiStoreSearchReqDTO);
        actiStoreSearchReqDTO.setIsObcModel(true);
        actiStoreSearchReqDTO.setPmtNewMember(this.checkPmtNewMember(req.getMerCode(), req.getMemberId()));
        List<ActiPriceSearchRespDTO> actList = promoteService.queryStoreAllAct(actiStoreSearchReqDTO);
        if (CollectionUtils.isEmpty(actList)) {
            return null;
        }
        //2、根据正在进行和未开始活动来分类集合
        List<ActiPriceSearchRespDTO> underWayList = actList.stream().filter(a -> a.getStartTime().isBefore(LocalDateTime.now())).collect(Collectors.toList());
        List<ActiPriceSearchRespDTO> noOnList = actList.stream().filter(a -> a.getStartTime().isAfter(LocalDateTime.now())).collect(Collectors.toList());
        List<ActiPriceSearchRespDTO> limitList = new ArrayList<>();
        List<ActiPriceSearchRespDTO> fullList = new ArrayList<>();
        List<ActiPriceSearchRespDTO> addList = new ArrayList<>();
        List<ActiPriceSearchRespDTO> moreDiscountList = new ArrayList<>();
        //3、如果正在进行中的活动集合不为空，处理数据
        if (!CollectionUtils.isEmpty(underWayList)) {
            underWayList.forEach(n -> {
                n.setRemainTime(this.setRemainTime(n.getEndTime()));
                n.setValidStatus(1);
            });
            this.addStoreAct(underWayList, limitList, fullList, addList, moreDiscountList);
            //特殊处理限时特惠活动
            this.dealLimitAct(noOnList, limitList);
            limitList.sort(Comparator.comparing(ActiPriceSearchRespDTO::getEndTime));
            actList.clear();
            //清空查询到的活动集合列表并重新按优先顺序添加活动集合
            ListUtils.addAllList(actList, limitList, moreDiscountList, fullList, addList);
            //清空分类集合后重新添加未开始的分类活动
            ListUtils.clearAllList(limitList, moreDiscountList, fullList, addList);
        } else {
            actList.clear();
        }
        //4、如果未开始的活动集合不为空，处理顺序
        if (!CollectionUtils.isEmpty(noOnList)) {
            noOnList.forEach(n -> {
                n.setRemainTime(this.setRemainTime(n.getStartTime()));
                n.setValidStatus(0);
            });
            noOnList.sort(Comparator.comparing(ActiPriceSearchRespDTO::getStartTime));
            //然后再添加未开始的活动
            this.addStoreAct(noOnList, limitList, fullList, addList, moreDiscountList);
            ListUtils.addAllList(actList, limitList, moreDiscountList, fullList, addList);
        }
        return actList;
    }

    /**
     * 处理限时特惠活动
     *
     * @param noOnList  c
     * @param limitList c
     */
    private void dealLimitAct(List<ActiPriceSearchRespDTO> noOnList, List<ActiPriceSearchRespDTO> limitList) {
        Iterator<ActiPriceSearchRespDTO> iterator = limitList.iterator();
        while (iterator.hasNext()) {
            //如果是无周期的活动，不作处理
            ActiPriceSearchRespDTO next = iterator.next();
            if (YesOrNoType.NO.getCode().equals(next.getIsCycle())
                    || next.getIsCycle() == null || next.getCycleSelection() == null) {
                continue;
            }
            //有周期的活动判断是那个类型
            switch (next.getCycleSelection()) {
                //每日的活动筛除已结束的和未开始的
                case 0:
                    if (next.getCycleEndTime().isBefore(LocalTime.now())) {
                        iterator.remove();
                        break;
                    }
                    if (next.getCycleStartTime().isAfter(LocalTime.now())) {
                        next.setStartTime(DateUtils.spliceDateAndTime(new Date(), next.getCycleStartTime()));
                        noOnList.add(next);
                        iterator.remove();
                        break;
                    }
                    //重新设置结束时间和倒计时时间
                    next.setEndTime(DateUtils.spliceDateAndTime(new Date(), next.getCycleEndTime()));
                    next.setRemainTime(this.setRemainTime(next.getEndTime()));
                    break;
                case 1:
                    String[] split = next.getCycleSelectionValue().split(",");
                    int dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
                    if (Arrays.binarySearch(split, String.valueOf(dayOfWeek)) < 1) {
                        iterator.remove();
                        break;
                    }
                    if (next.getCycleEndTime().isBefore(LocalTime.now())) {
                        iterator.remove();
                        break;
                    }
                    if (next.getCycleStartTime().isAfter(LocalTime.now())) {
                        next.setStartTime(DateUtils.spliceDateAndTime(new Date(), next.getCycleStartTime()));
                        noOnList.add(next);
                        iterator.remove();
                        break;
                    }
                    next.setEndTime(DateUtils.spliceDateAndTime(new Date(), next.getCycleEndTime()));
                    next.setRemainTime(this.setRemainTime(next.getEndTime()));
                    break;
                case 2:
                    String[] split1 = next.getCycleSelectionValue().split(",");
                    int dayOfMonth = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
                    if (Arrays.binarySearch(split1, String.valueOf(dayOfMonth)) < 1) {
                        iterator.remove();
                        break;
                    }
                    if (next.getCycleEndTime().isBefore(LocalTime.now())) {
                        iterator.remove();
                        break;
                    }
                    if (next.getCycleStartTime().isAfter(LocalTime.now())) {
                        next.setStartTime(DateUtils.spliceDateAndTime(new Date(), next.getCycleStartTime()));
                        noOnList.add(next);
                        iterator.remove();
                    }
                    next.setEndTime(DateUtils.spliceDateAndTime(new Date(), next.getCycleEndTime()));
                    next.setRemainTime(this.setRemainTime(next.getEndTime()));
                    break;
                default:
                    throw new IllegalStateException("Unexpected value: " + next.getCycleSelection());
            }
        }
    }

    /**
     * 计算出倒计时（距离开始或距离结束）
     *
     * @param time c
     * @return c
     */
    private String setRemainTime(LocalDateTime time) {
        String remainTime = null;
        long l = time.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        if (l > System.currentTimeMillis()) {
            remainTime = String.valueOf(l - System.currentTimeMillis());
        }
        return remainTime;
    }

    /**
     * 分类添加门店活动数据
     *
     * @param actList          c
     * @param limitList        c
     * @param fullList         c
     * @param addList          c
     * @param moreDiscountList c
     */
    private void addStoreAct(List<ActiPriceSearchRespDTO> actList, List<ActiPriceSearchRespDTO> limitList
            , List<ActiPriceSearchRespDTO> fullList, List<ActiPriceSearchRespDTO> addList, List<ActiPriceSearchRespDTO> moreDiscountList) {
        actList.forEach(a -> {
            switch (a.getPmtType()) {
                case 11:
                    limitList.add(a);
                    break;
                case 14:
                    fullList.add(a);
                    break;
                case 15:
                    addList.add(a);
                    break;
                case 21:
                    moreDiscountList.add(a);
                    break;
                default:
                    break;
            }
        });
    }


    public Integer checkPmtNewMember(String merCode, String userId) {
        if (StringUtils.isEmpty(userId)) {
            return LocalConst.STATUS_ZERO;
        }
        ResponseBase<Boolean> countMemberOrder = orderInfoClient.checkMemberOrder(merCode, userId);
        if (countMemberOrder == null || !countMemberOrder.checkSuccess()) {
            log.warn("checkMemberOrder req =>{}，res =>{}", userId, JSON.toJSONString(countMemberOrder));
            return LocalConst.STATUS_ONE;
        }
        Boolean memberOrderFlag = countMemberOrder.getData();
        if (memberOrderFlag == null || !memberOrderFlag) {
            log.warn("checkMemberOrder req =>{}，res =>{}", userId, JSON.toJSONString(countMemberOrder));
            return LocalConst.STATUS_ONE;
        }
        return LocalConst.STATUS_TWO;
    }
}
