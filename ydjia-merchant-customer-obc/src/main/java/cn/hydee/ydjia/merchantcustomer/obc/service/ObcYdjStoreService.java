package cn.hydee.ydjia.merchantcustomer.obc.service;

import cn.hydee.ydjia.merchantcustomer.feign.domain.YdjStore;
import cn.hydee.ydjia.merchantcustomer.feign.dto.req.StoreListReqDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.ActiPriceSearchRespDTO;
import cn.hydee.ydjia.merchantcustomer.feign.dto.resp.StoreListResDTO;
import cn.hydee.ydjia.merchantcustomer.obc.dto.req.ObcStoreHomePageReqDto;
import cn.hydee.ydjia.merchantcustomer.obc.dto.resp.ObcStoreHomePageDto;

import java.util.List;


/**
 * 微商城门店服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/10/21 11:20
 */
public interface ObcYdjStoreService {
    /**
     * 查找中心店（旗舰店）
     *
     * @param merCode 请求对象
     * @return StoreListResDTO 返回对象
     */
    StoreListResDTO queryCenterStore(String merCode, Boolean flag);

    /**
     * 查微商城中心店
     *
     * @param merCode 商家编码
     * @return List<YdjStore> 返回对象
     */
    List<YdjStore> queryYdjCenterStore(String merCode);

    /**
     * 查询OBC模式门店活动列表
     *
     * @param req c
     * @return c
     */
    List<ActiPriceSearchRespDTO> queryObcStoreAct(ObcStoreHomePageReqDto req);

}
