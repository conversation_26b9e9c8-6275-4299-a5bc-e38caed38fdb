package cn.hydee.ydjia.merchantmanager.controller;

import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.ydjia.merchantmanager.dto.SdpUserInfoDTO;
import cn.hydee.ydjia.merchantmanager.dto.req.*;
import cn.hydee.ydjia.merchantmanager.dto.resp.EmployeeResDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.SdpUserExportExcelDTO;
import cn.hydee.ydjia.merchantmanager.dto.resp.UserDetailRespDto;
import cn.hydee.ydjia.merchantmanager.dto.resp.UserSearchRespDto;
import cn.hydee.ydjia.merchantmanager.enums.ErrorType;
import cn.hydee.ydjia.merchantmanager.enums.SdpUserTypeEnum;
import cn.hydee.ydjia.merchantmanager.service.SdpUserInfoService;
import cn.hydee.ydjia.merchantmanager.util.ExcelUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Classname SdpUserInfoController
 * @Description 分销用户信息
 * @Date 2020/9/16 11:02
 * @Created lizhaoyang-2695
 */
@RestController
@RequestMapping(value = "/${api.version}/sdp/userinfo")
public class SdpUserInfoController extends BaseController {
    @Autowired
    private SdpUserInfoService sdpUserInfoService;


    @ApiOperation(value = "批量新增员工", notes = "批量新增员工")
    @PostMapping("/addBatch")
    public ResponseBase<Boolean> addBatch(@Valid @RequestBody UserInfoAddBatchReqDto dto, BindingResult bindingResult) {
        checkValid(bindingResult);
        return sdpUserInfoService.addBatch(dto);
    }

    @PostMapping("/_search")
    @ApiOperation(value = "搜索用户")
    public ResponseBase<PageDTO<UserSearchRespDto>> search(@Valid @RequestBody UserSearchReqDto dto, BindingResult result) {
        checkValid(result);
        return sdpUserInfoService.search(dto);
    }

    @GetMapping("/detail/{userId}")
    @ApiOperation(value = "用户详情")
    public ResponseBase<UserDetailRespDto> detail(@PathVariable String userId, @RequestHeader String merCode) {
        return sdpUserInfoService.detail(userId, merCode);
    }

    @PostMapping(value="/batchDownQrCode")
    @ApiOperation(value = "下载二维码")
    public void downQrCode(@Valid @RequestBody UserDownCodeReqDto dto) {
          sdpUserInfoService.downQrCode(dto);
//          return generateBitSuccess(true);
    }

    @PostMapping("/checkUserMobile")
    @ApiOperation(value = "检测手机号码")
    public ResponseBase<SdpUserInfoDTO> checkUserMobile(@Valid @RequestBody UserCheckMobileReqDto dto, BindingResult result) {
        checkValid(result);
        return sdpUserInfoService.checkUserMobile(dto);
    }

    @PostMapping("/batchUpdateRelate")
    @ApiOperation(value = "批量调整关系链")
    public ResponseBase<Boolean> batchUpdateRelate(@Valid @RequestBody UserUpdatRelationReqDto dto, BindingResult result,@RequestHeader String userName) {
        checkValid(result);
        dto.setOperationName(userName);
        return sdpUserInfoService.batchUpdateRelate(dto);
    }

    @PostMapping("/_export")
    @ApiOperation(value = "导出用户信息")
    public void export(@RequestBody UserSearchReqDto dto, BindingResult result, HttpServletResponse response) throws IOException {
        checkValid(result);

        List<UserSearchRespDto> userList = sdpUserInfoService.allSdpUsersList(dto.getMerCode());
        if (CollectionUtils.isEmpty(userList)) {
            throw WarnException.builder().code(ErrorType.EXCEL_ROW_NULL.getCode()).
                    tipMessage(ErrorType.EXCEL_ROW_NULL.getMsg()).build();
        }
        List<SdpUserExportExcelDTO> excelDTOS = userList.stream().map(t->{
            SdpUserExportExcelDTO excelDTO = new SdpUserExportExcelDTO();
            StringBuilder sdpUser = new StringBuilder(t.getUserName());
            if (StringUtils.isNotBlank(t.getUserMobile())) {
                sdpUser.append("\n").append(t.getUserMobile());
            }
            excelDTO.setSdpUser(sdpUser.toString());
            excelDTO.setUserType(SdpUserTypeEnum.getDesc(t.getUserType()));
            StringBuilder leaderUser = new StringBuilder();
            if (StringUtils.isNotBlank(t.getLeaderUserName())) {
                leaderUser.append(t.getLeaderUserName());
            }
            if (StringUtils.isNotBlank(t.getLeaderMobile())) {
                leaderUser.append("\n").append(t.getLeaderMobile());
            }
            excelDTO.setLeaderUser(leaderUser.toString());
            excelDTO.setCommissionAmount(t.getCommissionAmount());
            excelDTO.setDirectInviteCount(t.getDirectInvitCount());
            excelDTO.setIndirectInviteCount(t.getIndirectInvitCount());
            String empStatus = Objects.nonNull(t.getEmpStatus()) ? (Objects.equals(1,t.getEmpStatus()) ? "在职" : "离职") : "";
            excelDTO.setEmpStatus(empStatus);
            excelDTO.setCreateTime(t.getCreateTime());
            return excelDTO;
        }).collect(Collectors.toList());
        ExcelUtil.exportSheets("分销员信息", "sdpUser", excelDTOS, SdpUserExportExcelDTO.class, response);
    }

    @PostMapping("/getSdpUserList")
    @ApiOperation(value = "搜索员工用户")
    public ResponseBase<PageDTO<EmployeeResDTO>> search(@Valid @RequestBody QueryEmpDTO queryEmpDTO, BindingResult result) {
        checkValid(result);
        return sdpUserInfoService.getSdpUserList(queryEmpDTO);
    }


    @PostMapping("/addAllEmp")
    public Boolean addAllEmp() {
        sdpUserInfoService.handleSdpUserInfo();
        return true;
    }

}
