package cn.hydee.ydjia.merchantcustomer.feign.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 商城公共设置
 * <AUTHOR>
 * @date 17:47 2020/4/9
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_public")
public class CmsPublic extends DomainBase{
    @ApiModelProperty(value = "主键ID,创建时不传")
    private String id;
    @NotBlank(message = "商家编码不可为空")
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @NotBlank(message = "类型不可为空")
    @ApiModelProperty(value = "配置类型，wheel-首页轮播，notice-公告")
    private String type;
    @ApiModelProperty(value = "备注，长度100")
    private String remark;
    private String img;
    private String url;
    private Date startTime;
    private Date endTime;
    private Short sort;
    private String value;
    private String posCode;
}