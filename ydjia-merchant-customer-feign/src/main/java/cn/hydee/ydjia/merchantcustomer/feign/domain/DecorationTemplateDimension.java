package cn.hydee.ydjia.merchantcustomer.feign.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("decoration_template_dimension")
public class DecorationTemplateDimension {
    
    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 页面标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 搜索预显
     */
    private String searchHint;

    /**
     * 页面主色，背景颜色
     */
    private String backgroundColor;

    /**
     * 有无边框：0-无，1-有
     */
    private Integer borderFlag;

    /**
     * 边框大小，单位px
     */
    private Integer borderSize;

    /**
     * 圆角大小，单位px，0-表示为直角
     */
    private Integer radiusSize;

    /**
     * 边框颜色
     */
    private String borderColor;

    /**
     * 分享描述
     */
    private String shareDesc;

    /**
     * 分享图片url
     */
    private String shareImg;

    /**
     * 海报图链接
     */
    private String posterUrl;

    /**
     * 配置类型：1-首页，2-DM单，3-好物甄选
     */
    private Integer type;

    /**
     * 同步开关状态：0-未开启，1-已开启
     */
    private Integer syncStatus;

    /**
     * 是否有效：1-有效，0-无效
     */
    @TableLogic(value = "1", delval = "0")
    private Integer isValid;

    /**
     * 是否预览模板：0-非预览，1-预览
     */
    private Integer isPreview;

    /**
     * 模板展示图片
     */
    private String showImg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

}