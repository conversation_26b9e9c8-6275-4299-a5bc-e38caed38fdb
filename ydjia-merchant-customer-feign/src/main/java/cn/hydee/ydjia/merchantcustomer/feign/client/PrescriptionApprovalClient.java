package cn.hydee.ydjia.merchantcustomer.feign.client;

import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.ydjia.merchantcustomer.feign.util.LocalConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = LocalConst.ORDER_APP_NAME)
public interface PrescriptionApprovalClient {

    /**
     * 商户是否升级小蜜
     * @param merCode
     * @return
     */
    @GetMapping("/${api.version}/request-approval/isMedicalCloudVersion/{merCode}")
    ResponseBase<Boolean> isMedicalCloudVersion(@PathVariable String merCode);
}
