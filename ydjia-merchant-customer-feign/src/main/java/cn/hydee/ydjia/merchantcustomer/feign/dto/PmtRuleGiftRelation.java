package cn.hydee.ydjia.merchantcustomer.feign.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 满减赠规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PmtRuleGiftRelation {

    @ApiModelProperty(value = "规则id")
    private String specId;

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "规则ID,规则表主键ID")
    private Long ruleId;

    @ApiModelProperty(value = "赠品ID,赠品表主键ID")
    private Long giftId;

    @ApiModelProperty(value = "单个赠品单次赠送件数")
    private Integer amount;

    @ApiModelProperty(value = "赠品名称")
    private String name;

}