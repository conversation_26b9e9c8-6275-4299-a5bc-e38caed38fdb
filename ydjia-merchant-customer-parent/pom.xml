<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.hydee.starter</groupId>
        <artifactId>hydee-spring-boot-starter-parent</artifactId>
        <version>1.2.1</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.hydee.ydjia</groupId>
    <artifactId>ydjia-merchant-customer-parent</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>../ydjia-merchant-customer-feign</module>
        <module>../ydjia-merchant-customer-obc</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <common.pool.version>2.5.0</common.pool.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hydee.starter</groupId>
            <artifactId>hydee-spring-boot-starter</artifactId>
            <version>4.8.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hydee.ydjia</groupId>
            <artifactId>ydjia-srm-adapter-sdk</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-boot-starter -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>2.5.4</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.10</version>
        </dependency>
        <!--
                http请求类工具
        -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.2.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.3</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.4</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
            <version>5.8.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${common.pool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.1.5.RELEASE</version>
            <exclusions>
                <!-- 去掉对Lettuce的依赖，Spring Boot优先使用Lettuce作为Redis客户端-->
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hydee</groupId>
            <artifactId>honey-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.hydee.starter</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hydee.starter</groupId>
            <artifactId>hydee-cache-refresh</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.hydee</groupId>
            <artifactId>hydee-hippo4j-spring-boot-starter</artifactId>
            <version>3.4.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.hydee.starter</groupId>
            <artifactId>hydee-mybatisplus-spring-boot-starter</artifactId>
            <version>3.4.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-middle-coupon-sdk</artifactId>
            <version>1.2.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yxt</groupId>
                    <artifactId>yxt-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yxt.middle</groupId>
            <artifactId>yxt-middle-baseinfo-sdk</artifactId>
            <version>1.26.6-RELEASE</version>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>local-releases</id>
            <name>releases</name>
            <url>https://nexus.hxyxt.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>local-snapshots</id>
            <name>snapshots</name>
            <url>https://nexus.hxyxt.com/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>ban-snapshot</id>
                        <configuration>
                            <rules>
                                <requireReleaseDeps>
                                    <!--enforcer自定义配置 更多配置项参考：https://maven.apache.org/enforcer/maven-enforcer-plugin/-->
                                    <!--例如：排除指定依赖-->
                                    <excludes>
                                        <!--格式：groupId:artifactId:version，支持表达式*，匹配所有，.-->
                                        <exclude>cn.hydee.starter:hydee-cache-refresh</exclude>
                                        <exclude>cn.hydee.starter:seata-spring-boot-starter</exclude>
                                        <exclude>cn.hydee.starter:hydee-mybatisplus-spring-boot-starter</exclude>
                                        <exclude>com.hydee:honey-common</exclude>
                                        <exclude>cn.hydee:hydee-hippo4j-spring-boot-starter</exclude>
                                        <exclude>cn.hydee.ydjia:ydjia-srm-adapter-sdk</exclude>
                                        <exclude>cn.hydee.ydjia:ydjia-merchant-customer-feign</exclude>
                                        <exclude>cn.hydee.ydjia:ydjia-merchant-customer-obc</exclude>
                                    </excludes>
                                    <failWhenParentIsSnapshot>false</failWhenParentIsSnapshot>
                                </requireReleaseDeps>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
